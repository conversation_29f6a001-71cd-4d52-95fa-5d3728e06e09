name: Build Test

on:
  workflow_dispatch:
  push:
    tags:
      - "T*"

jobs:
  build:
    runs-on: ubuntu-22.04

    steps:
      - name: checkout android
        uses: actions/checkout@v2
        with:
          fetch-depth: 0
          path: android

      - name: Set variables
        run: |
          COMMIT=$(cat android/.rn-commit)
          echo "COMMIT=$COMMIT" >> $GITHUB_ENV

      - name: checkout react-native
        uses: actions/checkout@v3
        with:
          repository: "iotexproject/react-native-iopay"
          ref: "${{ env.COMMIT }}"
          token: "${{secrets.RN_ACCESS_TOKEN}}"
          path: rn

      - run: mv rn/* ./

      - name: set up nodejs
        uses: actions/setup-node@v2
        with:
          node-version: "18.20.3"
#           cache: "yarn"

      - name: set up SDK 11
        uses: actions/setup-java@v1
        with:
          java-version: 17

      - name: Caching
        uses: actions/cache@v3
        with:
          path: ~/android/.gradle
          key: jars-${{ hashFiles ('andrpod/build.gradle') }}-${{ hashFiles('android/app/build.gradle') }}
          restore-keys: jars-${{ hashFiles('andrpod/build.gradle')}}-${{ hashFiles('andrpod/app/build.gradle') }}

      - run: yarn install && npx rn-nodeify --install --hack --yarn && npx jetify

      - run: cd /usr/local/lib/android/sdk/build-tools/31.0.0 && mv d8 dx && cd lib && mv d8.jar dx.jar

      - name: Build Release APK
        run: |
          cd android
          sed -i "s/INTERNAL_BUILD/${{ github.ref_name }}/g" app/build.gradle
          ./gradlew assembleotherMarketRelease
        
      - uses: r0adkll/sign-android-release@v1
        name: Sign app APK
        id: sign_app
        with:
          releaseDirectory: android/app/build/outputs/apk/otherMarket/release
          signingKeyBase64: ${{ secrets.SIGNING_KEY }}
          alias: ${{ secrets.ALIAS }}
          keyStorePassword: ${{ secrets.KEY_STORE_PASSWORD }}
          keyPassword: ${{ secrets.KEY_PASSWORD }}
        env:
          BUILD_TOOLS_VERSION: "31.0.0"

      - uses: keithweaver/aws-s3-github-action@v1.0.0
        with:
          command: cp
          source: ${{steps.sign_app.outputs.signedReleaseFile}}
          destination: s3://iopay-app/testApk/iopay-${{ github.ref_name }}.apk
          aws_access_key_id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws_secret_access_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws_region: us-east-1
