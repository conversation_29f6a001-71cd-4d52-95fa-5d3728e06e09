name: Build Release

on:
  workflow_dispatch:
  release:
    types: [published]
  push:
    tags:
      - "v*"

jobs:
  build:
    runs-on: ubuntu-22.04

    steps:
      - name: checkout android
        uses: actions/checkout@v2
        with:
          fetch-depth: 0
          path: android

      - name: Set variables
        run: |
          COMMIT=$(cat android/.rn-commit)
          echo "COMMIT=$COMMIT" >> $GITHUB_ENV

      - name: checkout react-native
        uses: actions/checkout@v3
        with:
          repository: "iotexproject/react-native-iopay"
          ref: "${{ env.COMMIT }}"
          token: "${{secrets.RN_ACCESS_TOKEN}}"
          path: rn

      - run: mv rn/* ./

      - name: set up nodejs
        uses: actions/setup-node@v2
        with:
          node-version: "18.20.3"
#           cache: "yarn"

      - name: set up SDK 11
        uses: actions/setup-java@v1
        with:
          java-version: 17

      - name: Caching
        uses: actions/cache@v3
        with:
          path: ~/android/.gradle
          key: jars-${{ hashFiles ('andrpod/build.gradle') }}-${{ hashFiles('android/app/build.gradle') }}
          restore-keys: jars-${{ hashFiles('andrpod/build.gradle')}}-${{ hashFiles('andrpod/app/build.gradle') }}

      - run: yarn install && npx rn-nodeify --install --hack --yarn && npx jetify

      - run: cd /usr/local/lib/android/sdk/build-tools/31.0.0 && mv d8 dx && cd lib && mv d8.jar dx.jar

      - name: Build Release APK
        run: |
          cd android
          LatestTag=$(git describe --tags --abbrev=0 $(git rev-list --tags --max-count=1))
          sed -i 's/applicationId "io.iotex.iopay"/applicationId "io.iotex.iopay.prod"/g' app/build.gradle
          sed -i "s/INTERNAL_BUILD/${LatestTag}/g" app/build.gradle
          ./gradlew assembleotherMarketRelease

          sed -i 's/applicationId "io.iotex.iopay.prod"/applicationId "io.iotex.iopay.gp"/g' app/build.gradle
          ./gradlew assembleplayStoreRelease

      - uses: r0adkll/sign-android-release@v1
        name: Sign app APK
        id: sign_app
        with:
          releaseDirectory: android/app/build/outputs/apk/otherMarket/release
          signingKeyBase64: ${{ secrets.SIGNING_KEY }}
          alias: ${{ secrets.ALIAS }}
          keyStorePassword: ${{ secrets.KEY_STORE_PASSWORD }}
          keyPassword: ${{ secrets.KEY_PASSWORD }}
        env:
          BUILD_TOOLS_VERSION: "31.0.0"

      - uses: keithweaver/aws-s3-github-action@v1.0.0
        with:
          command: cp
          source: ${{steps.sign_app.outputs.signedReleaseFile}}
          destination: s3://iopay-app/iopay-${{ github.ref_name }}.apk
          aws_access_key_id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws_secret_access_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws_region: us-east-1

      - uses: keithweaver/aws-s3-github-action@v1.0.0
        with:
          command: cp
          source: ${{steps.sign_app.outputs.signedReleaseFile}}
          destination: s3://iopay-app/iopay-release.apk
          aws_access_key_id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws_secret_access_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws_region: us-east-1
          
          
      - uses: r0adkll/sign-android-release@v1
        name: Sign app APK gp
        id: sign_app2
        with:
          releaseDirectory: android/app/build/outputs/apk/playStore/release
          signingKeyBase64: ${{ secrets.SIGNING_KEY }}
          alias: ${{ secrets.ALIAS }}
          keyStorePassword: ${{ secrets.KEY_STORE_PASSWORD }}
          keyPassword: ${{ secrets.KEY_PASSWORD }}
        env:
          BUILD_TOOLS_VERSION: "31.0.0"


      - uses: keithweaver/aws-s3-github-action@v1.0.0
        with:
          command: cp
          source: ${{steps.sign_app2.outputs.signedReleaseFile}}
          destination: s3://iopay-app/iopay-${{ github.ref_name }}-gp.apk
          aws_access_key_id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws_secret_access_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws_region: us-east-1

      - uses: keithweaver/aws-s3-github-action@v1.0.0
        with:
          command: cp
          source: ${{steps.sign_app2.outputs.signedReleaseFile}}
          destination: s3://iopay-app/iopay-release-gp.apk
          aws_access_key_id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws_secret_access_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws_region: us-east-1

      - name: Upload binaries to release
        uses: svenstaro/upload-release-action@v2
        with:
          repo_token: ${{ secrets.GITHUB_TOKEN }}
          file: ${{steps.sign_app.outputs.signedReleaseFile}}
          asset_name: iopay-${{ github.ref_name }}.apk
          tag: ${{ github.ref }}

      - name: Upload binaries to release
        uses: svenstaro/upload-release-action@v2
        with:
          repo_token: ${{ secrets.GITHUB_TOKEN }}
          file: ${{steps.sign_app2.outputs.signedReleaseFile}}
          asset_name: iopay-${{ github.ref_name }}-gp.apk
          tag: ${{ github.ref }}

      - name: Upload binaries to release
        uses: svenstaro/upload-release-action@v2
        with:
           repo_token: ${{ secrets.GITHUB_TOKEN }}
           file: android/app/proguardMapping.txt
           asset_name: iopay-${{ github.ref_name }}-proguardMapping.txt
           tag: ${{ github.ref }}
           
           
           
           
           
#      - uses: keithweaver/aws-s3-github-action@v1.0.0
#        with:
#          command: cp
#          source: ${{steps.sign_app2.outputs.signedReleaseFile}}
#          destination: s3://iopay-1317968648/iopay-${{ github.ref_name }}-gp.apk
#          aws_access_key_id: ${{ secrets.COS_SECRETID }}
#          aws_secret_access_key: ${{ secrets.COS_SECRETKEY }}
#          flags: --endpoint-url=https://cos.ap-shanghai.myqcloud.com
#
#      - uses: keithweaver/aws-s3-github-action@v1.0.0
#        with:
#          command: cp
#          source: ${{steps.sign_app2.outputs.signedReleaseFile}}
#          destination: s3://iopay-1317968648/iopay-release-gp.apk
#          aws_access_key_id: ${{ secrets.COS_SECRETID }}
#          aws_secret_access_key: ${{ secrets.COS_SECRETKEY }}
#          flags: --endpoint-url=https://cos.ap-shanghai.myqcloud.com
