[![CircleCI](https://circleci.com/gh/iotexproject/iopay-android.svg?style=svg&circle-token=****************************************)](https://circleci.com/gh/iotexproject/iopay-android)

# IoPay - IoTeX Android Wallet

```bash
<NAME_EMAIL>:iotexproject/iotex-wallet-android2.git
cd iotex-wallet-android2
# init submodule
git submodule update --init


# open project with Android Studio
studio .
```

* [Backend](https://github.com/iotexproject/web-iopay-home)
* [Site](https://iopay.iotex.io)

**Working with Apollo-graphql**

1. Download schema

You may need to intall nodejs and apollo-codegen locally
`npm install -g apollo-codegen@0.19.1` first
```
apollo schema:download --endpoint=https://iopay.iotex.io/api-gateway/ /app/src/main/graphql/io/iotex/iopay/schema.json
```

2. Update  graphql queries

When endpoint changes graphql, we will need to update grapql sources in `/app/src/main/graphql/io/iotex/iopay/` directory
Then re-sync project with gradle, apollo-codegen will generate classes for query data.
We can access fields of fetched data from these classes.


**Sync or Build**

If you get this error when sync or build
```
Execution failed for task ':app:installApolloCodegen'.
```
You may need to intall nodejs and apollo-codegen locally
`npm install -g apollo-codegen@0.19.1` and set gradle system property `apollographql.useGlobalApolloCodegen` (for example in gradle.properties file):
```
systemProp.apollographql.useGlobalApolloCodegen=true
```
reference [apollo-android](https://github.com/apollographql/apollo-android#use-system-pre-installed-apollo-codegen)