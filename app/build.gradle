apply plugin: 'com.android.application'
apply plugin: 'kotlin-android'
//apply plugin: 'kotlin-kapt'
apply plugin: 'com.apollographql.apollo'
apply plugin: 'io.fabric'
apply plugin: 'kotlin-parcelize'
apply plugin: 'com.google.gms.google-services'
apply plugin: 'kotlinx-serialization'
apply plugin: 'com.google.firebase.firebase-perf'
apply plugin: "com.facebook.react"
apply plugin: "com.google.devtools.ksp"

static def generateVersionCode() {
    def result = "git rev-list HEAD --count".execute().text.trim() //unix
    if (result.empty) result = "PowerShell -Command git rev-list HEAD --count".execute().text.trim()
    //windows
    if (result.empty) throw new RuntimeException("Could not generate versioncode on this platform? Cmd output: ${result.text}")
    return result.toInteger()
}

apollo {
    service("analytic") {
        sourceFolder.set("io/iotex/analytic")
        rootPackageName.set("io.iotex.analytic")
    }
    service("api") {
        sourceFolder.set("io/iotex/api")
        rootPackageName.set("io.iotex.api")
    }
    service("erc20") {
        sourceFolder.set("io/iotex/erc20")
        rootPackageName.set("io.iotex.erc20")
    }
    service("common") {
        sourceFolder.set("io/iotex/common")
        rootPackageName.set("io.iotex.common")
    }
    service("iotex") {
        sourceFolder.set("io/iotex/iotex")
        rootPackageName.set("io.iotex.iotex")
    }
    service("metapebble") {
        sourceFolder.set("io/iotex/metapebble")
        rootPackageName.set("io.iotex.metapebble")
    }
    service("network") {
        sourceFolder.set("io/iotex/network")
        rootPackageName.set("io.iotex.network")
    }
    service("reward") {
        sourceFolder.set("io/iotex/reward")
        rootPackageName.set("io.iotex.reward")
    }
    service("signature") {
        sourceFolder.set("io/iotex/signature")
        rootPackageName.set("io.iotex.signature")
    }
    service("smartcontract") {
        sourceFolder.set("io/iotex/smartcontract")
        rootPackageName.set("io.iotex.smartcontract")
    }
    service("stake") {
        sourceFolder.set("io/iotex/stake")
        rootPackageName.set("io.iotex.stake")
    }
    service("token") {
        sourceFolder.set("io/iotex/token")
        rootPackageName.set("io.iotex.token")
    }
    service("ucam") {
        sourceFolder.set("io/iotex/ucam")
        rootPackageName.set("io.iotex.ucam")
    }
}

android {
    defaultConfig {
        applicationId "io.iotex.iopay"
        minSdkVersion 24
        targetSdkVersion 35
        versionCode generateVersionCode()
        versionName "INTERNAL_BUILD (${generateVersionCode()})"
        // to be replaced in release version using tag
        multiDexEnabled true
        vectorDrawables.useSupportLibrary = true
        testInstrumentationRunner "android.support.test.runner.AndroidJUnitRunner"
        ndk.abiFilters 'armeabi-v7a', 'arm64-v8a'
    }
    namespace 'io.iotex.iopay'
    lintOptions {
        disable 'InvalidPackage'
        abortOnError false
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
//    kotlinOptions {
//        jvmTarget = '1.8'
//    }
    packagingOptions {
        exclude 'lib/x86_64/darwin/libscrypt.dylib'
        exclude 'lib/x86_64/freebsd/libscrypt.so'
        exclude 'lib/x86_64/linux/libscrypt.so'
        exclude 'META-INF/INDEX.LIST'
    }
    flavorDimensions "updater"

    buildTypes {
        debug {
            minifyEnabled true // easy find minify error
            zipAlignEnabled false
            shrinkResources false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            FirebasePerformance {
                instrumentationEnabled false
            }
        }
        release {
            minifyEnabled true
            zipAlignEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    productFlavors {
        playStore {
            dimension "updater"
            buildConfigField 'boolean', 'AUTO_UPDATE_APK', 'false'
        }
        otherMarket {
            dimension "updater"
            buildConfigField 'boolean', 'AUTO_UPDATE_APK', 'true'
        }
    }
    configurations {
        all*.exclude module: 'bcprov-jdk15to18'
        all*.exclude module: 'protobuf-javalite'
        all*.exclude module: 'protolite-well-known-types'
        all*.exclude module: 'bcprov-jdk18on'
        all*.exclude module: 'slf4j-android'
        all*.exclude module: 'listenablefuture'
    }
    dataBinding {
        enabled = true
    }
    buildFeatures {
        buildConfig = true
        aidl = true
    }
}

configurations.all {
    resolutionStrategy {
        force 'androidx.core:core:1.8.0'
        force 'androidx.core:core-ktx:1.8.0'
    }
}

// don't delete this, it's used by the react-native debuggableVariants
//react {
//    debuggableVariants = ['playStoreDebug', 'otherMarketDebug']
//}

apollo {
    customTypeMapping = [
            "DateTime": "java.lang.String"
    ]
}

dependencies {
    implementation 'androidx.legacy:legacy-support-v4:1.0.0'
    testImplementation 'junit:junit:4.12'
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk8:$kotlin_version"
    implementation "org.jetbrains.kotlin:kotlin-reflect:$kotlin_version"
    implementation "org.jetbrains.kotlinx:kotlinx-serialization-core:1.0.1"
    implementation "org.jetbrains.kotlinx:kotlinx-serialization-json:1.0.1"
    implementation ("androidx.appcompat:appcompat:1.3.1") {
        version {
            strictly '1.3.1'
        }
    }

    implementation 'androidx.activity:activity-ktx:1.6.1'
    implementation 'androidx.fragment:fragment-ktx:1.6.1'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.0'

    implementation 'org.bouncycastle:bcprov-jdk15on:1.70'
    implementation 'com.lambdaworks:scrypt:1.4.0'
    implementation 'org.slf4j:slf4j-android:1.7.25'
    implementation 'com.fasterxml.jackson.core:jackson-databind:2.10.0.pr1'
    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-core:1.3.9"
    implementation "androidx.lifecycle:lifecycle-runtime-ktx:2.5.1"

    ksp "androidx.room:room-compiler:2.6.1"
    // For Kotlin use kapt instead of annotationProcessor
    implementation "androidx.room:room-runtime:2.6.1"
    implementation "androidx.room:room-ktx:2.6.1"
    implementation "androidx.lifecycle:lifecycle-extensions:2.2.0"
    implementation 'com.android.support:multidex:1.0.3'
    implementation 'com.google.guava:guava:27.0.1-android'
    implementation project(':iopaysdk')

    implementation project(':walletconnect2')
    implementation project(':userop')

    implementation project(':base')
    implementation project(':w3bstream')
    implementation project(':lockscreen')
    implementation project(':web3')
    implementation project(':bitcoin-lib')
    implementation project(':solana')
    implementation 'com.google.android.material:material:1.3.0-alpha04'

    implementation "org.jetbrains.anko:anko-commons:$anko_version"
    implementation 'com.google.zxing:core:3.3.0'
    implementation 'org.passay:passay:1.5.0'

    //SmartRefreshLayout
    implementation 'com.scwang.smartrefresh:SmartRefreshLayout:1.1.0-alpha-14'
    //SmartRefreshLayout header
    implementation 'com.scwang.smartrefresh:SmartRefreshHeader:1.1.0-alpha-14'
    //swipelayout
    implementation "com.daimajia.swipelayout:library:1.2.0@aar"
    implementation "com.github.iotexproject:iotex-antenna-java:0.6.3"
    annotationProcessor "org.projectlombok:lombok:1.18.32"
    // qrcode scanning without google play service
    implementation 'com.journeyapps:zxing-android-embedded:3.6.0'
    //apollo
    implementation "com.apollographql.apollo:apollo-android-support:$apolloVersion"
    implementation "com.apollographql.apollo:apollo-runtime:$apolloVersion"
    implementation("com.apollographql.apollo:apollo-coroutines-support:$apolloVersion")
    implementation "io.reactivex.rxjava2:rxandroid:2.0.1"
    //imageloader
    implementation "com.github.bumptech.glide:glide:4.14.2"
    ksp 'com.github.bumptech.glide:ksp:4.14.2'
    implementation 'jp.wasabeef:glide-transformations:4.3.0'
    //images-slider
    implementation 'io.github.youth5201314:banner:2.2.2'

    //materialprogressbar
    implementation 'me.zhanghai.android.materialprogressbar:library:1.6.1'

    implementation 'com.caverock:androidsvg-aar:1.4'

    implementation platform('com.google.firebase:firebase-bom:31.1.1')
    implementation 'com.google.firebase:firebase-perf-ktx'
    implementation 'com.google.firebase:firebase-crashlytics-ktx'
    implementation 'com.google.firebase:firebase-analytics-ktx'
    implementation 'com.google.firebase:firebase-messaging-ktx'


    implementation 'com.github.iwgang:simplifyspan:2.1'
    implementation "androidx.recyclerview:recyclerview:1.1.0"

    implementation 'com.jakewharton.timber:timber:4.7.1'
    implementation 'io.grpc:grpc-core:1.41.0'
    implementation 'io.grpc:grpc-protobuf:1.41.0'
    implementation 'io.grpc:grpc-stub:1.41.0'
    implementation 'io.grpc:grpc-okhttp:1.41.0'

    // react native
    implementation "com.facebook.react:react-android"
//    implementation "com.facebook.react:hermes-android"
    implementation "org.webkit:android-jsc-intl:+"

    implementation 'com.drakeet.multitype:multitype:4.0.0'
    implementation 'androidx.core:core-ktx:1.3.2'
    implementation 'com.github.takusemba:spotlight:2.0.5'
    implementation "com.android.installreferrer:installreferrer:2.2"
    // For Kotlin users also import the Kotlin extensions library for Play In-App Review:
    implementation("com.google.android.play:review:2.0.2")
    implementation("com.google.android.play:review-ktx:2.0.2")
    implementation 'com.contrarywind:Android-PickerView:4.1.9'
    implementation 'com.lahm.library:easy-protector-release:1.1.2'

    implementation 'com.squareup.moshi:moshi-kotlin:1.12.0'
    implementation ('net.osslabz.evm:evm-abi-decoder:0.0.6') {
        exclude group: "org.bouncycastle", module: "bcprov-jdk18on"
    }

    implementation("io.github.funkatronics:multimult:0.2.0"){
        exclude group: "androidx.core"
    }

    implementation 'com.auth0:java-jwt:4.2.1'

    implementation 'com.robinhood.ticker:ticker:2.0.4'

    implementation 'io.supercharge:shimmerlayout:2.1.0'
}

apply from: file("../../node_modules/@react-native-community/cli-platform-android/native_modules.gradle"); applyNativeModulesAppBuildGradle(project)
