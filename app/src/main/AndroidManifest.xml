<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="io.iotex.iopay"
    tools:ignore="LockedOrientationActivity">

    <uses-feature android:name="android.hardware.camera" />

    <uses-permission android:name="android.permission.USE_FINGERPRINT" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />

    <!--geo location upload-->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE"/>

    <queries>
        <package android:name="com.machinefi.metapebble.dev" />
    </queries>

    <queries>
        <package android:name="com.kubi.kucoin" />
    </queries>

    <queries>
        <package android:name="com.binance.dev" />
    </queries>

    <queries>
        <package android:name="com.gateio.gateio" />
    </queries>

    <queries>
        <package android:name="com.dunamu.exchange" />
    </queries>

    <queries>
        <package android:name="com.bitget.exchange" />
    </queries>

    <queries>
        <package android:name="pro.huobi" />
    </queries>

    <application
        android:name=".IoPayApplication"
        android:allowBackup="false"
        android:hardwareAccelerated="true"
        android:icon="@mipmap/ic_app_logo"
        android:label="@string/app_name"
        android:largeHeap="true"
        android:roundIcon="@mipmap/ic_app_logo_r"
        android:supportsRtl="true"
        android:theme="@style/AppTheme"
        android:usesCleartextTraffic="true"
        tools:replace="android:allowBackup"
        tools:ignore="LockedOrientationActivity">
        <activity
            android:name=".reactnative.ReactNativeActivity"
            android:exported="false" />

        <uses-library
            android:name="org.apache.http.legacy"
            android:required="false" />

        <service
            android:name=".service.FCMPushService"
            android:exported="false">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>
        <!-- Set custom default icon. This is used when no icon is set for incoming notification messages.
        See README(https://goo.gl/l4GJaQ) for more. -->
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_icon"
            android:resource="@mipmap/ic_app_logo" />
        <!-- Set color used with incoming notification messages. This is used when no color is set for the incoming
             notification message. See README(https://goo.gl/6BKBk7) for more. -->
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_color"
            android:resource="@color/colorAccent" />

        <activity
            android:name=".network.NetworkSelectActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />
        <activity
            android:name=".network.NetworkSwitchActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />
        <activity
            android:name=".network.FirstNetworkSwitchActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />
        <activity
            android:name=".setting.DAppAuthActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />
        <activity
            android:name=".setting.book.EditAddressBookActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />
        <activity
            android:name=".setting.book.AddressBookActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />
        <activity
            android:name=".setting.GeneralActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />
        <activity
            android:name=".setting.AppearanceActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />
        <activity
            android:name=".setting.SecurityPrivacyActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />
        <activity
            android:name=".setting.AuthenticationActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />
        <activity
            android:name=".setting.AboutIoPayActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />
        <activity
            android:name=".wallet.add.SuccessNewActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />
        <activity
            android:name=".wallet.ViewPrivateKeyActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />
        <activity
            android:name=".wallet.TransferActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />
        <activity
            android:name=".token.TransferTypeActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />
        <activity
            android:name=".wallet.receive.ReceiveActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />
        <activity
            android:name=".wallet.receive.ReceiveDetailActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />
        <activity
            android:name=".xapp.XAppsActivity"
            android:configChanges="keyboardHidden|screenSize|orientation"
            android:hardwareAccelerated="true"
            android:launchMode="singleTask"
            android:windowSoftInputMode="adjustPan" />
        <activity android:name=".xapp.WebActivity"/>
        <activity
            android:name=".xapp.SearchXAppsActivity"
            android:configChanges="keyboardHidden|screenSize|orientation" />
        <activity
            android:name=".token.TokenListActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".wallet.LearnMoreActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".wallet.qrcode.IoScanQRCodeActivity"
            android:hardwareAccelerated="true"
            android:screenOrientation="portrait" />

        <activity
            android:name=".wallet.add.UserAgreementActivity"
            android:noHistory="true"
            android:screenOrientation="portrait" />

        <activity
            android:name=".dapp.MyDAppActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustPan">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <!-- Accepts URIs that begin with "example://gizmos” -->
                <data
                    android:host="sign"
                    android:scheme="io.iotex.iopay" />
            </intent-filter>
        </activity>

        <activity
            android:name=".wallet.external.ExternalActivity"
            android:exported="true"
            android:screenOrientation="portrait">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <!-- Accepts URIs that begin with "example://gizmos” -->
                <data
                    android:host="io.iotex.iopay"
                    android:pathPrefix="/open"
                    android:scheme="iopay" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <!-- Accepts URIs that begin with "example://gizmos” -->
                <data android:scheme="iopay" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.SEND" />
                <data android:mimeType="text/plain" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter>
                <data android:scheme="wc" />
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
            </intent-filter>
        </activity>
        <activity
            android:name=".LoadActivity"
            android:exported="true"
            android:configChanges="uiMode"
            android:screenOrientation="portrait"
            android:theme="@style/SplashTheme">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name=".InitialGuideActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".wallet.DeepLinkActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />
        <activity
            android:name=".token.AddERC20Activity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />
        <activity
            android:name=".token.TokenDetailActivity"
            android:windowSoftInputMode="stateHidden|adjustPan"
            android:screenOrientation="portrait" />
        <activity
            android:name=".network.AddOrEditNetworkActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".network.NetworkNodeSettingActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />
        <activity
            android:name=".transaction.NftTransferActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".wallet.NftTokenDetailActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".wallet.add.MnemonicActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".wallet.add.MnemonicVerifyActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".wallet.add.NameWalletActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".wallet.add.WalletImportActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".wallet.add.SelectMnemonicWalletActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".setting.MnemonicListActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".setting.ExportMnemonicActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".setting.ManageActionActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".wallet.NftSearchActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".network.SwitchNetworkActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".xapp.wc.WcAuthActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />

        <activity
            android:name=".xapp.wc.WcManagerActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />

        <meta-data
            android:name="firebase_performance_logcat_enabled"
            android:value="true" />

        <activity android:name="com.facebook.react.devsupport.DevSettingsActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"/>

        <activity android:name=".meta.ui.MetaHistoryActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"/>

        <activity android:name=".meta.ui.MetaSettingActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"/>

        <activity android:name=".meta.ui.w3bstream.W3bStreamActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"/>

        <activity android:name=".meta.ui.GeoCreateActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"/>

        <activity android:name=".meta.ui.GeoHomeActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"/>

        <activity android:name=".meta.ui.GeoRegisterActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"/>

        <activity android:name=".wallet.NftListActivity"
            android:screenOrientation="portrait"/>

        <service android:name=".meta.MetaUploadService" />

        <activity android:name=".wallet.list.SwitchWalletActivity"
            android:launchMode="singleTop"
            android:windowSoftInputMode="adjustPan|stateHidden"
            android:screenOrientation="portrait" />

        <activity android:name=".wallet.aawallet.AAWalletChooserActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"/>

        <activity android:name=".wallet.aawallet.SendRecoverEmailActivity"
            android:screenOrientation="portrait"/>

        <activity android:name=".wallet.aawallet.SetupRecoverEmailActivity"
            android:screenOrientation="portrait"/>

        <activity android:name=".wallet.aawallet.SetupRecoverEmailActivityV2"
            android:screenOrientation="portrait"/>

        <activity android:name=".wallet.aawallet.RecoverAAWalletActivity"
            android:screenOrientation="portrait"/>

        <activity android:name=".wallet.aawallet.ActivateAAProcessActivity"
            android:screenOrientation="portrait"/>

        <activity android:name=".wallet.add.WalletAddActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"/>

        <activity android:name=".wallet.add.PrivateKeyVerifyActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"/>

        <activity android:name=".wallet.add.WalletImportMethodActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"/>
        <activity android:name=".wallet.add.WalletAddMnemonicActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"/>
        <activity android:name=".wallet.add.WalletAddPrivateKeyActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"/>
        <activity android:name=".setting.LanguageSwitchActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"/>

        <activity android:name=".setting.SwapMenuActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"/>
        <activity android:name=".wallet.add.SelectMnemonicPathActivity"
            android:windowSoftInputMode="adjustResize"
            android:screenOrientation="portrait"/>


        <activity android:name=".NetworkErrorActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"/>

        <activity android:name=".setting.InviteFriendActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"/>

        <activity android:name=".record.ActionRecordActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"/>
        <activity android:name=".home.SwapActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />
        <activity
            android:name=".setting.UserFeedBackActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"/>

        <activity
            android:name=".token.StakeLearnMoreActivity"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"/>
    </application>


</manifest>
