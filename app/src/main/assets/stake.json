[{"inputs": [{"internalType": "uint32", "name": "offset", "type": "uint32"}, {"internalType": "uint32", "name": "limit", "type": "uint32"}], "name": "buckets", "outputs": [{"components": [{"internalType": "uint64", "name": "index", "type": "uint64"}, {"internalType": "address", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address"}, {"internalType": "uint256", "name": "stakedAmount", "type": "uint256"}, {"internalType": "uint32", "name": "stakedDuration", "type": "uint32"}, {"internalType": "int64", "name": "createTime", "type": "int64"}, {"internalType": "int64", "name": "stakeStartTime", "type": "int64"}, {"internalType": "int64", "name": "unstakeStartTime", "type": "int64"}, {"internalType": "bool", "name": "autoStake", "type": "bool"}, {"internalType": "address", "name": "owner", "type": "address"}], "internalType": "struct IStaking.VoteBucket[]", "name": "", "type": "tuple[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "string", "name": "candName", "type": "string"}, {"internalType": "uint32", "name": "offset", "type": "uint32"}, {"internalType": "uint32", "name": "limit", "type": "uint32"}], "name": "bucketsByCandidate", "outputs": [{"components": [{"internalType": "uint64", "name": "index", "type": "uint64"}, {"internalType": "address", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address"}, {"internalType": "uint256", "name": "stakedAmount", "type": "uint256"}, {"internalType": "uint32", "name": "stakedDuration", "type": "uint32"}, {"internalType": "int64", "name": "createTime", "type": "int64"}, {"internalType": "int64", "name": "stakeStartTime", "type": "int64"}, {"internalType": "int64", "name": "unstakeStartTime", "type": "int64"}, {"internalType": "bool", "name": "autoStake", "type": "bool"}, {"internalType": "address", "name": "owner", "type": "address"}], "internalType": "struct IStaking.VoteBucket[]", "name": "", "type": "tuple[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint64[]", "name": "indexes", "type": "uint64[]"}], "name": "bucketsByIndexes", "outputs": [{"components": [{"internalType": "uint64", "name": "index", "type": "uint64"}, {"internalType": "address", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address"}, {"internalType": "uint256", "name": "stakedAmount", "type": "uint256"}, {"internalType": "uint32", "name": "stakedDuration", "type": "uint32"}, {"internalType": "int64", "name": "createTime", "type": "int64"}, {"internalType": "int64", "name": "stakeStartTime", "type": "int64"}, {"internalType": "int64", "name": "unstakeStartTime", "type": "int64"}, {"internalType": "bool", "name": "autoStake", "type": "bool"}, {"internalType": "address", "name": "owner", "type": "address"}], "internalType": "struct IStaking.VoteBucket[]", "name": "", "type": "tuple[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "voter", "type": "address"}, {"internalType": "uint32", "name": "offset", "type": "uint32"}, {"internalType": "uint32", "name": "limit", "type": "uint32"}], "name": "bucketsByVoter", "outputs": [{"components": [{"internalType": "uint64", "name": "index", "type": "uint64"}, {"internalType": "address", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address"}, {"internalType": "uint256", "name": "stakedAmount", "type": "uint256"}, {"internalType": "uint32", "name": "stakedDuration", "type": "uint32"}, {"internalType": "int64", "name": "createTime", "type": "int64"}, {"internalType": "int64", "name": "stakeStartTime", "type": "int64"}, {"internalType": "int64", "name": "unstakeStartTime", "type": "int64"}, {"internalType": "bool", "name": "autoStake", "type": "bool"}, {"internalType": "address", "name": "owner", "type": "address"}], "internalType": "struct IStaking.VoteBucket[]", "name": "", "type": "tuple[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "bucketsCount", "outputs": [{"internalType": "uint64", "name": "total", "type": "uint64"}, {"internalType": "uint64", "name": "active", "type": "uint64"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint64", "name": "bucketIndex", "type": "uint64"}], "name": "candidateActivate", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "owner<PERSON><PERSON><PERSON>", "type": "address"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [{"components": [{"internalType": "address", "name": "owner<PERSON><PERSON><PERSON>", "type": "address"}, {"internalType": "address", "name": "operatorAddress", "type": "address"}, {"internalType": "address", "name": "rewardAddress", "type": "address"}, {"internalType": "string", "name": "name", "type": "string"}, {"internalType": "uint256", "name": "totalWeightedVotes", "type": "uint256"}, {"internalType": "uint64", "name": "selfStakeBucketIdx", "type": "uint64"}, {"internalType": "uint256", "name": "selfStakingTokens", "type": "uint256"}], "internalType": "struct IStaking.Candidate", "name": "", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "string", "name": "candName", "type": "string"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [{"components": [{"internalType": "address", "name": "owner<PERSON><PERSON><PERSON>", "type": "address"}, {"internalType": "address", "name": "operatorAddress", "type": "address"}, {"internalType": "address", "name": "rewardAddress", "type": "address"}, {"internalType": "string", "name": "name", "type": "string"}, {"internalType": "uint256", "name": "totalWeightedVotes", "type": "uint256"}, {"internalType": "uint64", "name": "selfStakeBucketIdx", "type": "uint64"}, {"internalType": "uint256", "name": "selfStakingTokens", "type": "uint256"}], "internalType": "struct IStaking.Candidate", "name": "", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint64", "name": "bucketIndex", "type": "uint64"}, {"internalType": "bool", "name": "endorse", "type": "bool"}], "name": "candidateEndorsement", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "string", "name": "name", "type": "string"}, {"internalType": "address", "name": "operatorAddress", "type": "address"}, {"internalType": "address", "name": "rewardAddress", "type": "address"}, {"internalType": "address", "name": "owner<PERSON><PERSON><PERSON>", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "uint32", "name": "duration", "type": "uint32"}, {"internalType": "bool", "name": "autoStake", "type": "bool"}, {"internalType": "uint8[]", "name": "data", "type": "uint8[]"}], "name": "<PERSON><PERSON><PERSON><PERSON>", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "string", "name": "name", "type": "string"}, {"internalType": "address", "name": "operatorAddress", "type": "address"}, {"internalType": "address", "name": "rewardAddress", "type": "address"}], "name": "candidateUpdate", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint32", "name": "offset", "type": "uint32"}, {"internalType": "uint32", "name": "limit", "type": "uint32"}], "name": "candidates", "outputs": [{"components": [{"internalType": "address", "name": "owner<PERSON><PERSON><PERSON>", "type": "address"}, {"internalType": "address", "name": "operatorAddress", "type": "address"}, {"internalType": "address", "name": "rewardAddress", "type": "address"}, {"internalType": "string", "name": "name", "type": "string"}, {"internalType": "uint256", "name": "totalWeightedVotes", "type": "uint256"}, {"internalType": "uint64", "name": "selfStakeBucketIdx", "type": "uint64"}, {"internalType": "uint256", "name": "selfStakingTokens", "type": "uint256"}], "internalType": "struct IStaking.Candidate[]", "name": "", "type": "tuple[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "string", "name": "candName", "type": "string"}, {"internalType": "uint64", "name": "bucketIndex", "type": "uint64"}, {"internalType": "uint8[]", "name": "data", "type": "uint8[]"}], "name": "changeCandidate", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "string", "name": "candName", "type": "string"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "uint32", "name": "duration", "type": "uint32"}, {"internalType": "bool", "name": "autoStake", "type": "bool"}, {"internalType": "uint8[]", "name": "data", "type": "uint8[]"}], "name": "createStake", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "uint64", "name": "bucketIndex", "type": "uint64"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "uint8[]", "name": "data", "type": "uint8[]"}], "name": "depositToStake", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "uint64", "name": "bucketIndex", "type": "uint64"}, {"internalType": "uint32", "name": "duration", "type": "uint32"}, {"internalType": "bool", "name": "autoStake", "type": "bool"}, {"internalType": "uint8[]", "name": "data", "type": "uint8[]"}], "name": "restake", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "totalStakingAmount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "voterAddress", "type": "address"}, {"internalType": "uint64", "name": "bucketIndex", "type": "uint64"}, {"internalType": "uint8[]", "name": "data", "type": "uint8[]"}], "name": "transferStake", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint64", "name": "bucketIndex", "type": "uint64"}, {"internalType": "uint8[]", "name": "data", "type": "uint8[]"}], "name": "unstake", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint64", "name": "bucketIndex", "type": "uint64"}, {"internalType": "uint8[]", "name": "data", "type": "uint8[]"}], "name": "withdrawStake", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint64", "name": "bucketIndex", "type": "uint64"}], "name": "endorseCandidate", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint64", "name": "bucketIndex", "type": "uint64"}], "name": "intentToRevokeEndorsement", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint64", "name": "bucketIndex", "type": "uint64"}], "name": "revokeEndorsement", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint64", "name": "bucketIndex", "type": "uint64"}], "name": "migrateStake", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}, {"internalType": "uint8[]", "name": "payload", "type": "uint8[]"}], "name": "candidateTransferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint32", "name": "offset", "type": "uint32"}, {"internalType": "uint32", "name": "limit", "type": "uint32"}], "name": "compositeBuckets", "outputs": [{"components": [{"internalType": "uint64", "name": "index", "type": "uint64"}, {"internalType": "address", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address"}, {"internalType": "uint256", "name": "stakedAmount", "type": "uint256"}, {"internalType": "uint32", "name": "stakedDuration", "type": "uint32"}, {"internalType": "int64", "name": "createTime", "type": "int64"}, {"internalType": "int64", "name": "stakeStartTime", "type": "int64"}, {"internalType": "int64", "name": "unstakeStartTime", "type": "int64"}, {"internalType": "bool", "name": "autoStake", "type": "bool"}, {"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "contractAddress", "type": "address"}, {"internalType": "uint64", "name": "stakedDurationBlockNumber", "type": "uint64"}, {"internalType": "uint64", "name": "createBlockHeight", "type": "uint64"}, {"internalType": "uint64", "name": "stakeStartBlockHeight", "type": "uint64"}, {"internalType": "uint64", "name": "unstakeStartBlockHeight", "type": "uint64"}], "internalType": "struct IStaking.CompositeVoteBucket[]", "name": "", "type": "tuple[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "string", "name": "candName", "type": "string"}, {"internalType": "uint32", "name": "offset", "type": "uint32"}, {"internalType": "uint32", "name": "limit", "type": "uint32"}], "name": "compositeBucketsByCandidate", "outputs": [{"components": [{"internalType": "uint64", "name": "index", "type": "uint64"}, {"internalType": "address", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address"}, {"internalType": "uint256", "name": "stakedAmount", "type": "uint256"}, {"internalType": "uint32", "name": "stakedDuration", "type": "uint32"}, {"internalType": "int64", "name": "createTime", "type": "int64"}, {"internalType": "int64", "name": "stakeStartTime", "type": "int64"}, {"internalType": "int64", "name": "unstakeStartTime", "type": "int64"}, {"internalType": "bool", "name": "autoStake", "type": "bool"}, {"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "contractAddress", "type": "address"}, {"internalType": "uint64", "name": "stakedDurationBlockNumber", "type": "uint64"}, {"internalType": "uint64", "name": "createBlockHeight", "type": "uint64"}, {"internalType": "uint64", "name": "stakeStartBlockHeight", "type": "uint64"}, {"internalType": "uint64", "name": "unstakeStartBlockHeight", "type": "uint64"}], "internalType": "struct IStaking.CompositeVoteBucket[]", "name": "", "type": "tuple[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint64[]", "name": "indexes", "type": "uint64[]"}], "name": "compositeBucketsByIndexes", "outputs": [{"components": [{"internalType": "uint64", "name": "index", "type": "uint64"}, {"internalType": "address", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address"}, {"internalType": "uint256", "name": "stakedAmount", "type": "uint256"}, {"internalType": "uint32", "name": "stakedDuration", "type": "uint32"}, {"internalType": "int64", "name": "createTime", "type": "int64"}, {"internalType": "int64", "name": "stakeStartTime", "type": "int64"}, {"internalType": "int64", "name": "unstakeStartTime", "type": "int64"}, {"internalType": "bool", "name": "autoStake", "type": "bool"}, {"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "contractAddress", "type": "address"}, {"internalType": "uint64", "name": "stakedDurationBlockNumber", "type": "uint64"}, {"internalType": "uint64", "name": "createBlockHeight", "type": "uint64"}, {"internalType": "uint64", "name": "stakeStartBlockHeight", "type": "uint64"}, {"internalType": "uint64", "name": "unstakeStartBlockHeight", "type": "uint64"}], "internalType": "struct IStaking.CompositeVoteBucket[]", "name": "", "type": "tuple[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "voter", "type": "address"}, {"internalType": "uint32", "name": "offset", "type": "uint32"}, {"internalType": "uint32", "name": "limit", "type": "uint32"}], "name": "compositeBucketsByVoter", "outputs": [{"components": [{"internalType": "uint64", "name": "index", "type": "uint64"}, {"internalType": "address", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address"}, {"internalType": "uint256", "name": "stakedAmount", "type": "uint256"}, {"internalType": "uint32", "name": "stakedDuration", "type": "uint32"}, {"internalType": "int64", "name": "createTime", "type": "int64"}, {"internalType": "int64", "name": "stakeStartTime", "type": "int64"}, {"internalType": "int64", "name": "unstakeStartTime", "type": "int64"}, {"internalType": "bool", "name": "autoStake", "type": "bool"}, {"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "contractAddress", "type": "address"}, {"internalType": "uint64", "name": "stakedDurationBlockNumber", "type": "uint64"}, {"internalType": "uint64", "name": "createBlockHeight", "type": "uint64"}, {"internalType": "uint64", "name": "stakeStartBlockHeight", "type": "uint64"}, {"internalType": "uint64", "name": "unstakeStartBlockHeight", "type": "uint64"}], "internalType": "struct IStaking.CompositeVoteBucket[]", "name": "", "type": "tuple[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "compositeTotalStakingAmount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "contractAddress", "type": "address"}], "name": "contractStakeBucketTypes", "outputs": [{"components": [{"internalType": "uint256", "name": "stakedAmount", "type": "uint256"}, {"internalType": "uint32", "name": "stakedDuration", "type": "uint32"}], "internalType": "struct IStaking.BucketType[]", "name": "", "type": "tuple[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "string", "name": "candName", "type": "string"}, {"internalType": "uint32", "name": "offset", "type": "uint32"}, {"internalType": "uint32", "name": "limit", "type": "uint32"}], "name": "compositeBucketsByCandidateV3", "outputs": [{"components": [{"internalType": "uint64", "name": "index", "type": "uint64"}, {"internalType": "address", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address"}, {"internalType": "uint256", "name": "stakedAmount", "type": "uint256"}, {"internalType": "uint32", "name": "stakedDuration", "type": "uint32"}, {"internalType": "int64", "name": "createTime", "type": "int64"}, {"internalType": "int64", "name": "stakeStartTime", "type": "int64"}, {"internalType": "int64", "name": "unstakeStartTime", "type": "int64"}, {"internalType": "bool", "name": "autoStake", "type": "bool"}, {"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "contractAddress", "type": "address"}, {"internalType": "uint64", "name": "stakedDurationBlockNumber", "type": "uint64"}, {"internalType": "uint64", "name": "createBlockHeight", "type": "uint64"}, {"internalType": "uint64", "name": "stakeStartBlockHeight", "type": "uint64"}, {"internalType": "uint64", "name": "unstakeStartBlockHeight", "type": "uint64"}, {"internalType": "uint64", "name": "endorsementExpireBlockHeight", "type": "uint64"}], "internalType": "struct IStaking.CompositeVoteBucketV3[]", "name": "", "type": "tuple[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint64[]", "name": "indexes", "type": "uint64[]"}], "name": "compositeBucketsByIndexesV3", "outputs": [{"components": [{"internalType": "uint64", "name": "index", "type": "uint64"}, {"internalType": "address", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address"}, {"internalType": "uint256", "name": "stakedAmount", "type": "uint256"}, {"internalType": "uint32", "name": "stakedDuration", "type": "uint32"}, {"internalType": "int64", "name": "createTime", "type": "int64"}, {"internalType": "int64", "name": "stakeStartTime", "type": "int64"}, {"internalType": "int64", "name": "unstakeStartTime", "type": "int64"}, {"internalType": "bool", "name": "autoStake", "type": "bool"}, {"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "contractAddress", "type": "address"}, {"internalType": "uint64", "name": "stakedDurationBlockNumber", "type": "uint64"}, {"internalType": "uint64", "name": "createBlockHeight", "type": "uint64"}, {"internalType": "uint64", "name": "stakeStartBlockHeight", "type": "uint64"}, {"internalType": "uint64", "name": "unstakeStartBlockHeight", "type": "uint64"}, {"internalType": "uint64", "name": "endorsementExpireBlockHeight", "type": "uint64"}], "internalType": "struct IStaking.CompositeVoteBucketV3[]", "name": "", "type": "tuple[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "voter", "type": "address"}, {"internalType": "uint32", "name": "offset", "type": "uint32"}, {"internalType": "uint32", "name": "limit", "type": "uint32"}], "name": "compositeBucketsByVoterV3", "outputs": [{"components": [{"internalType": "uint64", "name": "index", "type": "uint64"}, {"internalType": "address", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address"}, {"internalType": "uint256", "name": "stakedAmount", "type": "uint256"}, {"internalType": "uint32", "name": "stakedDuration", "type": "uint32"}, {"internalType": "int64", "name": "createTime", "type": "int64"}, {"internalType": "int64", "name": "stakeStartTime", "type": "int64"}, {"internalType": "int64", "name": "unstakeStartTime", "type": "int64"}, {"internalType": "bool", "name": "autoStake", "type": "bool"}, {"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "contractAddress", "type": "address"}, {"internalType": "uint64", "name": "stakedDurationBlockNumber", "type": "uint64"}, {"internalType": "uint64", "name": "createBlockHeight", "type": "uint64"}, {"internalType": "uint64", "name": "stakeStartBlockHeight", "type": "uint64"}, {"internalType": "uint64", "name": "unstakeStartBlockHeight", "type": "uint64"}, {"internalType": "uint64", "name": "endorsementExpireBlockHeight", "type": "uint64"}], "internalType": "struct IStaking.CompositeVoteBucketV3[]", "name": "", "type": "tuple[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "compositeBucketsCount", "outputs": [{"internalType": "uint64", "name": "total", "type": "uint64"}, {"internalType": "uint64", "name": "active", "type": "uint64"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint32", "name": "offset", "type": "uint32"}, {"internalType": "uint32", "name": "limit", "type": "uint32"}], "name": "compositeBucketsV3", "outputs": [{"components": [{"internalType": "uint64", "name": "index", "type": "uint64"}, {"internalType": "address", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address"}, {"internalType": "uint256", "name": "stakedAmount", "type": "uint256"}, {"internalType": "uint32", "name": "stakedDuration", "type": "uint32"}, {"internalType": "int64", "name": "createTime", "type": "int64"}, {"internalType": "int64", "name": "stakeStartTime", "type": "int64"}, {"internalType": "int64", "name": "unstakeStartTime", "type": "int64"}, {"internalType": "bool", "name": "autoStake", "type": "bool"}, {"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "contractAddress", "type": "address"}, {"internalType": "uint64", "name": "stakedDurationBlockNumber", "type": "uint64"}, {"internalType": "uint64", "name": "createBlockHeight", "type": "uint64"}, {"internalType": "uint64", "name": "stakeStartBlockHeight", "type": "uint64"}, {"internalType": "uint64", "name": "unstakeStartBlockHeight", "type": "uint64"}, {"internalType": "uint64", "name": "endorsementExpireBlockHeight", "type": "uint64"}], "internalType": "struct IStaking.CompositeVoteBucketV3[]", "name": "", "type": "tuple[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "id", "type": "address"}], "name": "candidateByIDV3", "outputs": [{"components": [{"internalType": "address", "name": "owner<PERSON><PERSON><PERSON>", "type": "address"}, {"internalType": "address", "name": "operatorAddress", "type": "address"}, {"internalType": "address", "name": "rewardAddress", "type": "address"}, {"internalType": "string", "name": "name", "type": "string"}, {"internalType": "uint256", "name": "totalWeightedVotes", "type": "uint256"}, {"internalType": "uint64", "name": "selfStakeBucketIdx", "type": "uint64"}, {"internalType": "uint256", "name": "selfStakingTokens", "type": "uint256"}, {"internalType": "address", "name": "id", "type": "address"}], "internalType": "struct IStaking.CandidateV3", "name": "", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "owner<PERSON><PERSON><PERSON>", "type": "address"}], "name": "candidateByAddressV3", "outputs": [{"components": [{"internalType": "address", "name": "owner<PERSON><PERSON><PERSON>", "type": "address"}, {"internalType": "address", "name": "operatorAddress", "type": "address"}, {"internalType": "address", "name": "rewardAddress", "type": "address"}, {"internalType": "string", "name": "name", "type": "string"}, {"internalType": "uint256", "name": "totalWeightedVotes", "type": "uint256"}, {"internalType": "uint64", "name": "selfStakeBucketIdx", "type": "uint64"}, {"internalType": "uint256", "name": "selfStakingTokens", "type": "uint256"}, {"internalType": "address", "name": "id", "type": "address"}], "internalType": "struct IStaking.CandidateV3", "name": "", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "string", "name": "candName", "type": "string"}], "name": "candidateByNameV3", "outputs": [{"components": [{"internalType": "address", "name": "owner<PERSON><PERSON><PERSON>", "type": "address"}, {"internalType": "address", "name": "operatorAddress", "type": "address"}, {"internalType": "address", "name": "rewardAddress", "type": "address"}, {"internalType": "string", "name": "name", "type": "string"}, {"internalType": "uint256", "name": "totalWeightedVotes", "type": "uint256"}, {"internalType": "uint64", "name": "selfStakeBucketIdx", "type": "uint64"}, {"internalType": "uint256", "name": "selfStakingTokens", "type": "uint256"}, {"internalType": "address", "name": "id", "type": "address"}], "internalType": "struct IStaking.CandidateV3", "name": "", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint32", "name": "offset", "type": "uint32"}, {"internalType": "uint32", "name": "limit", "type": "uint32"}], "name": "candidatesV3", "outputs": [{"components": [{"internalType": "address", "name": "owner<PERSON><PERSON><PERSON>", "type": "address"}, {"internalType": "address", "name": "operatorAddress", "type": "address"}, {"internalType": "address", "name": "rewardAddress", "type": "address"}, {"internalType": "string", "name": "name", "type": "string"}, {"internalType": "uint256", "name": "totalWeightedVotes", "type": "uint256"}, {"internalType": "uint64", "name": "selfStakeBucketIdx", "type": "uint64"}, {"internalType": "uint256", "name": "selfStakingTokens", "type": "uint256"}, {"internalType": "address", "name": "id", "type": "address"}], "internalType": "struct IStaking.CandidateV3[]", "name": "", "type": "tuple[]"}], "stateMutability": "view", "type": "function"}]