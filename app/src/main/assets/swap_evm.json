[{"inputs": [{"components": [{"internalType": "address", "name": "permit2", "type": "address"}, {"internalType": "address", "name": "weth9", "type": "address"}, {"internalType": "address", "name": "seaportV1_5", "type": "address"}, {"internalType": "address", "name": "seaportV1_4", "type": "address"}, {"internalType": "address", "name": "openseaConduit", "type": "address"}, {"internalType": "address", "name": "nftxZap", "type": "address"}, {"internalType": "address", "name": "x2y2", "type": "address"}, {"internalType": "address", "name": "foundation", "type": "address"}, {"internalType": "address", "name": "sudoswap", "type": "address"}, {"internalType": "address", "name": "elementMarket", "type": "address"}, {"internalType": "address", "name": "nft20Zap", "type": "address"}, {"internalType": "address", "name": "cryptopunks", "type": "address"}, {"internalType": "address", "name": "looksRareV2", "type": "address"}, {"internalType": "address", "name": "routerRewardsDistributor", "type": "address"}, {"internalType": "address", "name": "looksRareRewardsDistributor", "type": "address"}, {"internalType": "address", "name": "looksRareToken", "type": "address"}, {"internalType": "address", "name": "v2Factory", "type": "address"}, {"internalType": "address", "name": "v3Factory", "type": "address"}, {"internalType": "bytes32", "name": "pairInitCodeHash", "type": "bytes32"}, {"internalType": "bytes32", "name": "poolInitCodeHash", "type": "bytes32"}], "internalType": "struct RouterParameters", "name": "params", "type": "tuple"}], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "RewardsSent", "type": "event"}, {"inputs": [{"internalType": "bytes", "name": "looksRareClaim", "type": "bytes"}], "name": "collectRewards", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes", "name": "commands", "type": "bytes"}, {"internalType": "bytes[]", "name": "inputs", "type": "bytes[]"}], "name": "execute", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "bytes", "name": "commands", "type": "bytes"}, {"internalType": "bytes[]", "name": "inputs", "type": "bytes[]"}, {"internalType": "uint256", "name": "deadline", "type": "uint256"}], "name": "execute", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}, {"internalType": "address", "name": "", "type": "address"}, {"internalType": "uint256[]", "name": "", "type": "uint256[]"}, {"internalType": "uint256[]", "name": "", "type": "uint256[]"}, {"internalType": "bytes", "name": "", "type": "bytes"}], "name": "onERC1155BatchReceived", "outputs": [{"internalType": "bytes4", "name": "", "type": "bytes4"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}, {"internalType": "address", "name": "", "type": "address"}, {"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "bytes", "name": "", "type": "bytes"}], "name": "onERC1155Received", "outputs": [{"internalType": "bytes4", "name": "", "type": "bytes4"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}, {"internalType": "address", "name": "", "type": "address"}, {"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "bytes", "name": "", "type": "bytes"}], "name": "onERC721Received", "outputs": [{"internalType": "bytes4", "name": "", "type": "bytes4"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "int256", "name": "amount0Delta", "type": "int256"}, {"internalType": "int256", "name": "amount1Delta", "type": "int256"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "uniswapV3SwapCallback", "outputs": [], "stateMutability": "nonpayable", "type": "function"}]