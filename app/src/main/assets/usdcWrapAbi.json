[{"type": "constructor", "inputs": [{"name": "_usdc_e", "type": "address", "internalType": "contract IERC20Burnable"}, {"name": "_iousdc", "type": "address", "internalType": "contract IERC20"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "balance", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "deposit", "inputs": [{"name": "_amount", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "iousdc", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract IERC20"}], "stateMutability": "view"}, {"type": "function", "name": "usdc_e", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract IERC20Burnable"}], "stateMutability": "view"}, {"type": "function", "name": "withdraw", "inputs": [{"name": "_amount", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}]