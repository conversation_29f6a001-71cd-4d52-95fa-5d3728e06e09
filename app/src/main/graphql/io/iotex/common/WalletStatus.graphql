query status($address:String!,$deviceToken:String!) {
    walletStatus(address: $address, deviceToken: $deviceToken ) {
           ...WalletStatus
    }
}


mutation register($address:String!,
  $deviceToken:String!,
  $registerPushNotification:Boolean!,
  $platform:String
){
  registerWalletStatus(
    address: $address,
    deviceToken:$deviceToken,
    registerPushNotification:$registerPushNotification,
    platform:$platform
  ) {
        ...WalletStatus
	}
}

fragment WalletStatus on IoPayWalletData {
    wallet{
        address
        deviceToken
        registerPushNotification
        platform
    }
}
