query AppVersionQuery($limit: Int = 1,$order_by:order_by) {
  version_control_android_2(limit: $limit, order_by: [{version_code: $order_by}]) {
    id
    log
    log_cn
    url
    version_name
    version_code
    forced_code
    target_version_code
    upgrade_content_en
    upgrade_content_cn
  }
}
query AppImageCacheQuery {
  app_img_cache_update {
    id
    versionCode
  }
}
query AppSettingPebbleMenu {
   setting_pebble_menu {
      id
      platform
      display
      menu_display
   }
}
query NewsLetterPushSetting($deviceToken: String) {
   iopay_device_config(where: {deviceToken: {_eq: $deviceToken}}) {
      deviceToken
      news_push
   }
}

mutation InsertNewsLetterPushSetting($deviceToken:String,$news_push:Boolean)  {
   insert_iopay_device_config_one(object: {deviceToken:$deviceToken,news_push: $news_push}) {
      deviceToken
      news_push
    }
}

mutation UpdateNewsLetterPushSetting($deviceToken:String,$news_push:Boolean)  {
   update_iopay_device_config(_set: {news_push: $news_push}, where: {deviceToken: {_eq: $deviceToken}}) {
       returning{
          deviceToken
          news_push
       }
    }
}
