query WalletInfoQuery($address:String,$chainId:Int,$platform:String) {
  wallet_info(where: {address: {_eq: $address}, chainId: {_eq: $chainId}, platform: {_eq: $platform}}) {
    address
    chainId
    id
    platform
  }
}

mutation InsertWalletInfoMutation($address:String,$ads:String,$balance:String,$chainId:Int,$platform:String) {
  insert_wallet_info(objects: [{address: $address, ads: $ads, balance:$balance, chainId: $chainId, platform: $platform}]) {
    returning {
      address
      chainId
      id
      platform
    }
  }
}

mutation UpdateWalletInfoMutation($balance:String,$id:Int,$platform:String)  {
   update_wallet_info(_set: {balance: $balance}, where: {id: {_eq: $id}, platform: {_eq: $platform}}) {
      returning {
        address
        chainId
        id
        platform
      }
    }
}