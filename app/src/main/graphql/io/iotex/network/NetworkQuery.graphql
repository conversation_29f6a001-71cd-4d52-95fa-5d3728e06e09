query Network($order_by:order_by ) {
  network_list(order_by:[{order: $order_by}]) {
    chainId
    id
    explorer
    icon
    logoUrl
    name
    platform
    rpc
    shortName
    tokensAlias
    multicallAddr
    swapUrl
    devMode
    theme_bg
    theme_bg2
    order
    networkName
    bg_color_end
    bg_color_start
    bg_image
    nativeCurrencyMarket {
      price_change_24h
      sparkline_in_7d
      current_price
      token {
        symbol
        logo
        decimals
        name
      }
    }
    network_config{
        token_approval_checker
        chain_icon
        chain_icon_selected
        gas_station
        account_factory
        entry_point
        contract_email

    }
    network_chain_theme{
        theme_color
        logo_image
        back_image
    }
  }
}
