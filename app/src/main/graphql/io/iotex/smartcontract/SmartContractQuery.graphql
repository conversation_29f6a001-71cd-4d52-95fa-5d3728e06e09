query NftList($address: String!, $contract: String, $chainId: Int){
  result: MetaPebbleNFT(calls:[{address: $contract, chainId: $chainId}]){
    address
    tokenList(address:$address){
      tokenURI
      tokenId
      consumed
      approved
    }
  }
}

query RegistrationResult($contract: String, $chainId: Int, $imei:String){
  result: Registration(calls:[{address: $contract, chainId: $chainId}]){
    find(imei:$imei)
  }
}

