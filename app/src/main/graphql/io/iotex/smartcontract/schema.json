{"__schema": {"queryType": {"name": "Query"}, "mutationType": null, "subscriptionType": null, "types": [{"kind": "OBJECT", "name": "Query", "description": null, "fields": [{"name": "ETH", "description": null, "args": [{"name": "rpcURL", "description": null, "type": {"kind": "LIST", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null}], "type": {"kind": "OBJECT", "name": "Multicall", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "BSC", "description": null, "args": [{"name": "rpcURL", "description": null, "type": {"kind": "LIST", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null}], "type": {"kind": "OBJECT", "name": "Multicall", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "Polygon", "description": null, "args": [{"name": "rpcURL", "description": null, "type": {"kind": "LIST", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null}], "type": {"kind": "OBJECT", "name": "Multicall", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "IoTeX_Mainnet", "description": null, "args": [{"name": "rpcURL", "description": null, "type": {"kind": "LIST", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null}], "type": {"kind": "OBJECT", "name": "Multicall", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "IoTeX_Testnet", "description": null, "args": [{"name": "rpcURL", "description": null, "type": {"kind": "LIST", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null}], "type": {"kind": "OBJECT", "name": "Multicall", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "MetaPebbleNFT", "description": null, "args": [{"name": "calls", "description": null, "type": {"kind": "LIST", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "CrossChainCalls", "ofType": null}}, "defaultValue": "[]"}], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "OBJECT", "name": "MetaPebbleNFT", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "MetaPebbleRegistrar", "description": null, "args": [{"name": "calls", "description": null, "type": {"kind": "LIST", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "CrossChainCalls", "ofType": null}}, "defaultValue": "[]"}], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "OBJECT", "name": "MetaPebbleRegistrar", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "Registration", "description": null, "args": [{"name": "calls", "description": null, "type": {"kind": "LIST", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "CrossChainCalls", "ofType": null}}, "defaultValue": "[]"}], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "OBJECT", "name": "Registration", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "SCALAR", "name": "String", "description": "The `String` scalar type represents textual data, represented as UTF-8 character sequences. The String type is most often used by GraphQL to represent free-form human-readable text.", "fields": null, "inputFields": null, "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "MetaPebbleNFT", "description": null, "fields": [{"name": "address", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "chainId", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "approve", "description": null, "args": [{"name": "to", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "tokenId", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "balanceOf", "description": null, "args": [{"name": "owner", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "baseURI", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "consume", "description": null, "args": [{"name": "tokenId", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "consumed", "description": null, "args": [{"name": "args0", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "cost", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "getApproved", "description": null, "args": [{"name": "tokenId", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "isApprovedForAll", "description": null, "args": [{"name": "owner", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "operator", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "maxTokens", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "mint", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "name", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "nextTokenId", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "owner", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "ownerOf", "description": null, "args": [{"name": "tokenId", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "renounceOwnership", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "safeTransferFrom", "description": null, "args": [{"name": "from", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "to", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "tokenId", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "_data", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "setApprovalForAll", "description": null, "args": [{"name": "operator", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "approved", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "setCost", "description": null, "args": [{"name": "_val", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "setMaxTokens", "description": null, "args": [{"name": "_val", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "supportsInterface", "description": null, "args": [{"name": "interfaceId", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "symbol", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "tokenByIndex", "description": null, "args": [{"name": "index", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "tokenOfOwnerByIndex", "description": null, "args": [{"name": "owner", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "index", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "tokenURI", "description": null, "args": [{"name": "tokenId", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "totalSupply", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "transferFrom", "description": null, "args": [{"name": "from", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "to", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "tokenId", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "transferOwnership", "description": null, "args": [{"name": "new<PERSON>wner", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "test", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "tokenList", "description": null, "args": [{"name": "address", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null}], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "OBJECT", "name": "NFTItem", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "MetaPebbleRegistrar", "description": null, "fields": [{"name": "address", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "chainId", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "admin", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "changeAdmin", "description": null, "args": [{"name": "newAdmin", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "implementation", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "upgradeTo", "description": null, "args": [{"name": "newImplementation", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "upgradeToAndCall", "description": null, "args": [{"name": "newImplementation", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "data", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "core", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "encode", "description": null, "args": [{"name": "imei", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "pubkey", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "sn", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "timestamp", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "initialize", "description": null, "args": [{"name": "core_", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "nft_", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "registration_", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "isOperator", "description": null, "args": [{"name": "_address", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "nft", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "pause", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "paused", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "register", "description": null, "args": [{"name": "tokenId", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "imei", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "pubkey", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "sn", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "timestamp", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "signature", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "authentication", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "registration", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "unpause", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "Registration", "description": null, "fields": [{"name": "address", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "chainId", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "admin", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "changeAdmin", "description": null, "args": [{"name": "newAdmin", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "implementation", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "upgradeTo", "description": null, "args": [{"name": "newImplementation", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "upgradeToAndCall", "description": null, "args": [{"name": "newImplementation", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "data", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "core", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "factory", "description": null, "args": [{"name": "args0", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "find", "description": null, "args": [{"name": "imei", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "initialize", "description": null, "args": [{"name": "core_", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "isOperator", "description": null, "args": [{"name": "_address", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "pause", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "paused", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "ship", "description": null, "args": [{"name": "imei", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "owner", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "sn", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "key", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "unpause", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "ApprovalReturnType", "description": null, "fields": [{"name": "needApprove", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "data", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "allownace", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "SCALAR", "name": "Boolean", "description": "The `Boolean` scalar type represents `true` or `false`.", "fields": null, "inputFields": null, "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "SwapArgsType", "description": null, "fields": null, "inputFields": [{"name": "sellToken", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "buyToken", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "sellAmount", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "buyAmount", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "recipient", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "max<PERSON><PERSON><PERSON>", "description": null, "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "defaultValue": "120"}, {"name": "slippagePercentage", "description": null, "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "defaultValue": "0.005"}, {"name": "offlinePrice", "description": null, "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": "false"}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "SCALAR", "name": "Int", "description": "The `Int` scalar type represents non-fractional signed whole numeric values. Int can represent values between -(2^31) and 2^31 - 1.", "fields": null, "inputFields": null, "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "SCALAR", "name": "Float", "description": "The `Float` scalar type represents signed double-precision fractional values as specified by [IEEE 754](https://en.wikipedia.org/wiki/IEEE_floating_point).", "fields": null, "inputFields": null, "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "TubeArgsType", "description": null, "fields": null, "inputFields": [{"name": "token", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "amount", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "targetTubeID", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "to", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "Multicall", "description": null, "fields": [{"name": "name", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "chainId", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "MetaPebbleNFT", "description": null, "args": [{"name": "address", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}}}, "defaultValue": null}], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "OBJECT", "name": "MetaPebbleNFT", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "MetaPebbleRegistrar", "description": null, "args": [{"name": "address", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}}}, "defaultValue": null}], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "OBJECT", "name": "MetaPebbleRegistrar", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "Registration", "description": null, "args": [{"name": "address", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}}}, "defaultValue": null}], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "OBJECT", "name": "Registration", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "CrossChainCalls", "description": null, "fields": null, "inputFields": [{"name": "address", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null}, {"name": "chainId", "description": null, "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "defaultValue": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "ENUM", "name": "AnyDataField", "description": null, "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "price", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "market_cap", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "balance", "description": null, "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "OBJECT", "name": "StatsReturnType", "description": null, "fields": [{"name": "priceUSD", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "priceCoin", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "PhaseReturnType", "description": null, "fields": [{"name": "phaseZero", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "phaseOne", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "phaseTwo", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "phaseThree", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "amount", "description": null, "fields": [{"name": "amount", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "path", "description": null, "args": [], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "OBJECT", "name": "ERC20", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "from", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "to", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "router", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "data", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "value", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "slippagePercentage", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Float", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "sellToken", "description": null, "args": [], "type": {"kind": "OBJECT", "name": "ERC20", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "buyToken", "description": null, "args": [], "type": {"kind": "OBJECT", "name": "ERC20", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "TubeResponseType", "description": null, "fields": [{"name": "data", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "NFTItem", "description": null, "fields": [{"name": "tokenId", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "consumed", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "tokenURI", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "approved", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "ERC20", "description": null, "fields": [{"name": "checkApproval", "description": null, "args": [{"name": "user", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null}, {"name": "spender", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null}, {"name": "amount", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null}, {"name": "withData", "description": null, "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": "false"}], "type": {"kind": "OBJECT", "name": "ApprovalReturnType", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "any", "description": null, "args": [{"name": "field", "description": null, "type": {"kind": "ENUM", "name": "AnyDataField", "ofType": null}, "defaultValue": null}, {"name": "params", "description": null, "type": {"kind": "LIST", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null}], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "LPToken", "description": null, "fields": [{"name": "Token0", "description": null, "args": [], "type": {"kind": "OBJECT", "name": "ERC20", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "Token1", "description": null, "args": [], "type": {"kind": "OBJECT", "name": "ERC20", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "priceUSD", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "totalLiquidityUSD", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "LiquidityGaugeV3", "description": null, "fields": [{"name": "LPToken", "description": null, "args": [], "type": {"kind": "OBJECT", "name": "LPToken", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "liquidityUSD", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "gauge_weight", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "totalRewards", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "nextEpochWeight", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "boost", "description": null, "args": [{"name": "account", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null}], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "LaunchEvent", "description": null, "fields": [{"name": "stats", "description": null, "args": [], "type": {"kind": "OBJECT", "name": "StatsReturnType", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "phase", "description": null, "args": [], "type": {"kind": "OBJECT", "name": "PhaseReturnType", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "totalSupply", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "MasterChefPoint", "description": null, "fields": [{"name": "liquidityUSDs", "description": null, "args": [], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "totalRewards", "description": null, "args": [], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "boosts", "description": null, "args": [], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "lpPrices", "description": null, "args": [], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "UniswapRouter", "description": null, "fields": [{"name": "swap", "description": null, "args": [{"name": "args", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "SwapArgsType", "ofType": null}, "defaultValue": null}], "type": {"kind": "OBJECT", "name": "amount", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "ERC20Tube", "description": null, "fields": [{"name": "tube", "description": null, "args": [{"name": "args", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "TubeArgsType", "ofType": null}, "defaultValue": null}], "type": {"kind": "OBJECT", "name": "TubeResponseType", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "__<PERSON><PERSON><PERSON>", "description": "A GraphQL Schema defines the capabilities of a GraphQL server. It exposes all available types and directives on the server, as well as the entry points for query, mutation, and subscription operations.", "fields": [{"name": "description", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "types", "description": "A list of all types supported by this server.", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__Type", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "queryType", "description": "The type that query operations will be rooted at.", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__Type", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "mutationType", "description": "If this server supports mutation, the type that mutation operations will be rooted at.", "args": [], "type": {"kind": "OBJECT", "name": "__Type", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "subscriptionType", "description": "If this server support subscription, the type that subscription operations will be rooted at.", "args": [], "type": {"kind": "OBJECT", "name": "__Type", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "directives", "description": "A list of all directives supported by this server.", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__Directive", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "__Type", "description": "The fundamental unit of any GraphQL Schema is the type. There are many kinds of types in GraphQL as represented by the `__TypeKind` enum.\n\nDepending on the kind of a type, certain fields describe information about that type. Scalar types provide no information beyond a name, description and optional `specifiedByUrl`, while Enum types provide their values. Object and Interface types provide the fields they describe. Abstract types, Union and Interface, provide the Object types possible at runtime. List and NonNull types compose other types.", "fields": [{"name": "kind", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "__TypeKind", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "name", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "description", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "specifiedByUrl", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "fields", "description": null, "args": [{"name": "includeDeprecated", "description": null, "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": "false"}], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__Field", "ofType": null}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "interfaces", "description": null, "args": [], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__Type", "ofType": null}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "possibleTypes", "description": null, "args": [], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__Type", "ofType": null}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "enum<PERSON><PERSON><PERSON>", "description": null, "args": [{"name": "includeDeprecated", "description": null, "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": "false"}], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__<PERSON>umV<PERSON><PERSON>", "ofType": null}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "inputFields", "description": null, "args": [{"name": "includeDeprecated", "description": null, "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": "false"}], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__InputValue", "ofType": null}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "ofType", "description": null, "args": [], "type": {"kind": "OBJECT", "name": "__Type", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "ENUM", "name": "__TypeKind", "description": "An enum describing what kind of type a given `__Type` is.", "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "SCALAR", "description": "Indicates this type is a scalar.", "isDeprecated": false, "deprecationReason": null}, {"name": "OBJECT", "description": "Indicates this type is an object. `fields` and `interfaces` are valid fields.", "isDeprecated": false, "deprecationReason": null}, {"name": "INTERFACE", "description": "Indicates this type is an interface. `fields`, `interfaces`, and `possibleTypes` are valid fields.", "isDeprecated": false, "deprecationReason": null}, {"name": "UNION", "description": "Indicates this type is a union. `possibleTypes` is a valid field.", "isDeprecated": false, "deprecationReason": null}, {"name": "ENUM", "description": "Indicates this type is an enum. `enumValues` is a valid field.", "isDeprecated": false, "deprecationReason": null}, {"name": "INPUT_OBJECT", "description": "Indicates this type is an input object. `inputFields` is a valid field.", "isDeprecated": false, "deprecationReason": null}, {"name": "LIST", "description": "Indicates this type is a list. `ofType` is a valid field.", "isDeprecated": false, "deprecationReason": null}, {"name": "NON_NULL", "description": "Indicates this type is a non-null. `ofType` is a valid field.", "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "OBJECT", "name": "__Field", "description": "Object and Interface types are described by a list of Fields, each of which has a name, potentially a list of arguments, and a return type.", "fields": [{"name": "name", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "description", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "args", "description": null, "args": [{"name": "includeDeprecated", "description": null, "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": "false"}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__InputValue", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "type", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__Type", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "isDeprecated", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "deprecationReason", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "__InputValue", "description": "Arguments provided to Fields or Directives and the input fields of an InputObject are represented as Input Values which describe their type and optionally a default value.", "fields": [{"name": "name", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "description", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "type", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__Type", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "defaultValue", "description": "A GraphQL-formatted string representing the default value for this input value.", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "isDeprecated", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "deprecationReason", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "__<PERSON>umV<PERSON><PERSON>", "description": "One possible value for a given Enum. Enum values are unique values, not a placeholder for a string or numeric value. However an Enum value is returned in a JSON response as a string.", "fields": [{"name": "name", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "description", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "isDeprecated", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "deprecationReason", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "__Directive", "description": "A Directive provides a way to describe alternate runtime execution and type validation behavior in a GraphQL document.\n\nIn some cases, you need to provide options to alter GraphQL's execution behavior in ways field arguments will not suffice, such as conditionally including or skipping a field. Directives provide this by describing additional information to the executor.", "fields": [{"name": "name", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "description", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "isRepeatable", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "locations", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "__DirectiveLocation", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "args", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__InputValue", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "ENUM", "name": "__DirectiveLocation", "description": "A Directive can be adjacent to many parts of the GraphQL language, a __DirectiveLocation describes one such possible adjacencies.", "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "QUERY", "description": "Location adjacent to a query operation.", "isDeprecated": false, "deprecationReason": null}, {"name": "MUTATION", "description": "Location adjacent to a mutation operation.", "isDeprecated": false, "deprecationReason": null}, {"name": "SUBSCRIPTION", "description": "Location adjacent to a subscription operation.", "isDeprecated": false, "deprecationReason": null}, {"name": "FIELD", "description": "Location adjacent to a field.", "isDeprecated": false, "deprecationReason": null}, {"name": "FRAGMENT_DEFINITION", "description": "Location adjacent to a fragment definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "FRAGMENT_SPREAD", "description": "Location adjacent to a fragment spread.", "isDeprecated": false, "deprecationReason": null}, {"name": "INLINE_FRAGMENT", "description": "Location adjacent to an inline fragment.", "isDeprecated": false, "deprecationReason": null}, {"name": "VARIABLE_DEFINITION", "description": "Location adjacent to a variable definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "SCHEMA", "description": "Location adjacent to a schema definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "SCALAR", "description": "Location adjacent to a scalar definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "OBJECT", "description": "Location adjacent to an object type definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "FIELD_DEFINITION", "description": "Location adjacent to a field definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "ARGUMENT_DEFINITION", "description": "Location adjacent to an argument definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "INTERFACE", "description": "Location adjacent to an interface definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "UNION", "description": "Location adjacent to a union definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "ENUM", "description": "Location adjacent to an enum definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "ENUM_VALUE", "description": "Location adjacent to an enum value definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "INPUT_OBJECT", "description": "Location adjacent to an input object type definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "INPUT_FIELD_DEFINITION", "description": "Location adjacent to an input object field definition.", "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}], "directives": [{"name": "include", "description": "Directs the executor to include this field or fragment only when the `if` argument is true.", "locations": ["FIELD", "FRAGMENT_SPREAD", "INLINE_FRAGMENT"], "args": [{"name": "if", "description": "Included when true.", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "defaultValue": null}]}, {"name": "skip", "description": "Directs the executor to skip this field or fragment when the `if` argument is true.", "locations": ["FIELD", "FRAGMENT_SPREAD", "INLINE_FRAGMENT"], "args": [{"name": "if", "description": "Skipped when true.", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "defaultValue": null}]}, {"name": "deprecated", "description": "Marks an element of a GraphQL schema as no longer supported.", "locations": ["FIELD_DEFINITION", "ARGUMENT_DEFINITION", "INPUT_FIELD_DEFINITION", "ENUM_VALUE"], "args": [{"name": "reason", "description": "Explains why this element was deprecated, usually also including a suggestion for how to access supported similar data. Formatted using the Markdown syntax, as specified by [CommonMark](https://commonmark.org/).", "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": "\"No longer supported\""}]}, {"name": "specifiedBy", "description": "Exposes a URL that specifies the behaviour of this scalar.", "locations": ["SCALAR"], "args": [{"name": "url", "description": "The URL that specifies the behaviour of this scalar.", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null}]}]}}