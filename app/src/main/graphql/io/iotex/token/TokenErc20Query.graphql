query token_erc20($walletAddress:String,$contract_address:String,$limit:Int, $offset: Int,$order_by:order_by){
  token_erc20(where: { _or:[ { recipient:{_eq: $walletAddress}},
                        { sender:{_eq: $walletAddress}}],
                         contract_address:{_eq: $contract_address}
                       },
                       ,limit: $limit, offset: $offset ,order_by:[{id: $order_by}]) {
    action_hash
    amount
    contract_address
    id
    recipient
    sender
    timestamp
    block_height
  }
}