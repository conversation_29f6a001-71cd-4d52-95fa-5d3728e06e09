package io.iotex.iopay

import android.content.Context
import android.content.Intent
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.viewpager2.adapter.FragmentStateAdapter
import androidx.viewpager2.widget.ViewPager2
import com.blankj.utilcode.util.BarUtils
import com.blankj.utilcode.util.ColorUtils
import com.blankj.utilcode.util.SPUtils
import io.iotex.base.BaseViewModel
import io.iotex.base.bindbase.BaseBindActivity
import io.iotex.iopay.databinding.ActivityInitialGuideBinding
import io.iotex.iopay.util.Constant.mGuideSources
import io.iotex.iopay.util.SPConstant.SP_INITIAL_GUIDE
import io.iotex.iopay.wallet.add.UserAgreementActivity

class InitialGuideActivity :
    BaseBindActivity<BaseViewModel, ActivityInitialGuideBinding>(R.layout.activity_initial_guide) {
    companion object {
        fun start(context: Context) = context.startActivity(
            Intent(context, InitialGuideActivity::class.java)
        )
    }

    private val mVpAdapter by lazy { InitialGuideAdapter(this) }


    override fun initView() {
        BarUtils.setStatusBarColor(this, ColorUtils.getColor(R.color.transparent))
        mBinding.initialGuideVp.run {
            adapter = mVpAdapter
            registerOnPageChangeCallback(mPageChangeCallback)
        }
        mBinding.initialGuideIndicator.setupWithViewPager(mBinding.initialGuideVp)
    }

    private val mPageChangeCallback by lazy {
        object : ViewPager2.OnPageChangeCallback() {
            override fun onPageSelected(index: Int) {
                super.onPageSelected(index)
                mBinding.initialGuideStartEnjoy.run {
                    if (index == mGuideSources.size - 1) {
                        text = getString(R.string.get_started)
                        setOnClickListener {
                            val intent =
                                Intent(this@InitialGuideActivity, UserAgreementActivity::class.java)
                            startActivity(intent)
                            SPUtils.getInstance().put(SP_INITIAL_GUIDE, true)
                            finish()
                        }
                    } else {
                        text = getString(R.string.next_pf)
                        setOnClickListener {
                            mBinding.initialGuideVp.currentItem = index + 1
                        }
                    }

                }

            }
        }
    }

    inner class InitialGuideAdapter(fa: FragmentActivity) : FragmentStateAdapter(fa) {
        override fun getItemCount(): Int = mGuideSources.size
        override fun createFragment(position: Int): Fragment {
            return InitialGuideFragment.newInstance(position)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        mBinding.initialGuideVp.unregisterOnPageChangeCallback(mPageChangeCallback)
    }
}