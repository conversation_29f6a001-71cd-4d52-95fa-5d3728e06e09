package io.iotex.iopay

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import io.iotex.iopay.fragment.base.BaseFragment
import io.iotex.iopay.util.Constant.mGuideSources

class InitialGuideFragment : BaseFragment() {
    companion object {
        private const val ARGS_KEY_FRAGMENT_INDEX = "guide_page_index"

        fun newInstance(index: Int) = InitialGuideFragment().apply {
            arguments = Bundle().apply {
                putInt(ARGS_KEY_FRAGMENT_INDEX, index)
            }
        }
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        val view = inflater.inflate(R.layout.fragment_initial_guide, container, false)
        val index = arguments?.getInt(ARGS_KEY_FRAGMENT_INDEX, 0) ?: 0
        if (index >= 0 && index < mGuideSources.size) {
            val model = mGuideSources[index]
            view.apply {
                findViewById<ImageView>(R.id.initial_guide_img)?.setImageResource(model.imgSrc)
                findViewById<TextView>(R.id.initial_guide_tip)?.text = getString(model.title)
                findViewById<TextView>(R.id.initial_guide_sub_tip)?.text = getString(model.subTitle)

            }
        }

        return view
    }


}