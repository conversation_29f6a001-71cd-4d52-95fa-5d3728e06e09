package io.iotex.iopay

import android.content.Context
import android.content.res.Configuration
import android.graphics.Typeface
import android.os.Process
import androidx.appcompat.app.AppCompatDelegate
import androidx.multidex.MultiDexApplication
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.AppUtils
import com.blankj.utilcode.util.LogUtils
import com.blankj.utilcode.util.ToastUtils
import com.blankj.utilcode.util.Utils
import com.facebook.react.JSEngineResolutionAlgorithm
import com.facebook.react.PackageList
import com.facebook.react.ReactApplication
import com.facebook.react.ReactNativeHost
import com.facebook.react.ReactPackage
import com.facebook.soloader.SoLoader
import com.google.firebase.crashlytics.FirebaseCrashlytics
import com.lahm.library.EasyProtectorLib
import com.machinefi.walletconnect2.WC2Helper
import io.iotex.base.language.MultiLanguage
import io.iotex.iopay.data.UserStore
import io.iotex.iopay.data.db.AppDatabase
import io.iotex.iopay.reactnative.MyAppPackage
import io.iotex.iopay.support.lifecycle.IoLifecycleHandler
import io.iotex.iopay.util.Config
import io.iotex.iopay.util.NotificationHelper
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.xapp.WC2CallbackImpl
import io.iotex.stake.DelegateQuery
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.bouncycastle.jce.provider.BouncyCastleProvider
import timber.log.Timber
import java.lang.reflect.Field
import java.security.Security
import kotlin.system.exitProcess

class IoPayApplication : MultiDexApplication(), ReactApplication {

    val bpCandidates = mutableMapOf<String, DelegateQuery.Iopay_delegate>()
    val lifecycleHandler = IoLifecycleHandler()

    override fun onCreate() {
        super.onCreate()
        if (UserStore.isDarkTheme()) {
            AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_YES)
        } else {
            AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_NO)
        }

        SoLoader.init(this, false)

        if (AppUtils.isAppDebug()) {
            Timber.plant(Timber.DebugTree())
        }
        setTypeface()

        ioPayApplication = this
        CoroutineScope(Dispatchers.IO).launch {
            // if set MODE_NIGHT_YES it cause no effect when change language
            AppDatabase.getInstance(this@IoPayApplication)
            NotificationHelper.createNotificationChannel(this@IoPayApplication)
            Utils.init(this@IoPayApplication)
            if (!BuildConfig.DEBUG) {
                checkSafe()
            }

            LogUtils.getConfig().setLogHeadSwitch(false).setLogSwitch(AppUtils.isAppDebug())

            WC2Helper.initialize(this@IoPayApplication, WC2CallbackImpl())
        }
        registerActivityLifecycleCallbacks(lifecycleHandler)

    }

    private fun checkSafe() {
        val isEmulator = EasyProtectorLib.checkIsRunningInEmulator(Utils.getApp()) {
        }
        val isRoot = EasyProtectorLib.checkIsRoot()
        val isXposed = EasyProtectorLib.checkIsXposedExist()
        val signature = AppUtils.getAppSignaturesMD5().joinToString(":")
        val signatureValid = signature == Config.SIGNATURE_MD5
        if (isEmulator || isRoot || isXposed || !signatureValid) {
            ToastUtils.showShort(R.string.iopay_unsafe_state)
            ActivityUtils.finishAllActivities()
            exitProcess(0)
            Process.killProcess(Process.myPid())
        }
    }

    companion object {
        private lateinit var ioPayApplication: IoPayApplication

        init {
            val bouncyCastleProvider = Security.getProvider(BouncyCastleProvider.PROVIDER_NAME)
            if (bouncyCastleProvider != null) {
                Security.removeProvider(BouncyCastleProvider.PROVIDER_NAME)
            }
            Security.addProvider(BouncyCastleProvider())
        }

        fun getInstance(): IoPayApplication {
            return ioPayApplication
        }

        fun getAppContext(): Context {
            return ioPayApplication.applicationContext
        }

        fun getAppChannelId(context: Context): String {
            return "${context.packageName}-${context.getString(R.string.iopay_channel_name)}"
        }
    }

    override fun attachBaseContext(base: Context?) {
        super.attachBaseContext(MultiLanguage.setLocal(base as Context))
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        MultiLanguage.onConfigurationChanged(applicationContext)
        super.onConfigurationChanged(newConfig)
    }

    fun initBpCandidates(bpCandidates: MutableList<DelegateQuery.Iopay_delegate>) {
        this.bpCandidates.clear()
        bpCandidates.forEach { bpCandidate ->
            bpCandidate.meta()?.temp_eth_address()?.let {
                this.bpCandidates[it] = bpCandidate
            }
        }
    }

    fun getBpCandidate(ioAddress: String?): DelegateQuery.Iopay_delegate? {
        ioAddress?.let { address ->
            return this.bpCandidates[WalletHelper.convertWeb3Address(address)]
        }
        return null
    }
    private fun setTypeface() {
       val  typeFace = Typeface.createFromAsset(assets, "fonts/Roboto-Regular.ttf")
        try {
            val field: Field = Typeface::class.java.getDeclaredField("DEFAULT")
            field.isAccessible = true
            field.set(null, typeFace)
        } catch (e: NoSuchFieldException) {
            e.printStackTrace()
        } catch (e: IllegalAccessException) {
            e.printStackTrace()
        }
    }

    private val mReactNativeHost = object: ReactNativeHost(this) {

        override fun getUseDeveloperSupport(): Boolean {
            return BuildConfig.DEBUG
        }

        override fun getPackages(): MutableList<ReactPackage> {
            try {
                val packages: ArrayList<ReactPackage> = PackageList(application).packages
                // 有一些第三方可能不能自动链接，对于这些包我们可以用下面的方式手动添加进来：
                packages.add(MyAppPackage())
                return packages
            }catch (e:Exception){
                e.printStackTrace()
                FirebaseCrashlytics.getInstance().recordException(e)
            }
            return ArrayList()
        }

        override fun getJSMainModuleName(): String {
            return "index"
        }

        override fun getJSEngineResolutionAlgorithm(): JSEngineResolutionAlgorithm {
            return JSEngineResolutionAlgorithm.JSC
        }
    }

    override val reactNativeHost: ReactNativeHost = mReactNativeHost
}
