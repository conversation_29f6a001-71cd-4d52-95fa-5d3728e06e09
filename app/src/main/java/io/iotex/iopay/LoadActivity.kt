package io.iotex.iopay

import android.content.Intent
import android.os.Bundle
import androidx.core.view.isVisible
import androidx.lifecycle.ViewModelProvider
import com.blankj.utilcode.util.AppUtils
import com.blankj.utilcode.util.BarUtils
import com.blankj.utilcode.util.ColorUtils
import com.blankj.utilcode.util.NetworkUtils
import com.blankj.utilcode.util.SPUtils
import com.machinefi.lockscreen.Config.SP_AUTH_LOGIN
import com.machinefi.lockscreen.LockAuthHelper
import com.machinefi.lockscreen.databinding.ActivityLockScreenBinding
import io.iotex.base.BaseViewModel
import io.iotex.base.bindbase.BaseBindActivity
import io.iotex.iopay.MainActivity.Companion.KEY_IS_VISITOR
import io.iotex.iopay.dapp.viewmodel.DiscoverViewModel
import io.iotex.iopay.home.dialog.SwapTokenViewModel
import io.iotex.iopay.network.viewmodel.NetworkSelectViewModel
import io.iotex.iopay.network.viewmodel.NetworkSwitchViewModel
import io.iotex.iopay.record.ActionRecordViewModel
import io.iotex.iopay.repo.GiftRepo
import io.iotex.iopay.repo.TransferEntryMapRepo
import io.iotex.iopay.token.TokenListViewModel
import io.iotex.iopay.transaction.approve.viewmodel.CertifiedContractViewModel
import io.iotex.iopay.util.FireBaseUtil
import io.iotex.iopay.util.IoPayConstant
import io.iotex.iopay.util.IoPayConstant.Companion.PUSH_HASH
import io.iotex.iopay.util.IoPayConstant.Companion.PUSH_TYPE
import io.iotex.iopay.util.NotificationHelper
import io.iotex.iopay.util.SPConstant
import io.iotex.iopay.util.SPConstant.SP_ACTION_USER_REINSTALL
import io.iotex.iopay.viewmodel.ContractErrorViewModel
import io.iotex.iopay.viewmodel.setting.GeneralViewModel
import io.iotex.iopay.wallet.add.UserAgreementActivity
import io.iotex.iopay.wallet.viewmodel.SplashViewModel

class LoadActivity : BaseBindActivity<BaseViewModel, ActivityLockScreenBinding>(com.machinefi.lockscreen.R.layout.activity_lock_screen) {

    private val mDiscoverViewModel by lazy {
        ViewModelProvider(this)[DiscoverViewModel::class.java]
            .apply {
                lifecycle.addObserver(this)
            }
    }

    private val mNetworkSelectViewModel by lazy {
        ViewModelProvider(this)[NetworkSelectViewModel::class.java]
            .apply {
                lifecycle.addObserver(this)
            }
    }

    private val mCertifiedViewModel by lazy {
        ViewModelProvider(this)[CertifiedContractViewModel::class.java]
            .apply {
                lifecycle.addObserver(this)
            }
    }

    private val mContractErrorViewModel by lazy {
        ViewModelProvider(this)[ContractErrorViewModel::class.java]
            .apply {
                lifecycle.addObserver(this)
            }
    }

    private val mSplashViewModel by lazy {
        ViewModelProvider(this)[SplashViewModel::class.java]
            .apply {
                lifecycle.addObserver(this)
            }
    }

    private val mNetworkSwitchViewModel by lazy {
        ViewModelProvider(this)[NetworkSwitchViewModel::class.java]
            .apply {
                lifecycle.addObserver(this)
            }
    }

    private val mActionRecordViewModel by lazy {
        ViewModelProvider(this)[ActionRecordViewModel::class.java]
            .apply {
                lifecycle.addObserver(this)
            }
    }

    private val mGeneralViewModel by lazy {
        ViewModelProvider(this)[GeneralViewModel::class.java]
            .apply {
                lifecycle.addObserver(this)
            }
    }

    private val mTokenListViewModel by lazy {
        ViewModelProvider(this)[TokenListViewModel::class.java]
            .apply {
                lifecycle.addObserver(this)
            }
    }

    private val mSwapTokenViewModel by lazy {
        ViewModelProvider(this)[SwapTokenViewModel::class.java]
            .apply {
                lifecycle.addObserver(this)
            }
    }

    private val transferEntryMapRepo by lazy {
        TransferEntryMapRepo()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        BarUtils.setStatusBarColor(this@LoadActivity, ColorUtils.getColor(R.color.transparent))
        val shouldLock = SPUtils.getInstance().getBoolean(SP_AUTH_LOGIN, false)
        mBinding.rlContent.isVisible = shouldLock
        launchApp()
        initPreData()
        initPush()
    }

    private fun launchApp() {
        if (NetworkUtils.isConnected()) {
            MainActivity.getIsVisitor {
                launchMain(it)
            }
        } else {
            startActivity(Intent(this, NetworkErrorActivity::class.java))
            finish()
        }
    }

    private fun launchMain(isVisitor:Boolean) {
        val extras = intent.extras
        val i = if (!SPUtils.getInstance().getBoolean(SPConstant.SP_INITIAL_GUIDE, false)) {
            Intent(this, InitialGuideActivity::class.java)
        } else if (!SPUtils.getInstance().getBoolean(IoPayConstant.AGREEMENT)) {
            Intent(this, UserAgreementActivity::class.java)
        } else {
            Intent(this, MainActivity::class.java)
        }
        if (extras?.getString("actHash") != null && extras.getString("actionType") != null) {
            val hash = extras.getString("actHash").toString().trim().lowercase()
            val type = extras.getString("actionType").toString().trim().lowercase()
            i.putExtra(PUSH_HASH, hash)
            i.putExtra(PUSH_TYPE, type)
        }
        val shouldLock = SPUtils.getInstance().getBoolean(SP_AUTH_LOGIN, true)
        i.putExtra(KEY_IS_VISITOR, isVisitor)
        if (!isVisitor && shouldLock) {
            LockAuthHelper.showAuthLock(this, false) {
                startActivity(i)
                finish()
            }
        } else {
            startActivity(i)
            finish()
        }

        if(SPUtils.getInstance().getBoolean(SPConstant.SP_INSTALL_AND_START_FOR_THE_FIRST_TIME,true)){
            SPUtils.getInstance().put(SPConstant.SP_INSTALL_AND_START_FOR_THE_FIRST_TIME,false)
            FireBaseUtil.logFireBase("install_and_start_for_the_first_time")
        } else {
            val versionName = AppUtils.getAppVersionName()
            if (SPUtils.getInstance().getString(SP_ACTION_USER_REINSTALL) != versionName) {
                SPUtils.getInstance().put(SP_ACTION_USER_REINSTALL, versionName)
                FireBaseUtil.logFireBase("action_user_reinstall")
            }
        }
    }

    private fun initPreData(){
        mDiscoverViewModel.fetchDAppsOneDay()
        mCertifiedViewModel.getCertifiedContractOneDay()
        mNetworkSelectViewModel.getChainListFromNetWorkOneTime()
        mContractErrorViewModel.fetchContractErrorOneDay()
        mSplashViewModel.oldAddressToEvm()
        mSplashViewModel.loadAppSwitch()
        mNetworkSwitchViewModel.fetchNetworkListOneDay()
        mNetworkSwitchViewModel.loadAAConfig()
        transferEntryMapRepo.getStakeName("")
        mActionRecordViewModel.getRecordByApi()
        GiftRepo().getTaskRemote()
        mNetworkSwitchViewModel.loadNetworkFromJson()
        mTokenListViewModel.loadTokenFromJson()
    }

    private fun initPush(){
        val deviceToken = SPUtils.getInstance().getString(IoPayConstant.DEVICE_TOKEN, "")
        if (deviceToken.isEmpty()) {
            NotificationHelper.registerFirebaseMessaging(callback = { token ->
                if (token != null) {
                    mGeneralViewModel.queryNotifyPush(token)
                    mGeneralViewModel.queryNewsLetterPush(token)
                }
            })
        } else {
            mGeneralViewModel.queryNotifyPush(deviceToken)
            mGeneralViewModel.queryNewsLetterPush(deviceToken)
        }
    }
}
