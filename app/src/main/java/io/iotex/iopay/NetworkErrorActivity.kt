package io.iotex.iopay

import com.blankj.utilcode.util.NetworkUtils
import io.iotex.base.BaseViewModel
import io.iotex.base.bindbase.BaseBindActivity
import io.iotex.iopay.databinding.ActivityNetworkErrorBinding

class NetworkErrorActivity:BaseBindActivity<BaseViewModel,ActivityNetworkErrorBinding>(R.layout.activity_network_error) {

    override fun initView() {
        mBinding.tvRetry.setOnClickListener {
            showLoading()
            if (NetworkUtils.isConnected()) {
                MainActivity.getIsVisitor {
                    MainActivity.startActivity(this, true, it)
                }
            } else {
                mBinding.tvRetry.postDelayed({
                    hideLoading()
                }, 1000)
            }
        }
    }
}