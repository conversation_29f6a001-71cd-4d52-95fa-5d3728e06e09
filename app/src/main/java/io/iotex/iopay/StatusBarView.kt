package io.iotex.iopay

import android.content.Context
import android.util.AttributeSet
import android.view.View
import io.iotex.iopay.util.extension.dp2px


class StatusBarView @JvmOverloads constructor(
    context: Context?,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(
    context,
    attrs,
    defStyleAttr
) {

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        var height: Int = getStatusHeight()
        if (height <= 0) {
            height = 20.dp2px()
        }
        super.onMeasure(widthMeasureSpec, MeasureSpec.makeMeasureSpec(height, MeasureSpec.EXACTLY))
    }

    private fun getStatusHeight(): Int {
        var result = 0
        val resourceId: Int = context.resources
            .getIdentifier("status_bar_height", "dimen", "android")
        if (resourceId > 0) {
            result = context.resources.getDimensionPixelSize(resourceId)
        }
        return result
    }
}