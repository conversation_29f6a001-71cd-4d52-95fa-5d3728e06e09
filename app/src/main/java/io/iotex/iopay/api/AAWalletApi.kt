package io.iotex.iopay.api

import io.iotex.iopay.meta.bean.Response
import okhttp3.RequestBody
import retrofit2.http.Body
import retrofit2.http.POST
import retrofit2.http.Url

interface AAWalletApi {

    @POST
    suspend fun queryBoundAddresses(@Url url: String, @Body body: RequestBody): Response<EmailAccountWrapper>

}

data class EmailAccountWrapper(
    val emailAccounts: List<EmailAccount>
)

data class EmailAccount(
    val account: String,
    val email: String,
    val enable: Boolean,
    val id: String,
    var balance: String = "0",
    var exists: Boolean = false
)