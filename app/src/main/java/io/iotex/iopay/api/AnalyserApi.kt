package io.iotex.iopay.api

import io.iotex.iopay.meta.bean.ActionResponse
import io.iotex.iopay.meta.bean.ActionXRC20Response
import io.iotex.iopay.meta.bean.ActionXRC721Response
import io.iotex.iopay.util.Config
import retrofit2.http.*

interface AnalyserApi {

    @Headers(Config.AUTHORIZATION_HEAD)
    @POST("api.ActionService.ActionByAddress")
    suspend fun actionByAccount(@Body params: @JvmSuppressWildcards Map<String, Any>): ActionResponse

    @Headers(Config.AUTHORIZATION_HEAD)
    @POST("api.XRC20Service.XRC20ByAddress")
    suspend fun actionXRC20ByAccount(@Body params: @JvmSuppressWildcards Map<String, Any>): ActionXRC20Response

    @Headers(Config.AUTHORIZATION_HEAD)
    @POST("api.XRC721Service.XRC721ByAddress")
    suspend fun actionXRC721ByAccount(@Body params: @JvmSuppressWildcards Map<String, Any>): ActionXRC721Response

}