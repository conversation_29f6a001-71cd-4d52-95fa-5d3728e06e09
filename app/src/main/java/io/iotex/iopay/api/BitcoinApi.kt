package io.iotex.iopay.api

import io.iotex.iopay.meta.bean.BitcoinFee
import io.iotex.iopay.meta.bean.BitcoinNftResponse
import io.iotex.iopay.meta.bean.BitcoinTransactionReceipt
import io.iotex.iopay.meta.bean.UnspentUTXO
import okhttp3.RequestBody
import okhttp3.ResponseBody
import retrofit2.Response
import retrofit2.http.*

interface BitcoinApi {

    @GET("api/v1/fees/recommended")
    suspend fun getBitcoinFee(): BitcoinFee

    @GET("api/address/{address}/utxo")
    suspend fun getUtxoList(@Path("address") address: String): List<UnspentUTXO>

    @GET("api/tx/{transactionId}")
    suspend fun getTransactionDetail(@Path("transactionId") transactionId: String): BitcoinTransactionReceipt

    @POST("api/tx")
    suspend fun broadcastTx(@Body txHex: RequestBody): Response<ResponseBody>

    @GET("https://btc.tokenview.io/api/ord/address/{address}/{page}/50")
    suspend fun getNftList(@Path("address") address: String, @Path("page") page: Int): BitcoinNftResponse

}