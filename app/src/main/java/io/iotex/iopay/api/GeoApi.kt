package io.iotex.iopay.api

import com.machinefi.w3bstream.bean.AuthRequest
import com.machinefi.w3bstream.bean.AuthResult
import io.iotex.iopay.meta.bean.OwnerBean
import io.iotex.iopay.meta.bean.RequestWrapper
import io.iotex.iopay.meta.bean.ResponseWrapper
import io.iotex.iopay.meta.bean.RetrievePubKeyBody
import retrofit2.http.Body
import retrofit2.http.POST

interface GeoApi {

    @POST("api/query_device")
    suspend fun requestRegisterResult(@Body params: Map<String, String>): ResponseWrapper<OwnerBean>

    @POST("api/data")
    suspend fun upload(@Body params: Map<String, String>): Any

    @POST("api/retrieve")
    suspend fun retrieve(@Body params: RequestWrapper<RetrievePubKeyBody>): ResponseWrapper<Boolean>

    @POST("api/sign")
    suspend fun sign(@Body request: AuthRequest): ResponseWrapper<AuthResult>
}