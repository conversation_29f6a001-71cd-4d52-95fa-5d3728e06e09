package io.iotex.iopay.api

import io.iotex.iopay.meta.bean.JsonAddressBean
import io.iotex.iopay.meta.bean.JsonTokenRiskBean
import io.iotex.iopay.meta.bean.ResponseWrapper
import io.iotex.iopay.viewmodel.data.ClaimResponse
import retrofit2.http.GET
import retrofit2.http.QueryMap

interface IoPayApi {

    @GET("api/trpc/topic_register")
    suspend fun topicRegister(@QueryMap params: @JvmSuppressWildcards Map<String, Any>): Any

    @GET("api/trpc/new_wallet_claim")
    suspend fun newWalletClaim(@QueryMap params: @JvmSuppressWildcards Map<String, Any>): List<ClaimResponse>

    @GET("api/trpc/register_push")
    suspend fun registerPush(@QueryMap params: @JvmSuppressWildcards Map<String, Any>): Any

    @GET("api/trpc/space_id")
    suspend fun getSpaceId(@QueryMap params: @JvmSuppressWildcards Map<String, Any>): List<ResponseWrapper<JsonAddressBean>>

    @GET("api/trpc/token_risk")
    suspend fun getTokenRisk(@QueryMap params: @JvmSuppressWildcards Map<String, Any>): List<ResponseWrapper<JsonTokenRiskBean>>
}