package io.iotex.iopay.api

import io.iotex.iopay.data.bean.FeedBackError
import io.iotex.iopay.data.bean.GiftAction
import io.iotex.iopay.data.bean.GiftResponse
import io.iotex.iopay.data.bean.SourceToken
import io.iotex.iopay.data.bean.TokenPrice
import io.iotex.iopay.data.bean.UserFeedBack
import okhttp3.ResponseBody
import retrofit2.http.*

interface IoPayGatewayApi {

    @POST("point/upload_event")
    suspend fun requestGift(@Header("Authorization") jwt: String, @Body params: GiftAction): GiftResponse?

    @GET("iopay/queryKlineData")
    suspend fun requestStock(@QueryMap params: @JvmSuppressWildcards Map<String, Any>): ResponseBody

    @GET("iopay/queryQuestions")
    suspend fun requestQuestion(@QueryMap params: @JvmSuppressWildcards Map<String, Any>): List<String>

    @GET("iopay/queryTokenPrice")
    suspend fun queryTokenPrice(@Query("id") id: String): TokenPrice

    @GET("iopay/queryTokenList")
    suspend fun queryTokenList(@Query("platform") platform: String): List<SourceToken>?

    @POST("iopay/userFeedBack")
    suspend fun userFeedBack(@Header("Authorization") jwt: String, @Body params: UserFeedBack): FeedBackError

}