package io.iotex.iopay.api

import io.iotex.iopay.data.db.NftTokenEntry
import okhttp3.ResponseBody
import retrofit2.http.GET
import retrofit2.http.Path
import retrofit2.http.Query

interface NftApi {

    @GET("token/{chainId}/{ethAddress}/image/{tokenId}")
    suspend fun getNftTokenLogo(
        @Path("chainId") chainId: Int,
        @Path("ethAddress") ethAddress: String,
        @Path("tokenId") tokenId: String
    ): ResponseBody

    @GET("account/{chainId}/own/{address}")
    suspend fun getNftList(
        @Path("chainId") chainId: Int,
        @Path("address") address: String,
        @Query("type") type: String,
        @Query("skip") skip: String,
        @Query("first") size: String
    ): List<NftTokenEntry>

}