package io.iotex.iopay.api

import io.iotex.iopay.data.bean.OkxSwapResp
import retrofit2.http.*

interface OkxSwapApi {

    @GET("api/v5/dex/aggregator/swap-instruction")
    suspend fun okxSwapInstruction(
        @QueryMap params: @JvmSuppressWildcards Map<String, Any>,
        @HeaderMap headers: HashMap<String,String>
    ): OkxSwapResp

    @GET("api/v5/dex/aggregator/swap")
    suspend fun okxSwap(
        @QueryMap params: @JvmSuppressWildcards Map<String, Any>,
        @HeaderMap headers: HashMap<String,String>
    ): OkxSwapResp

}