package io.iotex.iopay.api

import io.iotex.iopay.data.bean.SwapTokenRequest
import io.iotex.iopay.data.bean.SwapTokenResp
import io.iotex.iopay.data.bean.UniApprovalRequest
import io.iotex.iopay.data.bean.UniApprovalResp
import io.iotex.iopay.data.bean.UniSwapRequest
import io.iotex.iopay.data.bean.UniSwapResp
import io.iotex.iopay.data.bean.UniSwapTokenRequest
import io.iotex.iopay.data.bean.UniSwapTokenResp
import retrofit2.http.*

interface SwapApi {

    @POST
    suspend fun postSwapApi(@Url url: String, @Body params: SwapTokenRequest): SwapTokenResp

    @POST("v1/quote")
    suspend fun uniSwapQuote(@Body params: UniSwapTokenRequest, @HeaderMap headers: HashMap<String,String>): UniSwapTokenResp

    @POST("v1/check_approval")
    suspend fun checkApproval(@Body params: UniApprovalRequest, @HeaderMap headers: HashMap<String,String>): UniApprovalResp

    @POST("v1/swap")
    suspend fun uniSwap(@Body params:@JvmSuppressWildcards UniSwapRequest, @HeaderMap headers: HashMap<String,String>): UniSwapResp

}