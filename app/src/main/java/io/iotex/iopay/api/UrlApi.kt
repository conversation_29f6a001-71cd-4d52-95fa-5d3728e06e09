package io.iotex.iopay.api

import io.iotex.iopay.data.db.NetworkChainInfo
import io.iotex.iopay.meta.bean.ChainIdResult
import io.iotex.iopay.meta.bean.GasTimeResponse
import io.iotex.iopay.meta.bean.GasTrackerResponse
import okhttp3.ResponseBody
import retrofit2.http.*

interface UrlApi {

    @GET
    suspend fun requestCustomNetwork(@Url url: String): ArrayList<NetworkChainInfo>

    @POST
    suspend fun requestEthChainId(@Url url: String,@Body params: Map<String, String>): ChainIdResult

    @GET
    suspend fun getGasTracker(@Url url: String): GasTrackerResponse

    @GET
    suspend fun getGasTime(@Url url: String): GasTimeResponse

    @POST
    suspend fun postFirebaseEvent(@Url url: String, @Body params: Map<String, String>): ResponseBody

}