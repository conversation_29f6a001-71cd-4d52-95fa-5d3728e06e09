package io.iotex.iopay.base

import kotlinx.coroutines.*

open class BaseLaunchRepo : CoroutineScope by CoroutineScope(Dispatchers.IO) {

    private var singJob: Job? = null
    fun addLaunch(
        onError: ((Throwable) -> Unit)? = null,
        block: suspend CoroutineScope.() -> Unit
    ) {
        val exceptionHandler = CoroutineExceptionHandler { _, exception ->
            exception.printStackTrace()
            onError?.invoke(exception)
            this.cancel()
        }
        launch(SupervisorJob() + exceptionHandler) {
            block.invoke(this)
        }
    }

    fun addLaunchSingle(
        onError: ((Throwable) -> Unit)? = null,
        block: suspend CoroutineScope.() -> Unit
    ) {
        val exceptionHandler = CoroutineExceptionHandler { _, exception ->
            exception.printStackTrace()
            onError?.invoke(exception)
            this.cancel()
        }
        if (singJob?.isCancelled == false) singJob?.cancel()
        singJob = launch(SupervisorJob() + exceptionHandler) {
            block.invoke(this)
        }
    }

}