package io.iotex.iopay.contract

object Abis {
    const val VITA_ABI = """
[
   {
      "constant": true,
      "inputs": [],
      "name": "name",
      "outputs": [
         {
            "name": "",
            "type": "string"
         }
      ],
      "payable": false,
      "stateMutability": "view",
      "type": "function"
   },
   {
      "constant": true,
      "inputs": [],
      "name": "cycleIncrementalSupply",
      "outputs": [
         {
            "name": "",
            "type": "uint256"
         }
      ],
      "payable": false,
      "stateMutability": "view",
      "type": "function"
   },
   {
      "constant": true,
      "inputs": [],
      "name": "totalSupply",
      "outputs": [
         {
            "name": "",
            "type": "uint256"
         }
      ],
      "payable": false,
      "stateMutability": "view",
      "type": "function"
   },
   {
      "constant": true,
      "inputs": [],
      "name": "donationPoolAddress",
      "outputs": [
         {
            "name": "",
            "type": "address"
         }
      ],
      "payable": false,
      "stateMutability": "view",
      "type": "function"
   },
   {
      "constant": true,
      "inputs": [],
      "name": "decimals",
      "outputs": [
         {
            "name": "",
            "type": "uint8"
         }
      ],
      "payable": false,
      "stateMutability": "view",
      "type": "function"
   },
   {
      "constant": true,
      "inputs": [
         {
            "name": "",
            "type": "address"
         }
      ],
      "name": "lastClaimViewIDs",
      "outputs": [
         {
            "name": "",
            "type": "uint256"
         }
      ],
      "payable": false,
      "stateMutability": "view",
      "type": "function"
   },
   {
      "constant": false,
      "inputs": [],
      "name": "unpause",
      "outputs": [],
      "payable": false,
      "stateMutability": "nonpayable",
      "type": "function"
   },
   {
      "constant": true,
      "inputs": [],
      "name": "genesisPoolAddress",
      "outputs": [
         {
            "name": "",
            "type": "address"
         }
      ],
      "payable": false,
      "stateMutability": "view",
      "type": "function"
   },
   {
      "constant": true,
      "inputs": [],
      "name": "vps",
      "outputs": [
         {
            "name": "",
            "type": "address"
         }
      ],
      "payable": false,
      "stateMutability": "view",
      "type": "function"
   },
   {
      "constant": true,
      "inputs": [],
      "name": "paused",
      "outputs": [
         {
            "name": "",
            "type": "bool"
         }
      ],
      "payable": false,
      "stateMutability": "view",
      "type": "function"
   },
   {
      "constant": true,
      "inputs": [],
      "name": "lastDonationPoolClaimViewID",
      "outputs": [
         {
            "name": "",
            "type": "uint256"
         }
      ],
      "payable": false,
      "stateMutability": "view",
      "type": "function"
   },
   {
      "constant": true,
      "inputs": [
         {
            "name": "_owner",
            "type": "address"
         }
      ],
      "name": "balanceOf",
      "outputs": [
         {
            "name": "",
            "type": "uint256"
         }
      ],
      "payable": false,
      "stateMutability": "view",
      "type": "function"
   },
   {
      "constant": false,
      "inputs": [],
      "name": "pause",
      "outputs": [],
      "payable": false,
      "stateMutability": "nonpayable",
      "type": "function"
   },
   {
      "constant": true,
      "inputs": [],
      "name": "rewardPoolAddress",
      "outputs": [
         {
            "name": "",
            "type": "address"
         }
      ],
      "payable": false,
      "stateMutability": "view",
      "type": "function"
   },
   {
      "constant": true,
      "inputs": [],
      "name": "owner",
      "outputs": [
         {
            "name": "",
            "type": "address"
         }
      ],
      "payable": false,
      "stateMutability": "view",
      "type": "function"
   },
   {
      "constant": true,
      "inputs": [
         {
            "name": "",
            "type": "address"
         }
      ],
      "name": "authNonces",
      "outputs": [
         {
            "name": "",
            "type": "uint256"
         }
      ],
      "payable": false,
      "stateMutability": "view",
      "type": "function"
   },
   {
      "constant": true,
      "inputs": [],
      "name": "symbol",
      "outputs": [
         {
            "name": "",
            "type": "string"
         }
      ],
      "payable": false,
      "stateMutability": "view",
      "type": "function"
   },
   {
      "constant": true,
      "inputs": [],
      "name": "lastRewardPoolClaimViewID",
      "outputs": [
         {
            "name": "",
            "type": "uint256"
         }
      ],
      "payable": false,
      "stateMutability": "view",
      "type": "function"
   },
   {
      "constant": true,
      "inputs": [
         {
            "name": "_owner",
            "type": "address"
         },
         {
            "name": "_spender",
            "type": "address"
         }
      ],
      "name": "allowance",
      "outputs": [
         {
            "name": "",
            "type": "uint256"
         }
      ],
      "payable": false,
      "stateMutability": "view",
      "type": "function"
   },
   {
      "constant": true,
      "inputs": [],
      "name": "cycleLength",
      "outputs": [
         {
            "name": "",
            "type": "uint8"
         }
      ],
      "payable": false,
      "stateMutability": "view",
      "type": "function"
   },
   {
      "constant": false,
      "inputs": [
         {
            "name": "newOwner",
            "type": "address"
         }
      ],
      "name": "transferOwnership",
      "outputs": [],
      "payable": false,
      "stateMutability": "nonpayable",
      "type": "function"
   },
   {
      "inputs": [
         {
            "name": "_vps",
            "type": "address"
         },
         {
            "name": "_genesisPoolAddress",
            "type": "address"
         },
         {
            "name": "_rewardPoolAddress",
            "type": "address"
         },
         {
            "name": "_donationPoolAddress",
            "type": "address"
         }
      ],
      "payable": false,
      "stateMutability": "nonpayable",
      "type": "constructor"
   },
   {
      "anonymous": false,
      "inputs": [
         {
            "indexed": false,
            "name": "claimer",
            "type": "address"
         },
         {
            "indexed": false,
            "name": "owner",
            "type": "address"
         },
         {
            "indexed": false,
            "name": "amount",
            "type": "uint256"
         },
         {
            "indexed": false,
            "name": "viewID",
            "type": "uint256"
         }
      ],
      "name": "Claim",
      "type": "event"
   },
   {
      "anonymous": false,
      "inputs": [
         {
            "indexed": false,
            "name": "height",
            "type": "uint256"
         },
         {
            "indexed": false,
            "name": "incremetnalSupply",
            "type": "uint256"
         }
      ],
      "name": "Decay",
      "type": "event"
   },
   {
      "anonymous": false,
      "inputs": [
         {
            "indexed": false,
            "name": "viewID",
            "type": "uint256"
         }
      ],
      "name": "UpdateView",
      "type": "event"
   },
   {
      "anonymous": false,
      "inputs": [],
      "name": "Pause",
      "type": "event"
   },
   {
      "anonymous": false,
      "inputs": [],
      "name": "Unpause",
      "type": "event"
   },
   {
      "anonymous": false,
      "inputs": [
         {
            "indexed": true,
            "name": "previousOwner",
            "type": "address"
         },
         {
            "indexed": true,
            "name": "newOwner",
            "type": "address"
         }
      ],
      "name": "OwnershipTransferred",
      "type": "event"
   },
   {
      "anonymous": false,
      "inputs": [
         {
            "indexed": true,
            "name": "owner",
            "type": "address"
         },
         {
            "indexed": true,
            "name": "spender",
            "type": "address"
         },
         {
            "indexed": false,
            "name": "value",
            "type": "uint256"
         }
      ],
      "name": "Approval",
      "type": "event"
   },
   {
      "anonymous": false,
      "inputs": [
         {
            "indexed": true,
            "name": "from",
            "type": "address"
         },
         {
            "indexed": true,
            "name": "to",
            "type": "address"
         },
         {
            "indexed": false,
            "name": "value",
            "type": "uint256"
         }
      ],
      "name": "Transfer",
      "type": "event"
   },
   {
      "constant": false,
      "inputs": [
         {
            "name": "_to",
            "type": "address"
         },
         {
            "name": "_value",
            "type": "uint256"
         }
      ],
      "name": "transfer",
      "outputs": [
         {
            "name": "",
            "type": "bool"
         }
      ],
      "payable": false,
      "stateMutability": "nonpayable",
      "type": "function"
   },
   {
      "constant": false,
      "inputs": [
         {
            "name": "_from",
            "type": "address"
         },
         {
            "name": "_to",
            "type": "address"
         },
         {
            "name": "_value",
            "type": "uint256"
         }
      ],
      "name": "transferFrom",
      "outputs": [
         {
            "name": "",
            "type": "bool"
         }
      ],
      "payable": false,
      "stateMutability": "nonpayable",
      "type": "function"
   },
   {
      "constant": false,
      "inputs": [
         {
            "name": "_spender",
            "type": "address"
         },
         {
            "name": "_value",
            "type": "uint256"
         }
      ],
      "name": "approve",
      "outputs": [
         {
            "name": "",
            "type": "bool"
         }
      ],
      "payable": false,
      "stateMutability": "nonpayable",
      "type": "function"
   },
   {
      "constant": false,
      "inputs": [
         {
            "name": "_spender",
            "type": "address"
         },
         {
            "name": "_addedValue",
            "type": "uint256"
         }
      ],
      "name": "increaseApproval",
      "outputs": [
         {
            "name": "success",
            "type": "bool"
         }
      ],
      "payable": false,
      "stateMutability": "nonpayable",
      "type": "function"
   },
   {
      "constant": false,
      "inputs": [
         {
            "name": "_spender",
            "type": "address"
         },
         {
            "name": "_subtractedValue",
            "type": "uint256"
         }
      ],
      "name": "decreaseApproval",
      "outputs": [
         {
            "name": "success",
            "type": "bool"
         }
      ],
      "payable": false,
      "stateMutability": "nonpayable",
      "type": "function"
   },
   {
      "constant": false,
      "inputs": [
         {
            "name": "_newRewardPool",
            "type": "address"
         }
      ],
      "name": "setRewardPoolAddress",
      "outputs": [],
      "payable": false,
      "stateMutability": "nonpayable",
      "type": "function"
   },
   {
      "constant": false,
      "inputs": [
         {
            "name": "_newDonationPool",
            "type": "address"
         }
      ],
      "name": "setDonationPoolAddress",
      "outputs": [],
      "payable": false,
      "stateMutability": "nonpayable",
      "type": "function"
   },
   {
      "constant": false,
      "inputs": [
         {
            "name": "_newVPS",
            "type": "address"
         }
      ],
      "name": "setVPS",
      "outputs": [],
      "payable": false,
      "stateMutability": "nonpayable",
      "type": "function"
   },
   {
      "constant": true,
      "inputs": [],
      "name": "cycle",
      "outputs": [
         {
            "name": "",
            "type": "uint256"
         }
      ],
      "payable": false,
      "stateMutability": "view",
      "type": "function"
   },
   {
      "constant": true,
      "inputs": [],
      "name": "stakingPoolSize",
      "outputs": [
         {
            "name": "",
            "type": "uint256"
         }
      ],
      "payable": false,
      "stateMutability": "view",
      "type": "function"
   },
   {
      "constant": true,
      "inputs": [],
      "name": "rewardPoolSize",
      "outputs": [
         {
            "name": "",
            "type": "uint256"
         }
      ],
      "payable": false,
      "stateMutability": "view",
      "type": "function"
   },
   {
      "constant": true,
      "inputs": [],
      "name": "donationPoolSize",
      "outputs": [
         {
            "name": "",
            "type": "uint256"
         }
      ],
      "payable": false,
      "stateMutability": "view",
      "type": "function"
   },
   {
      "constant": true,
      "inputs": [],
      "name": "incrementalSupply",
      "outputs": [
         {
            "name": "",
            "type": "uint256"
         }
      ],
      "payable": false,
      "stateMutability": "view",
      "type": "function"
   },
   {
      "constant": true,
      "inputs": [],
      "name": "decayedIncrementalSupply",
      "outputs": [
         {
            "name": "",
            "type": "uint256"
         }
      ],
      "payable": false,
      "stateMutability": "view",
      "type": "function"
   },
   {
      "constant": false,
      "inputs": [],
      "name": "updateCycle",
      "outputs": [],
      "payable": false,
      "stateMutability": "nonpayable",
      "type": "function"
   },
   {
      "constant": true,
      "inputs": [
         {
            "name": "owner",
            "type": "address"
         }
      ],
      "name": "claimableAmount",
      "outputs": [
         {
            "name": "",
            "type": "uint256"
         }
      ],
      "payable": false,
      "stateMutability": "view",
      "type": "function"
   },
   {
      "constant": false,
      "inputs": [],
      "name": "claim",
      "outputs": [],
      "payable": false,
      "stateMutability": "nonpayable",
      "type": "function"
   },
   {
      "constant": false,
      "inputs": [
         {
            "name": "owner",
            "type": "address"
         },
         {
            "name": "signature",
            "type": "bytes"
         },
         {
            "name": "nonce",
            "type": "uint256"
         }
      ],
      "name": "claimAs",
      "outputs": [],
      "payable": false,
      "stateMutability": "nonpayable",
      "type": "function"
   },
   {
      "constant": false,
      "inputs": [
         {
            "name": "amount",
            "type": "uint256"
         }
      ],
      "name": "burn",
      "outputs": [],
      "payable": false,
      "stateMutability": "nonpayable",
      "type": "function"
   }
]
    """

    const val BID_ABI = """
[
   {
      "constant": true,
      "inputs": [],
      "name": "round",
      "outputs": [
         {
            "name": "",
            "type": "uint256"
         }
      ],
      "payable": false,
      "stateMutability": "view",
      "type": "function"
   },
   {
      "constant": false,
      "inputs": [
         {
            "name": "addrs",
            "type": "address[]"
         }
      ],
      "name": "removeAddressesFromWhitelist",
      "outputs": [
         {
            "name": "success",
            "type": "bool"
         }
      ],
      "payable": false,
      "stateMutability": "nonpayable",
      "type": "function"
   },
   {
      "constant": false,
      "inputs": [
         {
            "name": "addr",
            "type": "address"
         }
      ],
      "name": "removeAddressFromWhitelist",
      "outputs": [
         {
            "name": "success",
            "type": "bool"
         }
      ],
      "payable": false,
      "stateMutability": "nonpayable",
      "type": "function"
   },
   {
      "constant": true,
      "inputs": [],
      "name": "paused",
      "outputs": [
         {
            "name": "",
            "type": "bool"
         }
      ],
      "payable": false,
      "stateMutability": "view",
      "type": "function"
   },
   {
      "constant": true,
      "inputs": [],
      "name": "nextBidToSettle",
      "outputs": [
         {
            "name": "",
            "type": "uint256"
         }
      ],
      "payable": false,
      "stateMutability": "view",
      "type": "function"
   },
   {
      "constant": false,
      "inputs": [
         {
            "name": "addr",
            "type": "address"
         }
      ],
      "name": "addAddressToWhitelist",
      "outputs": [
         {
            "name": "success",
            "type": "bool"
         }
      ],
      "payable": false,
      "stateMutability": "nonpayable",
      "type": "function"
   },
   {
      "constant": true,
      "inputs": [],
      "name": "owner",
      "outputs": [
         {
            "name": "",
            "type": "address"
         }
      ],
      "payable": false,
      "stateMutability": "view",
      "type": "function"
   },
   {
      "constant": true,
      "inputs": [
         {
            "name": "",
            "type": "address"
         }
      ],
      "name": "whitelist",
      "outputs": [
         {
            "name": "",
            "type": "bool"
         }
      ],
      "payable": false,
      "stateMutability": "view",
      "type": "function"
   },
   {
      "constant": false,
      "inputs": [
         {
            "name": "addrs",
            "type": "address[]"
         }
      ],
      "name": "addAddressesToWhitelist",
      "outputs": [
         {
            "name": "success",
            "type": "bool"
         }
      ],
      "payable": false,
      "stateMutability": "nonpayable",
      "type": "function"
   },
   {
      "constant": false,
      "inputs": [
         {
            "name": "newOwner",
            "type": "address"
         }
      ],
      "name": "transferOwnership",
      "outputs": [],
      "payable": false,
      "stateMutability": "nonpayable",
      "type": "function"
   },
   {
      "inputs": [
         {
            "name": "_vitaTokenAddress",
            "type": "address"
         }
      ],
      "payable": false,
      "stateMutability": "nonpayable",
      "type": "constructor"
   },
   {
      "anonymous": false,
      "inputs": [
         {
            "indexed": false,
            "name": "round",
            "type": "uint256"
         },
         {
            "indexed": false,
            "name": "claimedAmount",
            "type": "uint256"
         },
         {
            "indexed": false,
            "name": "burnedAmount",
            "type": "uint256"
         }
      ],
      "name": "NewRound",
      "type": "event"
   },
   {
      "anonymous": false,
      "inputs": [
         {
            "indexed": true,
            "name": "round",
            "type": "uint256"
         },
         {
            "indexed": true,
            "name": "sender",
            "type": "address"
         },
         {
            "indexed": false,
            "name": "collateral",
            "type": "uint256"
         }
      ],
      "name": "VitaBidden",
      "type": "event"
   },
   {
      "anonymous": false,
      "inputs": [
         {
            "indexed": true,
            "name": "round",
            "type": "uint256"
         },
         {
            "indexed": true,
            "name": "sender",
            "type": "address"
         },
         {
            "indexed": false,
            "name": "collateral",
            "type": "uint256"
         },
         {
            "indexed": false,
            "name": "vita",
            "type": "uint256"
         }
      ],
      "name": "VitaBought",
      "type": "event"
   },
   {
      "anonymous": false,
      "inputs": [
         {
            "indexed": false,
            "name": "finished",
            "type": "bool"
         }
      ],
      "name": "VitaBidsSettled",
      "type": "event"
   },
   {
      "anonymous": false,
      "inputs": [
         {
            "indexed": true,
            "name": "receiver",
            "type": "address"
         },
         {
            "indexed": false,
            "name": "collateral",
            "type": "uint256"
         }
      ],
      "name": "CollateralWithdrawn",
      "type": "event"
   },
   {
      "anonymous": false,
      "inputs": [],
      "name": "Pause",
      "type": "event"
   },
   {
      "anonymous": false,
      "inputs": [],
      "name": "Unpause",
      "type": "event"
   },
   {
      "anonymous": false,
      "inputs": [
         {
            "indexed": false,
            "name": "addr",
            "type": "address"
         }
      ],
      "name": "WhitelistedAddressAdded",
      "type": "event"
   },
   {
      "anonymous": false,
      "inputs": [
         {
            "indexed": false,
            "name": "addr",
            "type": "address"
         }
      ],
      "name": "WhitelistedAddressRemoved",
      "type": "event"
   },
   {
      "anonymous": false,
      "inputs": [
         {
            "indexed": true,
            "name": "previousOwner",
            "type": "address"
         },
         {
            "indexed": true,
            "name": "newOwner",
            "type": "address"
         }
      ],
      "name": "OwnershipTransferred",
      "type": "event"
   },
   {
      "constant": true,
      "inputs": [
         {
            "name": "_round",
            "type": "uint256"
         }
      ],
      "name": "getTotalBidsValue",
      "outputs": [
         {
            "name": "",
            "type": "uint256"
         }
      ],
      "payable": false,
      "stateMutability": "view",
      "type": "function"
   },
   {
      "constant": true,
      "inputs": [
         {
            "name": "_round",
            "type": "uint256"
         }
      ],
      "name": "getAvailableVitaValue",
      "outputs": [
         {
            "name": "",
            "type": "uint256"
         }
      ],
      "payable": false,
      "stateMutability": "view",
      "type": "function"
   },
   {
      "constant": true,
      "inputs": [
         {
            "name": "_round",
            "type": "uint256"
         }
      ],
      "name": "getNumBids",
      "outputs": [
         {
            "name": "",
            "type": "uint256"
         }
      ],
      "payable": false,
      "stateMutability": "view",
      "type": "function"
   },
   {
      "constant": true,
      "inputs": [
         {
            "name": "_round",
            "type": "uint256"
         },
         {
            "name": "_address",
            "type": "address"
         }
      ],
      "name": "getBid",
      "outputs": [
         {
            "name": "",
            "type": "uint256"
         }
      ],
      "payable": false,
      "stateMutability": "view",
      "type": "function"
   },
   {
      "constant": false,
      "inputs": [],
      "name": "bid",
      "outputs": [],
      "payable": true,
      "stateMutability": "payable",
      "type": "function"
   },
   {
      "constant": false,
      "inputs": [
         {
            "name": "_count",
            "type": "uint256"
         }
      ],
      "name": "settle",
      "outputs": [],
      "payable": false,
      "stateMutability": "nonpayable",
      "type": "function"
   },
   {
      "constant": false,
      "inputs": [],
      "name": "reset",
      "outputs": [],
      "payable": false,
      "stateMutability": "nonpayable",
      "type": "function"
   },
   {
      "constant": false,
      "inputs": [
         {
            "name": "_receiver",
            "type": "address"
         },
         {
            "name": "_amount",
            "type": "uint256"
         }
      ],
      "name": "transferCollateral",
      "outputs": [],
      "payable": false,
      "stateMutability": "nonpayable",
      "type": "function"
   },
   {
      "constant": false,
      "inputs": [],
      "name": "pause",
      "outputs": [],
      "payable": false,
      "stateMutability": "nonpayable",
      "type": "function"
   },
   {
      "constant": false,
      "inputs": [],
      "name": "unpause",
      "outputs": [],
      "payable": false,
      "stateMutability": "nonpayable",
      "type": "function"
   }
]
    """

    const val VITA_MART_ABI = """
[
  {
    constant: false,
    inputs: [
      {
        name: "addrs",
        type: "address[]"
      }
    ],
    name: "addAddressesToWhitelist",
    outputs: [
      {
        name: "success",
        type: "bool"
      }
    ],
    payable: false,
    stateMutability: "nonpayable",
    type: "function"
  },
  {
    constant: false,
    inputs: [
      {
        name: "addr",
        type: "address"
      }
    ],
    name: "addAddressToWhitelist",
    outputs: [
      {
        name: "success",
        type: "bool"
      }
    ],
    payable: false,
    stateMutability: "nonpayable",
    type: "function"
  },
  {
    constant: false,
    inputs: [
      {
        name: "_id",
        type: "uint256"
      }
    ],
    name: "cancel",
    outputs: [],
    payable: false,
    stateMutability: "nonpayable",
    type: "function"
  },
  {
    constant: false,
    inputs: [
      {
        name: "_id",
        type: "uint256"
      },
      {
        name: "_amount",
        type: "uint256"
      }
    ],
    name: "order",
    outputs: [],
    payable: false,
    stateMutability: "nonpayable",
    type: "function"
  },
  {
    constant: false,
    inputs: [],
    name: "pause",
    outputs: [],
    payable: false,
    stateMutability: "nonpayable",
    type: "function"
  },
  {
    constant: false,
    inputs: [
      {
        name: "addrs",
        type: "address[]"
      }
    ],
    name: "removeAddressesFromWhitelist",
    outputs: [
      {
        name: "success",
        type: "bool"
      }
    ],
    payable: false,
    stateMutability: "nonpayable",
    type: "function"
  },
  {
    constant: false,
    inputs: [
      {
        name: "addr",
        type: "address"
      }
    ],
    name: "removeAddressFromWhitelist",
    outputs: [
      {
        name: "success",
        type: "bool"
      }
    ],
    payable: false,
    stateMutability: "nonpayable",
    type: "function"
  },
  {
    constant: false,
    inputs: [
      {
        name: "_id",
        type: "uint256"
      },
      {
        name: "_proof",
        type: "string"
      }
    ],
    name: "submitProof",
    outputs: [],
    payable: false,
    stateMutability: "nonpayable",
    type: "function"
  },
  {
    constant: false,
    inputs: [
      {
        name: "_id",
        type: "uint256"
      },
      {
        name: "_link",
        type: "string"
      },
      {
        name: "_paypal",
        type: "string"
      }
    ],
    name: "submitReview",
    outputs: [],
    payable: false,
    stateMutability: "nonpayable",
    type: "function"
  },
  {
    constant: false,
    inputs: [
      {
        name: "newOwner",
        type: "address"
      }
    ],
    name: "transferOwnership",
    outputs: [],
    payable: false,
    stateMutability: "nonpayable",
    type: "function"
  },
  {
    constant: false,
    inputs: [],
    name: "unpause",
    outputs: [],
    payable: false,
    stateMutability: "nonpayable",
    type: "function"
  },
  {
    inputs: [
      {
        name: "_config",
        type: "address"
      }
    ],
    payable: false,
    stateMutability: "nonpayable",
    type: "constructor"
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        name: "id",
        type: "uint256"
      },
      {
        indexed: true,
        name: "buyer",
        type: "address"
      },
      {
        indexed: false,
        name: "amount",
        type: "uint256"
      }
    ],
    name: "OrderPlaced",
    type: "event"
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        name: "id",
        type: "uint256"
      },
      {
        indexed: true,
        name: "buyer",
        type: "address"
      }
    ],
    name: "ProofSubmitted",
    type: "event"
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        name: "id",
        type: "uint256"
      },
      {
        indexed: true,
        name: "buyer",
        type: "address"
      }
    ],
    name: "ReviewSubmitted",
    type: "event"
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        name: "id",
        type: "uint256"
      },
      {
        indexed: true,
        name: "buyer",
        type: "address"
      }
    ],
    name: "OrderCancelled",
    type: "event"
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: false,
        name: "addr",
        type: "address"
      }
    ],
    name: "WhitelistedAddressAdded",
    type: "event"
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: false,
        name: "addr",
        type: "address"
      }
    ],
    name: "WhitelistedAddressRemoved",
    type: "event"
  },
  {
    anonymous: false,
    inputs: [],
    name: "Pause",
    type: "event"
  },
  {
    anonymous: false,
    inputs: [],
    name: "Unpause",
    type: "event"
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        name: "previousOwner",
        type: "address"
      },
      {
        indexed: true,
        name: "newOwner",
        type: "address"
      }
    ],
    name: "OwnershipTransferred",
    type: "event"
  },
  {
    constant: true,
    inputs: [],
    name: "config",
    outputs: [
      {
        name: "",
        type: "address"
      }
    ],
    payable: false,
    stateMutability: "view",
    type: "function"
  },
  {
    constant: true,
    inputs: [
      {
        name: "_id",
        type: "uint256"
      },
      {
        name: "_buyer",
        type: "address"
      }
    ],
    name: "orderableAmountByBuyer",
    outputs: [
      {
        name: "remaining_",
        type: "uint256"
      }
    ],
    payable: false,
    stateMutability: "view",
    type: "function"
  },
  {
    constant: true,
    inputs: [],
    name: "owner",
    outputs: [
      {
        name: "",
        type: "address"
      }
    ],
    payable: false,
    stateMutability: "view",
    type: "function"
  },
  {
    constant: true,
    inputs: [],
    name: "paused",
    outputs: [
      {
        name: "",
        type: "bool"
      }
    ],
    payable: false,
    stateMutability: "view",
    type: "function"
  },
  {
    constant: true,
    inputs: [
      {
        name: "",
        type: "address"
      }
    ],
    name: "whitelist",
    outputs: [
      {
        name: "",
        type: "bool"
      }
    ],
    payable: false,
    stateMutability: "view",
    type: "function"
  }
]
"""

    const val N2E_ABI =
        """[{"constant":true,"inputs":[],"name":"count","outputs":[{"name":"","type":"uint256"}],"payable":false,"stateMutability":"view","type":"function"},{"constant":true,"inputs":[],"name":"safe","outputs":[{"name":"","type":"address"}],"payable":false,"stateMutability":"view","type":"function"},{"constant":false,"inputs":[],"name":"withdraw","outputs":[],"payable":false,"stateMutability":"nonpayable","type":"function"},{"constant":false,"inputs":[],"name":"unpause","outputs":[],"payable":false,"stateMutability":"nonpayable","type":"function"},{"constant":false,"inputs":[{"name":"_fee","type":"uint256"}],"name":"setDepositFee","outputs":[],"payable":false,"stateMutability":"nonpayable","type":"function"},{"constant":false,"inputs":[{"name":"_maxAmount","type":"uint256"}],"name":"setMaxAmount","outputs":[],"payable":false,"stateMutability":"nonpayable","type":"function"},{"constant":true,"inputs":[],"name":"paused","outputs":[{"name":"","type":"bool"}],"payable":false,"stateMutability":"view","type":"function"},{"constant":false,"inputs":[{"name":"_safe","type":"address"}],"name":"setSafe","outputs":[],"payable":false,"stateMutability":"nonpayable","type":"function"},{"constant":true,"inputs":[],"name":"maxAmount","outputs":[{"name":"","type":"uint256"}],"payable":false,"stateMutability":"view","type":"function"},{"constant":true,"inputs":[],"name":"depositFee","outputs":[{"name":"","type":"uint256"}],"payable":false,"stateMutability":"view","type":"function"},{"constant":false,"inputs":[],"name":"pause","outputs":[],"payable":false,"stateMutability":"nonpayable","type":"function"},{"constant":false,"inputs":[{"name":"_minAmount","type":"uint256"}],"name":"setMinAmount","outputs":[],"payable":false,"stateMutability":"nonpayable","type":"function"},{"constant":true,"inputs":[],"name":"owner","outputs":[{"name":"","type":"address"}],"payable":false,"stateMutability":"view","type":"function"},{"constant":true,"inputs":[],"name":"minAmount","outputs":[{"name":"","type":"uint256"}],"payable":false,"stateMutability":"view","type":"function"},{"constant":true,"inputs":[{"name":"_offset","type":"uint256"},{"name":"_limit","type":"uint256"}],"name":"getRecords","outputs":[{"name":"_customers","type":"address[]"},{"name":"_receivers","type":"address[]"},{"name":"_amounts","type":"uint256[]"},{"name":"_fees","type":"uint256[]"}],"payable":false,"stateMutability":"view","type":"function"},{"constant":false,"inputs":[{"name":"newOwner","type":"address"}],"name":"transferOwnership","outputs":[],"payable":false,"stateMutability":"nonpayable","type":"function"},{"constant":true,"inputs":[],"name":"gasLimit","outputs":[{"name":"","type":"uint256"}],"payable":false,"stateMutability":"view","type":"function"},{"inputs":[{"name":"_safe","type":"address"},{"name":"_fee","type":"uint256"},{"name":"_minAmount","type":"uint256"},{"name":"_maxAmount","type":"uint256"}],"payable":false,"stateMutability":"nonpayable","type":"constructor"},{"payable":true,"stateMutability":"payable","type":"fallback"},{"anonymous":false,"inputs":[{"indexed":true,"name":"customer","type":"address"},{"indexed":true,"name":"receiver","type":"address"},{"indexed":false,"name":"amount","type":"uint256"},{"indexed":false,"name":"fee","type":"uint256"}],"name":"Receipt","type":"event"},{"anonymous":false,"inputs":[],"name":"Pause","type":"event"},{"anonymous":false,"inputs":[],"name":"Unpause","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"name":"previousOwner","type":"address"},{"indexed":true,"name":"newOwner","type":"address"}],"name":"OwnershipTransferred","type":"event"},{"constant":false,"inputs":[],"name":"deposit","outputs":[],"payable":true,"stateMutability":"payable","type":"function"},{"constant":false,"inputs":[{"name":"_to","type":"address"}],"name":"depositTo","outputs":[],"payable":true,"stateMutability":"payable","type":"function"},{"constant":false,"inputs":[{"name":"_gasLimit","type":"uint256"}],"name":"setGasLimit","outputs":[],"payable":false,"stateMutability":"nonpayable","type":"function"}]"""

    const val NATIVE_TOKEN_ABI = """
[
  {
    "constant":false,
    "inputs":[{"name":"_pyggIndex","type":"uint256"},{"name":"_data","type":"bytes"}],
    "name":"withdraw",
    "outputs":[],
    "payable":false,
    "stateMutability":"nonpayable",
    "type":"function"
  },
  {
    "constant":false,
    "inputs":[
      {"name":"_canName","type":"bytes12"},
      {"name":"_stakeDuration","type":"uint256"},
      {"name":"_nonDecay","type":"bool"},
      {"name":"_data","type":"bytes"}
    ],
    "name":"createPygg",
    "outputs":[{"name":"","type":"uint256"}],
    "payable":true,
    "stateMutability":"payable",
    "type":"function"
  },
  {
    "constant":true,
    "inputs":[],
    "name":"maxPyggsPerAddr",
    "outputs":[{"name":"","type":"uint256"}],
    "payable":false,
    "stateMutability":"view",
    "type":"function"
  },
  {
    "constant":false,
    "inputs":[{"name":"addrs","type":"address[]"}],
    "name":"removeAddressesFromWhitelist",
    "outputs":[{"name":"success","type":"bool"}],
    "payable":false,
    "stateMutability":"nonpayable",
    "type":"function"
  },
  {
    "constant":false,
    "inputs":[{"name":"addr","type":"address"}],
    "name":"removeAddressFromWhitelist",
    "outputs":[{"name":"success","type":"bool"}],
    "payable":false,
    "stateMutability":"nonpayable",
    "type":"function"
  },
  {
    "constant":true,
    "inputs":[{"name":"_address","type":"address"}],
    "name":"isOwner",
    "outputs":[{"name":"","type":"bool"}],
    "payable":false,
    "stateMutability":"view",
    "type":"function"
  },
  {
    "constant":false,
    "inputs":[],
    "name":"unpause",
    "outputs":[],
    "payable":false,
    "stateMutability":"nonpayable",
    "type":"function"
  },
  {
    "constant":true,
    "inputs":[{"name":"","type":"address"},{"name":"","type":"uint256"}],
    "name":"stakeholders",
    "outputs":[{"name":"","type":"uint256"}],
    "payable":false,
    "stateMutability":"view",
    "type":"function"
  },
  {
    "constant":true,
    "inputs":[],
    "name":"paused",
    "outputs":[{"name":"","type":"bool"}],
    "payable":false,
    "stateMutability":"view",
    "type":"function"
  },
  {
    "constant":true,
    "inputs":[],
    "name":"minStakeDuration",
    "outputs":[{"name":"","type":"uint256"}],
    "payable":false,
    "stateMutability":"view",
    "type":"function"
  },
  {
    "constant":true,
    "inputs":[],
    "name":"secondsPerDay",
    "outputs":[{"name":"","type":"uint256"}],
    "payable":false,
    "stateMutability":"view",
    "type":"function"
  },
  {
    "constant":true,
    "inputs":[{"name":"_prevIndex","type":"uint256"},{"name":"_limit","type":"uint256"}],
    "name":"getActivePyggCreateTimes",
    "outputs":[
      {"name":"count","type":"uint256"},
      {"name":"indexes","type":"uint256[]"},
      {"name":"createTimes","type":"uint256[]"}
    ],
    "payable":false,
    "stateMutability":"view",
    "type":"function"
  },
  {
    "constant":false,
    "inputs":[{"name":"_pyggIndex","type":"uint256"},{"name":"_data","type":"bytes"}],
    "name":"storeToPygg",
    "outputs":[],
    "payable":true,
    "stateMutability":"payable",
    "type":"function"
  },
  {
    "constant":true,
    "inputs":[],
    "name":"maxStakeDuration",
    "outputs":[{"name":"","type":"uint256"}],
    "payable":false,
    "stateMutability":"view",
    "type":"function"
  },
  {
    "constant":false,
    "inputs":[
      {"name":"_pyggIndex","type":"uint256"},
      {"name":"_stakeDuration","type":"uint256"},
      {"name":"_nonDecay","type":"bool"},
      {"name":"_data","type":"bytes"}
    ],
    "name":"restake",
    "outputs":[],
    "payable":false,
    "stateMutability":"nonpayable",
    "type":"function"
  },
  {
    "constant":false,
    "inputs":[{"name":"addr","type":"address"}],
    "name":"addAddressToWhitelist",
    "outputs":[{"name":"success","type":"bool"}],
    "payable":false,
    "stateMutability":"nonpayable",
    "type":"function"
  },
  {
    "constant":false,
    "inputs":[
      {"name":"_pyggIndex","type":"uint256"},
      {"name":"_newOwner","type":"address"},
      {"name":"_data","type":"bytes"}
    ],
    "name":"transferOwnershipOfPygg",
    "outputs":[],
    "payable":false,
    "stateMutability":"nonpayable",
    "type":"function"
  },
  {
    "constant":true,
    "inputs":[],
    "name":"totalStaked",
    "outputs":[{"name":"","type":"uint256"}],
    "payable":false,
    "stateMutability":"view",
    "type":"function"
  },
  {
    "constant":false,
    "inputs":[],
    "name":"pause",
    "outputs":[],
    "payable":false,
    "stateMutability":"nonpayable",
    "type":"function"
  },
  {
    "constant":true,
    "inputs":[],
    "name":"owner",
    "outputs":[{"name":"","type":"address"}],
    "payable":false,
    "stateMutability":"view",
    "type":"function"
  },
  {
    "constant":true,
    "inputs":[{"name":"_prevIndex","type":"uint256"},{"name":"_limit","type":"uint256"}],
    "name":"getActivePyggIdx",
    "outputs":[{"name":"count","type":"uint256"},{"name":"indexes","type":"uint256[]"}],
    "payable":false,
    "stateMutability":"view",
    "type":"function"
  },
  {
    "constant":true,
    "inputs":[{"name":"","type":"address"}],
    "name":"whitelist",
    "outputs":[{"name":"","type":"bool"}],
    "payable":false,
    "stateMutability":"view",
    "type":"function"
  },
  {
    "constant":true,
    "inputs":[],
    "name":"unStakeDuration",
    "outputs":[{"name":"","type":"uint256"}],
    "payable":false,
    "stateMutability":"view",
    "type":"function"
  },
  {
    "constant":false,
    "inputs":[{"name":"_pyggIndex","type":"uint256"},{"name":"_data","type":"bytes"}],
    "name":"unstake",
    "outputs":[],
    "payable":false,
    "stateMutability":"nonpayable",
    "type":"function"
  },
  {
    "constant":true,
    "inputs":[{"name":"","type":"uint256"}],
    "name":"pyggs",
    "outputs":[
      {"name":"canName","type":"bytes12"},
      {"name":"stakedAmount","type":"uint256"},
      {"name":"stakeDuration","type":"uint256"},
      {"name":"stakeStartTime","type":"uint256"},
      {"name":"nonDecay","type":"bool"},
      {"name":"unstakeStartTime","type":"uint256"},
      {"name":"pyggOwner","type":"address"},
      {"name":"createTime","type":"uint256"},
      {"name":"prev","type":"uint256"},
      {"name":"next","type":"uint256"}
    ],
    "payable":false,
    "stateMutability":"view",
    "type":"function"
  },
  {
    "constant":true,
    "inputs":[{"name":"_owner","type":"address"}],
    "name":"getPyggIndexesByAddress",
    "outputs":[{"name":"","type":"uint256[]"}],
    "payable":false,
    "stateMutability":"view",
    "type":"function"
  },
  {
    "constant":false,
    "inputs":[
      {"name":"_pyggIndex","type":"uint256"},
      {"name":"_canName","type":"bytes12"},
      {"name":"_data","type":"bytes"}
    ],
    "name":"revote",
    "outputs":[],
    "payable":false,
    "stateMutability":"nonpayable",
    "type":"function"
  },
  {
    "constant":true,
    "inputs":[{"name":"_prevIndex","type":"uint256"},{"name":"_limit","type":"uint256"}],
    "name":"getActivePyggs",
    "outputs":[
      {"name":"count","type":"uint256"},
      {"name":"indexes","type":"uint256[]"},
      {"name":"stakeStartTimes","type":"uint256[]"},
      {"name":"stakeDurations","type":"uint256[]"},
      {"name":"decays","type":"bool[]"},
      {"name":"stakedAmounts","type":"uint256[]"},
      {"name":"canNames","type":"bytes12[]"},
      {"name":"owners","type":"address[]"}
    ],
    "payable":false,
    "stateMutability":"view",
    "type":"function"
  },
  {
    "constant":false,
    "inputs":[{"name":"addrs","type":"address[]"}],
    "name":"addAddressesToWhitelist",
    "outputs":[{"name":"success","type":"bool"}],
    "payable":false,
    "stateMutability":"nonpayable",
    "type":"function"
  },
  {
    "constant":true,
    "inputs":[],
    "name":"minStakeAmount",
    "outputs":[{"name":"","type":"uint256"}],
    "payable":false,
    "stateMutability":"view",
    "type":"function"
  },
  {
    "constant":false,
    "inputs":[{"name":"_newOwner","type":"address"}],
    "name":"transferOwnership",
    "outputs":[],
    "payable":false,
    "stateMutability":"nonpayable",
    "type":"function"
  },
  {
    "inputs":[
      {"name":"_minStakeAmount","type":"uint256"},
      {"name":"_maxPyggsPerAddr","type":"uint256"}
    ],
    "payable":false,
    "stateMutability":"nonpayable",
    "type":"constructor"
  },
  {
    "anonymous":false,
    "inputs":[
      {"indexed":false,"name":"pyggIndex","type":"uint256"},
      {"indexed":false,"name":"canName","type":"bytes12"},
      {"indexed":false,"name":"amount","type":"uint256"},
      {"indexed":false,"name":"stakeDuration","type":"uint256"},
      {"indexed":false,"name":"stakeStartTime","type":"uint256"},
      {"indexed":false,"name":"nonDecay","type":"bool"},
      {"indexed":false,"name":"pyggOwner","type":"address"},
      {"indexed":false,"name":"data","type":"bytes"}
    ],
    "name":"PyggCreated",
    "type":"event"
  },
  {
    "anonymous":false,
    "inputs":[
      {"indexed":false,"name":"pyggIndex","type":"uint256"},
      {"indexed":false,"name":"canName","type":"bytes12"},
      {"indexed":false,"name":"amount","type":"uint256"},
      {"indexed":false,"name":"stakeDuration","type":"uint256"},
      {"indexed":false,"name":"stakeStartTime","type":"uint256"},
      {"indexed":false,"name":"nonDecay","type":"bool"},
      {"indexed":false,"name":"pyggOwner","type":"address"},
      {"indexed":false,"name":"data","type":"bytes"}
    ],
    "name":"PyggUpdated",
    "type":"event"
  },
  {
    "anonymous":false,
    "inputs":[
      {"indexed":false,"name":"pyggIndex","type":"uint256"},
      {"indexed":false,"name":"canName","type":"bytes12"},
      {"indexed":false,"name":"amount","type":"uint256"},
      {"indexed":false,"name":"data","type":"bytes"}
    ],
    "name":"PyggUnstake",
    "type":"event"
  },
  {
    "anonymous":false,
    "inputs":[
      {"indexed":false,"name":"pyggIndex","type":"uint256"},
      {"indexed":false,"name":"canName","type":"bytes12"},
      {"indexed":false,"name":"amount","type":"uint256"},
      {"indexed":false,"name":"data","type":"bytes"}
    ],
    "name":"PyggWithdraw",
    "type":"event"
  },
  {
    "anonymous":false,
    "inputs":[{"indexed":false,"name":"addr","type":"address"}],
    "name":"WhitelistedAddressAdded",
    "type":"event"
  },
  {
    "anonymous":false,
    "inputs":[{"indexed":false,"name":"addr","type":"address"}],
    "name":"WhitelistedAddressRemoved",
    "type":"event"
  },
  {"anonymous":false,"inputs":[],"name":"Pause","type":"event"},
  {"anonymous":false,"inputs":[],"name":"Unpause","type":"event"}
]
"""
    const val POLL_ABI = """
[
    {
      "constant": true,
      "inputs": [],
      "name": "status",
      "outputs": [
        {
          "name": "",
          "type": "uint8"
        }
      ],
      "payable": false,
      "stateMutability": "view",
      "type": "function"
    },
    {
      "constant": true,
      "inputs": [],
      "name": "multiChoice",
      "outputs": [
        {
          "name": "",
          "type": "uint256"
        }
      ],
      "payable": false,
      "stateMutability": "view",
      "type": "function"
    },
    {
      "constant": true,
      "inputs": [
        {
          "name": "",
          "type": "address"
        }
      ],
      "name": "ballots",
      "outputs": [
        {
          "name": "flag",
          "type": "bool"
        }
      ],
      "payable": false,
      "stateMutability": "view",
      "type": "function"
    },
    {
      "constant": true,
      "inputs": [
        {
          "name": "_address",
          "type": "address"
        }
      ],
      "name": "isOwner",
      "outputs": [
        {
          "name": "",
          "type": "bool"
        }
      ],
      "payable": false,
      "stateMutability": "view",
      "type": "function"
    },
    {
      "constant": true,
      "inputs": [],
      "name": "vps",
      "outputs": [
        {
          "name": "",
          "type": "address"
        }
      ],
      "payable": false,
      "stateMutability": "view",
      "type": "function"
    },
    {
      "constant": true,
      "inputs": [],
      "name": "owner",
      "outputs": [
        {
          "name": "",
          "type": "address"
        }
      ],
      "payable": false,
      "stateMutability": "view",
      "type": "function"
    },
    {
      "constant": true,
      "inputs": [],
      "name": "canRevote",
      "outputs": [
        {
          "name": "",
          "type": "bool"
        }
      ],
      "payable": false,
      "stateMutability": "view",
      "type": "function"
    },
    {
      "constant": true,
      "inputs": [],
      "name": "optionCount",
      "outputs": [
        {
          "name": "",
          "type": "uint256"
        }
      ],
      "payable": false,
      "stateMutability": "view",
      "type": "function"
    },
    {
      "constant": false,
      "inputs": [
        {
          "name": "_newOwner",
          "type": "address"
        }
      ],
      "name": "transferOwnership",
      "outputs": [],
      "payable": false,
      "stateMutability": "nonpayable",
      "type": "function"
    },
    {
      "inputs": [
        {
          "name": "_uri",
          "type": "string"
        },
        {
          "name": "_hash",
          "type": "bytes32"
        },
        {
          "name": "_vpsAddress",
          "type": "address"
        },
        {
          "name": "_optionCount",
          "type": "uint256"
        },
        {
          "name": "_canRevote",
          "type": "bool"
        },
        {
          "name": "_multiChoice",
          "type": "uint8"
        }
      ],
      "payable": false,
      "stateMutability": "nonpayable",
      "type": "constructor"
    },
    {
      "anonymous": false,
      "inputs": [
        {
          "indexed": true,
          "name": "_from",
          "type": "address"
        },
        {
          "indexed": false,
          "name": "_value",
          "type": "uint256"
        }
      ],
      "name": "OnVote",
      "type": "event"
    },
    {
      "anonymous": false,
      "inputs": [
        {
          "indexed": false,
          "name": "newIsOpen",
          "type": "bool"
        }
      ],
      "name": "OnStatusChange",
      "type": "event"
    },
    {
      "constant": false,
      "inputs": [],
      "name": "pause",
      "outputs": [],
      "payable": false,
      "stateMutability": "nonpayable",
      "type": "function"
    },
    {
      "constant": false,
      "inputs": [],
      "name": "unpause",
      "outputs": [],
      "payable": false,
      "stateMutability": "nonpayable",
      "type": "function"
    },
    {
      "constant": false,
      "inputs": [],
      "name": "start",
      "outputs": [],
      "payable": false,
      "stateMutability": "nonpayable",
      "type": "function"
    },
    {
      "constant": false,
      "inputs": [],
      "name": "end",
      "outputs": [],
      "payable": false,
      "stateMutability": "nonpayable",
      "type": "function"
    },
    {
      "constant": true,
      "inputs": [],
      "name": "isNew",
      "outputs": [
        {
          "name": "",
          "type": "bool"
        }
      ],
      "payable": false,
      "stateMutability": "view",
      "type": "function"
    },
    {
      "constant": true,
      "inputs": [],
      "name": "isActive",
      "outputs": [
        {
          "name": "",
          "type": "bool"
        }
      ],
      "payable": false,
      "stateMutability": "view",
      "type": "function"
    },
    {
      "constant": true,
      "inputs": [],
      "name": "isPaused",
      "outputs": [
        {
          "name": "",
          "type": "bool"
        }
      ],
      "payable": false,
      "stateMutability": "view",
      "type": "function"
    },
    {
      "constant": true,
      "inputs": [],
      "name": "isEnded",
      "outputs": [
        {
          "name": "",
          "type": "bool"
        }
      ],
      "payable": false,
      "stateMutability": "view",
      "type": "function"
    },
    {
      "constant": false,
      "inputs": [
        {
          "name": "_opt",
          "type": "uint256"
        }
      ],
      "name": "vote",
      "outputs": [
        {
          "name": "",
          "type": "bool"
        }
      ],
      "payable": false,
      "stateMutability": "nonpayable",
      "type": "function"
    },
    {
      "constant": false,
      "inputs": [
        {
          "name": "_opts",
          "type": "uint256[]"
        }
      ],
      "name": "voteMultiple",
      "outputs": [
        {
          "name": "",
          "type": "bool"
        }
      ],
      "payable": false,
      "stateMutability": "nonpayable",
      "type": "function"
    },
    {
      "constant": true,
      "inputs": [],
      "name": "numOfVoters",
      "outputs": [
        {
          "name": "",
          "type": "uint256"
        }
      ],
      "payable": false,
      "stateMutability": "view",
      "type": "function"
    },
    {
      "constant": true,
      "inputs": [
        {
          "name": "_offset",
          "type": "uint256"
        },
        {
          "name": "_limit",
          "type": "uint256"
        }
      ],
      "name": "voters",
      "outputs": [
        {
          "name": "addrs_",
          "type": "address[]"
        }
      ],
      "payable": false,
      "stateMutability": "view",
      "type": "function"
    },
    {
      "constant": true,
      "inputs": [
        {
          "name": "_opt",
          "type": "uint256"
        },
        {
          "name": "_voters",
          "type": "address[]"
        }
      ],
      "name": "voted",
      "outputs": [
        {
          "name": "voted_",
          "type": "bool[]"
        }
      ],
      "payable": false,
      "stateMutability": "view",
      "type": "function"
    },
    {
      "constant": false,
      "inputs": [
        {
          "name": "_isOpen",
          "type": "bool"
        }
      ],
      "name": "setStatus",
      "outputs": [
        {
          "name": "success",
          "type": "bool"
        }
      ],
      "payable": false,
      "stateMutability": "nonpayable",
      "type": "function"
    },
    {
      "constant": true,
      "inputs": [
        {
          "name": "addr",
          "type": "address"
        }
      ],
      "name": "ballotOf",
      "outputs": [
        {
          "name": "",
          "type": "uint256"
        }
      ],
      "payable": false,
      "stateMutability": "view",
      "type": "function"
    },
    {
      "constant": true,
      "inputs": [
        {
          "name": "_addr",
          "type": "address"
        }
      ],
      "name": "ballotsOf",
      "outputs": [
        {
          "name": "ballots_",
          "type": "uint256[]"
        }
      ],
      "payable": false,
      "stateMutability": "view",
      "type": "function"
    },
    {
      "constant": true,
      "inputs": [
        {
          "name": "addr",
          "type": "address"
        }
      ],
      "name": "weightOf",
      "outputs": [
        {
          "name": "",
          "type": "uint256"
        }
      ],
      "payable": false,
      "stateMutability": "view",
      "type": "function"
    },
    {
      "constant": true,
      "inputs": [],
      "name": "getStatus",
      "outputs": [
        {
          "name": "",
          "type": "bool"
        }
      ],
      "payable": false,
      "stateMutability": "view",
      "type": "function"
    },
    {
      "constant": true,
      "inputs": [
        {
          "name": "_opt",
          "type": "uint256"
        }
      ],
      "name": "weightedVoteCountsOf",
      "outputs": [
        {
          "name": "count_",
          "type": "uint256"
        }
      ],
      "payable": false,
      "stateMutability": "view",
      "type": "function"
    },
    {
      "constant": true,
      "inputs": [],
      "name": "winningOption",
      "outputs": [
        {
          "name": "winningOption_",
          "type": "uint256"
        }
      ],
      "payable": false,
      "stateMutability": "view",
      "type": "function"
    },
    {
      "constant": true,
      "inputs": [],
      "name": "description",
      "outputs": [
        {
          "name": "",
          "type": "string"
        }
      ],
      "payable": false,
      "stateMutability": "view",
      "type": "function"
    },
    {
      "constant": true,
      "inputs": [
        {
          "name": "_opt",
          "type": "uint256"
        }
      ],
      "name": "optionDescription",
      "outputs": [
        {
          "name": "",
          "type": "string"
        }
      ],
      "payable": false,
      "stateMutability": "view",
      "type": "function"
    },
    {
      "constant": true,
      "inputs": [],
      "name": "availableOptions",
      "outputs": [
        {
          "name": "options_",
          "type": "uint256[]"
        }
      ],
      "payable": false,
      "stateMutability": "view",
      "type": "function"
    }
  ]
"""

    const val NFT_ABI =
        """[{"inputs":[],"payable":false,"stateMutability":"nonpayable","type":"constructor"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"owner","type":"address"},{"indexed":true,"internalType":"address","name":"approved","type":"address"},{"indexed":true,"internalType":"uint256","name":"tokenId","type":"uint256"}],"name":"Approval","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"owner","type":"address"},{"indexed":true,"internalType":"address","name":"operator","type":"address"},{"indexed":false,"internalType":"bool","name":"approved","type":"bool"}],"name":"ApprovalForAll","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"account","type":"address"}],"name":"MinterAdded","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"account","type":"address"}],"name":"MinterRemoved","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"from","type":"address"},{"indexed":true,"internalType":"address","name":"to","type":"address"},{"indexed":true,"internalType":"uint256","name":"tokenId","type":"uint256"}],"name":"Transfer","type":"event"},{"constant":false,"inputs":[{"internalType":"address","name":"minter","type":"address"}],"name":"addMinter","outputs":[],"payable":false,"stateMutability":"nonpayable","type":"function"},{"constant":false,"inputs":[{"internalType":"address","name":"to","type":"address"},{"internalType":"uint256","name":"tokenId","type":"uint256"}],"name":"approve","outputs":[],"payable":false,"stateMutability":"nonpayable","type":"function"},{"constant":true,"inputs":[{"internalType":"address","name":"owner","type":"address"}],"name":"balanceOf","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"payable":false,"stateMutability":"view","type":"function"},{"constant":false,"inputs":[{"internalType":"uint256","name":"tokenId","type":"uint256"}],"name":"burn","outputs":[],"payable":false,"stateMutability":"nonpayable","type":"function"},{"constant":true,"inputs":[{"internalType":"uint256","name":"tokenId","type":"uint256"}],"name":"getApproved","outputs":[{"internalType":"address","name":"","type":"address"}],"payable":false,"stateMutability":"view","type":"function"},{"constant":true,"inputs":[{"internalType":"address","name":"owner","type":"address"},{"internalType":"address","name":"operator","type":"address"}],"name":"isApprovedForAll","outputs":[{"internalType":"bool","name":"","type":"bool"}],"payable":false,"stateMutability":"view","type":"function"},{"constant":true,"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"isMinter","outputs":[{"internalType":"bool","name":"","type":"bool"}],"payable":false,"stateMutability":"view","type":"function"},{"constant":false,"inputs":[{"internalType":"address","name":"to","type":"address"},{"internalType":"uint256","name":"tokenId","type":"uint256"}],"name":"mint","outputs":[{"internalType":"bool","name":"","type":"bool"}],"payable":false,"stateMutability":"nonpayable","type":"function"},{"constant":true,"inputs":[{"internalType":"uint256","name":"tokenId","type":"uint256"}],"name":"ownerOf","outputs":[{"internalType":"address","name":"","type":"address"}],"payable":false,"stateMutability":"view","type":"function"},{"constant":false,"inputs":[],"name":"renouncMinter","outputs":[],"payable":false,"stateMutability":"nonpayable","type":"function"},{"constant":false,"inputs":[{"internalType":"address","name":"to","type":"address"},{"internalType":"uint256","name":"tokenId","type":"uint256"},{"internalType":"bytes","name":"_data","type":"bytes"}],"name":"safeMint","outputs":[{"internalType":"bool","name":"","type":"bool"}],"payable":false,"stateMutability":"nonpayable","type":"function"},{"constant":false,"inputs":[{"internalType":"address","name":"to","type":"address"},{"internalType":"uint256","name":"tokenId","type":"uint256"}],"name":"safeMint","outputs":[{"internalType":"bool","name":"","type":"bool"}],"payable":false,"stateMutability":"nonpayable","type":"function"},{"constant":false,"inputs":[{"internalType":"address","name":"from","type":"address"},{"internalType":"address","name":"to","type":"address"},{"internalType":"uint256","name":"tokenId","type":"uint256"}],"name":"safeTransferFrom","outputs":[],"payable":false,"stateMutability":"nonpayable","type":"function"},{"constant":false,"inputs":[{"internalType":"address","name":"from","type":"address"},{"internalType":"address","name":"to","type":"address"},{"internalType":"uint256","name":"tokenId","type":"uint256"},{"internalType":"bytes","name":"_data","type":"bytes"}],"name":"safeTransferFrom","outputs":[],"payable":false,"stateMutability":"nonpayable","type":"function"},{"constant":false,"inputs":[{"internalType":"address","name":"to","type":"address"},{"internalType":"bool","name":"approved","type":"bool"}],"name":"setApprovalForAll","outputs":[],"payable":false,"stateMutability":"nonpayable","type":"function"},{"constant":true,"inputs":[{"internalType":"bytes4","name":"interfaceId","type":"bytes4"}],"name":"supportsInterface","outputs":[{"internalType":"bool","name":"","type":"bool"}],"payable":false,"stateMutability":"view","type":"function"},{"constant":true,"inputs":[{"internalType":"uint256","name":"index","type":"uint256"}],"name":"tokenByIndex","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"payable":false,"stateMutability":"view","type":"function"},{"constant":true,"inputs":[{"internalType":"address","name":"owner","type":"address"},{"internalType":"uint256","name":"index","type":"uint256"}],"name":"tokenOfOwnerByIndex","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"payable":false,"stateMutability":"view","type":"function"},{"constant":true,"inputs":[{"internalType":"address","name":"owner","type":"address"}],"name":"tokensByOwner","outputs":[{"internalType":"uint256[]","name":"","type":"uint256[]"}],"payable":false,"stateMutability":"view","type":"function"},{"constant":true,"inputs":[],"name":"totalSupply","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"payable":false,"stateMutability":"view","type":"function"},{"constant":false,"inputs":[{"internalType":"address","name":"from","type":"address"},{"internalType":"address","name":"to","type":"address"},{"internalType":"uint256","name":"tokenId","type":"uint256"}],"name":"transferFrom","outputs":[],"payable":false,"stateMutability":"nonpayable","type":"function"}]"""

    const val COMPOUND_INTEREST_ABI = """[
    {
        "anonymous": false,
        "inputs": [
            {
                "indexed": true,
                "internalType": "address",
                "name": "previousOwner",
                "type": "address"
            },
            {
                "indexed": true,
                "internalType": "address",
                "name": "newOwner",
                "type": "address"
            }
        ],
        "name": "OwnershipTransferred",
        "type": "event"
    },
    {
        "anonymous": false,
        "inputs": [
            {
                "indexed": false,
                "internalType": "address",
                "name": "account",
                "type": "address"
            }
        ],
        "name": "Paused",
        "type": "event"
    },
    {
        "anonymous": false,
        "inputs": [
            {
                "indexed": false,
                "internalType": "address",
                "name": "account",
                "type": "address"
            }
        ],
        "name": "Unpaused",
        "type": "event"
    },
    {
        "constant": true,
        "inputs": [
            {
                "internalType": "address",
                "name": "",
                "type": "address"
            }
        ],
        "name": "buckets",
        "outputs": [
            {
                "internalType": "int256",
                "name": "",
                "type": "int256"
            }
        ],
        "payable": false,
        "stateMutability": "view",
        "type": "function"
    },
    {
        "constant": true,
        "inputs": [],
        "name": "owner",
        "outputs": [
            {
                "internalType": "address",
                "name": "",
                "type": "address"
            }
        ],
        "payable": false,
        "stateMutability": "view",
        "type": "function"
    },
    {
        "constant": true,
        "inputs": [],
        "name": "paused",
        "outputs": [
            {
                "internalType": "bool",
                "name": "",
                "type": "bool"
            }
        ],
        "payable": false,
        "stateMutability": "view",
        "type": "function"
    },
    {
        "constant": true,
        "inputs": [
            {
                "internalType": "address",
                "name": "",
                "type": "address"
            }
        ],
        "name": "registrants",
        "outputs": [
            {
                "internalType": "bool",
                "name": "",
                "type": "bool"
            }
        ],
        "payable": false,
        "stateMutability": "view",
        "type": "function"
    },
    {
        "constant": false,
        "inputs": [],
        "name": "renounceOwnership",
        "outputs": [],
        "payable": false,
        "stateMutability": "nonpayable",
        "type": "function"
    },
    {
        "constant": false,
        "inputs": [
            {
                "internalType": "address",
                "name": "newOwner",
                "type": "address"
            }
        ],
        "name": "transferOwnership",
        "outputs": [],
        "payable": false,
        "stateMutability": "nonpayable",
        "type": "function"
    },
    {
        "constant": false,
        "inputs": [],
        "name": "pause",
        "outputs": [],
        "payable": false,
        "stateMutability": "nonpayable",
        "type": "function"
    },
    {
        "constant": false,
        "inputs": [],
        "name": "unpause",
        "outputs": [],
        "payable": false,
        "stateMutability": "nonpayable",
        "type": "function"
    },
    {
        "constant": false,
        "inputs": [
            {
                "internalType": "int256",
                "name": "bucketId",
                "type": "int256"
            }
        ],
        "name": "register",
        "outputs": [],
        "payable": false,
        "stateMutability": "nonpayable",
        "type": "function"
    },
    {
        "constant": false,
        "inputs": [],
        "name": "unregister",
        "outputs": [],
        "payable": false,
        "stateMutability": "nonpayable",
        "type": "function"
    },
    {
        "constant": true,
        "inputs": [
            {
                "internalType": "address",
                "name": "owner",
                "type": "address"
            }
        ],
        "name": "bucket",
        "outputs": [
            {
                "internalType": "int256",
                "name": "",
                "type": "int256"
            }
        ],
        "payable": false,
        "stateMutability": "view",
        "type": "function"
    }]
"""

    const val ExchangeABI = """[
    {
        "anonymous": false, 
        "inputs": [
            {
                "indexed": true, 
                "internalType": "address", 
                "name": "provider", 
                "type": "address"
            }, 
            {
                "indexed": true, 
                "internalType": "uint256", 
                "name": "iotx_amount", 
                "type": "uint256"
            }, 
            {
                "indexed": true, 
                "internalType": "uint256", 
                "name": "token_amount", 
                "type": "uint256"
            }
        ], 
        "name": "AddLiquidity", 
        "type": "event"
    }, 
    {
        "anonymous": false, 
        "inputs": [
            {
                "indexed": true, 
                "internalType": "address", 
                "name": "owner", 
                "type": "address"
            }, 
            {
                "indexed": true, 
                "internalType": "address", 
                "name": "spender", 
                "type": "address"
            }, 
            {
                "indexed": false, 
                "internalType": "uint256", 
                "name": "value", 
                "type": "uint256"
            }
        ], 
        "name": "Approval", 
        "type": "event"
    }, 
    {
        "anonymous": false, 
        "inputs": [
            {
                "indexed": true, 
                "internalType": "address", 
                "name": "buyer", 
                "type": "address"
            }, 
            {
                "indexed": true, 
                "internalType": "uint256", 
                "name": "tokens_sold", 
                "type": "uint256"
            }, 
            {
                "indexed": true, 
                "internalType": "uint256", 
                "name": "iotx_bought", 
                "type": "uint256"
            }
        ], 
        "name": "IotxPurchase", 
        "type": "event"
    }, 
    {
        "anonymous": false, 
        "inputs": [
            {
                "indexed": true, 
                "internalType": "address", 
                "name": "provider", 
                "type": "address"
            }, 
            {
                "indexed": true, 
                "internalType": "uint256", 
                "name": "iotx_amount", 
                "type": "uint256"
            }, 
            {
                "indexed": true, 
                "internalType": "uint256", 
                "name": "token_amount", 
                "type": "uint256"
            }
        ], 
        "name": "RemoveLiquidity", 
        "type": "event"
    }, 
    {
        "anonymous": false, 
        "inputs": [
            {
                "indexed": true, 
                "internalType": "address", 
                "name": "buyer", 
                "type": "address"
            }, 
            {
                "indexed": true, 
                "internalType": "uint256", 
                "name": "iotx_sold", 
                "type": "uint256"
            }, 
            {
                "indexed": true, 
                "internalType": "uint256", 
                "name": "tokens_bought", 
                "type": "uint256"
            }
        ], 
        "name": "TokenPurchase", 
        "type": "event"
    }, 
    {
        "anonymous": false, 
        "inputs": [
            {
                "indexed": true, 
                "internalType": "address", 
                "name": "from", 
                "type": "address"
            }, 
            {
                "indexed": true, 
                "internalType": "address", 
                "name": "to", 
                "type": "address"
            }, 
            {
                "indexed": false, 
                "internalType": "uint256", 
                "name": "value", 
                "type": "uint256"
            }
        ], 
        "name": "Transfer", 
        "type": "event"
    }, 
    {
        "payable": true, 
        "stateMutability": "payable", 
        "type": "fallback"
    }, 
    {
        "constant": false, 
        "inputs": [
            {
                "internalType": "uint256", 
                "name": "min_liquidity", 
                "type": "uint256"
            }, 
            {
                "internalType": "uint256", 
                "name": "max_tokens", 
                "type": "uint256"
            }, 
            {
                "internalType": "uint256", 
                "name": "deadline", 
                "type": "uint256"
            }
        ], 
        "name": "addLiquidity", 
        "outputs": [
            {
                "internalType": "uint256", 
                "name": "", 
                "type": "uint256"
            }
        ], 
        "payable": true, 
        "stateMutability": "payable", 
        "type": "function"
    }, 
    {
        "constant": true, 
        "inputs": [
            {
                "internalType": "address", 
                "name": "owner", 
                "type": "address"
            }, 
            {
                "internalType": "address", 
                "name": "spender", 
                "type": "address"
            }
        ], 
        "name": "allowance", 
        "outputs": [
            {
                "internalType": "uint256", 
                "name": "", 
                "type": "uint256"
            }
        ], 
        "payable": false, 
        "stateMutability": "view", 
        "type": "function"
    }, 
    {
        "constant": false, 
        "inputs": [
            {
                "internalType": "address", 
                "name": "spender", 
                "type": "address"
            }, 
            {
                "internalType": "uint256", 
                "name": "value", 
                "type": "uint256"
            }
        ], 
        "name": "approve", 
        "outputs": [
            {
                "internalType": "bool", 
                "name": "", 
                "type": "bool"
            }
        ], 
        "payable": false, 
        "stateMutability": "nonpayable", 
        "type": "function"
    }, 
    {
        "constant": true, 
        "inputs": [
            {
                "internalType": "address", 
                "name": "owner", 
                "type": "address"
            }
        ], 
        "name": "balanceOf", 
        "outputs": [
            {
                "internalType": "uint256", 
                "name": "", 
                "type": "uint256"
            }
        ], 
        "payable": false, 
        "stateMutability": "view", 
        "type": "function"
    }, 
    {
        "constant": true, 
        "inputs": [ ], 
        "name": "decimals", 
        "outputs": [
            {
                "internalType": "uint256", 
                "name": "", 
                "type": "uint256"
            }
        ], 
        "payable": false, 
        "stateMutability": "view", 
        "type": "function"
    }, 
    {
        "constant": false, 
        "inputs": [
            {
                "internalType": "address", 
                "name": "spender", 
                "type": "address"
            }, 
            {
                "internalType": "uint256", 
                "name": "subtractedValue", 
                "type": "uint256"
            }
        ], 
        "name": "decreaseAllowance", 
        "outputs": [
            {
                "internalType": "bool", 
                "name": "", 
                "type": "bool"
            }
        ], 
        "payable": false, 
        "stateMutability": "nonpayable", 
        "type": "function"
    }, 
    {
        "constant": true, 
        "inputs": [ ], 
        "name": "factoryAddress", 
        "outputs": [
            {
                "internalType": "address", 
                "name": "", 
                "type": "address"
            }
        ], 
        "payable": false, 
        "stateMutability": "view", 
        "type": "function"
    }, 
    {
        "constant": true, 
        "inputs": [
            {
                "internalType": "uint256", 
                "name": "input_amount", 
                "type": "uint256"
            }, 
            {
                "internalType": "uint256", 
                "name": "input_reserve", 
                "type": "uint256"
            }, 
            {
                "internalType": "uint256", 
                "name": "output_reserve", 
                "type": "uint256"
            }
        ], 
        "name": "getInputPrice", 
        "outputs": [
            {
                "internalType": "uint256", 
                "name": "", 
                "type": "uint256"
            }
        ], 
        "payable": false, 
        "stateMutability": "pure", 
        "type": "function"
    }, 
    {
        "constant": true, 
        "inputs": [
            {
                "internalType": "uint256", 
                "name": "iotx_sold", 
                "type": "uint256"
            }
        ], 
        "name": "getIotxToTokenInputPrice", 
        "outputs": [
            {
                "internalType": "uint256", 
                "name": "", 
                "type": "uint256"
            }
        ], 
        "payable": false, 
        "stateMutability": "view", 
        "type": "function"
    }, 
    {
        "constant": true, 
        "inputs": [
            {
                "internalType": "uint256", 
                "name": "tokens_bought", 
                "type": "uint256"
            }
        ], 
        "name": "getIotxToTokenOutputPrice", 
        "outputs": [
            {
                "internalType": "uint256", 
                "name": "", 
                "type": "uint256"
            }
        ], 
        "payable": false, 
        "stateMutability": "view", 
        "type": "function"
    }, 
    {
        "constant": true, 
        "inputs": [
            {
                "internalType": "uint256", 
                "name": "output_amount", 
                "type": "uint256"
            }, 
            {
                "internalType": "uint256", 
                "name": "input_reserve", 
                "type": "uint256"
            }, 
            {
                "internalType": "uint256", 
                "name": "output_reserve", 
                "type": "uint256"
            }
        ], 
        "name": "getOutputPrice", 
        "outputs": [
            {
                "internalType": "uint256", 
                "name": "", 
                "type": "uint256"
            }
        ], 
        "payable": false, 
        "stateMutability": "pure", 
        "type": "function"
    }, 
    {
        "constant": true, 
        "inputs": [
            {
                "internalType": "uint256", 
                "name": "tokens_sold", 
                "type": "uint256"
            }
        ], 
        "name": "getTokenToIotxInputPrice", 
        "outputs": [
            {
                "internalType": "uint256", 
                "name": "", 
                "type": "uint256"
            }
        ], 
        "payable": false, 
        "stateMutability": "view", 
        "type": "function"
    }, 
    {
        "constant": true, 
        "inputs": [
            {
                "internalType": "uint256", 
                "name": "iotx_bought", 
                "type": "uint256"
            }
        ], 
        "name": "getTokenToIotxOutputPrice", 
        "outputs": [
            {
                "internalType": "uint256", 
                "name": "", 
                "type": "uint256"
            }
        ], 
        "payable": false, 
        "stateMutability": "view", 
        "type": "function"
    }, 
    {
        "constant": false, 
        "inputs": [
            {
                "internalType": "address", 
                "name": "spender", 
                "type": "address"
            }, 
            {
                "internalType": "uint256", 
                "name": "addedValue", 
                "type": "uint256"
            }
        ], 
        "name": "increaseAllowance", 
        "outputs": [
            {
                "internalType": "bool", 
                "name": "", 
                "type": "bool"
            }
        ], 
        "payable": false, 
        "stateMutability": "nonpayable", 
        "type": "function"
    }, 
    {
        "constant": false, 
        "inputs": [
            {
                "internalType": "uint256", 
                "name": "min_tokens", 
                "type": "uint256"
            }, 
            {
                "internalType": "uint256", 
                "name": "deadline", 
                "type": "uint256"
            }
        ], 
        "name": "iotxToTokenSwapInput", 
        "outputs": [
            {
                "internalType": "uint256", 
                "name": "", 
                "type": "uint256"
            }
        ], 
        "payable": true, 
        "stateMutability": "payable", 
        "type": "function"
    }, 
    {
        "constant": false, 
        "inputs": [
            {
                "internalType": "uint256", 
                "name": "tokens_bought", 
                "type": "uint256"
            }, 
            {
                "internalType": "uint256", 
                "name": "deadline", 
                "type": "uint256"
            }
        ], 
        "name": "iotxToTokenSwapOutput", 
        "outputs": [
            {
                "internalType": "uint256", 
                "name": "", 
                "type": "uint256"
            }
        ], 
        "payable": true, 
        "stateMutability": "payable", 
        "type": "function"
    }, 
    {
        "constant": false, 
        "inputs": [
            {
                "internalType": "uint256", 
                "name": "min_tokens", 
                "type": "uint256"
            }, 
            {
                "internalType": "uint256", 
                "name": "deadline", 
                "type": "uint256"
            }, 
            {
                "internalType": "address", 
                "name": "recipient", 
                "type": "address"
            }
        ], 
        "name": "iotxToTokenTransferInput", 
        "outputs": [
            {
                "internalType": "uint256", 
                "name": "", 
                "type": "uint256"
            }
        ], 
        "payable": true, 
        "stateMutability": "payable", 
        "type": "function"
    }, 
    {
        "constant": false, 
        "inputs": [
            {
                "internalType": "uint256", 
                "name": "tokens_bought", 
                "type": "uint256"
            }, 
            {
                "internalType": "uint256", 
                "name": "deadline", 
                "type": "uint256"
            }, 
            {
                "internalType": "address", 
                "name": "recipient", 
                "type": "address"
            }
        ], 
        "name": "iotxToTokenTransferOutput", 
        "outputs": [
            {
                "internalType": "uint256", 
                "name": "", 
                "type": "uint256"
            }
        ], 
        "payable": true, 
        "stateMutability": "payable", 
        "type": "function"
    }, 
    {
        "constant": true, 
        "inputs": [ ], 
        "name": "name", 
        "outputs": [
            {
                "internalType": "bytes32", 
                "name": "", 
                "type": "bytes32"
            }
        ], 
        "payable": false, 
        "stateMutability": "view", 
        "type": "function"
    }, 
    {
        "constant": false, 
        "inputs": [
            {
                "internalType": "uint256", 
                "name": "amount", 
                "type": "uint256"
            }, 
            {
                "internalType": "uint256", 
                "name": "min_iotx", 
                "type": "uint256"
            }, 
            {
                "internalType": "uint256", 
                "name": "min_tokens", 
                "type": "uint256"
            }, 
            {
                "internalType": "uint256", 
                "name": "deadline", 
                "type": "uint256"
            }
        ], 
        "name": "removeLiquidity", 
        "outputs": [
            {
                "internalType": "uint256", 
                "name": "", 
                "type": "uint256"
            }, 
            {
                "internalType": "uint256", 
                "name": "", 
                "type": "uint256"
            }
        ], 
        "payable": false, 
        "stateMutability": "nonpayable", 
        "type": "function"
    }, 
    {
        "constant": false, 
        "inputs": [
            {
                "internalType": "address", 
                "name": "token_addr", 
                "type": "address"
            }
        ], 
        "name": "setup", 
        "outputs": [ ], 
        "payable": false, 
        "stateMutability": "nonpayable", 
        "type": "function"
    }, 
    {
        "constant": true, 
        "inputs": [ ], 
        "name": "symbol", 
        "outputs": [
            {
                "internalType": "bytes32", 
                "name": "", 
                "type": "bytes32"
            }
        ], 
        "payable": false, 
        "stateMutability": "view", 
        "type": "function"
    }, 
    {
        "constant": true, 
        "inputs": [ ], 
        "name": "tokenAddress", 
        "outputs": [
            {
                "internalType": "address", 
                "name": "", 
                "type": "address"
            }
        ], 
        "payable": false, 
        "stateMutability": "view", 
        "type": "function"
    }, 
    {
        "constant": false, 
        "inputs": [
            {
                "internalType": "uint256", 
                "name": "tokens_sold", 
                "type": "uint256"
            }, 
            {
                "internalType": "uint256", 
                "name": "min_tokens_bought", 
                "type": "uint256"
            }, 
            {
                "internalType": "uint256", 
                "name": "min_iotx_bought", 
                "type": "uint256"
            }, 
            {
                "internalType": "uint256", 
                "name": "deadline", 
                "type": "uint256"
            }, 
            {
                "internalType": "address payable", 
                "name": "exchange_addr", 
                "type": "address"
            }
        ], 
        "name": "tokenToExchangeSwapInput", 
        "outputs": [
            {
                "internalType": "uint256", 
                "name": "", 
                "type": "uint256"
            }
        ], 
        "payable": false, 
        "stateMutability": "nonpayable", 
        "type": "function"
    }, 
    {
        "constant": false, 
        "inputs": [
            {
                "internalType": "uint256", 
                "name": "tokens_bought", 
                "type": "uint256"
            }, 
            {
                "internalType": "uint256", 
                "name": "max_tokens_sold", 
                "type": "uint256"
            }, 
            {
                "internalType": "uint256", 
                "name": "max_iotx_sold", 
                "type": "uint256"
            }, 
            {
                "internalType": "uint256", 
                "name": "deadline", 
                "type": "uint256"
            }, 
            {
                "internalType": "address payable", 
                "name": "exchange_addr", 
                "type": "address"
            }
        ], 
        "name": "tokenToExchangeSwapOutput", 
        "outputs": [
            {
                "internalType": "uint256", 
                "name": "", 
                "type": "uint256"
            }
        ], 
        "payable": false, 
        "stateMutability": "nonpayable", 
        "type": "function"
    }, 
    {
        "constant": false, 
        "inputs": [
            {
                "internalType": "uint256", 
                "name": "tokens_sold", 
                "type": "uint256"
            }, 
            {
                "internalType": "uint256", 
                "name": "min_tokens_bought", 
                "type": "uint256"
            }, 
            {
                "internalType": "uint256", 
                "name": "min_iotx_bought", 
                "type": "uint256"
            }, 
            {
                "internalType": "uint256", 
                "name": "deadline", 
                "type": "uint256"
            }, 
            {
                "internalType": "address", 
                "name": "recipient", 
                "type": "address"
            }, 
            {
                "internalType": "address payable", 
                "name": "exchange_addr", 
                "type": "address"
            }
        ], 
        "name": "tokenToExchangeTransferInput", 
        "outputs": [
            {
                "internalType": "uint256", 
                "name": "", 
                "type": "uint256"
            }
        ], 
        "payable": false, 
        "stateMutability": "nonpayable", 
        "type": "function"
    }, 
    {
        "constant": false, 
        "inputs": [
            {
                "internalType": "uint256", 
                "name": "tokens_bought", 
                "type": "uint256"
            }, 
            {
                "internalType": "uint256", 
                "name": "max_tokens_sold", 
                "type": "uint256"
            }, 
            {
                "internalType": "uint256", 
                "name": "max_iotx_sold", 
                "type": "uint256"
            }, 
            {
                "internalType": "uint256", 
                "name": "deadline", 
                "type": "uint256"
            }, 
            {
                "internalType": "address", 
                "name": "recipient", 
                "type": "address"
            }, 
            {
                "internalType": "address payable", 
                "name": "exchange_addr", 
                "type": "address"
            }
        ], 
        "name": "tokenToExchangeTransferOutput", 
        "outputs": [
            {
                "internalType": "uint256", 
                "name": "", 
                "type": "uint256"
            }
        ], 
        "payable": false, 
        "stateMutability": "nonpayable", 
        "type": "function"
    }, 
    {
        "constant": false, 
        "inputs": [
            {
                "internalType": "uint256", 
                "name": "tokens_sold", 
                "type": "uint256"
            }, 
            {
                "internalType": "uint256", 
                "name": "min_iotx", 
                "type": "uint256"
            }, 
            {
                "internalType": "uint256", 
                "name": "deadline", 
                "type": "uint256"
            }
        ], 
        "name": "tokenToIotxSwapInput", 
        "outputs": [
            {
                "internalType": "uint256", 
                "name": "", 
                "type": "uint256"
            }
        ], 
        "payable": false, 
        "stateMutability": "nonpayable", 
        "type": "function"
    }, 
    {
        "constant": false, 
        "inputs": [
            {
                "internalType": "uint256", 
                "name": "iotx_bought", 
                "type": "uint256"
            }, 
            {
                "internalType": "uint256", 
                "name": "max_tokens", 
                "type": "uint256"
            }, 
            {
                "internalType": "uint256", 
                "name": "deadline", 
                "type": "uint256"
            }
        ], 
        "name": "tokenToIotxSwapOutput", 
        "outputs": [
            {
                "internalType": "uint256", 
                "name": "", 
                "type": "uint256"
            }
        ], 
        "payable": false, 
        "stateMutability": "nonpayable", 
        "type": "function"
    }, 
    {
        "constant": false, 
        "inputs": [
            {
                "internalType": "uint256", 
                "name": "tokens_sold", 
                "type": "uint256"
            }, 
            {
                "internalType": "uint256", 
                "name": "min_iotx", 
                "type": "uint256"
            }, 
            {
                "internalType": "uint256", 
                "name": "deadline", 
                "type": "uint256"
            }, 
            {
                "internalType": "address payable", 
                "name": "recipient", 
                "type": "address"
            }
        ], 
        "name": "tokenToIotxTransferInput", 
        "outputs": [
            {
                "internalType": "uint256", 
                "name": "", 
                "type": "uint256"
            }
        ], 
        "payable": false, 
        "stateMutability": "nonpayable", 
        "type": "function"
    }, 
    {
        "constant": false, 
        "inputs": [
            {
                "internalType": "uint256", 
                "name": "iotx_bought", 
                "type": "uint256"
            }, 
            {
                "internalType": "uint256", 
                "name": "max_tokens", 
                "type": "uint256"
            }, 
            {
                "internalType": "uint256", 
                "name": "deadline", 
                "type": "uint256"
            }, 
            {
                "internalType": "address payable", 
                "name": "recipient", 
                "type": "address"
            }
        ], 
        "name": "tokenToIotxTransferOutput", 
        "outputs": [
            {
                "internalType": "uint256", 
                "name": "", 
                "type": "uint256"
            }
        ], 
        "payable": false, 
        "stateMutability": "nonpayable", 
        "type": "function"
    }, 
    {
        "constant": false, 
        "inputs": [
            {
                "internalType": "uint256", 
                "name": "tokens_sold", 
                "type": "uint256"
            }, 
            {
                "internalType": "uint256", 
                "name": "min_tokens_bought", 
                "type": "uint256"
            }, 
            {
                "internalType": "uint256", 
                "name": "min_iotx_bought", 
                "type": "uint256"
            }, 
            {
                "internalType": "uint256", 
                "name": "deadline", 
                "type": "uint256"
            }, 
            {
                "internalType": "address", 
                "name": "token_addr", 
                "type": "address"
            }
        ], 
        "name": "tokenToTokenSwapInput", 
        "outputs": [
            {
                "internalType": "uint256", 
                "name": "", 
                "type": "uint256"
            }
        ], 
        "payable": false, 
        "stateMutability": "nonpayable", 
        "type": "function"
    }, 
    {
        "constant": false, 
        "inputs": [
            {
                "internalType": "uint256", 
                "name": "tokens_bought", 
                "type": "uint256"
            }, 
            {
                "internalType": "uint256", 
                "name": "max_tokens_sold", 
                "type": "uint256"
            }, 
            {
                "internalType": "uint256", 
                "name": "max_iotx_sold", 
                "type": "uint256"
            }, 
            {
                "internalType": "uint256", 
                "name": "deadline", 
                "type": "uint256"
            }, 
            {
                "internalType": "address", 
                "name": "token_addr", 
                "type": "address"
            }
        ], 
        "name": "tokenToTokenSwapOutput", 
        "outputs": [
            {
                "internalType": "uint256", 
                "name": "", 
                "type": "uint256"
            }
        ], 
        "payable": false, 
        "stateMutability": "nonpayable", 
        "type": "function"
    }, 
    {
        "constant": false, 
        "inputs": [
            {
                "internalType": "uint256", 
                "name": "tokens_sold", 
                "type": "uint256"
            }, 
            {
                "internalType": "uint256", 
                "name": "min_tokens_bought", 
                "type": "uint256"
            }, 
            {
                "internalType": "uint256", 
                "name": "min_iotx_bought", 
                "type": "uint256"
            }, 
            {
                "internalType": "uint256", 
                "name": "deadline", 
                "type": "uint256"
            }, 
            {
                "internalType": "address", 
                "name": "recipient", 
                "type": "address"
            }, 
            {
                "internalType": "address", 
                "name": "token_addr", 
                "type": "address"
            }
        ], 
        "name": "tokenToTokenTransferInput", 
        "outputs": [
            {
                "internalType": "uint256", 
                "name": "", 
                "type": "uint256"
            }
        ], 
        "payable": false, 
        "stateMutability": "nonpayable", 
        "type": "function"
    }, 
    {
        "constant": false, 
        "inputs": [
            {
                "internalType": "uint256", 
                "name": "tokens_bought", 
                "type": "uint256"
            }, 
            {
                "internalType": "uint256", 
                "name": "max_tokens_sold", 
                "type": "uint256"
            }, 
            {
                "internalType": "uint256", 
                "name": "max_iotx_sold", 
                "type": "uint256"
            }, 
            {
                "internalType": "uint256", 
                "name": "deadline", 
                "type": "uint256"
            }, 
            {
                "internalType": "address", 
                "name": "recipient", 
                "type": "address"
            }, 
            {
                "internalType": "address", 
                "name": "token_addr", 
                "type": "address"
            }
        ], 
        "name": "tokenToTokenTransferOutput", 
        "outputs": [
            {
                "internalType": "uint256", 
                "name": "", 
                "type": "uint256"
            }
        ], 
        "payable": false, 
        "stateMutability": "nonpayable", 
        "type": "function"
    }, 
    {
        "constant": true, 
        "inputs": [ ], 
        "name": "totalSupply", 
        "outputs": [
            {
                "internalType": "uint256", 
                "name": "", 
                "type": "uint256"
            }
        ], 
        "payable": false, 
        "stateMutability": "view", 
        "type": "function"
    }, 
    {
        "constant": false, 
        "inputs": [
            {
                "internalType": "address", 
                "name": "to", 
                "type": "address"
            }, 
            {
                "internalType": "uint256", 
                "name": "value", 
                "type": "uint256"
            }
        ], 
        "name": "transfer", 
        "outputs": [
            {
                "internalType": "bool", 
                "name": "", 
                "type": "bool"
            }
        ], 
        "payable": false, 
        "stateMutability": "nonpayable", 
        "type": "function"
    }, 
    {
        "constant": false, 
        "inputs": [
            {
                "internalType": "address", 
                "name": "from", 
                "type": "address"
            }, 
            {
                "internalType": "address", 
                "name": "to", 
                "type": "address"
            }, 
            {
                "internalType": "uint256", 
                "name": "value", 
                "type": "uint256"
            }
        ], 
        "name": "transferFrom", 
        "outputs": [
            {
                "internalType": "bool", 
                "name": "", 
                "type": "bool"
            }
        ], 
        "payable": false, 
        "stateMutability": "nonpayable", 
        "type": "function"
    }
]"""

    const val FactoryABI = """[
    {
        "anonymous": false, 
        "inputs": [
            {
                "indexed": true, 
                "internalType": "address", 
                "name": "token", 
                "type": "address"
            }, 
            {
                "indexed": true, 
                "internalType": "address", 
                "name": "exchange", 
                "type": "address"
            }
        ], 
        "name": "NewExchange", 
        "type": "event"
    }, 
    {
        "constant": false, 
        "inputs": [
            {
                "internalType": "address", 
                "name": "token", 
                "type": "address"
            }
        ], 
        "name": "createExchange", 
        "outputs": [
            {
                "internalType": "address", 
                "name": "", 
                "type": "address"
            }
        ], 
        "payable": false, 
        "stateMutability": "nonpayable", 
        "type": "function"
    }, 
    {
        "constant": true, 
        "inputs": [
            {
                "internalType": "address", 
                "name": "token", 
                "type": "address"
            }
        ], 
        "name": "getExchange", 
        "outputs": [
            {
                "internalType": "address", 
                "name": "", 
                "type": "address"
            }
        ], 
        "payable": false, 
        "stateMutability": "view", 
        "type": "function"
    }, 
    {
        "constant": true, 
        "inputs": [
            {
                "internalType": "address", 
                "name": "exchange", 
                "type": "address"
            }
        ], 
        "name": "getToken", 
        "outputs": [
            {
                "internalType": "address", 
                "name": "", 
                "type": "address"
            }
        ], 
        "payable": false, 
        "stateMutability": "view", 
        "type": "function"
    }, 
    {
        "constant": true, 
        "inputs": [
            {
                "internalType": "uint256", 
                "name": "token_id", 
                "type": "uint256"
            }
        ], 
        "name": "getTokenWithId", 
        "outputs": [
            {
                "internalType": "address", 
                "name": "", 
                "type": "address"
            }
        ], 
        "payable": false, 
        "stateMutability": "view", 
        "type": "function"
    }, 
    {
        "constant": true, 
        "inputs": [ ], 
        "name": "tokenCount", 
        "outputs": [
            {
                "internalType": "uint256", 
                "name": "", 
                "type": "uint256"
            }
        ], 
        "payable": false, 
        "stateMutability": "view", 
        "type": "function"
    }
]"""

    const val IOTUBEABI = """[
    {
        "inputs": [
            {
                "internalType": "address", 
                "name": "_tokenList", 
                "type": "address"
            }, 
            {
                "internalType": "address", 
                "name": "_safe", 
                "type": "address"
            }
        ], 
        "payable": false, 
        "stateMutability": "nonpayable", 
        "type": "constructor"
    }, 
    {
        "anonymous": false, 
        "inputs": [
            {
                "indexed": true, 
                "internalType": "address", 
                "name": "previousOwner", 
                "type": "address"
            }, 
            {
                "indexed": true, 
                "internalType": "address", 
                "name": "newOwner", 
                "type": "address"
            }
        ], 
        "name": "OwnershipTransferred", 
        "type": "event"
    }, 
    {
        "anonymous": false, 
        "inputs": [ ], 
        "name": "Pause", 
        "type": "event"
    }, 
    {
        "anonymous": false, 
        "inputs": [
            {
                "indexed": true, 
                "internalType": "address", 
                "name": "customer", 
                "type": "address"
            }, 
            {
                "indexed": true, 
                "internalType": "address", 
                "name": "receiver", 
                "type": "address"
            }, 
            {
                "indexed": true, 
                "internalType": "address", 
                "name": "token", 
                "type": "address"
            }, 
            {
                "indexed": false, 
                "internalType": "uint256", 
                "name": "amount", 
                "type": "uint256"
            }, 
            {
                "indexed": false, 
                "internalType": "uint256", 
                "name": "fee", 
                "type": "uint256"
            }
        ], 
        "name": "Receipt", 
        "type": "event"
    }, 
    {
        "anonymous": false, 
        "inputs": [ ], 
        "name": "Unpause", 
        "type": "event"
    }, 
    {
        "payable": false, 
        "stateMutability": "nonpayable", 
        "type": "fallback"
    }, 
    {
        "constant": true, 
        "inputs": [
            {
                "internalType": "address", 
                "name": "_token", 
                "type": "address"
            }
        ], 
        "name": "count", 
        "outputs": [
            {
                "internalType": "uint256", 
                "name": "", 
                "type": "uint256"
            }
        ], 
        "payable": false, 
        "stateMutability": "view", 
        "type": "function"
    }, 
    {
        "constant": false, 
        "inputs": [
            {
                "internalType": "address", 
                "name": "_token", 
                "type": "address"
            }, 
            {
                "internalType": "uint256", 
                "name": "_amount", 
                "type": "uint256"
            }
        ], 
        "name": "deposit", 
        "outputs": [ ], 
        "payable": true, 
        "stateMutability": "payable", 
        "type": "function"
    }, 
    {
        "constant": true, 
        "inputs": [ ], 
        "name": "depositFee", 
        "outputs": [
            {
                "internalType": "uint256", 
                "name": "", 
                "type": "uint256"
            }
        ], 
        "payable": false, 
        "stateMutability": "view", 
        "type": "function"
    }, 
    {
        "constant": false, 
        "inputs": [
            {
                "internalType": "address", 
                "name": "_token", 
                "type": "address"
            }, 
            {
                "internalType": "address", 
                "name": "_to", 
                "type": "address"
            }, 
            {
                "internalType": "uint256", 
                "name": "_amount", 
                "type": "uint256"
            }
        ], 
        "name": "depositTo", 
        "outputs": [ ], 
        "payable": true, 
        "stateMutability": "payable", 
        "type": "function"
    }, 
    {
        "constant": true, 
        "inputs": [
            {
                "internalType": "address", 
                "name": "_token", 
                "type": "address"
            }, 
            {
                "internalType": "uint256", 
                "name": "_offset", 
                "type": "uint256"
            }, 
            {
                "internalType": "uint256", 
                "name": "_limit", 
                "type": "uint256"
            }
        ], 
        "name": "getRecords", 
        "outputs": [
            {
                "internalType": "address[]", 
                "name": "_customers", 
                "type": "address[]"
            }, 
            {
                "internalType": "address[]", 
                "name": "_receivers", 
                "type": "address[]"
            }, 
            {
                "internalType": "uint256[]", 
                "name": "_amounts", 
                "type": "uint256[]"
            }, 
            {
                "internalType": "uint256[]", 
                "name": "_fees", 
                "type": "uint256[]"
            }
        ], 
        "payable": false, 
        "stateMutability": "view", 
        "type": "function"
    }, 
    {
        "constant": true, 
        "inputs": [ ], 
        "name": "owner", 
        "outputs": [
            {
                "internalType": "address", 
                "name": "", 
                "type": "address"
            }
        ], 
        "payable": false, 
        "stateMutability": "view", 
        "type": "function"
    }, 
    {
        "constant": false, 
        "inputs": [ ], 
        "name": "pause", 
        "outputs": [ ], 
        "payable": false, 
        "stateMutability": "nonpayable", 
        "type": "function"
    }, 
    {
        "constant": true, 
        "inputs": [ ], 
        "name": "paused", 
        "outputs": [
            {
                "internalType": "bool", 
                "name": "", 
                "type": "bool"
            }
        ], 
        "payable": false, 
        "stateMutability": "view", 
        "type": "function"
    }, 
    {
        "constant": true, 
        "inputs": [ ], 
        "name": "safe", 
        "outputs": [
            {
                "internalType": "address", 
                "name": "", 
                "type": "address"
            }
        ], 
        "payable": false, 
        "stateMutability": "view", 
        "type": "function"
    }, 
    {
        "constant": false, 
        "inputs": [
            {
                "internalType": "uint256", 
                "name": "_fee", 
                "type": "uint256"
            }
        ], 
        "name": "setDepositFee", 
        "outputs": [ ], 
        "payable": false, 
        "stateMutability": "nonpayable", 
        "type": "function"
    }, 
    {
        "constant": true, 
        "inputs": [ ], 
        "name": "tokenList", 
        "outputs": [
            {
                "internalType": "contract TokenList", 
                "name": "", 
                "type": "address"
            }
        ], 
        "payable": false, 
        "stateMutability": "view", 
        "type": "function"
    }, 
    {
        "constant": false, 
        "inputs": [
            {
                "internalType": "address", 
                "name": "newOwner", 
                "type": "address"
            }
        ], 
        "name": "transferOwnership", 
        "outputs": [ ], 
        "payable": false, 
        "stateMutability": "nonpayable", 
        "type": "function"
    }, 
    {
        "constant": false, 
        "inputs": [ ], 
        "name": "unpause", 
        "outputs": [ ], 
        "payable": false, 
        "stateMutability": "nonpayable", 
        "type": "function"
    }, 
    {
        "constant": false, 
        "inputs": [
            {
                "internalType": "address", 
                "name": "_token", 
                "type": "address"
            }
        ], 
        "name": "withdrawToken", 
        "outputs": [ ], 
        "payable": false, 
        "stateMutability": "nonpayable", 
        "type": "function"
    }
]"""

    const val XRC20ABI = """[
    {
        "constant": true, 
        "inputs": [ ], 
        "name": "name", 
        "outputs": [
            {
                "name": "", 
                "type": "string"
            }
        ], 
        "payable": false, 
        "stateMutability": "view", 
        "type": "function"
    }, 
    {
        "constant": false, 
        "inputs": [
            {
                "name": "_spender", 
                "type": "address"
            }, 
            {
                "name": "_value", 
                "type": "uint256"
            }
        ], 
        "name": "approve", 
        "outputs": [
            {
                "name": "", 
                "type": "bool"
            }
        ], 
        "payable": false, 
        "stateMutability": "nonpayable", 
        "type": "function"
    }, 
    {
        "constant": true, 
        "inputs": [ ], 
        "name": "totalSupply", 
        "outputs": [
            {
                "name": "", 
                "type": "uint256"
            }
        ], 
        "payable": false, 
        "stateMutability": "view", 
        "type": "function"
    }, 
    {
        "constant": false, 
        "inputs": [
            {
                "name": "_from", 
                "type": "address"
            }, 
            {
                "name": "_to", 
                "type": "address"
            }, 
            {
                "name": "_value", 
                "type": "uint256"
            }
        ], 
        "name": "transferFrom", 
        "outputs": [
            {
                "name": "", 
                "type": "bool"
            }
        ], 
        "payable": false, 
        "stateMutability": "nonpayable", 
        "type": "function"
    }, 
    {
        "constant": true, 
        "inputs": [ ], 
        "name": "decimals", 
        "outputs": [
            {
                "name": "", 
                "type": "uint8"
            }
        ], 
        "payable": false, 
        "stateMutability": "view", 
        "type": "function"
    }, 
    {
        "constant": true, 
        "inputs": [
            {
                "name": "_owner", 
                "type": "address"
            }
        ], 
        "name": "balanceOf", 
        "outputs": [
            {
                "name": "balance", 
                "type": "uint256"
            }
        ], 
        "payable": false, 
        "stateMutability": "view", 
        "type": "function"
    }, 
    {
        "constant": true, 
        "inputs": [ ], 
        "name": "symbol", 
        "outputs": [
            {
                "name": "", 
                "type": "string"
            }
        ], 
        "payable": false, 
        "stateMutability": "view", 
        "type": "function"
    }, 
    {
        "constant": false, 
        "inputs": [
            {
                "name": "_to", 
                "type": "address"
            }, 
            {
                "name": "_value", 
                "type": "uint256"
            }
        ], 
        "name": "transfer", 
        "outputs": [
            {
                "name": "", 
                "type": "bool"
            }
        ], 
        "payable": false, 
        "stateMutability": "nonpayable", 
        "type": "function"
    }, 
    {
        "constant": true, 
        "inputs": [
            {
                "name": "_owner", 
                "type": "address"
            }, 
            {
                "name": "_spender", 
                "type": "address"
            }
        ], 
        "name": "allowance", 
        "outputs": [
            {
                "name": "", 
                "type": "uint256"
            }
        ], 
        "payable": false, 
        "stateMutability": "view", 
        "type": "function"
    }, 
    {
        "payable": true, 
        "stateMutability": "payable", 
        "type": "fallback"
    }, 
    {
        "anonymous": false, 
        "inputs": [
            {
                "indexed": true, 
                "name": "owner", 
                "type": "address"
            }, 
            {
                "indexed": true, 
                "name": "spender", 
                "type": "address"
            }, 
            {
                "indexed": false, 
                "name": "value", 
                "type": "uint256"
            }
        ], 
        "name": "Approval", 
        "type": "event"
    }, 
    {
        "anonymous": false, 
        "inputs": [
            {
                "indexed": true, 
                "name": "from", 
                "type": "address"
            }, 
            {
                "indexed": true, 
                "name": "to", 
                "type": "address"
            }, 
            {
                "indexed": false, 
                "name": "value", 
                "type": "uint256"
            }
        ], 
        "name": "Transfer", 
        "type": "event"
    }
]"""
}