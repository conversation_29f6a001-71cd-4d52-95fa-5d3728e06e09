package io.iotex.iopay.contract

import com.github.iotexproject.antenna.account.Account
import com.github.iotexproject.antenna.rpc.RPCMethod
import java.math.BigInteger

open class ERC20(address: String, provider: RPCMethod) : Token(address, provider, Abis.VITA_ABI), ERC20Interface {
    override fun name(callerAddress: String): String {
        val result = this.contract.read(callerAddress, "name")
        if (result.size > 0) {
            return result[0] as String
        }
        return ""
    }

    override fun symbol(callerAddress: String): String {
        val result = this.contract.read(callerAddress, "symbol")
        if (result.size > 0) {
            return result[0] as String
        }
        return ""
    }

    override fun decimals(callerAddress: String): Long {
        val result = this.contract.read(callerAddress, "decimals")
        if (result.size > 0) {
            val bgValue = result[0] as BigInteger
            return bgValue.toLong()
        }
        return 0
    }

    override fun totalSupply(callerAddress: String): BigInteger {
        val result = this.contract.read(callerAddress, "totalSupply")
        if (result.size > 0) {
            return result[0] as BigInteger
        }
        return BigInteger.ZERO
    }

    override fun balanceOf(owner: String, callerAddress: String): BigInteger {
        val result = this.contract.read(callerAddress, "balanceOf")
        if (result.size > 0) {
            return result[0] as BigInteger
        }
        return BigInteger.ZERO
    }

    override fun transfer(
        to: String,
        value: String,
        account: Account,
        gasPrice: String,
        gasLimit: Long,
        cb: (String?) -> Unit
    ) {
    }

    override fun allowance(
        owner: String,
        spender: String,
        account: Account,
        gasPrice: String?,
        gasLimit: Long?
    ): String {
        return this.contract.execute(null, gasLimit, gasPrice, account, "allowance", "0", owner, spender)
    }

    override fun approve(
        spender: String,
        value: BigInteger,
        account: Account,
        gasPrice: String?,
        gasLimit: Long?
    ): String {
        return this.contract.execute(null, gasLimit, gasPrice, account, "approve", "0", spender, value)
    }

    override fun transferFrom(
        from: String,
        to: String,
        value: BigInteger,
        account: Account,
        gasPrice: String?,
        gasLimit: Long?
    ): String {
        return this.contract.execute(null, gasLimit, gasPrice, account, "transferFrom", "0", from, to, value)
    }
}
