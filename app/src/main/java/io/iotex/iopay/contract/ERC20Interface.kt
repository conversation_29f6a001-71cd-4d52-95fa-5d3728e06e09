package io.iotex.iopay.contract

import com.github.iotexproject.antenna.account.Account
import java.math.BigInteger

/**
 * erc20 interface.
 *
 * <AUTHOR>
 */
interface ERC20Interface {
    fun name(callerAddress: String): String

    fun symbol(callerAddress: String): String

    fun decimals(callerAddress: String): Long

    fun totalSupply(callerAddress: String): BigInteger

    fun balanceOf(owner: String, callerAddress: String): BigInteger

    fun transfer(to: String, value: String, account: Account, gasPrice: String, gasLimit: Long, cb: (String?) -> Unit)

    fun allowance(owner: String, spender: String, account: Account, gasPrice: String?, gasLimit: Long?): String

    fun approve(spender: String, value: BigInteger, account: Account, gasPrice: String?, gasLimit: Long?): String

    fun transferFrom(
        from: String,
        to: String,
        value: BigInteger,
        account: Account,
        gasPrice: String?,
        gasLimit: Long?
    ): String
}

data class DecodedMethod(val method: String, val data: Map<String, Any?>)