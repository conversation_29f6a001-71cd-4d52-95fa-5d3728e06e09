package io.iotex.iopay.contract

import com.github.iotexproject.antenna.account.Account
import com.github.iotexproject.antenna.rpc.RPCMethod
import io.grpc.StatusRuntimeException
import java.math.BigInteger

class NFT(address: String, provider: RPCMethod) : Token(address, provider, Abis.NFT_ABI) {

    fun balanceOf(owner: String, callerAddress: String): BigInteger {
        try {
            val result = this.contract.read(callerAddress, "balanceOf", owner)
            if (result.size > 0) {
                return result[0] as BigInteger
            }
        } catch (e: Exception) {
            return BigInteger.ZERO
        }
        return BigInteger.ZERO
    }

    fun listOf(owner: String, callerAddress: String): MutableList<String>? {
        try {
            val result = contract.read(callerAddress, "tokensByOwner", owner)
            val tokenList = mutableListOf<String>()
            val objectResult = result[0] as Array<Any>
            for (element in objectResult) {
                tokenList.add(element.toString())
            }
            return tokenList
        } catch (e: StatusRuntimeException) {
            e.printStackTrace()
            return null
        }
    }

    fun transfer(
        to: String,
        from: String,
        tokeId: Int,
        account: Account,
        gasPrice: String?,
        gasLimit: Long?
    ): String {
        return this.contract.execute(
            null,
            gasLimit,
            gasPrice,
            account,
            "transferFrom",
            "0",
            from,
            to,
            tokeId
        )
    }
}