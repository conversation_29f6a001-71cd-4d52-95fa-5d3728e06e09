package io.iotex.iopay.contract

import com.github.iotexproject.antenna.contract.Contract
import com.github.iotexproject.antenna.rpc.RPCMethod
import com.github.iotexproject.antenna.utils.Numeric

open class Token(val address: String, val provider: RPCMethod, abi: String) {
    var contract = Contract(provider, address, abi)

    fun decodeMethod(data: String): DecodedMethod? {
        val method = this.contract.getFunctionBySignature(data.substring(0, 8))
        method ?: return null
        var dataMap = mutableMapOf<String, Any?>()
        var inputsData = method.decode(Numeric.hexStringToByteArray(data))
        for ((i, param) in method.inputs.withIndex()) {
            dataMap.put(param.name, inputsData.get(i))
        }
        return DecodedMethod(method.name, dataMap)
    }

    fun setXRC20TokenContract(abi: String) {
        contract = Contract(provider, address, abi)
    }
}