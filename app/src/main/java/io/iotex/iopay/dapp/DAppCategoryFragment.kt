package io.iotex.iopay.dapp

import android.os.Bundle
import android.view.View
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.LinearLayoutManager
import com.drakeet.multitype.MultiTypeAdapter
import io.iotex.api.DAppCategoriesQuery
import io.iotex.base.bindbase.BaseBindFragment
import io.iotex.iopay.R
import io.iotex.iopay.dapp.item.DAppCategoryItemBinder
import io.iotex.iopay.dapp.item.DAppChainItemBinder
import io.iotex.iopay.dapp.viewmodel.DAppCategoryViewModel
import io.iotex.iopay.databinding.FragmentDappCategoryBinding
import io.iotex.iopay.support.eventbus.DAppChainEvent
import io.iotex.iopay.support.eventbus.RefreshDAppEvent
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

class DAppCategoryFragment :
    BaseBindFragment<DAppCategoryViewModel, FragmentDappCategoryBinding>(R.layout.fragment_dapp_category) {

    companion object {
        const val CATEGORY = "category"
        const val BANNER_IMAGE = "bannerImage"
        const val BANNER_LINK = "bannerLink"
        fun newInstance(category: String?,banner: DAppCategoriesQuery.Category_banner?): Fragment {
            val fragment = DAppCategoryFragment()
            val build = Bundle()
            build.putString(CATEGORY, category?:"")
            banner?.let {
                build.putString(BANNER_IMAGE, it.image())
                build.putString(BANNER_LINK, it.link())
            }
            fragment.arguments = build
            return fragment
        }
    }

    private val bannerImage by lazy {
        arguments?.getString(BANNER_IMAGE) ?: ""
    }

    private val bannerLink by lazy {
        arguments?.getString(BANNER_LINK) ?: ""
    }

    private val category by lazy {
        arguments?.getString(CATEGORY) ?: ""
    }

    private val adapterChain = MultiTypeAdapter()
    private val adapter = MultiTypeAdapter()

    private val chainItem by lazy {
        DAppChainItemBinder()
    }

    override fun initView() {
        EventBus.getDefault().register(this)
        adapter.register(DAppCategoryItemBinder(childFragmentManager).apply {
            headImage = bannerImage
            headLink = bannerLink
        })
        mBinding.recyclerView.adapter = adapter

        mBinding.recyclerViewCategory.layoutManager =
            LinearLayoutManager(requireContext())
        adapterChain.register(chainItem
            .apply {
                onItemClick = {
                    adapter.notifyDataSetChanged()
                    mViewModel.queryDAppByCategoryChain(category, it)
                }
            })
        mBinding.recyclerViewCategory.adapter = adapterChain
    }

    override fun initData() {
        mViewModel.getChainList()
        mViewModel.chainLiveData.observe(this) {
            adapterChain.items = it
            adapterChain.notifyDataSetChanged()
        }
        mViewModel.queryDAppByCategoryChain(category, "")
        mViewModel.dAppLiveData.observe(this) {
            if (!it.isNullOrEmpty()) {
                mBinding.emptyLayout.visibility = View.GONE
                mBinding.recyclerView.visibility = View.VISIBLE
                adapter.items = it
                adapter.notifyDataSetChanged()
            } else {
                mBinding.emptyLayout.visibility = View.VISIBLE
                mBinding.recyclerView.visibility = View.GONE
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onRefreshDAppEvent(event: RefreshDAppEvent) {
        mViewModel.getChainList()
        mViewModel.queryDAppByCategoryChain(category, chainItem.select)
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onRefreshDAppEvent(event: DAppChainEvent) {
        if(chainItem.select != event.name){
            chainItem.select = event.name
            adapterChain.notifyDataSetChanged()
            mViewModel.queryDAppByCategoryChain(category, chainItem.select)
        }
    }

    override fun onDestroy() {
        EventBus.getDefault().unregister(this)
        super.onDestroy()
    }
}