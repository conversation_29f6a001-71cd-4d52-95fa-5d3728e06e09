package io.iotex.iopay.dapp

import android.annotation.SuppressLint
import android.view.LayoutInflater
import android.view.View
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import com.google.android.material.tabs.TabLayout
import io.iotex.base.bindbase.BaseBindFragment
import io.iotex.iopay.R
import io.iotex.iopay.data.UserStore
import io.iotex.iopay.databinding.FragmentDiscoverBinding
import io.iotex.iopay.databinding.ViewTabRedDotBinding
import io.iotex.iopay.home.GiftFragment
import io.iotex.iopay.reactnative.ReactNewsFragment
import io.iotex.iopay.support.eventbus.DAppPageEvent
import io.iotex.iopay.support.eventbus.GiftPageEvent
import io.iotex.iopay.support.eventbus.GiftUploadEvent
import io.iotex.iopay.support.eventbus.NewsRedDotEvent
import io.iotex.iopay.util.FireBaseEvent
import io.iotex.iopay.util.FireBaseUtil
import io.iotex.iopay.util.PageEventUtil
import io.iotex.iopay.util.extension.setGone
import io.iotex.iopay.xapp.XAppsViewModel
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

class DiscoverFragment :
    BaseBindFragment<XAppsViewModel, FragmentDiscoverBinding>(R.layout.fragment_discover) {

    private val dAppFragment by lazy {
        DAppFragment()
    }

    private val reactNewsFragment by lazy {
        ReactNewsFragment.newInstance()
    }

    private val giftCenterFragment by lazy {
        GiftFragment.newInstance()
    }

    @SuppressLint("CommitTransaction")
    override fun initView() {
        EventBus.getDefault().register(this)
        mBinding.tabs.addTab(mBinding.tabs.newTab().apply {
            val bind = ViewTabRedDotBinding.inflate(LayoutInflater.from(requireContext()))
            bind.tvTitle.setTextColor(ContextCompat.getColor(requireContext(),R.color.color_title))
            bind.tvTitle.text = getString(R.string.multi_browser)
            bind.vRedDot.setGone()
            customView = bind.root
        })

        mBinding.tabs.addTab(mBinding.tabs.newTab().apply {
            val bind = ViewTabRedDotBinding.inflate(LayoutInflater.from(requireContext()))
            bind.tvTitle.setTextColor(ContextCompat.getColor(requireContext(),R.color.color_title_sub))
            bind.tvTitle.text = getString(R.string.menu_news)
            bind.vRedDot.setGone()
            customView = bind.root
        })

        if(UserStore.getSwitchGiftCenter()){
            mBinding.tabs.addTab(mBinding.tabs.newTab().apply {
                val bind = ViewTabRedDotBinding.inflate(LayoutInflater.from(requireContext()))
                bind.ivTitle.setImageResource(R.drawable.icon_gift_center)
                bind.vRedDot.setGone()
                customView = bind.root
            })
        }
        if(childFragmentManager.findFragmentByTag("dAppFragment") == null){
            childFragmentManager.beginTransaction()
                .add(R.id.flDiscover, dAppFragment,"dAppFragment")
                .commitNow()
        }

        if(childFragmentManager.findFragmentByTag("reactNewsFragment") == null){
            childFragmentManager.beginTransaction()
                .add(R.id.flDiscover, reactNewsFragment,"reactNewsFragment")
                .commitNow()
        }

        childFragmentManager.beginTransaction()
            .show(dAppFragment)
            .hide(reactNewsFragment)
            .commitNowAllowingStateLoss()
        if(UserStore.getSwitchGiftCenter()){
            if(childFragmentManager.findFragmentByTag("giftCenterFragment") == null){
                childFragmentManager.beginTransaction()
                    .add(R.id.flDiscover, giftCenterFragment,"giftCenterFragment")
                    .commitNow()
            }
            childFragmentManager.beginTransaction()
                .hide(giftCenterFragment)
                .commitNowAllowingStateLoss()
        }

        mBinding.tabs.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
            override fun onTabSelected(tab: TabLayout.Tab?) {
                val tvTitle = tab?.customView?.findViewById<TextView>(R.id.tvTitle)
                tvTitle?.setTextColor(ContextCompat.getColor(requireContext(),R.color.color_title))
                when (tab?.position) {
                    0 -> {
                        childFragmentManager.beginTransaction()
                            .show(dAppFragment)
                            .hide(reactNewsFragment)
                            .hide(giftCenterFragment)
                            .commitNowAllowingStateLoss()
                        PageEventUtil.logEvent(PageEventUtil.MENUBROWSER)
                    }
                    1 -> {
                        childFragmentManager.beginTransaction()
                            .hide(dAppFragment)
                            .show(reactNewsFragment)
                            .hide(giftCenterFragment)
                            .commitNowAllowingStateLoss()
                        PageEventUtil.logEvent(PageEventUtil.MENUEXPLORER)
                        FireBaseUtil.logFireBase(FireBaseEvent.ACTION_DISCOVER_CLICK_NEWS)
                        toggleRedDot(false, 1)
                    }
                    2 -> {
                        childFragmentManager.beginTransaction()
                            .hide(dAppFragment)
                            .hide(reactNewsFragment)
                            .show(giftCenterFragment)
                            .commitNowAllowingStateLoss()
                        toggleRedDot(false, 2)
                        FireBaseUtil.logFireBase(FireBaseEvent.ACTION_TAB_GIFT_CENTER)
                    }
                }
            }

            override fun onTabUnselected(tab: TabLayout.Tab?) {
                val tvTitle = tab?.customView?.findViewById<TextView>(R.id.tvTitle)
                tvTitle?.setTextColor(ContextCompat.getColor(requireContext(),R.color.color_title_sub))
            }

            override fun onTabReselected(tab: TabLayout.Tab?) {
                //nothing.
            }
        })
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onDAppPageEvent(event: DAppPageEvent) {
        mBinding.tabs.selectTab(mBinding.tabs.getTabAt(0))
    }


    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onGiftPageEvent(event: GiftPageEvent) {
        kotlin.runCatching {
            mBinding.tabs.selectTab(mBinding.tabs.getTabAt(2))
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onNewsRedDotEvent(event: NewsRedDotEvent) {
        if(mBinding.tabs.selectedTabPosition != 1){
            toggleRedDot(true, 1)
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onGiftUploadEvent(event: GiftUploadEvent) {
        if(mBinding.tabs.selectedTabPosition != 2){
            toggleRedDot(true, 2)
        }
    }

    private fun toggleRedDot(display:Boolean,index:Int){
        kotlin.runCatching {
            val vRedDot = mBinding.tabs.getTabAt(index)?.customView?.findViewById<View>(R.id.vRedDot)
            vRedDot?.isVisible = display
        }
    }

    override fun onDestroy() {
        EventBus.getDefault().unregister(this)
        super.onDestroy()
    }
}