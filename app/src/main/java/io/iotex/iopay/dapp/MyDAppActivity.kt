package io.iotex.iopay.dapp

import android.text.Editable
import android.text.TextWatcher
import androidx.recyclerview.widget.LinearLayoutManager
import com.drakeet.multitype.MultiTypeAdapter
import io.iotex.base.bindbase.BaseBindActivity
import io.iotex.iopay.R
import io.iotex.iopay.base.BaseBindToolbarActivity
import io.iotex.iopay.dapp.item.DAppCategoryItemBinder
import io.iotex.iopay.dapp.viewmodel.MyDAppViewModel
import io.iotex.iopay.databinding.ActivityMyDappBinding

class MyDAppActivity :
    BaseBindToolbarActivity<MyDAppViewModel, ActivityMyDappBinding>(R.layout.activity_my_dapp) {

    private val myAppsAdapter = MultiTypeAdapter()

    override fun initView() {
        setToolbarTitle(getString(R.string.dapps))

        myAppsAdapter.register(DAppCategoryItemBinder(supportFragmentManager,true)
            .apply {
                onDeleteClick = {
                    mViewModel.deleteDApp(it)
                }
            })
        mBinding.recyclerView.layoutManager = LinearLayoutManager(this)
        mBinding.recyclerView.adapter = myAppsAdapter
        mBinding.etSearch.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
            }

            override fun afterTextChanged(s: Editable?) {
                s?.let {
                    val content = it.toString().trim()
                    if (content.isNotBlank()) {
                        mViewModel.loadSearchDApps(content)
                    } else {
                        mViewModel.loadMyDApps()
                    }
                }
            }
        })
    }

    override fun initData() {
        mViewModel.loadMyDApps()
        mViewModel.myDAppLiveData.observe(this) {
            myAppsAdapter.items = it
            myAppsAdapter.notifyDataSetChanged()
        }

        mViewModel.deleteLiveData.observe(this) {
            mBinding.etSearch.setText("")
            mViewModel.loadMyDApps()
        }
    }
}