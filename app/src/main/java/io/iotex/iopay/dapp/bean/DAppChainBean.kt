package io.iotex.iopay.dapp.bean

import io.iotex.iopay.IoPayApplication
import io.iotex.iopay.R
import io.iotex.iopay.data.db.AppDatabase
import io.iotex.iopay.util.Config
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlin.coroutines.suspendCoroutine

data class DAppChainBean(
    val name: String,
    val icon: String,
    val iconSelected: String,
    val logoId: Int,
    val logoIdSelected: Int,
)

object DAppChainUtil {

    suspend fun getDAppChains(isVisitor:Boolean = false): ArrayList<DAppChainBean> {
        return suspendCoroutine { continuation ->
            val list =
                AppDatabase.getInstance(IoPayApplication.getInstance()).rpcNetworkDao()
                    .queryByDAppChain().ifEmpty {
                        AppDatabase.getInstance(IoPayApplication.getInstance()).rpcNetworkDao()
                            .queryMainNetworkImmutable()
                    }
            val chainList = ArrayList<DAppChainBean>()
            chainList.add(
                DAppChainBean(
                    "",
                    "",
                    "",
                    R.drawable.icon_dapp_chain_all,
                    R.drawable.icon_dapp_chain_all_selected
                )
            )
            val solanaChain = DAppChainBean(
                "solana",
                "",
                "",
                R.drawable.icon_dapp_solana_logo,
                R.drawable.icon_solana_logo
            )
            list.forEach {
                when (it.chainId) {
                    Config.IOTEX_CHAIN_ID -> {
                        chainList.add(
                            DAppChainBean(
                                it.shortName,
                                it.getDAppChainIcon(),
                                it.chainIconSelected,
                                R.drawable.icon_dapp_chain_iotex,
                                R.drawable.icon_iotex_network_logo
                            )
                        )
                    }
                    Config.IOTEX_TEST_CHAIN_ID -> {
                        //no add
                    }
                    Config.BSC_CHAIN_ID -> {
                        chainList.add(
                            DAppChainBean(
                                it.shortName,
                                it.getDAppChainIcon(),
                                it.chainIconSelected,
                                R.drawable.icon_dapp_chain_binance,
                                R.drawable.icon_dapp_chain_binance_selected
                            )
                        )
                    }
                    Config.POLYGON_CHAIN_ID -> {
                        if (isVisitor) {
                            chainList.add(
                                DAppChainBean(
                                    it.shortName,
                                    "",
                                    "",
                                    R.drawable.icon_dapp_chain_polygon,
                                    R.drawable.icon_polygon_logo
                                )
                            )
                        } else {
                            chainList.add(
                                DAppChainBean(
                                    it.shortName,
                                    it.getDAppChainIcon(),
                                    it.chainIconSelected,
                                    R.drawable.icon_dapp_chain_polygon,
                                    R.drawable.icon_dapp_chain_polygon_selected
                                )
                            )
                        }
                    }
                    Config.ETH_CHAIN_ID -> {
                        chainList.add(
                            DAppChainBean(
                                it.shortName,
                                it.getDAppChainIcon(),
                                it.chainIconSelected,
                                R.drawable.icon_dapp_chain_ethereum,
                                R.drawable.icon_dapp_chain_ethereum_selected
                            )
                        )
                    }
                    Config.AVAX_CHAIN_ID -> {
                        chainList.add(
                            DAppChainBean(
                                it.shortName,
                                it.getDAppChainIcon(),
                                it.chainIconSelected,
                                R.drawable.icon_dapp_chain_avax,
                                R.drawable.icon_dapp_chain_avax_selected
                            )
                        )
                    }
                    Config.FTM_CHAIN_ID -> {
                        chainList.add(
                            DAppChainBean(
                                it.shortName,
                                it.getDAppChainIcon(),
                                it.chainIconSelected,
                                R.drawable.icon_dapp_chain_fantom,
                                R.drawable.icon_dapp_chain_fantom_selected
                            )
                        )
                    }
                    Config.ARB_CHAIN_ID -> {
                        if(isVisitor){
                            chainList.add(
                                DAppChainBean(
                                    it.shortName,
                                    "",
                                    "",
                                    R.drawable.icon_dapp_chain_arbitrum,
                                    R.drawable.icon_arb_network_logo
                                )
                            )
                        }else{
                            chainList.add(
                                DAppChainBean(
                                    it.shortName,
                                    it.getDAppChainIcon(),
                                    it.chainIconSelected,
                                    R.drawable.icon_dapp_chain_arbitrum,
                                    R.drawable.icon_dapp_chain_arbitrum_selected
                                )
                            )
                        }
                    }
                    Config.BASE_CHAIN_ID -> {
                        if (isVisitor) {
                            chainList.add(
                                DAppChainBean(
                                    it.shortName,
                                    "",
                                    "",
                                    R.drawable.icon_dapp_chain_base,
                                    R.drawable.icon_base_network_logo
                                )
                            )
                        } else {
                            chainList.add(
                                DAppChainBean(
                                    it.shortName,
                                    it.getDAppChainIcon(),
                                    it.chainIconSelected,
                                    R.drawable.icon_dapp_chain_base,
                                    R.drawable.icon_dapp_chain_base_selected
                                )
                            )
                        }
                    }
                    Config.BITCOIN_MAIN_CHAIN_ID -> {
                        chainList.add(
                            DAppChainBean(
                                it.shortName,
                                it.getDAppChainIcon(),
                                it.chainIconSelected,
                                R.drawable.icon_dapp_chain_bitcoin,
                                R.drawable.icon_bitcoin_logo
                            )
                        )
                    }
                    Config.SOLANA_MAIN_CHAIN_ID -> {
                        chainList.add(
                            solanaChain
                        )
                    }
                    else -> {
                        chainList.add(
                            DAppChainBean(
                                it.shortName,
                                it.getDAppChainIcon(),
                                it.chainIconSelected,
                                R.drawable.icon_dapp_chain_default,
                                R.drawable.icon_dapp_chain_default
                            )
                        )
                    }
                }
            }
            if (!chainList.contains(solanaChain)) {
                chainList.add(solanaChain)
            }
            continuation.resumeWith(Result.success(chainList))
        }

    }

    fun parseChains(chains: String,isVisitor:Boolean = false, callBack: (ArrayList<DAppChainBean>) -> Unit) {
        CoroutineScope(Dispatchers.IO).launch {
            val chainList = ArrayList<DAppChainBean>()
            val all = getDAppChains(isVisitor)
            chains.replace("avalanche", "avax")
            all.forEach {
                if (it.name.isNotEmpty() && chains.contains(it.name, true)) {
                    chainList.add(it)
                }
            }
            CoroutineScope(Dispatchers.Main).launch {
                callBack.invoke(chainList)
            }
        }
    }
}