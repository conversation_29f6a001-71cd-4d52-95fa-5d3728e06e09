package io.iotex.iopay.dapp.dialog

import android.content.DialogInterface
import io.iotex.base.BaseViewModel
import io.iotex.base.bindbase.BaseBindDialog
import io.iotex.iopay.R
import io.iotex.iopay.databinding.DialogDappEnterTipsBinding

class DAppEnterTipsDialog @JvmOverloads constructor(
    val name:String = "",
) :BaseBindDialog<BaseViewModel, DialogDappEnterTipsBinding>(R.layout.dialog_dapp_enter_tips) {

    var onConfirmClick: ((Boolean) -> Unit)? = null
    var onCancelClick: (() -> Unit)? = null
    override fun initView() {
        mBinding.tvTitle.text = getString(R.string.you_will_be_redirected_to_a_third_party_dapp)+" \""+name+"\""

        mBinding.tvContent.text = String.format(getString(R.string.please_note_that_is_fully_managed_by_the_project),name)

        mBinding.ivClose.setOnClickListener {
            dismiss()
        }

        mBinding.tvGetIt.setOnClickListener {
            onConfirmClick?.invoke(mBinding.checkbox.isChecked)
            dismiss()
        }
        mBinding.llCheckboxWrapper.setOnClickListener {
            mBinding.checkbox.isChecked = !mBinding.checkbox.isChecked
        }
    }

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        onCancelClick?.invoke()
    }

}