package io.iotex.iopay.dapp.dialog

import io.iotex.base.bindbase.BaseBindDialog
import io.iotex.iopay.R
import io.iotex.iopay.base.BaseLaunchVM
import io.iotex.iopay.databinding.DialogDappNewWalletTipsBinding
import io.iotex.iopay.wallet.add.WalletAddActivity
import io.iotex.iopay.wallet.add.WalletImportActivity
import io.iotex.iopay.wallet.add.WalletImportMethodActivity

class DAppNewWalletTipsDialog:
    BaseBindDialog<BaseLaunchVM, DialogDappNewWalletTipsBinding>(R.layout.dialog_dapp_new_wallet_tips) {

    override fun initView() {
        mBinding.ivClose.setOnClickListener {
            dismiss()
        }

        mBinding.tvVisitor.setOnClickListener {
            WalletImportMethodActivity.startActivity(requireContext())
            dismiss()
        }

        mBinding.tvNewWallet.setOnClickListener {
            WalletAddActivity.startActivity(requireContext())
            dismiss()
        }
    }

}