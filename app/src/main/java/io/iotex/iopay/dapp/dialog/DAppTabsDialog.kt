package io.iotex.iopay.dapp.dialog

import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.appcompat.content.res.AppCompatResources
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.GridLayoutManager
import com.blankj.utilcode.util.ColorUtils
import com.blankj.utilcode.util.ScreenUtils
import com.drakeet.multitype.ItemViewBinder
import com.drakeet.multitype.MultiTypeAdapter
import io.iotex.api.DAppCategoriesQuery
import io.iotex.base.BaseViewModel
import io.iotex.base.bindbase.BaseBindDialog
import io.iotex.base.bindbase.BaseBindVH
import io.iotex.iopay.R
import io.iotex.iopay.databinding.DialogDappTabsBinding
import io.iotex.iopay.databinding.ItemDappTabsBinding
import io.iotex.iopay.util.FireBaseUtil

class DAppTabsDialog(val select: DAppCategoriesQuery.Dapp_category, val list: List<DAppCategoriesQuery.Dapp_category>) :
    BaseBindDialog<BaseViewModel, DialogDappTabsBinding>(R.layout.dialog_dapp_tabs) {

    private val adapter = MultiTypeAdapter()
    var onItemClick: ((Int) -> Unit)? = null

    override fun initView() {
        adapter.register(DAppTabsItemBinder())
        mBinding.recyclerView.layoutManager = GridLayoutManager(context, 3)
        mBinding.recyclerView.adapter = adapter
        adapter.items = list
        adapter.notifyDataSetChanged()
    }

    override fun onStart() {
        super.onStart()
        val attributes = dialog?.window?.attributes
        attributes?.width = ScreenUtils.getScreenWidth()
        attributes?.gravity = Gravity.BOTTOM
        dialog?.window?.attributes = attributes
    }

    inner class DAppTabsItemBinder : ItemViewBinder<DAppCategoriesQuery.Dapp_category, BaseBindVH<ItemDappTabsBinding>>() {
        override fun onBindViewHolder(holder: BaseBindVH<ItemDappTabsBinding>, item: DAppCategoriesQuery.Dapp_category) {
            val bind = holder.bind
            bind.tvTab.text = item.name()
            if (select == item) {
                bind.tvTab.setTextColor(ContextCompat.getColor(bind.tvTab.context, R.color.color_title))
                bind.tvTab.background =
                    AppCompatResources.getDrawable(requireContext(), R.drawable.shape_card_back_stroke_gradient)
            } else {
                bind.tvTab.setTextColor(ContextCompat.getColor(bind.tvTab.context, R.color.color_title_thr))
                bind.tvTab.background =
                    AppCompatResources.getDrawable(requireContext(), R.drawable.shape_card_back)
            }
            bind.root.setOnClickListener {
                onItemClick?.invoke(holder.adapterPosition)
                dismiss()
                val bundle = Bundle()
                bundle.putString("category", item.name().lowercase())
                FireBaseUtil.logFireBase("action_browser_all",bundle)
            }
        }

        override fun onCreateViewHolder(inflater: LayoutInflater, parent: ViewGroup): BaseBindVH<ItemDappTabsBinding> {
            return BaseBindVH(ItemDappTabsBinding.inflate(inflater))
        }

    }

}