package io.iotex.iopay.dapp.item

import android.view.LayoutInflater
import android.view.ViewGroup
import com.youth.banner.adapter.BannerAdapter
import io.iotex.api.DiscoveryBannerQuery
import io.iotex.base.bindbase.BaseBindVH
import io.iotex.iopay.R
import io.iotex.iopay.databinding.ItemDappBannerBinding
import io.iotex.iopay.util.extension.loadSvgOrImage

class DAppBannerAdapter(data: List<DiscoveryBannerQuery.Discovery_banner>) :
    BannerAdapter<DiscoveryBannerQuery.Discovery_banner, BaseBindVH<ItemDappBannerBinding>>(data) {

    override fun onCreateHolder(
        parent: ViewGroup,
        viewType: Int
    ): BaseBindVH<ItemDappBannerBinding> {
        return BaseBindVH(ItemDappBannerBinding.inflate(LayoutInflater.from(parent.context),parent,false))
    }

    override fun onBindView(
        holder: BaseB<PERSON>VH<ItemDappBannerBinding>,
        data: DiscoveryBannerQuery.Discovery_banner,
        position: Int,
        size: Int
    ) {
        holder.bind.ivImage.loadSvgOrImage(data.imgUrl(), R.drawable.default_banner)
    }

}