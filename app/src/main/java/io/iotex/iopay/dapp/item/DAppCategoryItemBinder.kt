package io.iotex.iopay.dapp.item

import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.drawable.Drawable
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import androidx.fragment.app.FragmentManager
import com.blankj.utilcode.util.ConvertUtils
import com.blankj.utilcode.util.SPUtils
import com.blankj.utilcode.util.ScreenUtils
import com.bumptech.glide.Glide
import com.bumptech.glide.request.target.CustomTarget
import com.bumptech.glide.request.transition.Transition
import com.drakeet.multitype.ItemViewBinder
import io.iotex.base.bindbase.BaseBindVH
import io.iotex.iopay.R
import io.iotex.iopay.dapp.dialog.DAppEnterTipsDialog
import io.iotex.iopay.data.db.DiscoverDApps
import io.iotex.iopay.databinding.ItemDappCategoryBinding
import io.iotex.iopay.util.FireBaseEvent
import io.iotex.iopay.util.FireBaseUtil
import io.iotex.iopay.util.IoPayConstant
import io.iotex.iopay.util.SPConstant
import io.iotex.iopay.util.SignUtils
import io.iotex.iopay.util.extension.loadSvgOrImage
import io.iotex.iopay.xapp.XAppsActivity

class DAppCategoryItemBinder(val manager: FragmentManager, val swipe: Boolean = false) :
    ItemViewBinder<DiscoverDApps, BaseBindVH<ItemDappCategoryBinding>>() {

    var onDeleteClick: ((String) -> Unit)? = null

    var headImage:String? = null
    var headLink:String? = null

    override fun onBindViewHolder(
        holder: BaseBindVH<ItemDappCategoryBinding>,
        item: DiscoverDApps
    ) {
        val bind = holder.bind
        bind.swipeLayout.isSwipeEnabled = swipe
        bind.ivIcon.loadSvgOrImage(item.img_url, R.drawable.ic_dapp_placeholder)
        bind.tvName.text = item.title
        bind.tvDesc.text = item.getDisplayContent()
        bind.tags.setChains(item.chains)
        if (holder.adapterPosition == adapterItems.size - 1) {
            bind.viewDivider.visibility = View.INVISIBLE
        } else {
            bind.viewDivider.visibility = View.VISIBLE
        }
        bind.llDelete.setOnClickListener {
            onDeleteClick?.invoke(item.url)
        }
        bind.tags.onClick = {
            onItemClick(bind.root.context, item)
        }
        bind.container.setOnClickListener {
            onItemClick(bind.root.context, item)
        }
        if (!headImage.isNullOrEmpty() && holder.adapterPosition == 0) {
            bind.ivHead.visibility = View.VISIBLE
            headImage?.let {
                setHeadImage(it,bind.ivHead)
            }
            bind.ivHead.setOnClickListener {
                val intent = Intent(bind.ivHead.context, XAppsActivity::class.java)
                intent.putExtra(IoPayConstant.BROWSER_URL, headLink)
                bind.ivHead.context.startActivity(intent)
                FireBaseUtil.logFireBase(FireBaseEvent.ACTION_BROWSER_DEPIN_BANNER)
            }
        }else{
            bind.ivHead.visibility = View.GONE
        }
        bind.executePendingBindings()
    }

    override fun onCreateViewHolder(
        inflater: LayoutInflater,
        parent: ViewGroup
    ): BaseBindVH<ItemDappCategoryBinding> {
        return BaseBindVH(ItemDappCategoryBinding.inflate(inflater))
    }

    var dAppEnterTipsDialog: DAppEnterTipsDialog? = null
    private fun onItemClick(context: Context, item: DiscoverDApps) {
        if (SPUtils.getInstance()
                .getBoolean(SPConstant.SP_DAPP_ENTER_TIPS_UNDERSTOOD + item.title)
        ) {
            goDAppBrowser(context, item)
        } else {
            if(dAppEnterTipsDialog == null){
                dAppEnterTipsDialog = DAppEnterTipsDialog(item.title).apply {
                    onConfirmClick = {
                        if (it) SPUtils.getInstance()
                            .put(SPConstant.SP_DAPP_ENTER_TIPS_UNDERSTOOD+item.title, true)
                        goDAppBrowser(context, item)
                    }
                    onCancelClick = {
                        dAppEnterTipsDialog = null
                    }
                }
                dAppEnterTipsDialog?.show(manager, System.currentTimeMillis().toString())
            }
        }
    }

    private fun goDAppBrowser(context: Context, item: DiscoverDApps) {
        val intent = Intent(context, XAppsActivity::class.java)
        intent.putExtra(IoPayConstant.BROWSER_URL, item.url)
        intent.putExtra(IoPayConstant.BROWSER_DAPP_NAME, item.title)
        intent.putExtra(IoPayConstant.BROWSER_DAPP_LOGO, item.img_url)
        intent.putExtra(IoPayConstant.X_APP_TITLE, item.title)
        context.startActivity(intent)
        SignUtils.recordDAppRecord(context, item)
        val bundleEvent = Bundle()
        bundleEvent.putString(FireBaseUtil.DAPP_NAME, item.title.lowercase())
        bundleEvent.putLong(FireBaseUtil.START_TIME, System.currentTimeMillis())
        bundleEvent.putString(FireBaseUtil.DAPP_URL, item.url.lowercase())
        FireBaseUtil.logFireBase(FireBaseEvent.DAPP_CLICK, bundleEvent)
    }

    private fun setHeadImage(headImage:String,image:ImageView){
        Glide.with(image).asBitmap().load(headImage).into(object :
            CustomTarget<Bitmap>() {
            override fun onResourceReady(
                bitmap: Bitmap,
                transition: Transition<in Bitmap>?
            ) {
                image.visibility = View.VISIBLE
                val layoutParams = image.layoutParams
                val width = ScreenUtils.getScreenWidth() - ConvertUtils.dp2px(85f)
                val height = width.toFloat() / bitmap.width * bitmap.height
                layoutParams.width = width
                layoutParams.height = height.toInt()
                image.layoutParams = layoutParams
                image.loadSvgOrImage(headImage, R.drawable.default_banner)

            }

            override fun onLoadCleared(placeholder: Drawable?) {
                //nothing.
            }

        })
    }

}

