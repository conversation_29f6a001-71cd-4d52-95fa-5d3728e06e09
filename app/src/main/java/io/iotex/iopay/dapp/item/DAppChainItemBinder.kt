package io.iotex.iopay.dapp.item

import android.os.Bundle
import android.view.LayoutInflater
import android.view.ViewGroup
import com.drakeet.multitype.ItemViewBinder
import io.iotex.base.bindbase.BaseBindVH
import io.iotex.iopay.dapp.bean.DAppChainBean
import io.iotex.iopay.databinding.ItemDappChainBinding
import io.iotex.iopay.support.eventbus.DAppChainEvent
import io.iotex.iopay.util.FireBaseEvent
import io.iotex.iopay.util.FireBaseUtil
import io.iotex.iopay.util.extension.loadSvgOrImage
import org.greenrobot.eventbus.EventBus

class DAppChainItemBinder : ItemViewBinder<DAppChainBean, BaseBindVH<ItemDappChainBinding>>() {
    var select = ""
    var onItemClick: ((String) -> Unit)? = null
    override fun onBindViewHolder(holder: BaseBindVH<ItemDappChainBinding>, item: DAppChainBean) {
        val bind = holder.bind
        if (select == item.name) {
            bind.ivCategory.loadSvgOrImage(item.iconSelected, item.logoIdSelected)
        } else {
            bind.ivCategory.loadSvgOrImage(item.icon, item.logoId)
        }
        bind.root.setOnClickListener {
            select = item.name
            onItemClick?.invoke(item.name)
            val bundle = Bundle()
            if(item.name.isNotEmpty()){
                bundle.putString("network", item.name.lowercase())
            } else {
                bundle.putString("network", "all")
            }
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_BROWSER_PAGE_CLICK_NETWORK, bundle)
            EventBus.getDefault().post(DAppChainEvent(select))
        }
    }

    override fun onCreateViewHolder(inflater: LayoutInflater, parent: ViewGroup): BaseBindVH<ItemDappChainBinding> {
        return BaseBindVH(ItemDappChainBinding.inflate(inflater))
    }

}

