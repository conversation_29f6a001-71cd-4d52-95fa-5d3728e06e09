package io.iotex.iopay.dapp.item

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.fragment.app.FragmentManager
import com.blankj.utilcode.util.SPUtils
import com.drakeet.multitype.ItemViewBinder
import io.iotex.base.bindbase.BaseBindVH
import io.iotex.iopay.R
import io.iotex.iopay.dapp.dialog.DAppEnterTipsDialog
import io.iotex.iopay.data.db.DiscoverDApps
import io.iotex.iopay.databinding.ItemDappHotBinding
import io.iotex.iopay.util.FireBaseEvent
import io.iotex.iopay.util.FireBaseUtil
import io.iotex.iopay.util.IoPayConstant
import io.iotex.iopay.util.SPConstant
import io.iotex.iopay.util.SignUtils
import io.iotex.iopay.util.extension.loadSvgOrImage
import io.iotex.iopay.xapp.XAppsActivity

class DAppHotItemBinder(val manager: FragmentManager) :
    ItemViewBinder<DiscoverDApps, BaseBindVH<ItemDappHotBinding>>() {

    override fun onBindViewHolder(holder: BaseBindVH<ItemDappHotBinding>, item: DiscoverDApps) {
        val bind = holder.bind
        if (holder.bind.root.tag == item) {
            return
        }
        holder.bind.root.tag = item
        bind.tvName.text = item.title
        bind.tags.setChains(item.chains, true)
        bind.ivIcon.loadSvgOrImage(item.img_url, R.drawable.ic_dapp_placeholder)
        bind.tags.onClick = {
            onItemClick(bind.root.context, item)
        }
        bind.root.setOnClickListener {
            onItemClick(bind.root.context, item)
        }
        bind.executePendingBindings()
    }

    override fun onCreateViewHolder(
        inflater: LayoutInflater,
        parent: ViewGroup
    ): BaseBindVH<ItemDappHotBinding> {
        return BaseBindVH(ItemDappHotBinding.inflate(inflater))
    }

    var dAppEnterTipsDialog: DAppEnterTipsDialog? = null
    private fun onItemClick(context: Context, item: DiscoverDApps) {
        if (SPUtils.getInstance().getBoolean(SPConstant.SP_DAPP_ENTER_TIPS_UNDERSTOOD+item.title)) {
            goDAppBrowser(context, item)
        } else {
            if(dAppEnterTipsDialog == null){
                dAppEnterTipsDialog = DAppEnterTipsDialog(item.title).apply {
                    onConfirmClick = {
                        if (it) SPUtils.getInstance()
                            .put(SPConstant.SP_DAPP_ENTER_TIPS_UNDERSTOOD+item.title, true)
                        goDAppBrowser(context, item)
                    }
                    onCancelClick = {
                        dAppEnterTipsDialog = null
                    }
                }
                dAppEnterTipsDialog?.show(manager, System.currentTimeMillis().toString())
            }
        }
    }

    private fun goDAppBrowser(context: Context, item: DiscoverDApps) {
        val intent = Intent(context, XAppsActivity::class.java)
        intent.putExtra(IoPayConstant.BROWSER_URL, item.url)
        intent.putExtra(IoPayConstant.BROWSER_DAPP_NAME, item.title)
        intent.putExtra(IoPayConstant.BROWSER_DAPP_LOGO, item.img_url)
        intent.putExtra(IoPayConstant.X_APP_TITLE, item.title)
        context.startActivity(intent)
        SignUtils.recordDAppRecord(context, item)
        val bundle = Bundle()
        bundle.putString("name", item.title.lowercase())
        FireBaseUtil.logFireBase(FireBaseEvent.ACTION_HOT_DAPP_CLICK,bundle)
    }
}