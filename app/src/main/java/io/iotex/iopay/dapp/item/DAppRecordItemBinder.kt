package io.iotex.iopay.dapp.item

import android.content.Context
import android.content.Intent
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.drakeet.multitype.ItemViewBinder
import io.iotex.base.bindbase.BaseBindVH
import io.iotex.iopay.R
import io.iotex.iopay.data.db.DAppRecord
import io.iotex.iopay.databinding.ItemDappRecordBinding
import io.iotex.iopay.util.IoPayConstant
import io.iotex.iopay.util.extension.loadSvgOrImage
import io.iotex.iopay.xapp.XAppsActivity

class DAppRecordItemBinder :
    ItemViewBinder<DAppRecord, BaseBindVH<ItemDappRecordBinding>>() {

    override fun onBindViewHolder(
        holder: BaseBindVH<ItemDappRecordBinding>,
        item: DAppRecord
    ) {
        val bind = holder.bind
        bind.ivIcon.loadSvgOrImage(item.logo, R.drawable.ic_dapp_placeholder)
        bind.tvName.text = item.url
        bind.tvDesc.text = item.loadUrl
        bind.tags.setChains(item.chains)
        if (holder.adapterPosition == adapterItems.size - 1) {
            bind.viewDivider.visibility = View.INVISIBLE
        } else {
            bind.viewDivider.visibility = View.VISIBLE
        }
        bind.tags.onClick = {
            goDAppBrowser(bind.root.context, item)
        }
        bind.root.setOnClickListener {
            goDAppBrowser(bind.root.context, item)
        }
        bind.executePendingBindings()
    }

    override fun onCreateViewHolder(
        inflater: LayoutInflater,
        parent: ViewGroup
    ): BaseBindVH<ItemDappRecordBinding> {
        return BaseBindVH(ItemDappRecordBinding.inflate(inflater))
    }

    private fun goDAppBrowser(context: Context, item: DAppRecord) {
        val intent = Intent(context, XAppsActivity::class.java)
        intent.putExtra(IoPayConstant.BROWSER_URL, item.loadUrl)
        context.startActivity(intent)
    }

}

