package io.iotex.iopay.dapp.viewmodel

import android.app.Application
import androidx.lifecycle.MutableLiveData
import com.blankj.utilcode.util.Utils
import io.iotex.iopay.base.BaseLaunchVM
import io.iotex.iopay.dapp.bean.DAppChainBean
import io.iotex.iopay.dapp.bean.DAppChainUtil
import io.iotex.iopay.data.db.AppDatabase
import io.iotex.iopay.data.db.DiscoverDApps

class DAppCategoryViewModel(application: Application) : BaseLaunchVM(application) {
    val chainLiveData = MutableLiveData<ArrayList<DAppChainBean>>()
    val dAppLiveData = MutableLiveData<List<DiscoverDApps>?>()

    fun getChainList() {
        addLaunch {
            val list = DAppChainUtil.getDAppChains()
            chainLiveData.postValue(list)
        }
    }

    fun queryDAppByCategoryChain(category: String, chains: String) {
        addLaunch {
            val list = AppDatabase.getInstance(Utils.getApp()).discoverDapps()
                .queryByCategoryChains(category, chains)
            if (chains.isNotEmpty() || list?.size != dAppLiveData.value?.size) {
                dAppLiveData.postValue(list)
            }
        }
    }
}