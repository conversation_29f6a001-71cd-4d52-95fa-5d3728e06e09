package io.iotex.iopay.dapp.viewmodel

import android.app.Application
import androidx.lifecycle.MutableLiveData
import com.apollographql.apollo.ApolloClient
import com.apollographql.apollo.coroutines.await
import com.blankj.utilcode.util.GsonUtils
import com.blankj.utilcode.util.SPUtils
import com.blankj.utilcode.util.Utils
import io.iotex.api.DiscoveryBannerQuery
import io.iotex.api.type.Order_by
import io.iotex.base.language.LanguageType
import io.iotex.base.language.MultiLanguage
import io.iotex.base.okHttpClient
import io.iotex.iopay.base.BaseLaunchVM
import io.iotex.iopay.data.db.AppDatabase
import io.iotex.iopay.data.db.DiscoverDApps
import io.iotex.iopay.data.db.RPCNetwork
import io.iotex.iopay.util.*

class DAppHotViewModel(application: Application) : BaseLaunchVM(application) {

    val bannerLiveData = MutableLiveData<List<DiscoveryBannerQuery.Discovery_banner>>()
    val curNetworkLiveData = MutableLiveData<RPCNetwork>()
    val hotDAppLiveData = MutableLiveData<List<DiscoverDApps>?>()
    val myDAppLiveData = MutableLiveData<List<DiscoverDApps>>()

    private val apolloClient by lazy {
        ApolloClient.builder()
            .serverUrl(Config.IoPayUrl)
            .okHttpClient(okHttpClient)
            .build()
    }

    fun loadDiscoverBanner() {
        addLaunch {
            val json = SPUtils.getInstance().getString(SPConstant.SP_DAPP_BANNER_QUERY)
            if (!json.isNullOrBlank()) {
                val type = GsonUtils.getListType(DiscoveryBannerQuery.Discovery_banner::class.java)
                val result =
                    GsonUtils.fromJson<List<DiscoveryBannerQuery.Discovery_banner>>(json, type)
                bannerLiveData.postValue(result)
                fetchDiscoverBanner()
            } else {
                fetchDiscoverBanner(true)
            }
        }
    }

    fun loadMyDApps() {
        addLaunch {
            var dApps = AppDatabase.getInstance(Utils.getApp()).dAppSort().queryDAppByLast()
            if (dApps.size > 4) dApps = dApps.subList(0, 4)
            val myList = ArrayList<DiscoverDApps>()
            dApps.forEach {
                myList.add(SignUtils.transDAppsToDiscoverDApps(it))
            }
            myDAppLiveData.postValue(myList)
        }
    }

    fun loadHotDApps() {
        addLaunch {
            val list = AppDatabase.getInstance(Utils.getApp()).discoverDapps()
                .queryByPopularity("hot")
            hotDAppLiveData.postValue(list)
        }
    }

    private fun fetchDiscoverBanner(update: Boolean = false) {
        addLaunch {
            val discoveryBannerQuery = DiscoveryBannerQuery.builder()
                .order_by(Order_by.DESC)
                .build()
            apolloClient.query(discoveryBannerQuery).await().data
                ?.discovery_banner()?.let { list ->
                    val localArea = MultiLanguage.getSetLanguageType(Utils.getApp()).getLanguage()
                    var resultArea = list.filter { it.area() == localArea }
                    if (resultArea.isEmpty()) {
                        resultArea = list.filter { it.area() == LanguageType.ENGLISH.getLanguage() }
                    }
                    val result = resultArea.filter {
                        it.expiration_at() == null || !DateTimeUtils.isExPirationTime(it.expiration_at() as String)
                    }.filter { data -> data.type() == "discover" }
                    val json = GsonUtils.toJson(result)
                    SPUtils.getInstance().put(SPConstant.SP_DAPP_BANNER_QUERY, json)
                    if (update) bannerLiveData.postValue(result)
                }
        }
    }

    fun getCurrentNetWork() {
        addLaunch {
            curNetworkLiveData.postValue(WalletHelper.getCurNetwork())
        }
    }
}