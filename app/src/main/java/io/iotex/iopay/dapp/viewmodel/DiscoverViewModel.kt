package io.iotex.iopay.dapp.viewmodel

import android.app.Application
import android.os.Bundle
import androidx.lifecycle.MutableLiveData
import com.apollographql.apollo.ApolloClient
import com.apollographql.apollo.coroutines.await
import com.blankj.utilcode.util.GsonUtils
import com.blankj.utilcode.util.SPUtils
import com.blankj.utilcode.util.Utils
import io.iotex.api.DAppCategoriesQuery
import io.iotex.api.type.Order_by
import io.iotex.base.RetrofitClient
import io.iotex.base.okHttpClient
import io.iotex.iopay.api.IoPayRestApi
import io.iotex.iopay.base.BaseLaunchVM
import io.iotex.iopay.data.UserStore
import io.iotex.iopay.data.db.AppDatabase
import io.iotex.iopay.data.db.DiscoverDApps
import io.iotex.iopay.util.Config
import io.iotex.iopay.util.DateTimeUtils
import io.iotex.iopay.util.FireBaseEvent
import io.iotex.iopay.util.FireBaseUtil
import io.iotex.iopay.util.SPConstant

class DiscoverViewModel(application: Application) : BaseLaunchVM(application) {

    val discoverCategoryDApps = MutableLiveData<List<DAppCategoriesQuery.Dapp_category>>()

    private val apolloClient by lazy {
        ApolloClient.builder()
            .serverUrl(Config.IoPayUrl)
            .okHttpClient(okHttpClient)
            .build()
    }

    private val apiService by lazy {
        RetrofitClient.createApiService(Config.IOPAY_REST_API_URL, IoPayRestApi::class.java)
    }

    fun loadDAppsCategory() {
        addLaunch {
            val json = SPUtils.getInstance().getString(SPConstant.SP_DAPP_CATEGORIES_QUERY_HOT)
            if (!json.isNullOrBlank()) {
                val type = GsonUtils.getListType(DAppCategoriesQuery.Dapp_category::class.java)
                val result = GsonUtils.fromJson<ArrayList<DAppCategoriesQuery.Dapp_category>>(json, type)
                discoverCategoryDApps.postValue(result)
                fetchDAppsCategory()
            } else {
                fetchDAppsCategory(true)
            }
        }
    }

    private fun fetchDAppsCategory(update: Boolean = false) {
        val categoriesQuery = DAppCategoriesQuery.builder()
            .order_by(Order_by.ASC)
            .build()

        addLaunch {
            apolloClient.query(categoriesQuery).await().data
                ?.dapp_category()?.let {
                    val list = ArrayList<DAppCategoriesQuery.Dapp_category>()
                    list.add(0, DAppCategoriesQuery.Dapp_category("", "", "Home", 0, false, null))
                    list.addAll(it)
                    if (update) discoverCategoryDApps.postValue(list)
                    val json = GsonUtils.toJson(list)
                    SPUtils.getInstance().put(SPConstant.SP_DAPP_CATEGORIES_QUERY_HOT, json)
                }
        }
    }

    val discoverDApps = MutableLiveData<Boolean>()

    fun loadDApps() {
        addLaunch {
            val count = AppDatabase.getInstance(Utils.getApp()).discoverDapps()
                .count()
            if (count == 0) {
                fetchDApps(true)
            } else {
                discoverDApps.postValue(true)
            }
        }
    }

    fun fetchDAppsOneDay() {
        if (DateTimeUtils.formatYMD(UserStore.getDAppTime()) != DateTimeUtils.formatYMD(System.currentTimeMillis())) {
            UserStore.setDAppTime(System.currentTimeMillis())
            fetchDApps(true)
        }
    }

    fun fetchDApps(update: Boolean = false) {
        addLaunchNoCancel("fetchDApps",onError = {
            val bundleEvent = Bundle()
            bundleEvent.putString("api", "dapp_list_latest_v2")
            bundleEvent.putString("msg", it.message)
            FireBaseUtil.logFireBase(
                FireBaseEvent.API_ERROR_CATCH,
                bundleEvent
            )
        }) {
            apiService.dAppList().dapp_list_latest_v2?.let { dApps ->
                if (dApps.isNotEmpty()) AppDatabase.getInstance(Utils.getApp()).discoverDapps()
                    .deleteAllDApp()
                dApps.forEach { dapp ->
                    AppDatabase.getInstance(Utils.getApp()).discoverDapps().insertDApp(
                        DiscoverDApps(
                            url = dapp.url() ?: "",
                            img_url = dapp.img_url() ?: "",
                            content_cn = dapp.content_cn() ?: "",
                            content = dapp.content() ?: "",
                            chains = dapp.chains() ?: "",
                            id = dapp.id().toString(),
                            tags = dapp.tags() ?: "",
                            title = dapp.title() ?: "",
                            weight = dapp.weight() ?: 0,
                            popularity = dapp.popularity() ?: ""
                        )
                    )
                }
                if (update) discoverDApps.postValue(true)
            }
        }
    }

}