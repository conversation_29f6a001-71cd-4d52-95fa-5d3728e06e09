package io.iotex.iopay.dapp.viewmodel

import android.app.Application
import androidx.lifecycle.MutableLiveData
import com.blankj.utilcode.util.Utils
import io.iotex.iopay.base.BaseLaunchVM
import io.iotex.iopay.data.db.AppDatabase
import io.iotex.iopay.data.db.DiscoverDApps
import io.iotex.iopay.util.*

class MyDAppViewModel(application: Application) : BaseLaunchVM(application) {

    val myDAppLiveData = MutableLiveData<List<DiscoverDApps>>()
    val deleteLiveData = MutableLiveData<Boolean>()

    fun loadMyDApps() {
        addLaunch {
            val dApps = AppDatabase.getInstance(Utils.getApp()).dAppSort().queryAll()
            val myList = ArrayList<DiscoverDApps>()
            dApps.forEach {
                myList.add(SignUtils.transDAppsToDiscoverDApps(it))
            }
            myDAppLiveData.postValue(myList)
        }
    }

    fun loadSearchDApps(query: String) {
        addLaunch {
            val dApps = AppDatabase.getInstance(Utils.getApp()).dAppSort().searchDApps(query)
            val myList = ArrayList<DiscoverDApps>()
            dApps?.forEach {
                myList.add(SignUtils.transDAppsToDiscoverDApps(it))
            }
            myDAppLiveData.postValue(myList)
        }
    }

    fun deleteDApp(url: String) {
        addLaunch {
            val dApp = AppDatabase.getInstance(Utils.getApp()).dAppSort().find(url)
            dApp?.let {
                AppDatabase.getInstance(Utils.getApp()).dAppSort().deleteDAppSort(dApp)
            }
            deleteLiveData.postValue(true)
        }
    }
}