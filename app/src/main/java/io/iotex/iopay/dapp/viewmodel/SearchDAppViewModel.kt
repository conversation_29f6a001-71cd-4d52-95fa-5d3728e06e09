package io.iotex.iopay.dapp.viewmodel

import android.app.Application
import androidx.lifecycle.MutableLiveData
import com.blankj.utilcode.util.Utils
import io.iotex.iopay.base.BaseLaunchVM
import io.iotex.iopay.data.db.AppDatabase
import io.iotex.iopay.data.db.DAppRecord
import io.iotex.iopay.data.db.DiscoverDApps
import io.iotex.iopay.util.RegexUtils
import java.net.URLEncoder

class SearchDAppViewModel(application: Application) : BaseLaunchVM(application) {

    val dAppRecordLiveData = MutableLiveData<List<DAppRecord>?>()
    val dAppLiveData = MutableLiveData<List<DiscoverDApps>?>()

    fun searchAllDAppRecord() {
        addLaunch {
            val records = AppDatabase.getInstance(Utils.getApp()).dAppRecordDao().queryAllDAppRecords()
            dAppRecordLiveData.postValue(records)
        }
    }

    fun searchDAppRecord(content:String) {
        addLaunch {
            val records = AppDatabase.getInstance(Utils.getApp()).dAppRecordDao()
                .searchRecords(content)
            dAppRecordLiveData.postValue(records)
        }
    }

    fun searchDApp(content:String) {
        addLaunch {
            val dApps = AppDatabase.getInstance(Utils.getApp()).discoverDapps()
                .searchDApps(content)
            dAppLiveData.postValue(dApps)
        }
    }

    fun recordDApp(content:String){
        addLaunch {
            var loadUrl = content
            if (!RegexUtils.isXAppsURl(loadUrl)) {
                loadUrl = "https://www.google.com/search?q=${URLEncoder.encode(loadUrl, "UTF-8")}"
            }
            if (RegexUtils.isLinkURl(content) && !content.startsWith("http")) {
                loadUrl = "https://$loadUrl"
            }
            val record = DAppRecord(content,loadUrl,System.currentTimeMillis())
            AppDatabase.getInstance(Utils.getApp()).dAppRecordDao().insertRecord(record)
        }
    }

    fun deleteAllDAppRecord(){
        addLaunch {
            AppDatabase.getInstance(Utils.getApp()).dAppRecordDao().deleteAllDAppRecord()
        }
    }

}