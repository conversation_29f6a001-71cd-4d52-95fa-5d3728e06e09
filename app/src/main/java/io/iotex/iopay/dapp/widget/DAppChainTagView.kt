package io.iotex.iopay.dapp.widget

import android.content.Context
import android.graphics.Rect
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.LinearLayout
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.viewbinding.ViewBinding
import com.drakeet.multitype.ItemViewBinder
import com.drakeet.multitype.MultiTypeAdapter
import io.iotex.iopay.R
import io.iotex.iopay.dapp.bean.DAppChainBean
import io.iotex.iopay.dapp.bean.DAppChainUtil
import io.iotex.iopay.databinding.ItemDappChainTagBinding
import io.iotex.iopay.databinding.ViewDappChainTagBinding
import io.iotex.iopay.util.extension.dp2px
import io.iotex.iopay.util.extension.loadSvgOrImage

class DAppChainTagView @JvmOverloads constructor(
    context: Context,
    attr: AttributeSet? = null,
    defStyle: Int = 0
) : FrameLayout(
    context,
    attr,
    defStyle
) {
    private val mBinding = DataBindingUtil.inflate<ViewDappChainTagBinding>(
        LayoutInflater.from(context),
        R.layout.view_dapp_chain_tag, this, true
    )

    var onClick:(()->Unit)?=null

    private val adapter = MultiTypeAdapter()

    fun setChains(chains: String, hot: Boolean = false, visitor:Boolean = false) {
        //recycler will cut padding,so remove and add.
        mBinding.llRoot.removeAllViews()
        val recyclerView = RecyclerView(context)
        mBinding.llRoot.addView(recyclerView)

        DAppChainUtil.parseChains(chains, visitor) { all ->
            var parseChains:List<DAppChainBean> = all
            if (hot) {
                if (parseChains.size == 1) {
                    val layoutParams = recyclerView.layoutParams as LinearLayout.LayoutParams
                    layoutParams.marginStart = 10.dp2px()
                    recyclerView.layoutParams = layoutParams
                }
                if (parseChains.size > 3) {
                    parseChains = parseChains.subList(0, 3)
                }
            }

            recyclerView.addItemDecoration(object : RecyclerView.ItemDecoration() {
                override fun getItemOffsets(
                    outRect: Rect,
                    view: View,
                    parent: RecyclerView,
                    state: RecyclerView.State
                ) {
                    super.getItemOffsets(outRect, view, parent, state)
                    if (parent.getChildAdapterPosition(view) != (parseChains.size - 1)) {
                        outRect.right = if(visitor) -15 else -25
                    }
                }
            })
            adapter.register(DAppChainTagItemBinder(visitor))
            adapter.items = parseChains
            recyclerView.layoutManager =
                LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
            recyclerView.adapter = adapter
        }
    }

    override fun onTouchEvent(event: MotionEvent?): Boolean {
        (parent as View).performClick()
        return false
    }

    inner class DAppChainTagItemBinder(private val visitor:Boolean = false) : ItemViewBinder<DAppChainBean, VH>() {
        override fun onBindViewHolder(holder: VH, item: DAppChainBean) {
            val bind = holder.bind as ItemDappChainTagBinding
            if(visitor){
                val layoutParams = bind.ivChain.layoutParams as LinearLayout.LayoutParams
                layoutParams.width = 10.dp2px()
                layoutParams.height = 10.dp2px()
                layoutParams.setMargins(0,0,0,0)
                bind.ivChain.layoutParams = layoutParams
            }
            bind.ivChain.loadSvgOrImage(item.iconSelected, item.logoIdSelected)
            bind.root.setOnClickListener {
                onClick?.invoke()
            }
        }

        override fun onCreateViewHolder(
            inflater: LayoutInflater,
            parent: ViewGroup
        ): VH {
            return VH(ItemDappChainTagBinding.inflate(inflater))
        }

    }

    class VH(viewBinding: ViewBinding) : RecyclerView.ViewHolder(viewBinding.root) {
        val bind = viewBinding
    }

}