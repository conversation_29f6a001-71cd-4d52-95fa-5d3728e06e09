package io.iotex.iopay.data

import com.apollographql.apollo.ApolloClient
import io.iotex.iotex.*
import io.reactivex.Observable
import io.reactivex.subjects.PublishSubject

abstract class IoPayDataV1Source(protected val apolloClient: ApolloClient) {

    protected val fetchWalletInfoSubject: PublishSubject<List<WalletInfoQuery.Wallet_info>> = PublishSubject.create()
    protected val insertWalletInfoSubject: PublishSubject<InsertWalletInfoMutation.Insert_wallet_info> = PublishSubject.create()
    protected val updateWalletInfoSubject: PublishSubject<UpdateWalletInfoMutation.Update_wallet_info> = PublishSubject.create()
    protected val exceptionSubject: PublishSubject<Throwable> = PublishSubject.create()

    val fetchWalletInfo: Observable<List<WalletInfoQuery.Wallet_info>> = fetchWalletInfoSubject.hide()
    val insertWalletInfo: Observable<InsertWalletInfoMutation.Insert_wallet_info> = insertWalletInfoSubject.hide()
    val updateWalletInfo: Observable<UpdateWalletInfoMutation.Update_wallet_info> = updateWalletInfoSubject.hide()

    val error: Observable<Throwable> = exceptionSubject.hide()

    abstract fun fetchWalletInfo(address:String,chainId:Int,platform:String)
    abstract fun insertWalletInfo(address:String,ads:String,balance:String,chainId:Int,platform:String)
    abstract fun updateWalletInfo(balance:String,id:Int,platform:String)
}