package io.iotex.iopay.data

import com.apollographql.apollo.ApolloCall
import com.apollographql.apollo.ApolloClient
import com.apollographql.apollo.api.Response
import com.apollographql.apollo.api.cache.http.HttpCachePolicy
import com.apollographql.apollo.exception.ApolloException
import io.iotex.iotex.type.Order_by
import io.iotex.iotex.*

class IoPayV1CallbackService(apolloClient: ApolloClient) : IoPayDataV1Source(apolloClient) {

    override fun fetchWalletInfo(address: String, chainId: Int, platform: String) {
        val query = WalletInfoQuery.builder()
            .address(address)
            .chainId(chainId)
            .platform(platform)
            .build()

        val callback = object : ApolloCall.Callback<WalletInfoQuery.Data>() {
            override fun onFailure(e: ApolloException) {
                exceptionSubject.onNext(e)
            }

            override fun onResponse(response: Response<WalletInfoQuery.Data>) {
                response.data()?.wallet_info()?.let {
                    fetchWalletInfoSubject.onNext(it)
                }
            }
        }

        apolloClient
            .query(query)
            .httpCachePolicy(HttpCachePolicy.NETWORK_FIRST)
            .enqueue(callback)
    }

    override fun insertWalletInfo(
        address: String,
        ads: String,
        balance: String,
        chainId: Int,
        platform: String
    ) {
        val mutation = InsertWalletInfoMutation.builder()
            .address(address)
            .ads(ads)
            .balance(balance)
            .chainId(chainId)
            .platform(platform)
            .build()

        val callback = object : ApolloCall.Callback<InsertWalletInfoMutation.Data>() {
            override fun onFailure(e: ApolloException) {
                exceptionSubject.onNext(e)
            }

            override fun onResponse(response: Response<InsertWalletInfoMutation.Data>) {
                response.data()?.insert_wallet_info()?.let {
                    insertWalletInfoSubject.onNext(it)
                }
            }
        }

        apolloClient
            .mutate(mutation)
            .enqueue(callback)

    }

    override fun updateWalletInfo(balance: String, id: Int, platform: String) {
        val mutation = UpdateWalletInfoMutation.builder()
            .balance(balance)
            .id(id)
            .platform(platform)
            .build()

        val callback = object : ApolloCall.Callback<UpdateWalletInfoMutation.Data>() {
            override fun onFailure(e: ApolloException) {
                exceptionSubject.onNext(e)
            }

            override fun onResponse(response: Response<UpdateWalletInfoMutation.Data>) {
                response.data()?.update_wallet_info()?.let {
                    updateWalletInfoSubject.onNext(it)
                }
            }
        }

        apolloClient
            .mutate(mutation)
            .enqueue(callback)
    }

}