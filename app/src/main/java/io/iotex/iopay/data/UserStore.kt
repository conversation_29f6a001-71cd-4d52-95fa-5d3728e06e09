package io.iotex.iopay.data

import androidx.appcompat.app.AppCompatDelegate
import com.blankj.utilcode.util.SPUtils
import io.iotex.iopay.data.db.RPCNetwork
import io.iotex.iopay.support.enum.Web3AddressType
import io.iotex.iopay.util.Config
import io.iotex.iopay.util.IoPayConstant
import io.iotex.iopay.util.SPConstant

object UserStore {
    private const val SP_WALLET_ADDRESS = "SP_WALLET_ADDRESS"
    private const val SP_NETWORK_SWAP_URL = "sp_network_swap_url"
    private const val SP_NETWORK_PRICE_SELECT = "sp_network_price_select"
    private const val SP_NETWORK_GAS_STATION = "sp_network_gas_station"
    private const val SP_NETWORK_TOKENS_ALIAS = "sp_network_tokens_alias"
    private const val SP_MESSAGE_COUNT = "sp_message_count"
    private const val SP_UPDATE_APP = "sp_update_app"
    private const val SP_EMPTY_TOKEN_CHAIN = "sp_empty_token_chain"
    private const val SP_RPC_NETWORK_NATIVE_DECIMALS = "sp_rpc_network_native_decimals"
    private const val SP_RPC_NETWORK_CHAIN_ID = "sp_rpc_network_id"
    private const val SP_ALL_NETWORK_INFO = "sp_all_network_info"
    private const val SP_RPC_NETWORK_NAME = "sp_rpc_network_name"
    private const val SP_PUSH_REGISTER = "sp_push_register"
    private const val SP_DAY_NIGHT_MODE_THEME = "sp_day_night_mode_theme"
    private const val SP_WEB3_ADDRESS_TYPE = "sp_web3_address_type"
    private const val SP_SWITCH_GIFT_CENTER = "sp_switch_gift_center"
    private const val SP_SWITCH_BINO_AI = "sp_switch_bino_ai"
    private const val SP_SWITCH_BUCKET_DIALOG = "sp_switch_bucket_dialog"
    private const val SP_SWITCH_GIFT_CENTER_URL = "sp_switch_gift_center_url"
    private const val SP_SWITCH_BINO_AI_URL = "sp_switch_bino_ai_url"
    private const val SP_SWITCH_RN_FAKE_DEPIN = "sp_switch_rn_fake_depin"
    private const val SP_RECORD_EVENT_TIME = "sp_record_event_time"
    private const val SP_FETCH_DAPP_TIME = "sp_fetch_dapp_time"
    private const val SP_FETCH_NETWORK_LIST_TIME = "sp_fetch_network_list_time"
    private const val SP_FETCH_CERTIFIED_CONTRACT_TIME = "sp_fetch_certified_contract_time"
    private const val SP_FETCH_CONTRACT_ERROR_TIME = "sp_fetch_contract_error_time"
    private const val SP_REVIEW_GOOGLE_PLAY_TIME = "sp_review_google_play_time"
    private const val SP_REVIEW_GOOGLE_PLAY_DONE = "sp_review_google_play_done"
    private const val SP_HOME_HIDE_ASSETS_CHECK = "sp_home_hide_assets_check"
    private const val SP_IGNORE_THIS_VERSION = "sp_ignore_this_version"
    private const val SP_LOAD_TOKEN_FROM_JSON = "sp_load_token_from_json"
    private const val SP_LOAD_SOLANA_TOKEN_DECIMALS_JSON = "sp_load_solana_token_decimals_json"
    private const val SP_DEFAULT_GAS_LIMIT_FOR_NETWORK = "sp_default_gas_limit_for_network"
    private const val SP_DEFAULT_GAS_PRICE_FOR_NETWORK = "sp_default_gas_price_for_network"

    fun getWalletAddress(): String {
        return SPUtils.getInstance().getString(SP_WALLET_ADDRESS)
    }

    fun setWalletAddress(address: String) {
        SPUtils.getInstance().put(SP_WALLET_ADDRESS, address)
    }

    fun getNetworkSymbol(): String {
        return SPUtils.getInstance()
            .getString(SPConstant.SP_RPC_NETWORK_NATIVE_CURRENCY, IoPayConstant.IOTX)
    }

    fun setNetworkSymbol(symbol: String) {
        return SPUtils.getInstance()
            .put(SPConstant.SP_RPC_NETWORK_NATIVE_CURRENCY, symbol)
    }

    fun getNetworkDecimals(): Int {
        return SPUtils.getInstance()
            .getInt(SP_RPC_NETWORK_NATIVE_DECIMALS, 18)
    }

    fun setNetworkDecimals(decimals: Int) {
        return SPUtils.getInstance()
            .put(SP_RPC_NETWORK_NATIVE_DECIMALS, decimals)
    }

    fun setNetWorkPrice(price: String) {
        SPUtils.getInstance()
            .put(SP_NETWORK_PRICE_SELECT, price)
    }

    fun getNetWorkPrice(): String {
        return SPUtils.getInstance()
            .getString(SP_NETWORK_PRICE_SELECT, "")
    }

    private fun setNetWorkSwapUrl(url: String) {
        SPUtils.getInstance()
            .put(SP_NETWORK_SWAP_URL, url)
    }

    fun getNetWorkSwapUrl(): String {
        return SPUtils.getInstance()
            .getString(SP_NETWORK_SWAP_URL, "")
    }

    fun setNetWorkGasStation(price: String) {
        SPUtils.getInstance()
            .put(SP_NETWORK_GAS_STATION, price)
    }

    fun getNetWorkGasStation(): String {
        return SPUtils.getInstance()
            .getString(SP_NETWORK_GAS_STATION, "")
    }

    fun setNetWorkTokensAlias(tokensAlias: String) {
        SPUtils.getInstance()
            .put(SP_NETWORK_TOKENS_ALIAS, tokensAlias)
    }

    fun getNetWorkTokensAlias(): String {
        return SPUtils.getInstance()
            .getString(SP_NETWORK_TOKENS_ALIAS, "")
    }

    fun getNetWorkName(): String{
        return SPUtils.getInstance().getString(SP_RPC_NETWORK_NAME, Config.IOTEX_NAME)
    }

    fun setNetWorkName(name:String){
        SPUtils.getInstance().put(SP_RPC_NETWORK_NAME, name)
    }

    fun setMessageCount(count:Int){
        SPUtils.getInstance().put(SP_MESSAGE_COUNT,count)
    }

    fun getMessageCount():Int{
        return SPUtils.getInstance().getInt(SP_MESSAGE_COUNT,0)
    }

    fun setUpdateApp(update: Boolean){
        SPUtils.getInstance().put(SP_UPDATE_APP,update)
    }

    fun getUpdateApp():Boolean{
        return SPUtils.getInstance().getBoolean(SP_UPDATE_APP,false)
    }

    fun getTokenEmpty(chainId: Int):Boolean{
        return SPUtils.getInstance().getBoolean(SP_EMPTY_TOKEN_CHAIN + chainId,false)
    }

    fun setTokenEmpty(chainId:Int,empty: Boolean){
        SPUtils.getInstance().put(SP_EMPTY_TOKEN_CHAIN + chainId,empty)
    }

    fun setChainId(chainId:Int){
        SPUtils.getInstance().put(SP_RPC_NETWORK_CHAIN_ID, chainId)
    }

    fun getChainId():Int{
        return SPUtils.getInstance().getInt(SP_RPC_NETWORK_CHAIN_ID, Config.IOTEX_CHAIN_ID)
    }

    fun setAllNetwork(boolean: Boolean){
        SPUtils.getInstance().put(SP_ALL_NETWORK_INFO, boolean)
    }

    fun getAllNetwork():Boolean{
        return SPUtils.getInstance().getBoolean(SP_ALL_NETWORK_INFO, false)
    }

    fun saveNetwork(network: RPCNetwork) {
        setNetWorkPrice(network.currencyPrice)
        setNetWorkName(network.name)
        setNetworkSymbol(network.currencySymbol)
        setNetworkDecimals(network.currencyDecimals)
        setNetWorkTokensAlias(network.tokensAlias)
        setChainId(network.chainId)
        setNetWorkGasStation(network.gasStation)

        if (network.chainId == Config.IOTEX_CHAIN_ID) {
            setNetWorkGasStation(Config.GAS_TRACKER_IOTEX)
        }
        setNetWorkSwapUrl(network.swapUrl)
    }

    fun setPushRegister(boolean: Boolean){
        SPUtils.getInstance().put(SP_PUSH_REGISTER, boolean)
    }

    fun getPushRegister():Boolean{
        return SPUtils.getInstance().getBoolean(SP_PUSH_REGISTER, true)
    }

    fun isDarkTheme(): Boolean {
        val theme = SPUtils.getInstance().getInt(SP_DAY_NIGHT_MODE_THEME, AppCompatDelegate.MODE_NIGHT_YES)
        return theme == AppCompatDelegate.MODE_NIGHT_YES
    }

    fun setDarkTheme(theme: Int) {
        SPUtils.getInstance().put(SP_DAY_NIGHT_MODE_THEME, theme)
    }

    fun getWeb3Type():Int{
        return SPUtils.getInstance().getInt(
            SP_WEB3_ADDRESS_TYPE,
            Web3AddressType.WEB3.type
        )
    }

    fun setWeb3Type(type:Web3AddressType){
        return SPUtils.getInstance().put(
            SP_WEB3_ADDRESS_TYPE,
            type.type
        )
    }

    fun getSwitchGiftCenter(): Boolean {
        return SPUtils.getInstance().getBoolean(
            SP_SWITCH_GIFT_CENTER,
            false
        )
    }

    fun setSwitchGiftCenter(enable: Boolean) {
        return SPUtils.getInstance().put(
            SP_SWITCH_GIFT_CENTER,
            enable
        )
    }

    fun getSwitchBinoAi(): Boolean {
        return SPUtils.getInstance().getBoolean(
            SP_SWITCH_BINO_AI,
            false
        )
    }

    fun setSwitchBinoAi(enable: Boolean) {
        return SPUtils.getInstance().put(
            SP_SWITCH_BINO_AI,
            enable
        )
    }

    fun getBucketDialog(): Boolean {
        return SPUtils.getInstance().getBoolean(
            SP_SWITCH_BUCKET_DIALOG,
            false
        )
    }

    fun setBucketDialog(enable: Boolean) {
        return SPUtils.getInstance().put(
            SP_SWITCH_BUCKET_DIALOG,
            enable
        )
    }

    fun getSwitchRNFakeDepin(): Boolean {
        return SPUtils.getInstance().getBoolean(SP_SWITCH_RN_FAKE_DEPIN, false)
    }

    fun setSwitchRNFakeDepin(enable: Boolean) {
        SPUtils.getInstance().put(SP_SWITCH_RN_FAKE_DEPIN, enable)
    }

    fun getGiftCenterUrl(): String {
        return SPUtils.getInstance().getString(
            SP_SWITCH_GIFT_CENTER_URL,""
        )
    }

    fun setGiftCenterUrl(enable: String) {
        return SPUtils.getInstance().put(
            SP_SWITCH_GIFT_CENTER_URL,
            enable
        )
    }

    fun getBinoAiUrl(): String {
        return SPUtils.getInstance().getString(
            SP_SWITCH_BINO_AI_URL,""
        )
    }

    fun setBinoAiUrl(enable: String) {
        return SPUtils.getInstance().put(
            SP_SWITCH_BINO_AI_URL,
            enable
        )
    }

    fun getRecordEventTime(): Long {
        return SPUtils.getInstance().getLong(
            SP_RECORD_EVENT_TIME,0
        )
    }

    fun setRecordEventTime(time: Long) {
        return SPUtils.getInstance().put(
            SP_RECORD_EVENT_TIME,
            time
        )
    }

    fun getDAppTime(): Long {
        return SPUtils.getInstance().getLong(
            SP_FETCH_DAPP_TIME,0
        )
    }

    fun setDAppTime(time: Long) {
        return SPUtils.getInstance().put(
            SP_FETCH_DAPP_TIME,
            time
        )
    }

    fun getNetworkListTime(): Long {
        return SPUtils.getInstance().getLong(
            SP_FETCH_NETWORK_LIST_TIME,0
        )
    }

    fun setNetworkListTime(time: Long) {
        return SPUtils.getInstance().put(
            SP_FETCH_NETWORK_LIST_TIME,
            time
        )
    }

    fun getCertifiedContractTime(): Long {
        return SPUtils.getInstance().getLong(
            SP_FETCH_CERTIFIED_CONTRACT_TIME,0
        )
    }

    fun setCertifiedContractTime(time: Long) {
        return SPUtils.getInstance().put(
            SP_FETCH_CERTIFIED_CONTRACT_TIME,
            time
        )
    }

    fun getContractErrorTime(): Long {
        return SPUtils.getInstance().getLong(
            SP_FETCH_CONTRACT_ERROR_TIME,0
        )
    }

    fun setContractErrorTime(time: Long) {
        return SPUtils.getInstance().put(
            SP_FETCH_CONTRACT_ERROR_TIME,
            time
        )
    }

    fun getHideAssetsCheck(): Boolean {
        return SPUtils.getInstance().getBoolean(SP_HOME_HIDE_ASSETS_CHECK, false)
    }

    fun setHideAssetsCheck(hide: Boolean) {
        SPUtils.getInstance().put(SP_HOME_HIDE_ASSETS_CHECK, hide)
    }

    fun getIgnoreVersion(): Int {
        return SPUtils.getInstance().getInt(SP_IGNORE_THIS_VERSION, 0)
    }

    fun setIgnoreVersion(version: Int) {
        SPUtils.getInstance().put(SP_IGNORE_THIS_VERSION, version)
    }

    fun getLoadTokenFromJson(): Boolean {
        return SPUtils.getInstance().getBoolean(SP_LOAD_TOKEN_FROM_JSON, false)
    }

    fun setLoadTokenFromJson(load: Boolean) {
        SPUtils.getInstance().put(SP_LOAD_TOKEN_FROM_JSON, load)
    }

    fun getLoadSolanaTokenDecimalsFromJson(): Boolean {
        return SPUtils.getInstance().getBoolean(SP_LOAD_SOLANA_TOKEN_DECIMALS_JSON, false)
    }

    fun setLoadSolanaTokenDecimalsFromJson(load: Boolean) {
        SPUtils.getInstance().put(SP_LOAD_SOLANA_TOKEN_DECIMALS_JSON, load)
    }

    fun setDefaultGasLimit(chainId: Int, limit: String) {
        SPUtils.getInstance().put(chainId.toString() + SP_DEFAULT_GAS_LIMIT_FOR_NETWORK, limit)
    }

    fun getDefaultGasLimit(chainId: Int): String {
        return SPUtils.getInstance()
            .getString(chainId.toString() + SP_DEFAULT_GAS_LIMIT_FOR_NETWORK,"")
    }

    fun setDefaultGasPrice(chainId: Int, price: String) {
        SPUtils.getInstance().put(chainId.toString() + SP_DEFAULT_GAS_PRICE_FOR_NETWORK, price)
    }

    fun getDefaultGasPrice(chainId: Int): String {
        return SPUtils.getInstance()
            .getString(chainId.toString() + SP_DEFAULT_GAS_PRICE_FOR_NETWORK,"")
    }

    fun getGoogleReviewTime(): Long {
        return SPUtils.getInstance().getLong(
            SP_REVIEW_GOOGLE_PLAY_TIME,0
        )
    }

    fun setGoogleReviewTime(time: Long) {
        return SPUtils.getInstance().put(
            SP_REVIEW_GOOGLE_PLAY_TIME,
            time
        )
    }

    fun getGoogleReviewDone(): Boolean {
        return SPUtils.getInstance().getBoolean(SP_REVIEW_GOOGLE_PLAY_DONE, false)
    }

    fun setGoogleReviewDone(done: Boolean) {
        SPUtils.getInstance().put(SP_REVIEW_GOOGLE_PLAY_DONE, done)
    }
}