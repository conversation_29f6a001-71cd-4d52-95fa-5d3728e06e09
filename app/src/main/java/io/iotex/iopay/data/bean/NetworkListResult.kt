package io.iotex.iopay.data.bean


data class NetworkListResult(
    val network_list:List<NetWork>?
){
    data class NetWork(
        val chainId:Int,
        val id:Int,
        val rpc:List<String>?,
        val devMode:Boolean,
        val name:String?,
        val shortName:String?,
        val platform:String,
        val explorer:String?,
        val network_chain_theme: NetworkChainTheme?,
        val tokensAlias:String?,
        val swapUrl:String?,
        val networkName:String?,
        val order:Int?,
        val network_config: NetworkConfig?,
        val nativeCurrencyMarket: NativeCurrencyMarket?,

    ){
        data class NativeCurrencyMarket(
            val price_change_24h:String?,
            val sparkline_in_7d:String?,
            val current_price:String?,
            val token:Token?,
        ){
            data class Token(
                val id:String?,
                val symbol:String?,
                val logo:String?,
                val decimals:String?,
                val name:String?,
            )

        }
        data class NetworkConfig(
            val token_approval_checker:String?,
            val chain_icon:String?,
            val chain_icon_light:String?,
            val chain_icon_selected:String?,
            val gas_station:String?,
            val gas_limit:String?,
            val gas_price:String?,
            val account_factory:String?,
            val entry_point:String?,
            val contract_email:String?,
            val dapp_chain:Boolean?,
            val token_category:String?,
            val frequency:Int?,
        )
        data class NetworkChainTheme(
            val theme_color:String?,
            val logo_image:String?,
            val back_image:String?,
        )
    }
}




