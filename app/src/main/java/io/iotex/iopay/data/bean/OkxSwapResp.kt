package io.iotex.iopay.data.bean

import com.solana.core.Transaction
import com.solana.core.versioned.VersionedTransaction


data class OkxSwapResp(
    val code: String,
    val msg: String,
    val data: List<OkxData>
)

data class OkxData(
    val routerResult: RouterResult,
    val tx: OkxTransaction
)

data class OkxTransaction(
    val data: String,
    val from: String,
    val to: String,
    val value: String
){
    var transaction: Transaction? = null
    var versionedTransaction: VersionedTransaction? = null
}

data class RouterResult(
    val dexRouterList: List<DexRouter>,
    val priceImpactPercentage: String,
    val fromTokenAmount: String,
    val toTokenAmount: String,
    val tradeFee: String
)

data class DexRouter(
    val subRouterList: List<SubRouter>
)

data class SubRouter(
    val fromToken: OkxToken,
    val toToken: OkxToken
)

data class OkxToken(
    val tokenContractAddress: String
)