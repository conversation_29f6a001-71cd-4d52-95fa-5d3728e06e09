package io.iotex.iopay.data.bean

import com.google.gson.Gson

data class StockBean(
    val prices: ArrayList<ArrayList<Float>>
)

fun getStockList(json: String): ArrayList<Float> {
    val price = ArrayList<Float>()
    val stock = runCatching { Gson().fromJson(json, StockBean::class.java) }.getOrNull()
    stock?.prices?.forEach {
        if (it.size == 2) {
            price.add(it[1])
        }
    }
    return price
}

data class TokenPrice(
    val symbol:String,
    val price:String
)