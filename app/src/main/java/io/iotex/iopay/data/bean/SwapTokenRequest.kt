package io.iotex.iopay.data.bean


data class SwapToken(
    val address:String,
    val decimals:Int,
)
data class Slippage(
    val numerator:Int,
    val denominator:Int//10000
)
data class SwapTokenRequest(
    val chainId:Int,
    val protocols:String,
    val token0:SwapToken,
    val token1:SwapToken,
    val recipient:String,
    val amount:String,
    val slippage:Slippage,
    val tradeType:String,//EXACT_INPUT
)

data class SwapRouter(
    val protocol:String,
    val tokenPath:ArrayList<TokenPath>,
)
data class TokenPath(
    val chainId:Int,
    val address:String,
)
data class Quote(
    val numerator:String
)
data class Trade(
    val priceImpact:String
)
data class MethodParameters(
    val calldata:String,
    val value:String,
    val to:String,
)
data class SwapTokenResp(
    val route:ArrayList<SwapRouter>,
    val quote:Quote,
    val trade:Trade,
    val methodParameters:MethodParameters,
)