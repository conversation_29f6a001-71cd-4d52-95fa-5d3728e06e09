package io.iotex.iopay.data.bean


data class TokenListResult(
    val tokens:List<TokenDecimals>?
){
    data class TokenDecimals(
        val address:String,
        val logoURI:String,
        val decimals:Int
    )
}

data class SourceToken(
    val id :String?,
    val contract :String?,
    val logo :String?,
    val name :String?,
    val symbol :String?,
    val rank_point :String?,
    val market_cap :String?,
    val current_price :String?,
    val decimals :String?,
    val is_depin_token :Boolean?,
    val is_official :Boolean?,
    val tags :List<String>?,
    val total_liquidity_usd :String?,
    val weight :Int?,
    val website :String?,
    val price_change_24h :String?,
    val sparkline_in_7d :Any?,
    val custom :Custom?,
){
    data class Custom(
        val meme:Boolean,
        val robotpump:Boolean,
        val mimo_disabled:Boolean,
        val category: ArrayList<String>?
    )
}
