package io.iotex.iopay.data.bean

import kotlin.math.floor


data class GasStrategies(
    val limitInflationFactor: Float = 1.15f,
    val displayLimitInflationFactor: Float = 1.15f,
    val priceInflationFactor: Float = 1.5f,
    val percentileThresholdFor1559Fee: Float = 75f,
    val minPriorityFeeGwei: Float = 2f,
    val maxPriorityFeeGwei: Float = 9f,
)

data class UniSwapTokenRequest(
    val amount: String,
    val swapper: String,
    val tokenIn: String,
    val tokenInChainId: Int?,
    val tokenOut: String,
    val tokenOutChainId: Int?,
    val type: String,
    val gasStrategies: List<GasStrategies> = arrayListOf(GasStrategies()),
    val urgency: String = "normal",
    val protocols: List<String> = arrayListOf("V4", "V3", "V2"),
    val autoSlippage: String = "DEFAULT",
)

data class UniToken(
    val amount:String,
    val token:String,
)
data class RouterToken(
    val chainId:Int,
    val address:String
)
data class RouterTokenPair(
    val tokenIn:RouterToken,
    val tokenOut:RouterToken
)
data class UniQuote(
    val input:UniToken,
    val output:UniToken,
    val priceImpact:String,
    val gasFee:String,
    val route:ArrayList<ArrayList<RouterTokenPair>>,
)
data class UniSwapTokenResp(
    val quote:Any,
    val permitData: Any?
)

data class UniApprovalRequest(
    val walletAddress:String,
    val amount:String,
    val token:String,
    val chainId:Int,
    val tokenOut:String,
    val tokenOutChainId:Int,
    val includeGasInfo:Boolean = true,
    val gasStrategies: List<GasStrategies> = arrayListOf(GasStrategies())
)

data class Approval(
    val to: String,
    val data: String,
    val value: String
)
data class UniApprovalResp(
    val approval: Approval?
)

data class UniSwapRequest(
    val quote: Any,
    val permitData: Any?,
    val signature:String?,
    val simulateTransaction:Boolean= true,
    val deadline:Double= floor((System.currentTimeMillis() / 1000f + 1800f).toDouble()),
    val gasStrategies: List<GasStrategies> = arrayListOf(GasStrategies()),
    val refreshGasPrice:Boolean =  true,
    val urgency:String =  "normal",
)
data class UniSwap(
    val data:String,
    val to:String,
    val value:String,
)
data class UniSwapResp(
    val swap:UniSwap
)