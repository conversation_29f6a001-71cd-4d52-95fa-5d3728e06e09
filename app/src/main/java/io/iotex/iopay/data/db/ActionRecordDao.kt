package io.iotex.iopay.data.db

import androidx.room.*
import io.iotex.iopay.util.Config

@Dao
interface ActionRecordDao {

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertOrReplace(vararg actionRecord: ActionRecordEntry)

    @Query("select * from ActionRecordEntry where chain_id = :chainId and timestamp = :timestamp limit 1")
    fun queryByTimestamp(chainId: Int, timestamp: String): ActionRecordEntry?

    @Query("select * from ActionRecordEntry where chain_id = :chainId and hash like '%'||:hash||'%' limit 1")
    fun queryByHash(chainId: Int, hash: String): ActionRecordEntry?

    @Query("select * from ActionRecordEntry where (`from` = :walletAddress or (`to` = :walletAddress and chain_id = ${Config.IOTEX_CHAIN_ID} and (type = $ACTION_TYPE_TRANSFER or type = $ACTION_TYPE_NFT))) order by timestamp desc limit 500")
    fun queryAll(walletAddress: String): List<ActionRecordEntry>

    @Query("select * from ActionRecordEntry where (`from` = :walletAddress or (`to` = :walletAddress and chain_id = ${Config.IOTEX_CHAIN_ID} and (type = $ACTION_TYPE_TRANSFER or type = $ACTION_TYPE_NFT))) and chain_id = :chainId order by timestamp desc limit 500")
    fun queryAllByChainId(walletAddress: String, chainId: Int): List<ActionRecordEntry>

    @Query("select * from ActionRecordEntry where (`from` = :walletAddress or (`to` = :walletAddress and chain_id = ${Config.IOTEX_CHAIN_ID} and (type = $ACTION_TYPE_TRANSFER or type = $ACTION_TYPE_NFT))) and chain_id = :chainId and contract = :contract order by timestamp desc limit 500")
    fun queryByContract(walletAddress: String, chainId: Int, contract: String): List<ActionRecordEntry>

    @Query("select * from ActionRecordEntry where `from` = :walletAddress and chain_id = :chainId and contract = :contract and nft_token_id = :tokenId limit 1")
    fun queryByNftTokenId(walletAddress: String, chainId: Int, contract: String, tokenId: String): ActionRecordEntry?
}