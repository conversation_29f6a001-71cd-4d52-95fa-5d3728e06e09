package io.iotex.iopay.data.db

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import com.blankj.utilcode.util.Utils
import io.iotex.iopay.R
import io.iotex.iopay.repo.TransferEntryMapRepo
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

@Entity(tableName = "ActionRecordEntry")
data class ActionRecordEntry(
    @PrimaryKey
    val timestamp: String,
    @ColumnInfo(name = "chain_id")
    val chainId: Int,
    var hash: String,
    var nonce: String,
    val from: String,
    var to: String,
    var value: String,
    var data: String,
    @ColumnInfo(name = "gas_price")
    var gasPrice: String,
    @ColumnInfo(name = "gas_limit")
    var gasLimit: String,
    @ColumnInfo(name = "max_priority_fee")
    var maxPriorityFee: String,
    @ColumnInfo(name = "max_fee_per_gas")
    var maxFeePerGas: String,
    var status: Int,
    var type: Int,
    val contract: String,
    var method: String,
    var name: String,
    @ColumnInfo(name = "nft_token_id")
    var nftTokenId: String,
    var nftTokenType: String,
    var symbol: String,
    val decimals: String,
    var origin: String,
    var cancel: Boolean,
){
    fun getMethodName(callBack:(String)->Unit){
        if (type == ACTION_TYPE_SWAP) {
            return callBack.invoke(Utils.getApp().getString(R.string.xrc_swap))
        }
        when (type) {
            ACTION_TYPE_TRANSFER,
            ACTION_TYPE_NFT -> {
                callBack.invoke(Utils.getApp().getString(R.string.transfer))
            }

            ACTION_TYPE_DELEGATE -> {
                callBack.invoke(Utils.getApp().getString(R.string.stake_change_candidate))
            }

            ACTION_TYPE_STAKE -> {
                callBack.invoke(Utils.getApp().getString(R.string.create_stake))
            }

            ACTION_TYPE_RESTAKE_UNLOCK,
            ACTION_TYPE_RESTAKE_LOCK,
            ACTION_TYPE_RESTAKE -> {
                callBack.invoke(Utils.getApp().getString(R.string.stake_method_edit))
            }

            ACTION_TYPE_DEPOSIT_STAKE -> {
                callBack.invoke(Utils.getApp().getString(R.string.stake_add_deposit))
            }

            ACTION_TYPE_UNSTAKE -> {
                callBack.invoke(Utils.getApp().getString(R.string.unstake))
            }

            ACTION_TYPE_WITHDRAW -> {
                callBack.invoke(Utils.getApp().getString(R.string.withdraw))
            }

            ACTION_TYPE_TRANSFER_BUCKET -> {
                callBack.invoke(Utils.getApp().getString(R.string.transfer_bucket))
            }

            ACTION_TYPE_CLAIM -> {
                callBack.invoke(Utils.getApp().getString(R.string.claim_vita))
            }

            ACTION_TYPE_BID -> {
                callBack.invoke(Utils.getApp().getString(R.string.bid_vita))
            }

            ACTION_TYPE_APPROVE -> {
                callBack.invoke(Utils.getApp().getString(R.string.approve))
            }

            ACTION_TYPE_BOUND_EMAIL -> {
                callBack.invoke(Utils.getApp().getString(R.string.setup_email))
            }

            ACTION_TYPE_COMPOUND_REGISTER -> {
                callBack.invoke(Utils.getApp().getString(R.string.compound_method_register))
            }

            ACTION_TYPE_COMPOUND_UNREGISTER -> {
                callBack.invoke(Utils.getApp().getString(R.string.compound_method_unregister))
            }

            ACTION_TYPE_MIGRATE -> {
                callBack.invoke(Utils.getApp().getString(R.string.stake_migrate))
            }

            else -> {
                MainScope().launch {
                    val name = withContext(Dispatchers.IO){
                        TransferEntryMapRepo().querySignatures(method)
                    }
                    callBack.invoke(name)
                }
            }
        }
    }

    fun getMethodNameEn(callBack:(String)->Unit){
        when (type) {
            ACTION_TYPE_TRANSFER,
            ACTION_TYPE_NFT -> {
                "Transfer"
            }

            ACTION_TYPE_DELEGATE -> {
                "Change Delegate"
            }

            ACTION_TYPE_STAKE -> {
                "Create Stake"
            }

            ACTION_TYPE_RESTAKE_UNLOCK,
            ACTION_TYPE_RESTAKE_LOCK,
            ACTION_TYPE_RESTAKE -> {
                "Edit"
            }

            ACTION_TYPE_DEPOSIT_STAKE -> {
                "Add Stake"
            }

            ACTION_TYPE_UNSTAKE -> {
                "UnStake"
            }

            ACTION_TYPE_WITHDRAW -> {
                "Withdraw"
            }

            ACTION_TYPE_TRANSFER_BUCKET -> {
                "Transfer Bucket"
            }

            ACTION_TYPE_APPROVE -> {
                "Approve"
            }

            ACTION_TYPE_BOUND_EMAIL -> {
                "Set up E-mail"
            }

            ACTION_TYPE_MIGRATE -> {
                "Migrate"
            }

            else -> {
                MainScope().launch {
                    val name = withContext(Dispatchers.IO){
                        TransferEntryMapRepo().querySignatures(method)
                    }
                    callBack.invoke(name)
                }
            }
        }
    }

    fun getChainImage(chainId: Int,callBack:(String)->Unit){
        MainScope().launch {
            val logo = withContext(Dispatchers.IO){
                AppDatabase.getInstance(Utils.getApp()).rpcNetworkDao().queryAllRPCNetwork()
                    .find { it.chainId == chainId }?.logo?:""
            }
            callBack.invoke(logo)
        }
    }

    fun getImageResource():Int{
        return when (type) {
            ACTION_TYPE_APPROVE -> {
                R.drawable.icon_action_approve
            }

            ACTION_TYPE_DEPOSIT_STAKE -> {
                R.drawable.icon_action_deposit
            }

            ACTION_TYPE_TRANSFER_BUCKET -> {
                R.drawable.icon_action_transfer_stake
            }

            ACTION_TYPE_STAKE,
            ACTION_TYPE_BOUND_EMAIL -> {
                R.drawable.icon_action_create_stake
            }
            ACTION_TYPE_DELEGATE -> {
                R.drawable.icon_action_delegate
            }
            ACTION_TYPE_WITHDRAW -> {
                R.drawable.icon_action_withdraw
            }
            ACTION_TYPE_RESTAKE -> {
                R.drawable.icon_action_restake
            }
            ACTION_TYPE_UNSTAKE -> {
                R.drawable.icon_action_unstake
            }

            else -> {
                R.drawable.icon_action_execution
            }
        }
    }

    fun getStatusName():String{
        return when (status) {
            STATUS_WAITING -> {
                Utils.getApp().getString(R.string.waiting)
            }

            STATUS_FAILED -> {
                Utils.getApp().getString(R.string.failed)
            }

            STATUS_SUCCESS -> {
                Utils.getApp().getString(R.string.success)
            }
            else -> {
                ""
            }
        }
    }
}

const val STATUS_SUCCESS = 1
const val STATUS_FAILED = 0
const val STATUS_WAITING = -1

const val ACTION_TYPE_TRANSFER = 1
const val ACTION_TYPE_DAPP = 2
const val ACTION_TYPE_STAKE = 3
const val ACTION_TYPE_RESTAKE = 4
const val ACTION_TYPE_CLAIM = 5
const val ACTION_TYPE_DELEGATE = 6
const val ACTION_TYPE_NFT = 7
const val ACTION_TYPE_BID = 8
const val ACTION_TYPE_DEPOSIT_STAKE = 9
const val ACTION_TYPE_TRANSFER_BUCKET = 10
const val ACTION_TYPE_SWAP = 11
const val ACTION_TYPE_UNSTAKE = 12
const val ACTION_TYPE_WITHDRAW = 13
const val ACTION_TYPE_COMPOUND_REGISTER = 14
const val ACTION_TYPE_COMPOUND_UNREGISTER = 15
const val ACTION_TYPE_BOUND_EMAIL = 16
const val ACTION_TYPE_APPROVE = 17
const val ACTION_TYPE_RESTAKE_UNLOCK = 18
const val ACTION_TYPE_RESTAKE_LOCK = 19
const val ACTION_TYPE_BITCOIN_TRANSFER = 20
const val ACTION_TYPE_MIGRATE = 21