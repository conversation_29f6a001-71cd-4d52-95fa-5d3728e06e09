package io.iotex.iopay.data.db

import androidx.room.*

@Dao
interface AddressBookDao {

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertOrReplace(item: AddressBookEntry)

    @Delete
    fun delete(item: AddressBookEntry)

    @Query("select * from AddressBook")
    fun queryAll(): List<AddressBookEntry>

    @Query("select * from AddressBook where address = :address limit 1")
    fun queryByAddress(address: String): AddressBookEntry?

    @Query("select * from AddressBook where name like '%'||:name||'%'")
    fun queryLikeName(name: String): List<AddressBookEntry>

    @Query("select * from AddressBook where name = :name")
    fun queryByName(name: String): AddressBookEntry?
}