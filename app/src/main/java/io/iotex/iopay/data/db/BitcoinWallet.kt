package io.iotex.iopay.data.db

import androidx.annotation.IntDef
import androidx.room.ColumnInfo
import androidx.room.Entity

@Entity(tableName = "BitcoinWallet", primaryKeys = ["bitcoin_address", "evm_address"])
data class
BitcoinWallet(

    @ColumnInfo(name = "bitcoin_address")
    val bitcoinAddress: String,

    @ColumnInfo(name = "evm_address")
    val evmAddress: String,

    @ColumnInfo(name = "chain_id")
    val chainId: Int,

    @ColumnInfo(name = "address_type")
    val addressType: Int,

    @ColumnInfo(name = "type_name")
    val typeName: String,

    val path: String
)

const val AddressType_Legacy = 1
const val AddressType_NativeSegwit = 2
const val AddressType_NestedSegwit = 3
const val AddressType_Taproot = 4
@Target(AnnotationTarget.VALUE_PARAMETER, AnnotationTarget.FIELD, AnnotationTarget.FUNCTION)
@MustBeDocumented
@IntDef(
    AddressType_Legacy,
    AddressType_NativeSegwit,
    AddressType_NestedSegwit,
    AddressType_Taproot,
)
@Retention(AnnotationRetention.SOURCE)
annotation
class BitcoinAddressType