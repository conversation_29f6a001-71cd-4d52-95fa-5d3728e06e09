package io.iotex.iopay.data.db

import androidx.room.*
import androidx.room.OnConflictStrategy.Companion.IGNORE
import io.iotex.iopay.util.WalletHelper

@Dao
interface BitcoinWalletDao {

    @Insert(onConflict = IGNORE)
    fun insertIfNotExists(vararg bitcoinWallet: BitcoinWallet)

    @Delete
    fun deleteWallet(bitcoinWallet: BitcoinWallet): Int

    @Query("select * from BitcoinWallet where evm_address = :address and chain_id = :chainId")
    fun queryByEvmAddress(address: String, chainId: Int): List<BitcoinWallet>

    @Query("select * from BitcoinWallet where evm_address = :address and address_type = :addressType and chain_id = :chainId")
    fun queryByEvmAddressAndType(address: String, @BitcoinAddressType addressType: Int, chainId: Int): BitcoinWallet?

}