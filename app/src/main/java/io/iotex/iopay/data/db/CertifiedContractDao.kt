package io.iotex.iopay.data.db

import androidx.room.*

@Dao
interface CertifiedContractDao {

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertIfNonExist(entry: CertifiedContractEntry)

    @Update
    fun update(entry: CertifiedContractEntry)

    @Query("select * from CertifiedContractEntry where contract = :contract limit 1")
    fun queryByContract(contract: String): CertifiedContractEntry?
}