package io.iotex.iopay.data.db

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query

@Dao
interface ContractErrorMsgDao {

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertIfNonExist(entry: ContractErrorMsgEntry)

    @Query("select * from ContractErrorMsgEntry where error = :error limit 1")
    fun queryByError(error: String): ContractErrorMsgEntry?
}