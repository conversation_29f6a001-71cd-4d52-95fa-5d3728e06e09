package io.iotex.iopay.data.db

import androidx.room.*

@Dao
interface DAppRecordDao {

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertRecord(record:DAppRecord): Long

    @Query("select * from DAppRecord order by time desc")
    fun queryAllDAppRecords(): List<DAppRecord>

    @Query("select * from DAppRecord where  url like '%'||:url||'%'")
    fun searchRecords(url: String): List<DAppRecord>?

    @Query("DELETE FROM DAppRecord")
    fun deleteAllDAppRecord()
}