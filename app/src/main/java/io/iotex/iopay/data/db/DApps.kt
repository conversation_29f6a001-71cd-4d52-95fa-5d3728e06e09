package io.iotex.iopay.data.db

import androidx.room.Entity
import androidx.room.PrimaryKey
import io.iotex.iopay.IoPayApplication

@Entity(tableName = "DApps")
data class DApps(
    @PrimaryKey
    var url: String,
    var title: String,
    var logo: String,
    var named: Boolean,
    var category: String,
    var content: String,
    var contentCN: String,
    var lastTime: Long,
    var intervalDay: Int,
    var count: Int,
    var weightValue: Double,
    var weight: Int,
    var chains: String = ""
) {

    fun getDisplayContent(): String {
        return if (IoPayApplication.getAppContext().resources.configuration.locale.country == "CN") contentCN else content
    }
}