package io.iotex.iopay.data.db

import androidx.room.*

@Dao
interface DAppsDao {

    @Insert(onConflict = OnConflictStrategy.IGNORE)
    fun insertIfNonExist(dApps: DApps): Long

    @Update
    fun updateDAppSort(dApps: DApps)

    @Query("select * from DApps where url = :url")
    fun find(url: String): DApps?

    @Query("select * from DApps where  title like '%'||:query||'%'")
    fun searchDApps(query: String): List<DApps>?

    @Query("select * from DApps order by weightValue desc")
    fun queryAll(): List<DApps>

    @Query("select * from DApps order by lastTime desc limit 4")
    fun queryDAppByLast(): List<DApps>

    @Delete
    fun deleteDAppSort(wallet: DApps): Int
}