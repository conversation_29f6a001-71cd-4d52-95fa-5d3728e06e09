package io.iotex.iopay.data.db

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import io.iotex.iopay.IoPayApplication

@Entity(tableName = "DappPromote")
data class DappPromote(
    @PrimaryKey
    val id: String,
    val title: String,
    val logo: String,
    val chain: String,
    val url: String,
    @ColumnInfo(name = "promote_content")
    val promoteContent: String,
    @ColumnInfo(name = "promote_content_cn")
    val promoteContentCN: String,
    val promote: Boolean,
    val token: String
) {
    fun displayPromoteContent(): String {
        return if (IoPayApplication.getAppContext().resources.configuration.locale.country == "CN")
            promoteContentCN else promoteContent
    }
}