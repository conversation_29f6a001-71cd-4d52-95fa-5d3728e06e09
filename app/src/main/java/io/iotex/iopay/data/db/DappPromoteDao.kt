package io.iotex.iopay.data.db

import androidx.room.*

@Dao
interface DappPromoteDao {

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertIfNonExist(dappPromote: DappPromote): Long

    @Query("select * from DappPromote")
    fun queryAll(): MutableList<DappPromote>?

    @Query("select * from DappPromote where id == :id and promote limit 1")
    fun queryPromoteDapp(id: String): DappPromote?

    @Query("DELETE from DappPromote")
    fun deleteAll()
}