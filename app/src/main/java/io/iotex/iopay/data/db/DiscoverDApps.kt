package io.iotex.iopay.data.db

import androidx.room.Entity
import androidx.room.PrimaryKey
import io.iotex.base.language.MultiLanguage
import io.iotex.iopay.IoPayApplication
import java.util.*

@Entity(tableName = "DiscoverDApps")
data class DiscoverDApps(
    @PrimaryKey
    var url: String,
    var img_url: String,
    var content_cn: String,
    var content: String,
    var chains: String,
    var id: String,
    var tags: String,
    var title: String,
    var popularity: String,
    var weight: Int
){
    fun getDisplayContent(): String {
        return if (IoPayApplication.getAppContext().resources.configuration.locale.country == "CN"
            && content_cn.isNotEmpty()
        ) content_cn else content
    }
}