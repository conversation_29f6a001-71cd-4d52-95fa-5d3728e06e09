package io.iotex.iopay.data.db

import androidx.room.*

@Dao
interface DiscoverDAppsDao {

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertDApp(dApps: DiscoverDApps): Long

    @Query("select * from DiscoverDApps where url = :url")
    fun findDApp(url: String): DiscoverDApps?

    @Query("select * from DiscoverDApps where tags like '%'||:tags||'%' and chains like '%'||:chains||'%' order by weight desc")
    fun queryByCategoryChains(tags: String,chains: String): List<DiscoverDApps>?

    @Query("select * from DiscoverDApps where popularity like '%'||:popularity||'%' order by weight desc")
    fun queryByPopularity(popularity: String): List<DiscoverDApps>?

    @Query("select * from DiscoverDApps where url =:url order by weight desc")
    fun queryByUrl(url: String): List<DiscoverDApps>?

    @Query("select * from DiscoverDApps where url like '%'||:host||'%' order by weight desc")
    fun queryLikeHost(host: String): List<DiscoverDApps>?

    @Query("select * from DiscoverDApps where  title like '%'||:query||'%' limit 100")
    fun searchDApps(query: String): List<DiscoverDApps>?

    @Query("select * from DiscoverDApps")
    fun queryAllDApps(): Array<DiscoverDApps>?

    @Query("select count(*) from DiscoverDApps")
    fun count(): Int

    @Query("DELETE FROM DiscoverDApps")
    fun deleteAllDApp()

}