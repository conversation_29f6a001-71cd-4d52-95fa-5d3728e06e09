package io.iotex.iopay.data.db

import androidx.lifecycle.LiveData
import androidx.room.*

@Dao
interface ERC20Dao {

    @Insert(onConflict = OnConflictStrategy.ABORT)
    fun insertIfNonExist(erc20: ERC20Entry)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertOrReplace(vararg erc20: ERC20Entry)

    @Update
    fun update(vararg erc20: ERC20Entry)

    @Query("select count(*) from erc20")
    fun count(): Int

    @Delete
    fun delete(erc20: ERC20Entry)

    @Query("select * from erc20 where wallet_address = :walletAddress and chain_id = :chainId")
    fun queryAll(walletAddress: String, chainId: Int): List<ERC20Entry>

    @Query("select * from erc20 where wallet_address = :walletAddress and chain_id = :chainId and (symbol like :str or name like :str)")
    fun queryByLike(walletAddress: String, chainId: Int, str: String): List<ERC20Entry>

    @Query("select * from erc20 where wallet_address = :walletAddress and chain_id = :chainId and status = :status")
    fun queryByStatus(walletAddress: String, chainId: Int, status: Int): List<ERC20Entry>

    @Query("select * from erc20 where wallet_address = :walletAddress and chain_id = :chainId and status = :status")
    fun queryByStatusLiveData(walletAddress: String, chainId: Int, status: Int): LiveData<List<ERC20Entry>>

    @Query("select * from erc20 where wallet_address = :walletAddress and chain_id = :chainId order by weight desc limit (:page*:size), :size")
    fun queryByLimit(walletAddress: String, chainId: Int, page: Int, size: Int): List<ERC20Entry>

    @Query("select * from erc20 where wallet_address = :walletAddress and chain_id = :chainId and address = :address limit 1")
    fun queryByAddress(walletAddress: String, chainId: Int, address: String): ERC20Entry?

    @Query("select * from erc20 where wallet_address = :walletAddress and chain_id = :chainId and symbol = :symbol")
    fun queryBySymbol(walletAddress: String, chainId: Int, symbol: String): List<ERC20Entry>?

    @Query("select * from erc20 where chain_id = :chainId and address = :address")
    fun queryByTokenAddress(chainId: Int, address: String): List<ERC20Entry>?

    @Query("select * from erc20 where chain_id = :chainId and wallet_address = :walletAddress")
    fun queryByTokensByWallet(chainId: Int, walletAddress: String): List<ERC20Entry>

    @Query("select * from erc20 where wallet_address = :walletAddress and chain_id = :chainId and address = :address limit 1")
    fun queryByAddressLiveData(walletAddress: String, chainId: Int, address: String): LiveData<ERC20Entry>

    @Query("select * from erc20 where wallet_address = :walletAddress and chain_id = :chainId and chain_id = :chainId and is_meme = :isMeme")
    fun queryMeMeToken(walletAddress: String, chainId: Int, isMeme: Boolean): List<ERC20Entry>
}