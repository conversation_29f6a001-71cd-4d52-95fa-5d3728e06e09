package io.iotex.iopay.data.db

import androidx.room.ColumnInfo
import androidx.room.Entity

@Entity(tableName = "erc20", primaryKeys = ["wallet_address", "chain_id", "address"])
data class ERC20Entry(

    @ColumnInfo(name = "wallet_address")
    var walletAddress: String,

    @ColumnInfo(name = "chain_id")
    val chainId: Int,

    val address: String,

    val id: String,

    val name: String,

    var symbol: String,

    var decimals: Int,

    val logo: String,

    var balance: String,

    var price: String,

    @ColumnInfo(name = "is_tag")
    var isTag: Int = 0,

    @ColumnInfo(name = "is_top")
    var isTop: Int = 0,

    @ColumnInfo(name = "risk_status")
    var riskStatus: String = "",

    var status: Int = 0,

    val timestamp: String,

    val weight: Int = 0,

    val website: String = "",

    @ColumnInfo(name = "is_depin_token")
    val isDepinToken: Boolean = false,

    @ColumnInfo(name = "is_official")
    val isOfficial: Boolean = false,

    @ColumnInfo(name = "update_time")
    val updateTime: String = "",

    val price_change_24h: String = "",

    val sparkline_in_7d: String = "",

    val rank_point: String = "",

    @ColumnInfo(name = "pubkey")
    var pubkey: String = "",

    @ColumnInfo(name = "is_meme")
    val isMeme: Boolean = false,

    @ColumnInfo(name = "robot_pump")
    val isRobotPump: Boolean = false,
)

