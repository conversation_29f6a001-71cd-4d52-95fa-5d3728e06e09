package io.iotex.iopay.data.db

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.Ignore
import androidx.room.PrimaryKey

@Entity(tableName = "mnemonic")
data class Mnemonic(

    @PrimaryKey
    val id: String,

    @ColumnInfo(name = "encrypted_mnemonic")
    val encryptedMnemonic: String,

    @ColumnInfo(name = "coin_type")
    val coinType: Int,

    var name: String,

    var avatar: String = ""
) {

    @Ignore
    var walletList: List<Wallet>? = null

    @Ignore
    var totalBalance: String = "0"
}

