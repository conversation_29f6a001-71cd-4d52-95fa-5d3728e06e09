package io.iotex.iopay.data.db

import androidx.lifecycle.LiveData
import androidx.room.*

@Dao
interface MnemonicDao {

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertIfNotExists(mnemonic: Mnemonic): Long

    @Update
    fun update(mnemonic: Mnemonic)

    @Delete
    fun delete(mnemonic: Mnemonic)

    @Query("select * from mnemonic")
    fun queryAll(): List<Mnemonic>

    @Query("select * from mnemonic")
    fun queryAllLD(): LiveData<List<Mnemonic>>

    @Query("select * from mnemonic where id = :id limit 1")
    fun queryByMnemonicId(id: String): Mnemonic?

    @Query("select count(*) from mnemonic")
    fun count(): Int
}