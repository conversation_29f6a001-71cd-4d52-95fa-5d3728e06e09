package io.iotex.iopay.data.db

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "network_aa_config")
data class NetworkAAConfig(
    @PrimaryKey
    @ColumnInfo(name = "chain_id")
    val chainId: Int,

    @ColumnInfo(name = "entry_point")
    val entryPoint: String,

    val factory: String,

    @ColumnInfo(name = "bound_email")
    val boundEmail: String,

    @ColumnInfo(name = "email_service")
    val emailService: String,

    @ColumnInfo(name = "bundler_service")
    val bundlerService: String,

    @ColumnInfo(name = "paymaster_service")
    val paymasterService: String,

    val subgraph: String,

    @ColumnInfo(name = "force_use_paymaster")
    val forceUsePaymaster: Boolean
)