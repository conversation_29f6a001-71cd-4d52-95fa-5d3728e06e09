package io.iotex.iopay.data.db

import androidx.room.*

@Dao
interface NetworkAAConfigDao {

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insert(vararg config: NetworkAAConfig)

    @Query("select * from network_aa_config")
    fun queryAll(): List<NetworkAAConfig>

    @Query("select * from network_aa_config where chain_id = :chainId limit 1")
    fun queryByChainId(chainId: Int): NetworkAAConfig?
}