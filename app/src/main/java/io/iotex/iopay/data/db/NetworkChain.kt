package io.iotex.iopay.data.db

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken

@Entity(tableName = "NetworkChain")
data class NetworkChain(
    @PrimaryKey
    val name: String,
    val icon: String,
    val chain: String,
    @ColumnInfo(name = "chain_id")
    val chainId: String,
    val rpc: String,
    @ColumnInfo(name = "info_url")
    val infoURL: String,
    val symbol: String
)

val chainJson = "{\n" +
    "  0: \"kardia\",\n" +
    "  1: \"ethereum\",\n" +
    "  8: \"ubiq\",\n" +
    "  10: \"optimism\",\n" +
    "  19: \"songbird\",\n" +
    "  20: \"elastos\",  \n" +
    "  25: \"cronos\",\n" +
    "  30: \"rsk\",\n" +
    "  40: \"telos\",\n" +
    "  50: \"xdc\",\n" +
    "  52: \"csc\",\n" +
    "  55: \"zyx\",\n" +
    "  56: \"binance\",\n" +
    "  57: \"syscoin\",\n" +
    "  60: \"gochain\",\n" +
    "  61: \"ethereumclassic\",\n" +
    "  66: \"okexchain\",\n" +
    "  70: \"hoo\",\n" +
    "  82: \"meter\",\n" +
    "  87: \"nova network\",\n" +
    "  88: \"tomochain\",\n" +
    "  100: \"xdai\",\n" +
    "  106: \"velas\",\n" +
    "  108: \"thundercore\",\n" +
    "  122: \"fuse\",\n" +
    "  128: \"heco\",\n" +
    "  137: \"polygon\",\n" +
    "  200: \"xdaiarb\",\n" +
    "  246: \"energyweb\",\n" +
    "  250: \"fantom\",\n" +
    "  269: \"hpb\",\n" +
    "  288: \"boba\",\n" +
    "  321: \"kucoin\",\n" +
    "  336: \"shiden\",\n" +
    "  361: \"theta\",\n" +
    "  416: \"sx\",\n" +
    "  534: \"candle\",\n" +
    "  592: \"astar\",\n" +
    "  820: \"callisto\",\n" +
    "  888: \"wanchain\",\n" +
    "  1088: \"metis\",\n" +
    "  1231: \"ultron\",\n" +
    "  1284: \"moonbeam\",\n" +
    "  1285: \"moonriver\",\n" +
    "  2000: \"dogechain\",\n" +
    "  2020: \"ronin\",\n" +
    "  2222: \"kava\",\n" +
    "  4689: \"iotex\",\n" +
    "  5050: \"xlc\",\n" +
    "  5551: \"nahmii\",\n" +
    "  6969: \"tombchain\",\n" +
    "  7700: \"canto\",\n" +
    "  8217: \"klaytn\",\n" +
    "  9001: \"evmos\",\n" +
    "  10000: \"smartbch\",\n" +
    "  32659: \"fusion\",\n" +
    "  42161: \"arbitrum\",\n" +
    "  42170: \"arb-nova\",\n" +
    "  42220: \"celo\",\n" +
    "  42262: \"oasis\",\n" +
    "  43114: \"avalanche\",\n" +
    "  47805: \"rei\",\n" +
    "  55555: \"reichain\",\n" +
    "  71402: \"godwoken\",\n" +
    "  333999: \"polis\",\n" +
    "  420420: \"kekchain\",\n" +
    "  888888: \"vision\",\n" +
    "  1313161554: \"aurora\",\n" +
    "  1666600000: \"harmony\",\n" +
    "  11297108109: \"palm\",\n" +
    "  836542336838601: \"curio\"\n" +
    "}"

data class NetworkChainInfo(
    val name: String?,
    val chain: String?,
    val chainId: String?,
    val rpc: ArrayList<String>?,
    val infoURL: String?,
    val nativeCurrency: NativeCurrency?
){
    val icon:String
    get() {
        if(!chainId.isNullOrEmpty()) {
            val type = object : TypeToken<HashMap<Long,String>>(){}.type
            val map = Gson().fromJson<HashMap<Long,String>>(chainJson,type)
            val simpleName = map[chainId.toLong()]
            if(!simpleName.isNullOrEmpty()){
                return "https://icons.llamao.fi/icons/chains/rsz_${simpleName}.jpg"
            }
        }
        return ""
    }
}

data class NativeCurrency(
    val name: String?,
    val symbol: String?,
    val decimals: String?
)

