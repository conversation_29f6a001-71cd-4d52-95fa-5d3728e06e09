package io.iotex.iopay.data.db

import androidx.room.*

@Dao
interface NetworkChainDao {

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertOrReplace(networkChain: NetworkChain): Long

    @Query("select * from NetworkChain where name like :name||'%' or chain_id like :name||'%' or chain like :name||'%'")
    fun querySearch(name: String): List<NetworkChain>?

    @Query("select * from NetworkChain")
    fun queryAll(): List<NetworkChain>?


    @Query("select count(*) from NetworkChain")
    fun count(): Int

    @Query("select * from NetworkChain where chain_id like :chainId")
    fun queryNetworkByChainId(chainId:Int): NetworkChain?
}