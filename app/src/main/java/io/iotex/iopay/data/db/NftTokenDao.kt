package io.iotex.iopay.data.db

import androidx.room.*

@Dao
interface NftTokenDao {

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insert(vararg token: NftTokenEntry)

    @Update
    fun update(token: NftTokenEntry)

    @Query("select * from NftToken where wallet_address = :address and chain_id = :chainId and contract = :contract and token_id = :tokenId limit 1")
    fun queryToken(address: String, chainId: Int, contract: String, tokenId: String): NftTokenEntry?

    @Query("select * from NftToken where wallet_address = :address and chain_id = :chainId and type = :type")
    fun queryTokensByType(address: String, chainId: Int, type: String): List<NftTokenEntry>

    @Query("select * from NftToken where wallet_address = :address and chain_id = :chainId and contract = :contract")
    fun queryTokensByContract(address: String, chainId: Int, contract: String): List<NftTokenEntry>

    @Query("select * from NftToken where wallet_address = :address and chain_id = :chainId")
    fun queryTokens(address: String, chainId: Int): List<NftTokenEntry>

    @Query("select * from NftToken where wallet_address = :address and chain_id = :chainId limit :skip, :first")
    fun queryTokens(address: String, chainId: Int, skip: Int, first: Int): List<NftTokenEntry>

    @Query("select * from NftToken where wallet_address = :address and chain_id = :chainId and (name like '%'||:searchStr||'%' or contract like '%'||:searchStr||'%' or token_id like '%'||:searchStr||'%') limit :skip, :first")
    fun queryTokensByLike(address: String, chainId: Int, searchStr: String, skip: Int, first: Int): List<NftTokenEntry>

    @Query("DELETE FROM NftToken where wallet_address = :address and chain_id = :chainId")
    fun deleteTokens(address: String, chainId: Int)

    @Query("select count(*) from NftToken where wallet_address = :address and chain_id = :chainId and type = :type")
    fun count(address: String, chainId: Int, type: String): Int

    @Query("select count(*) from NftToken where wallet_address = :address and chain_id = :chainId")
    fun count(address: String, chainId: Int): Int
}