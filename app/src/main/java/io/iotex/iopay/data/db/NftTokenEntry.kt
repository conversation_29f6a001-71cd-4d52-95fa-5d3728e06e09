package io.iotex.iopay.data.db

import android.os.Parcelable
import androidx.room.ColumnInfo
import androidx.room.Entity
import io.iotex.iopay.util.Config
import kotlinx.parcelize.Parcelize

@Parcelize
@Entity(tableName = "NftToken", primaryKeys = ["contract", "token_id", "wallet_address"])
data class NftTokenEntry(
    var contract: String = "",

    @ColumnInfo(name = "token_id")
    var tokenId: String = "",

    var name: String = "",

    var symbol: String = "",

    @ColumnInfo(name = "wallet_address")
    var walletAddress: String = "",

    @ColumnInfo(name = "chain_id")
    var chainId: Int = Config.IOTEX_CHAIN_ID,

    @ColumnInfo(name = "token_url")
    var tokenUrl: String = "",

    val type: String = NFT_TYPE_721,

    val amount: String = "0",

    val sbt: Boolean = false
) : Parcelable

const val NFT_TYPE_721 = "721"
const val NFT_TYPE_1155 = "1155"