package io.iotex.iopay.data.db

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import java.util.*

@Entity(tableName = "notification")
data class Notification(
    @ColumnInfo(name = "title") val title: String = "",
    @ColumnInfo(name = "content") val content: String = "",
    @ColumnInfo(name = "customContent") var customContent: String? = null
) {
    @PrimaryKey(autoGenerate = true)
    @ColumnInfo(name = "id")
    var id: Long = 0
    @ColumnInfo(name = "msg_id")
    var msgId: Long = 0
    @ColumnInfo(name = "address")
    var address: String = ""
    @ColumnInfo(name = "activity")
    var activity: String? = null
    @ColumnInfo(name = "notification_action_type")
    var notificationActionType: Int = 0
    @ColumnInfo(name = "read")
    var read: Boolean = false
    @ColumnInfo(name = "update_time")
    var updateTime: Long = Calendar.getInstance().timeInMillis
}