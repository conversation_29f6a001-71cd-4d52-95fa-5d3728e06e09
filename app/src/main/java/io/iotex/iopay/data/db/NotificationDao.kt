package io.iotex.iopay.data.db

import androidx.lifecycle.LiveData
import androidx.room.Dao
import androidx.room.Insert
import androidx.room.Query
import androidx.room.Update

@Dao
interface NotificationDao {

    @Insert
    fun insert(notification: Notification): Long

    @Query("select * from notification where id = :notificationId")
    fun find(notificationId: Long): Notification

    @Update
    fun update(notification: Notification)

    @Query("DELETE FROM notification WHERE id = :notificationId")
    fun delete(notificationId: Long)

    @Query("select * from notification order by update_time desc limit :offset,:limit")
    fun pagination(offset: Int, limit: Int): LiveData<List<Notification>>

    @Query("select * from notification order by update_time desc")
    fun pagination(): LiveData<List<Notification>>

    @Query("select count(*) from notification")
    fun count(): Int
    @Query("select count(*) from notification where read = 0")
    fun unreadCount(): LiveData<Int>
    @Query("DELETE from notification")
    fun deleteAll()
}