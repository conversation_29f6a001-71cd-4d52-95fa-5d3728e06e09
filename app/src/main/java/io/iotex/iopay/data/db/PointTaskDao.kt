package io.iotex.iopay.data.db

import androidx.room.*

@Dao
interface PointTaskDao {

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insert(pointTaskEntry: PointTaskEntry)

    @Query("select * from PointTaskEntry where action_type = :actionType limit 1")
    fun queryByActionType(actionType: String): PointTaskEntry?

    @Query("select * from PointTaskEntry where transaction_type = :transaction limit 1")
    fun queryByTransaction(transaction: String): PointTaskEntry?
}