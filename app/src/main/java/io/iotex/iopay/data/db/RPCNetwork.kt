package io.iotex.iopay.data.db

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import io.iotex.iopay.data.UserStore
import io.iotex.iopay.util.Config

@Entity(tableName = "RPCNetwork")
data class RPCNetwork(
    var id: String,
    @PrimaryKey
    @ColumnInfo(name = "chain_id")
    var chainId: Int,
    var name: String,

    @ColumnInfo(name = "short_name")
    var shortName: String,

    var platform: String,
    var rpc: String,
    var explorer: String,
    var logo: String,

    @ColumnInfo(name = "currency_symbol")
    var currencySymbol: String,

    @ColumnInfo(name = "currency_name")
    var currencyName: String,

    @ColumnInfo(name = "currency_logo")
    var currencyLogo: String,

    @ColumnInfo(name = "currency_decimals")
    var currencyDecimals: Int,

    @ColumnInfo(name = "currency_price")
    var currencyPrice: String,

    @ColumnInfo(name = "token_alias")
    var tokensAlias: String,

    @Deprecated("delete")
    @ColumnInfo(name = "multicall_address")
    var multicallAddr: String,

    @ColumnInfo(name = "swap_url")
    var swapUrl: String,

    @ColumnInfo(name = "dev_mode")
    var devMode: Int,

    @Deprecated("delete")
    @ColumnInfo(name = "theme_bg")
    var themeBg: String,

    @Deprecated("delete")
    @ColumnInfo(name = "theme_bg2")
    var themeBg2: String,

    @ColumnInfo(name = "networkName")
    var networkName: String,

    @ColumnInfo(name = "networkOrder")
    var networkOrder: Int = 0,

    @ColumnInfo(name = "bg_image")
    var bg_image: String  = "",

    @Deprecated("delete")
    @ColumnInfo(name = "bg_color_start")
    var bg_color_start: String  = "",

    @Deprecated("delete")
    @ColumnInfo(name = "bg_color_end")
    var bg_color_end: String = "",

    val immutable: Boolean = true,

    @ColumnInfo(name = "token_approval_checker")
    var tokenApprovalChecker: String = "",

    @ColumnInfo(name = "chain_icon")
    var chainIcon: String = "",

    @ColumnInfo(name = "chain_icon_selected")
    var chainIconSelected: String = "",

    @ColumnInfo(name = "gas_station")
    var gasStation: String = "",

    @ColumnInfo(name = "theme_color")
    var themeColor: String = "",

    @ColumnInfo(name = "price_change_24h")
    var price_change_24h: String = "",

    @ColumnInfo(name = "sparkline_in_7d")
    var sparkline_in_7d: String = "",

    @ColumnInfo(name = "chain_icon_light")
    var chainIconLight: String = "",

    @ColumnInfo(name = "currency_id")
    var currencyId: String = "",

    @ColumnInfo(name = "dapp_chain")
    var dapp_chain: Boolean = false,

    @ColumnInfo(name = "token_category")
    var token_category: String = "",

    var frequency: Int = 5,

){
    fun getDAppChainIcon():String{
        return if(UserStore.isDarkTheme()) chainIcon else chainIconLight
    }

    fun isBitcoinNetwork(): Boolean {
        return chainId == Config.BITCOIN_MAIN_CHAIN_ID || chainId == Config.BITCOIN_TEST_CHAIN_ID
    }
}

const val MODE_DEV = 1
const val MODE_MAIN = 0


