package io.iotex.iopay.data.db

import androidx.room.*
import io.iotex.iopay.util.Config

@Dao
interface RPCNetworkDao {

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertRPCNetworkDao(rpcNetwork: RPCNetwork): Long

    @Update
    fun update(rpcNetwork: RPCNetwork)

    @Delete
    fun delete(rpcNetwork: RPCNetwork)

    @Query("select * from RPCNetwork where name = :name ")
    fun queryRPCNetwork(name: String): RPCNetwork?

    @Query("select * from RPCNetwork where chain_id = :chainId ")
    fun queryRPCNetworkByChainId(chainId: Int): RPCNetwork?

    @Query("select * from RPCNetwork order by networkOrder asc")
    fun queryAllRPCNetwork(): List<RPCNetwork>

    @Query("select * from RPCNetwork where name like '%'||:search||'%' order by networkOrder asc")
    fun queryRPCNetworkBySearch(search:String): List<RPCNetwork>

    @Query("select * from RPCNetwork where dev_mode = 0 order by networkOrder asc")
    fun queryMainNetwork(): List<RPCNetwork>

    @Query("select * from RPCNetwork where dev_mode = 0 and immutable = 1 order by networkOrder asc")
    fun queryMainNetworkImmutable(): List<RPCNetwork>

    @Query("select * from RPCNetwork where dapp_chain = 1 order by networkOrder asc")
    fun queryByDAppChain(): List<RPCNetwork>

    @Query("select * from RPCNetwork where platform = :platform limit 1")
    fun queryByPlatform(platform: String): RPCNetwork?

    @Query("select * from RPCNetwork where chain_id = ${Config.BITCOIN_TEST_CHAIN_ID} or chain_id= ${Config.BITCOIN_MAIN_CHAIN_ID}")
    fun queryBitcoinNetwork(): List<RPCNetwork>
}