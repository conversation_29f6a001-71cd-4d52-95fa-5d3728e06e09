package io.iotex.iopay.data.db

import androidx.annotation.IntDef
import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.Ignore
import androidx.room.PrimaryKey

@Entity(tableName = "RPCNetworkNode", primaryKeys = ["chain_id", "rpc_url"])
data class RPCNetworkNode(

    @ColumnInfo(name = "chain_id")
    val chainId: Int,

    @ColumnInfo(name = "rpc_url")
    var rpcUrl: String,

    @ColumnInfo(name = "rpc_status")
    @NetworkLevel
    var rpcStatus: Int = NETWORK_GENERAL,

    val immutable: Boolean = false,

    var active: Boolean = false
){
    @Ignore
    var name = ""
}

const val NETWORK_SMOOTH = 1
const val NETWORK_GENERAL = 2
const val NETWORK_POOR = 3

@Target(AnnotationTarget.VALUE_PARAMETER, AnnotationTarget.FIELD, AnnotationTarget.FUNCTION)
@MustBeDocumented
@IntDef(NETWORK_SMOOTH, NETWORK_GENERAL, NETWORK_POOR)
@kotlin.annotation.Retention(AnnotationRetention.SOURCE)
annotation
class NetworkLevel

