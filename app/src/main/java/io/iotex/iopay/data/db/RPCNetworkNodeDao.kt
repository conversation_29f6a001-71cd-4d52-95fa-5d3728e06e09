package io.iotex.iopay.data.db

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Update

@Dao
interface RPCNetworkNodeDao {

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertRPCNetworkDao(node: RPCNetworkNode): Long

    @Update
    fun update(node: RPCNetworkNode)

    @Delete
    fun delete(node: RPCNetworkNode)

    @Query("delete from RPCNetworkNode where chain_id = :chainId and immutable = 1")
    fun deleteImmutableNodes(chainId: Int)

    @Query("select * from RPCNetworkNode where chain_id = :chainId and immutable = 1")
    fun queryImmutableNodes(chainId: Int): List<RPCNetworkNode>

    @Query("select * from RPCNetworkNode where chain_id = :chainId ")
    fun queryRPCNodesByChainId(chainId: Int): List<RPCNetworkNode>

    @Query("select * from RPCNetworkNode where chain_id = :chainId and active = 1 limit 1")
    fun queryRPCNodeActivated(chainId: Int): RPCNetworkNode?

    @Query("select * from RPCNetworkNode where active = 1")
    fun queryAllRPCNodeActivated(): List<RPCNetworkNode>

    @Query("select * from RPCNetworkNode where rpc_url = :url and chain_id = :chainId")
    fun queryRPCNodeByUrl(chainId: Int, url: String): RPCNetworkNode?
}