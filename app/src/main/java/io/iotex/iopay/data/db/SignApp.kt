package io.iotex.iopay.data.db

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "SignApp")
data class SignApp(
    @ColumnInfo(name = "walletId") var walletId: Long,
    @ColumnInfo(name = "token") val token: String,
    @ColumnInfo(name = "source") val source: String,
    @ColumnInfo(name = "nonce") val nonce: String,
    @ColumnInfo(name = "createAt") var createAt: Long,
    @ColumnInfo(name = "expireAt") var expireAt: Long
) {
    @PrimaryKey(autoGenerate = true)
    @ColumnInfo(name = "id")
    var signId: Long = 0
}