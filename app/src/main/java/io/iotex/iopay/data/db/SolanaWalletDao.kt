package io.iotex.iopay.data.db

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query

@Dao
interface SolanaWalletDao {
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertorReplace(solanaWallet: SolanaWallet)

    @Query("select * from SolanaWallet where address_evm = :addressEvm")
    fun queryByAddressEvm(addressEvm: String): SolanaWallet?

    @Query("select * from SolanaWallet where public_key_base58 = :publicKeyBase58")
    fun queryByPublicKeyBase58(publicKeyBase58: String): SolanaWallet?
}