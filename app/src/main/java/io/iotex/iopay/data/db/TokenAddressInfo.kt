package io.iotex.iopay.data.db

import androidx.room.ColumnInfo
import androidx.room.Entity

@Entity(tableName = "TokenAddressInfo", primaryKeys = ["address"])
data class TokenAddressInfo(
    @ColumnInfo(name = "address") val address: String,
    @ColumnInfo(name = "name") val name: String,
    @ColumnInfo(name = "logo") val logo: String,
    @ColumnInfo(name = "type") val type: String,
    @ColumnInfo(name = "image_urls") val image_urls: String?,
    @ColumnInfo(name = "remark") val remark: String = ""
)