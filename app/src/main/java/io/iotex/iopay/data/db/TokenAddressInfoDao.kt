package io.iotex.iopay.data.db

import androidx.lifecycle.LiveData
import androidx.room.*

@Dao
interface TokenAddressInfoDao {

    @Insert
    fun insert(tokenAddressInfo: TokenAddressInfo)

    @Query("select * from TokenAddressInfo where type = :type")
    fun queryByType(type: String): Array<TokenAddressInfo>

    @Query("DELETE from TokenAddressInfo")
    fun deleteAll()

    @Query("select * from TokenAddressInfo where address = :address")
    fun queryByAddress(address: String): LiveData<TokenAddressInfo>

    @Query("select count(*) from TokenAddressInfo where address = :address ")
    fun isHasAddressInfo(address: String): Int

    @Query("select * from TokenAddressInfo where type = :type")
    fun queryAddressByType(type: String): Array<TokenAddressInfo>?

    @Query("select * from TokenAddressInfo")
    fun queryAllAddress(): Array<TokenAddressInfo>?

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertIfNonExist(TokenAddressInfo: TokenAddressInfo)

    @Delete
    fun delete(info: TokenAddressInfo): Int
}