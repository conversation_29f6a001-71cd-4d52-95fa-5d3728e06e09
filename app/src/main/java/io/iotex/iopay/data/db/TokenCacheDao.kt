package io.iotex.iopay.data.db

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query

@Dao
interface TokenCacheDao {

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertOrReplace(vararg token: TokenCacheEntry)

    @Query("select * from TokenCacheEntry where wallet_address = :walletAddress and chain_id = :chainId")
    fun queryByWallet(walletAddress: String, chainId: Int): List<TokenCacheEntry>

    @Query("select * from TokenCacheEntry where wallet_address = :walletAddress and chain_id = :chainId and likeStatus = :likeStatus")
    fun queryByStatus(walletAddress: String, chainId: Int, likeStatus: Int): List<TokenCacheEntry>

    @Query("select * from TokenCacheEntry where wallet_address = :walletAddress and chain_id = :chainId and address = :address")
    fun queryByAddress(walletAddress: String, chainId: Int, address: String): TokenCacheEntry?
}