package io.iotex.iopay.data.db

import androidx.room.ColumnInfo
import androidx.room.Entity

@Entity(tableName = "TokenCacheEntry", primaryKeys = ["wallet_address", "chain_id", "address"])
class TokenCacheEntry(

    @ColumnInfo(name = "wallet_address")
    var walletAddress: String,

    @ColumnInfo(name = "chain_id")
    val chainId: Int,

    val address: String,

    var balance: String = "",

    var likeStatus: Int = 0,

    @ColumnInfo(name = "pubkey")
    var pubkey: String = "",
)

const val LIKE_STATUS_LIKE = 1
const val LIKE_STATUS_NORMAL = 0
const val LIKE_STATUS_UNLIKE = 2