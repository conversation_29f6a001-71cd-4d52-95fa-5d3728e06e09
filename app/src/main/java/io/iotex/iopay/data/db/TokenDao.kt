package io.iotex.iopay.data.db

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query

@Dao
interface TokenDao {

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertOrReplace(vararg token: TokenEntry)

    @Query("select * from TokenEntry where chain_id = :chainId and (symbol like '%'||:search||'%' or name like '%'||:search||'%')")
    fun queryByLike(chainId: Int, search: String): List<TokenEntry>

    @Query("select * from TokenEntry where chain_id = :chainId and address = :address limit 1")
    fun queryByAddress(chainId: Int, address: String): TokenEntry?

    @Query("select * from TokenEntry where symbol = :symbol")
    fun queryBySymbol(symbol: String): List<TokenEntry>

    @Query("select * from TokenEntry where chain_id = :chainId")
    fun queryByChain(chainId: Int): List<TokenEntry>

    @Query("select * from TokenEntry where chain_id = :chainId and mimo_disabled = 0")
    fun querySwapToken(chainId: Int): List<TokenEntry>

    @Query("select * from TokenEntry where chain_id = :chainId and category like '%'||:category||'%'")
    fun queryCategoryToken(chainId: Int,category:String): List<TokenEntry>

    @Delete
    fun delete(token: TokenEntry)
}