package io.iotex.iopay.data.db

import androidx.room.ColumnInfo
import androidx.room.Entity

@Entity(tableName = "TrustDapp", primaryKeys = ["wallet_address", "dapp"])
open class TrustDapp(
    @ColumnInfo(name = "wallet_address")
    open var walletAddress: String = "",

    @ColumnInfo(name = "dapp")
    open var dapp: String = "",

    @ColumnInfo(name = "truest_time")
    open var truest_time: Long = 0,

    @ColumnInfo(name = "dappName")
    open var dappName: String = "",

    @ColumnInfo(name = "logo")
    open var logo: String = ""

)
