package io.iotex.iopay.data.db

import androidx.room.*

@Dao
interface TrustDappDao {

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertOrReplace(trustDApp: TrustDapp)

    @Query("DELETE from TrustDapp")
    fun deleteAll()

    @Query("select * from TrustDapp where wallet_address = :walletAddress and dapp = :host ")
    fun queryTrustDApp(walletAddress: String, host: String): TrustDapp?

    @Query("select * from TrustDapp where wallet_address = :walletAddress")
    fun queryAllTrustDapp(walletAddress: String): Array<TrustDapp>

    @Delete
    fun delete(trustDapp: TrustDapp): Int
}