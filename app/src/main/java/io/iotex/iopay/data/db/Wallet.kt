package io.iotex.iopay.data.db

import android.os.Parcelable
import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.Ignore
import androidx.room.PrimaryKey
import io.iotex.iopay.util.Constant
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.util.extension.toEvmAddress
import io.iotex.iopay.wallet.add.PATH_ETH
import io.iotex.iopay.wallet.add.PathType
import kotlinx.parcelize.Parcelize

@Parcelize
@Entity(tableName = "WalletEntry")
data class Wallet(
    @PrimaryKey
    @ColumnInfo(name = "address")
    var address: String,

    @ColumnInfo(name = "alias")
    var alias: String,

    @ColumnInfo(name = "password")
    val password: String,

    @ColumnInfo(name = "file")
    val file: String,

    @ColumnInfo(name = "is_watch")
    var isWatch: Boolean,

    @ColumnInfo(name = "timestamp")
    var timestamp: String,

    @ColumnInfo(name = "order")
    var order: Int = 0,

    @ColumnInfo(name = "mnemonic_id")
    val mnemonicId: String = "",

    @ColumnInfo(name = "address_index")
    val addressIndex: Int = 0,

    @ColumnInfo(name = "aa_salt")
    var aaSalt: String = "",

    @ColumnInfo(name = "email")
    var email: String = "",

    @ColumnInfo(name = "effective")
    var effective: Boolean = false,

    @ColumnInfo(name = "address_type")
    var addressType: Int = AddressType_NativeSegwit,

    @ColumnInfo(name = "path")
    val path: String = Constant.MNEMONIC_PATH_ETH,

    @ColumnInfo(name = "path_type")
    @PathType
    val pathType: Int = PATH_ETH
): Parcelable {

    @get:Ignore
    val avatar: String
        get() {
            return if(WalletHelper.isBitcoinNetwork()){
                WalletHelper.getAddressAvatar(getBitcoinWallet()?.bitcoinAddress?:"")
            }else{
                WalletHelper.getAddressAvatar(address.toEvmAddress())
            }
        }

    @Ignore
    var curBalance: String = "0"

    @Ignore
    var totalBalance: String = "0"

    @Ignore
    var solanaWallet: SolanaWallet? = null

    @Ignore
    var bitcoinWallets: List<BitcoinWallet> = emptyList()

    fun isMnemonicWallet() = mnemonicId.isNotBlank()

    fun isSolanaPrivateWallet():Boolean {
        return WalletHelper.isSolanaPrivatakey(file)
    }

    fun isEvmPrivateWallet():Boolean {
        return mnemonicId.isEmpty() && !isWatch && !isSolanaPrivateWallet() && !isAAWallet()
    }

    fun isAAWallet() = aaSalt.isNotBlank()

    fun isBitcoinWatchWallet(): Boolean {
        return isWatch && WalletHelper.isBitcoinAddress(address)
    }

    fun isEvmWatchWallet():Boolean{
        return isWatch && WalletHelper.isEvmAddress(address)
    }

    fun isSolanaWatchWallet():Boolean{
        return isWatch && WalletHelper.isSolanaAddress(address)
    }

    fun getBitcoinWallet(chainId: Int = WalletHelper.getCurChainId()): BitcoinWallet? {
        return bitcoinWallets.firstOrNull {
            it.evmAddress.equals(address, true) &&
            it.addressType == addressType &&
            it.chainId == chainId
        }
    }

    fun getCurNetworkAddress(chainId: Int = WalletHelper.getCurChainId()): String {
        return if (WalletHelper.isBitcoinNetwork(chainId)) {
            getBitcoinWallet()?.bitcoinAddress ?: address.toEvmAddress()
        } else if (WalletHelper.isSolanaNetwork(chainId)) {
            solanaWallet?.publicKeyBase58 ?: address.toEvmAddress()
        } else {
            address.toEvmAddress()
        }
    }

}