package io.iotex.iopay.data.db

import androidx.room.ColumnInfo
import androidx.room.Entity

@Entity(tableName = "WalletCache",primaryKeys = ["address", "chain_id"])
data class WalletCache (

    @ColumnInfo(name = "chain_id")
    var chainId: Int,

    @ColumnInfo(name = "address")
    var address: String,

    @ColumnInfo(name = "balance")
    var balance: String = "0",

    @ColumnInfo(name = "available_balance")
    var availableBalance: String = "0",

    @ColumnInfo(name = "stake")
    var stake: String = "",

    @ColumnInfo(name = "fee_gas")
    var feeGas: String = "",
)