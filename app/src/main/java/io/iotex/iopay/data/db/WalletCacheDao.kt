package io.iotex.iopay.data.db

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query

@Dao
interface WalletCacheDao {

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertOrUpdate(walletCache: WalletCache)

    @Query("select * from WalletCache where address = :address and chain_id = :chainId")
    fun queryWalletCache(address: String, chainId: Int): WalletCache?
}