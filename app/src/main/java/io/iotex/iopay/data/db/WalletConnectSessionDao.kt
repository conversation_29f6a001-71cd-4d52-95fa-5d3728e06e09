package io.iotex.iopay.data.db

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query


@Dao
interface WalletConnectSessionDao {

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertOrReplace(entry:WalletConnectSessionEntry)

    @Query("select * from WalletConnectSessionEntry")
    fun queryAll(): List<WalletConnectSessionEntry>

    @Query("select * from WalletConnectSessionEntry where topic = :topic")
    fun queryByTopic(topic: String): WalletConnectSessionEntry?

    @Delete
    fun delete(entry: WalletConnectSessionEntry)
}