package io.iotex.iopay.data.db

import androidx.room.Entity
import androidx.room.Ignore

@Entity(tableName = "WalletConnectSessionEntry", primaryKeys = ["url", "topic"])
data class WalletConnectSessionEntry(
    val topic: String,
    val logo: String = "",
    val name: String = "",
    val url: String = "",
    val address: String = "",
    val chains: String = "",
) {
    @Ignore
    var walletLogo: String = ""

    @Ignore
    var walletName: String = ""
}