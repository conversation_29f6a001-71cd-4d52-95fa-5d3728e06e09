package io.iotex.iopay.data.db

import androidx.room.*
import androidx.room.OnConflictStrategy.Companion.IGNORE

@Dao
interface WalletDao {

    @Insert(onConflict = IGNORE)
    fun insertIfNotExists(vararg wallet: Wallet)

    @Query("select * from WalletEntry limit 1")
    fun queryFirstWallet(): Wallet?

    @Update
    fun updateWallet(vararg wallet: Wallet)

    @Query("select * from WalletEntry")
    fun queryAllWallets(): List<Wallet>

    @Query("select * from WalletEntry where address = :address")
    fun queryWalletByAddress(address: String): Wallet?

    @Query("select * from WalletEntry where alias like '%'||:alias||'%'")
    fun queryWalletsLikeAlias(alias: String): List<Wallet>

    @Query("select * from WalletEntry where alias = :alias")
    fun queryWalletByAlias(alias: String): Wallet?

    @Query("select * from WalletEntry where mnemonic_id = :mnemonicId order by `order` asc")
    fun queryByMnemonic(mnemonicId: String): List<Wallet>

    @Query("select * from WalletEntry where mnemonic_id = :mnemonicId order by `order` desc")
    fun queryAllByMnemonic(mnemonicId: String): List<Wallet>

    @Query("select * from WalletEntry where (mnemonic_id is null or mnemonic_id = '') and (alias like '%'||:search||'%' or address like '%'||:search||'%') order by `order` asc")
    fun searchPrivateKeyAndAAWallets(search: String): List<Wallet>

    @Query("select * from WalletEntry where (mnemonic_id is null or mnemonic_id = '') and (aa_salt is null or aa_salt = '') order by `order` asc")
    fun queryPrivateKeyWallets(): List<Wallet>

    @Query("select * from WalletEntry order by `order` desc limit 1")
    fun queryLatestWallet(): Wallet?

    @Query("select * from WalletEntry where (aa_salt not null and aa_salt != '') order by `order` asc")
    fun queryAAWallets(): List<Wallet>

    @Query("select count(*) from WalletEntry")
    fun count(): Int

    @Query("select * from WalletEntry limit 1")
    fun first(): Wallet?

    @Delete
    fun deleteWallet(wallet: Wallet): Int
}