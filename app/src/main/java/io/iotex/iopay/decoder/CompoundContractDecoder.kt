package io.iotex.iopay.decoder

import com.blankj.utilcode.util.Utils
import io.iotex.iopay.R
import io.iotex.iopay.data.db.ACTION_TYPE_COMPOUND_REGISTER
import io.iotex.iopay.data.db.ACTION_TYPE_COMPOUND_UNREGISTER
import io.iotex.iopay.transaction.bean.OptionEntry
import io.iotex.iopay.util.extension.toHexString
import net.osslabz.evm.abi.decoder.AbiDecoder
import net.osslabz.evm.abi.decoder.DecodedFunctionCall
import net.osslabz.evm.abi.util.HashUtil
import org.web3j.protocol.core.methods.request.Transaction
import java.math.BigInteger

class CompoundContractDecoder(override val transaction: Transaction) : ContractDecoder {
    enum class Method(val signature: String) {
        Register("register(int256)"),
        Unregister("unregister()");

        fun signature(): String {
            return HashUtil.hashAsKeccak(signature.toByteArray()).toHexString().substring(0, 10)
        }
    }
    override fun decode(): ContractDecodedData? {
        val decoder = AbiDecoder(Utils.getApp().assets.open("compound.json"))
        val functionCall = decoder.decodeFunctionCall(transaction.data)
        return when (resolveMethod()) {
            Method.Register -> decodeRegister(functionCall)
            Method.Unregister -> decodeUnregister(functionCall)
            else -> null
        }
    }

    private fun resolveMethod(): Method? {
        return Method.values().firstOrNull {
            transaction.data.startsWith(it.signature())
        }
    }

    private fun decodeRegister(functionCall: DecodedFunctionCall): ContractDecodedData {
        val bucketId = functionCall.getParam("bucketId").value as? BigInteger
        return ContractDecodedData(
            listOf(
                OptionEntry(Utils.getApp().getString(R.string.method), Utils.getApp().getString(R.string.compound_method_register)),
                OptionEntry(Utils.getApp().getString(R.string.bucket_index), Utils.getApp().getString(R.string.index_no, bucketId.toString())),
            ),
            Utils.getApp().getString(R.string.index_no, bucketId.toString()),
            "",
            ACTION_TYPE_COMPOUND_REGISTER,
            R.string.compound_method_register.toString()
        )
    }

    private fun decodeUnregister(functionCall: DecodedFunctionCall): ContractDecodedData {
        val bucketId = functionCall.getParam("bucketId")?.value as? BigInteger
        return ContractDecodedData(
            listOf(
                OptionEntry(Utils.getApp().getString(R.string.method), Utils.getApp().getString(R.string.compound_method_unregister)),
            ),
            if (bucketId == null) "" else Utils.getApp()
                .getString(R.string.index_no, bucketId.toString()),
            "",
            ACTION_TYPE_COMPOUND_UNREGISTER,
            R.string.compound_method_unregister.toString()
        )
    }
}