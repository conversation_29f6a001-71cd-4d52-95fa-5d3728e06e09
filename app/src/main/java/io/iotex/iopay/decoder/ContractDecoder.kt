package io.iotex.iopay.decoder

import com.blankj.utilcode.util.Utils
import io.iotex.iopay.R
import io.iotex.iopay.data.UserStore
import io.iotex.iopay.data.db.ACTION_TYPE_DAPP
import io.iotex.iopay.data.db.ACTION_TYPE_TRANSFER
import io.iotex.iopay.data.db.AppDatabase
import io.iotex.iopay.repo.TransferEntryMapRepo
import io.iotex.iopay.transaction.bean.OptionEntry
import io.iotex.iopay.util.AbiDecoderUtil
import io.iotex.iopay.util.TokenUtil
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.util.extension.asBigDecimal
import io.iotex.iopay.util.extension.cleanHexPrefix
import io.iotex.iopay.util.extension.i
import io.iotex.iopay.util.extension.toEvmAddress
import io.iotex.iopay.wallet.web3.Web3Delegate
import org.web3j.protocol.core.methods.request.Transaction
import org.web3j.utils.Convert
import java.math.BigDecimal
import java.math.BigInteger

interface ContractDecoder {
    val transaction: Transaction

    fun decode(): ContractDecodedData?
}

data class ContractDecodedData(
    val options: List<OptionEntry>,
    val value: String,
    val to: String,
    val type: Int,
    val signature: String,
    val tokenAddress: String = "",
)


fun createContractDecoder(transaction: Transaction): ContractDecoder? {
    "createContractDecoder ==> ${transaction.to.lowercase()}".i()
    return when (transaction.to.lowercase()) {
        //1
        "******************************************" -> SwapContractDecoder(transaction)
        "******************************************" -> SwapEvmContractDecoder(transaction)
        "******************************************" -> SwapEvmContractDecoder(transaction)
        "******************************************" -> WIOTXContractDecoder(transaction)
        //42
        "0xd0a1e359811322d97991e03f863a0c30c2cf029c" -> WIOTXContractDecoder(transaction)
        //42 56
        "0x10ed43c718714eb63d5aa57b78b54704e256024e" -> SwapContractDecoder(transaction)
        "0xbb4cdb9cbd36b01bd1cbaebf2de08d9173bc095c" -> WIOTXContractDecoder(transaction)
        //137
        "0xa5e0829caced8ffdd4de3c43696c57f7d7a678ff" -> SwapContractDecoder(transaction)
        "0x0d500b1d8e8ef31e21c99d1db9a6444d3adf1270" -> WIOTXContractDecoder(transaction)
        //4689
        "0x147cdae2bf7e809b9789ad0765899c06b361c5ce" -> SwapContractDecoder(transaction)
        "0xa6eb3902ca74265b7e5a1f66d39fa0cec0ca38ff" -> SwapV3ContractDecoder(transaction)
        "0xa00744882684c3e4747faefd68d283ea44099d03" -> WIOTXContractDecoder(transaction)
        "0x04c22afae6a03438b8fed74cb1cf441168df3f12" -> StakeContractDecoder(transaction)
        "0x68db92a6a78a39dcaff1745da9e89e230ef49d3d" -> IIP13ContractDecoder(transaction)
        "0x79f1670be20daecefb134e33d97f9e77340fd2c0" -> CompoundContractDecoder(transaction)
        "0x8ee521d2179576bcc4bd33a00904e96a11678052" -> IIP13ContractDecoderV2(transaction)
        "0x47b9985f071b7127263dd5438c8b02aad47c16bd" -> UsdcContractDecoder(transaction)
        //4690
        "0x52ab0fe2c3a94644de0888a3ba9ea1443672e61f" -> IIP13ContractDecoder(transaction)
        "0xb575dfa55d9cd04841567fe8461bfc79d068b9cf" -> CompoundContractDecoder(transaction)
        "0xfd68c6f94e25866b639fddebb2e6dc6807bfbe5f" -> IIP13ContractDecoderV2(transaction)
        "******************************************" -> IIP13ContractDecoderV3(transaction)
        "******************************************" -> IIP13ContractDecoderV3(transaction)
        "******************************************" -> IIP13ContractDecoderV3(transaction)
        else -> null
    }
}

fun decodeContract(
    from: String,
    to: String,
    data: String?,
    value: String?,
): ContractDecodedData {
    val options = ArrayList<OptionEntry>()
    val signature = Web3Delegate.resolveMethodCode(data)
    val method = TransferEntryMapRepo().querySignatures(signature)
    options.add(
        OptionEntry(Utils.getApp().getString(R.string.method), method),
    )
    options.add(
        OptionEntry(Utils.getApp().getString(R.string.from), from),
    )
    var decodeValue = ""
    var decodeContract = ""
    if (Web3Delegate.isContract(to)) {
        decodeContract = to
        options.add(
            OptionEntry(
                Utils.getApp().getString(R.string.contract_addr),
                to
            )
        )

        val erc20 = AppDatabase.getInstance(Utils.getApp()).tokenDao().queryByAddress(WalletHelper.getCurChainId(), to)
        if (erc20 != null) {
            val name = Web3Delegate.erc20Name(to.toEvmAddress())
            if (name.isNotEmpty()) {
                options.add(
                    OptionEntry(
                        Utils.getApp().getString(R.string.name),
                        name
                    )
                )
            }

            val amount = AbiDecoderUtil.resolveParam(data ?: "", "wad", "wiotx.json")
            decodeValue = TokenUtil.weiToTokenBN(amount, erc20.decimals.toLong())
            if (amount.asBigDecimal() != BigDecimal.ZERO) {
                options.add(
                    OptionEntry(
                        Utils.getApp().getString(R.string.amount),
                        TokenUtil.weiToTokenBN(amount, erc20.decimals.toLong()) + " " + erc20.symbol
                    )
                )
            }
        } else {
            val name = Web3Delegate.erc20Name(to.toEvmAddress())
            if (name.isNotEmpty()) {
                options.add(
                    OptionEntry(
                        Utils.getApp().getString(R.string.name),
                        name
                    )
                )
                val amount = AbiDecoderUtil.resolveParam(data ?: "", "wad", "wiotx.json")
                decodeValue = amount
                if (amount.asBigDecimal() != BigDecimal.ZERO) {
                    options.add(
                        OptionEntry(
                            Utils.getApp().getString(R.string.no_dot),
                            amount
                        )
                    )
                }
            }
        }
    } else {
        options.add(
            OptionEntry(
                Utils.getApp().getString(R.string.to),
                to
            )
        )
        val bigValue = if (!value.isNullOrBlank()) {
            BigInteger(value.cleanHexPrefix(), 16)
        } else {
            BigInteger.ZERO
        }
        decodeValue =
            TokenUtil.weiToTokenBN(bigValue.toString(), UserStore.getNetworkDecimals().toLong())
        if (bigValue != BigInteger.ZERO) {
            options.add(
                OptionEntry(
                    Utils.getApp().getString(R.string.amount),
                    "${
                        Convert.fromWei(
                            BigDecimal(bigValue),
                            Convert.Unit.ETHER
                        )
                    } ${UserStore.getNetworkSymbol()}"
                )
            )
        }
    }

    val type = if (data.isNullOrEmpty() || data.startsWith("0xa9059cbb")) {
        ACTION_TYPE_TRANSFER
    } else {
        ACTION_TYPE_DAPP
    }
    return ContractDecodedData(options, decodeValue, to, type, "", decodeContract)
}
