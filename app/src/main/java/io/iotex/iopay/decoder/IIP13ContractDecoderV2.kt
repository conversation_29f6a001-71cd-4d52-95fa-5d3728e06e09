package io.iotex.iopay.decoder

import com.blankj.utilcode.util.Utils
import io.iotex.iopay.R
import io.iotex.iopay.data.db.ACTION_TYPE_DELEGATE
import io.iotex.iopay.data.db.ACTION_TYPE_RESTAKE
import io.iotex.iopay.data.db.ACTION_TYPE_RESTAKE_LOCK
import io.iotex.iopay.data.db.ACTION_TYPE_RESTAKE_UNLOCK
import io.iotex.iopay.data.db.ACTION_TYPE_STAKE
import io.iotex.iopay.data.db.ACTION_TYPE_UNSTAKE
import io.iotex.iopay.data.db.ACTION_TYPE_WITHDRAW
import io.iotex.iopay.transaction.bean.OptionEntry
import io.iotex.iopay.util.IoPayConstant
import io.iotex.iopay.util.TokenUtil
import io.iotex.iopay.util.extension.asBigDecimal
import io.iotex.iopay.util.extension.cleanHexPrefix
import io.iotex.iopay.util.extension.toHexString
import net.osslabz.evm.abi.decoder.AbiDecoder
import net.osslabz.evm.abi.decoder.DecodedFunctionCall
import net.osslabz.evm.abi.util.HashUtil
import org.web3j.protocol.core.methods.request.Transaction
import java.math.BigDecimal
import java.math.BigInteger
import java.math.RoundingMode

class IIP13ContractDecoderV2(override val transaction: Transaction) : ContractDecoder {

    private val onDayBlack = "34560"

    enum class Method(val signature: String) {
        CreateStake("stake(uint256,uint256,address[])"),
        CreateStake2("stake(uint256,address)"),
        ChangeDelegate("changeDelegate(uint256,address)"),
        Restake("expandBucket(uint256,uint256)"),
        Lock("lock(uint256,uint256)"),
        Unlock("unlock(uint256)"),
        Unstake("unstake(uint256)"),
        Withdraw("withdraw(uint256,address)");

        fun signature(): String {
            return HashUtil.hashAsKeccak(signature.toByteArray()).toHexString().substring(0, 10)
        }
    }

    override fun decode(): ContractDecodedData? {
        val decoder = AbiDecoder(Utils.getApp().assets.open("liquidStakingV2.json"))
        val functionCall = decoder.decodeFunctionCall(transaction.data)
        return when (resolveMethod()) {
            Method.CreateStake -> decodeCreateStake(functionCall)
            Method.CreateStake2 -> decodeCreateStakeV2(functionCall, transaction.value)
            Method.ChangeDelegate -> decodeChangeDelegate(functionCall)
            Method.Restake -> decodeRestake(functionCall, transaction.value)
            Method.Lock -> decodeLock(functionCall)
            Method.Unlock -> decodeUnlock(functionCall)
            Method.Withdraw -> decodeWithdraw(functionCall)
            Method.Unstake -> decodeUnstake(functionCall)
            else -> null
        }
    }

    private fun resolveMethod(): Method? {
        return Method.values().firstOrNull {
            transaction.data.startsWith(it.signature())
        }
    }

    private fun decodeCreateStake(functionCall: DecodedFunctionCall): ContractDecodedData {
        val amount = functionCall.getParam("_amount").value as? BigInteger
        val duration = functionCall.getParam("_duration").value as? BigInteger
        val delegates = functionCall.getParam("_delegates").value as? Array<*>
        val delegate = if (delegates?.isNotEmpty() == true) delegates[0]?.toString() else ""
        val time = "${duration?.toBigDecimal()
            ?.divide(BigDecimal(onDayBlack), 18, RoundingMode.HALF_UP)
            ?.stripTrailingZeros()?.toPlainString()} ${Utils.getApp().getString(R.string.stake_duration_days)}"
        return ContractDecodedData(
            listOf(
                OptionEntry(Utils.getApp().getString(R.string.method), Utils.getApp().getString(R.string.create_stake)),
                OptionEntry(Utils.getApp().getString(R.string.candidate_name), delegate ?: ""),
                OptionEntry(Utils.getApp().getString(R.string.amount), "-${TokenUtil.weiToTokenBN(amount.toString())} ${IoPayConstant.IOTX}"),
                OptionEntry(Utils.getApp().getString(R.string.stake_duration), time),
                OptionEntry(Utils.getApp().getString(R.string.auto_stake), "ON")
            ),
            "-${TokenUtil.weiToTokenBN(amount.toString())} ${IoPayConstant.IOTX}",
            delegate ?: "",
            ACTION_TYPE_STAKE,
            ""
        )
    }

    private fun decodeCreateStakeV2(functionCall: DecodedFunctionCall, value: String): ContractDecodedData {
        val amount = BigInteger(value.cleanHexPrefix(), 16)
        val duration = functionCall.getParam("_duration").value as? BigInteger
        val delegate = functionCall.getParam("_delegate").value as? String
        val time = "${duration?.toBigDecimal()
            ?.divide(BigDecimal(onDayBlack), 18, RoundingMode.HALF_UP)
            ?.stripTrailingZeros()?.toPlainString()} ${Utils.getApp().getString(R.string.stake_duration_days)}"
        return ContractDecodedData(
            listOf(
                OptionEntry(Utils.getApp().getString(R.string.method), Utils.getApp().getString(R.string.create_stake)),
                OptionEntry(Utils.getApp().getString(R.string.candidate_name), delegate ?: ""),
                OptionEntry(Utils.getApp().getString(R.string.amount), "-${TokenUtil.weiToTokenBN(amount.toString())} ${IoPayConstant.IOTX}"),
                OptionEntry(Utils.getApp().getString(R.string.stake_duration), time),
                OptionEntry(Utils.getApp().getString(R.string.auto_stake), "ON")
            ),
            "-${TokenUtil.weiToTokenBN(amount.toString())} ${IoPayConstant.IOTX}",
            delegate ?: "",
            ACTION_TYPE_STAKE,
            ""
        )
    }

    private fun decodeChangeDelegate(functionCall: DecodedFunctionCall): ContractDecodedData {
        val tokenId = functionCall.getParam("_bucketId").value as? BigInteger
        val delegate = functionCall.getParam("_delegate").value as? String
        return if (tokenId == null || delegate == null) {
            throw Exception("invalid input")
        } else {
            ContractDecodedData(
                listOf(
                    OptionEntry(Utils.getApp().getString(R.string.method), Utils.getApp().getString(R.string.stake_change_candidate)),
                    OptionEntry(Utils.getApp().getString(R.string.candidate_name), delegate),
                    OptionEntry(Utils.getApp().getString(R.string.bucket_index), Utils.getApp().getString(R.string.index_no, tokenId.toString())),
                ),
                Utils.getApp().getString(R.string.index_no, tokenId.toString()),
                "",
                ACTION_TYPE_DELEGATE,
                ""
            )

        }
    }

    private fun decodeRestake(functionCall: DecodedFunctionCall, value: String): ContractDecodedData {
        val tokenId = functionCall.getParam("_bucketId").value as? BigInteger
        val amount = BigInteger(value.cleanHexPrefix(), 16)
        val duration = functionCall.getParam("_newDuration").value as? BigInteger
        val time = "${
            duration?.toBigDecimal()
                ?.divide(BigDecimal(onDayBlack), 18, RoundingMode.HALF_UP)
                ?.stripTrailingZeros()?.toPlainString()
        }".asBigDecimal()
        val option = arrayListOf(
            OptionEntry(
                Utils.getApp().getString(R.string.method),
                Utils.getApp().getString(R.string.stake_method_edit)
            ),
            OptionEntry(
                Utils.getApp().getString(R.string.bucket_index),
                Utils.getApp().getString(R.string.index_no, tokenId.toString())
            ),
        )
        if (time != BigDecimal.ZERO) {
            option.add(
                OptionEntry(
                    Utils.getApp().getString(R.string.stake_duration),
                    Utils.getApp().resources.getQuantityString(
                        R.plurals.day_num,
                        time.toInt(),
                        time.toInt()
                    )
                )
            )
        }
        if (amount != BigInteger.ZERO) {
            option.add(
                OptionEntry(
                    Utils.getApp().getString(R.string.amount),
                    "-${TokenUtil.weiToTokenBN(amount.toString())} ${IoPayConstant.IOTX}"
                )
            )
        }

        return if (tokenId == null || duration == null) {
            throw Exception("invalid input")
        } else {
            ContractDecodedData(
                option,
                Utils.getApp().getString(R.string.index_no, tokenId.toString()),
                "",
                ACTION_TYPE_RESTAKE,
                ""
            )
        }
    }

    private fun decodeLock(functionCall: DecodedFunctionCall): ContractDecodedData {
        val tokenId = functionCall.getParam("_bucketId").value as? BigInteger
        val duration = functionCall.getParam("_duration").value as? BigInteger
        return if (tokenId == null || duration == null) {
            throw Exception("invalid input")
        } else {
            val time = "${duration.toBigDecimal()
                .divide(BigDecimal(onDayBlack), 18, RoundingMode.HALF_UP)
                .stripTrailingZeros().toPlainString()} ${Utils.getApp().getString(R.string.stake_duration_days)}"
            ContractDecodedData(
                listOf(
                    OptionEntry(Utils.getApp().getString(R.string.method), Utils.getApp().getString(R.string.edit)),
                    OptionEntry(Utils.getApp().getString(R.string.bucket_index), Utils.getApp().getString(R.string.index_no, tokenId.toString())),
                    OptionEntry(Utils.getApp().getString(R.string.stake_duration), time)
                ),
                Utils.getApp().getString(R.string.index_no, tokenId.toString()),
                "",
                ACTION_TYPE_RESTAKE_LOCK,
                ""
            )
        }
    }

    private fun decodeUnlock(functionCall: DecodedFunctionCall): ContractDecodedData {
        val tokenId = functionCall.getParam("_bucketId").value as? BigInteger
        return if (tokenId == null) {
            throw Exception("invalid input")
        } else {
            ContractDecodedData(
                listOf(
                    OptionEntry(Utils.getApp().getString(R.string.method), Utils.getApp().getString(R.string.edit)),
                    OptionEntry(Utils.getApp().getString(R.string.bucket_index), Utils.getApp().getString(R.string.index_no, tokenId.toString()))
                ),
                Utils.getApp().getString(R.string.index_no, tokenId.toString()),
                "",
                ACTION_TYPE_RESTAKE_UNLOCK,
                ""
            )
        }
    }

    private fun decodeUnstake(functionCall: DecodedFunctionCall): ContractDecodedData {
        val tokenId = functionCall.getParam("_bucketId").value as? BigInteger
        return if (tokenId == null) {
            throw Exception("invalid input")
        } else {
            ContractDecodedData(
                listOf(
                    OptionEntry(Utils.getApp().getString(R.string.method), Utils.getApp().getString(R.string.stake_unstake)),
                    OptionEntry(Utils.getApp().getString(R.string.bucket_index), Utils.getApp().getString(R.string.index_no, tokenId.toString()))
                ),
                Utils.getApp().getString(R.string.index_no, tokenId.toString()),
                "",
                ACTION_TYPE_UNSTAKE,
                ""
            )
        }
    }

    private fun decodeWithdraw(functionCall: DecodedFunctionCall): ContractDecodedData {
        val bucketIdx = functionCall.getParam("_bucketId").value as? BigInteger
        val recipient = functionCall.getParam("_recipient").value as? String
        return if (bucketIdx == null) {
            throw Exception("invalid input")
        } else {
            ContractDecodedData(
                listOf(
                    OptionEntry(Utils.getApp().getString(R.string.method), Utils.getApp().getString(R.string.stake_withdraw)),
                    OptionEntry(Utils.getApp().getString(R.string.bucket_index), Utils.getApp().getString(R.string.index_no, bucketIdx.toString())),
                    OptionEntry(Utils.getApp().getString(R.string.stake_withdraw_receiption), recipient.toString())
                ),
                Utils.getApp().getString(R.string.index_no, bucketIdx.toString()),
                recipient.toString(),
                ACTION_TYPE_WITHDRAW,
                ""
            )
        }
    }
}