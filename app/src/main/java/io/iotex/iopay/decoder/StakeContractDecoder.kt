package io.iotex.iopay.decoder

import com.blankj.utilcode.util.Utils
import io.iotex.iopay.R
import io.iotex.iopay.data.db.ACTION_TYPE_DELEGATE
import io.iotex.iopay.data.db.ACTION_TYPE_DEPOSIT_STAKE
import io.iotex.iopay.data.db.ACTION_TYPE_MIGRATE
import io.iotex.iopay.data.db.ACTION_TYPE_RESTAKE_LOCK
import io.iotex.iopay.data.db.ACTION_TYPE_RESTAKE_UNLOCK
import io.iotex.iopay.data.db.ACTION_TYPE_STAKE
import io.iotex.iopay.data.db.ACTION_TYPE_TRANSFER_BUCKET
import io.iotex.iopay.data.db.ACTION_TYPE_UNSTAKE
import io.iotex.iopay.data.db.ACTION_TYPE_WITHDRAW
import io.iotex.iopay.transaction.bean.OptionEntry
import io.iotex.iopay.util.IoPayConstant
import io.iotex.iopay.util.TokenUtil
import io.iotex.iopay.util.extension.toHexString
import net.osslabz.evm.abi.decoder.AbiDecoder
import net.osslabz.evm.abi.decoder.DecodedFunctionCall
import net.osslabz.evm.abi.util.HashUtil
import org.web3j.protocol.core.methods.request.Transaction
import java.math.BigInteger

class StakeContractDecoder(override val transaction: Transaction) : ContractDecoder {

    enum class Method(val signature: String) {
        CreateStake("createStake(string,uint256,uint32,bool,uint8[])"),
        ReStake("restake(uint64,uint32,bool,uint8[])"),
        ChangeCandidate("changeCandidate(string,uint64,uint8[])"),
        UnStake("unstake(uint64,uint8[])"),
        DepositToStake("depositToStake(uint64,uint256,uint8[])"),
        TransferStake("transferStake(address,uint64,uint8[])"),
        Withdraw("withdrawStake(uint64,uint8[])"),
        Migrate("migrateStake(uint64)");

        fun signature(): String {
            return HashUtil.hashAsKeccak(signature.toByteArray()).toHexString().substring(0, 10)
        }
    }

    override fun decode(): ContractDecodedData? {
        val decoder = AbiDecoder(Utils.getApp().assets.open("stake.json"))
        val functionCall = decoder.decodeFunctionCall(transaction.data)
        return when(resolveMethod()) {
            Method.CreateStake -> decodeCreateStake(functionCall)
            Method.ReStake -> decodeReStake(functionCall)
            Method.ChangeCandidate -> decodeChangeCandidate(functionCall)
            Method.UnStake -> decodeUnstake(functionCall)
            Method.Withdraw -> decodeWithdraw(functionCall)
            Method.DepositToStake -> decodeDepositToStake(functionCall)
            Method.TransferStake -> decodeTransferStake(functionCall)
            Method.Migrate -> decodeMigrateStake(functionCall)
            else -> null
        }
    }

    private fun resolveMethod(): Method? {
        return Method.values().firstOrNull {
            transaction.data.startsWith(it.signature())
        }
    }

    private fun decodeCreateStake(functionCall: DecodedFunctionCall): ContractDecodedData {
        val canName = functionCall.getParam("candName").value as? String
        val amount = functionCall.getParam("amount").value as? BigInteger
        val duration = functionCall.getParam("duration").value as? BigInteger
        val auto = functionCall.getParam("autoStake").value as? Boolean
        return if (canName == null || amount == null || duration == null || auto == null) {
            throw Exception("invalid input")
        } else {
            val time =
                Utils.getApp().resources.getQuantityString(
                    R.plurals.day_num,
                    duration.toInt(),
                    duration.toInt()
                )
            ContractDecodedData(
                listOf(
                    OptionEntry(Utils.getApp().getString(R.string.method), Utils.getApp().getString(R.string.create_stake)),
                    OptionEntry(Utils.getApp().getString(R.string.candidate_name), canName),
                    OptionEntry(Utils.getApp().getString(R.string.amount), "-${TokenUtil.weiToTokenBN(amount.toString())} ${IoPayConstant.IOTX}"),
                    OptionEntry(Utils.getApp().getString(R.string.stake_duration), time),
                    OptionEntry(Utils.getApp().getString(R.string.auto_stake), if (auto) "ON" else "OFF")
                ),
                "-${TokenUtil.weiToTokenBN(amount.toString())} ${IoPayConstant.IOTX}",
                canName,
                ACTION_TYPE_STAKE,
                ""
            )
        }
    }

    private fun decodeReStake(functionCall: DecodedFunctionCall): ContractDecodedData {
        val bucketIndex = functionCall.getParam("bucketIndex").value as? BigInteger
        val duration = functionCall.getParam("duration").value as? BigInteger
        val auto = functionCall.getParam("autoStake").value as? Boolean
        val time =
            Utils.getApp().resources.getQuantityString(
                R.plurals.day_num,
                duration?.toInt() ?: 0,
                duration?.toInt() ?: 0
            )
        return ContractDecodedData(
            listOf(
                OptionEntry(Utils.getApp().getString(R.string.method), Utils.getApp().getString(R.string.edit_stake)),
                OptionEntry(Utils.getApp().getString(R.string.bucket_index), Utils.getApp().getString(R.string.index_no, bucketIndex.toString())),
                OptionEntry(Utils.getApp().getString(R.string.stake_duration), time),
                OptionEntry(Utils.getApp().getString(R.string.auto_stake), if (auto == true) "ON" else "OFF")
            ),
            Utils.getApp().getString(R.string.index_no, bucketIndex.toString()),
            "",
            if (auto == true) ACTION_TYPE_RESTAKE_LOCK else ACTION_TYPE_RESTAKE_UNLOCK,
            ""
        )
    }

    private fun decodeDepositToStake(functionCall: DecodedFunctionCall): ContractDecodedData {
        val bucketIndex = functionCall.getParam("bucketIndex").value as? BigInteger
        val amount = functionCall.getParam("amount").value as? BigInteger
        return ContractDecodedData(
            listOf(
                OptionEntry(Utils.getApp().getString(R.string.method), Utils.getApp().getString(R.string.stake_add_deposit)),
                OptionEntry(Utils.getApp().getString(R.string.bucket_index), Utils.getApp().getString(R.string.index_no, bucketIndex.toString())),
                OptionEntry(Utils.getApp().getString(R.string.amount), "-${TokenUtil.weiToTokenBN(amount.toString())} ${IoPayConstant.IOTX}"),
            ),
            Utils.getApp().getString(R.string.index_no, bucketIndex.toString()),
            "",
            ACTION_TYPE_DEPOSIT_STAKE,
            ""
        )
    }

    private fun decodeTransferStake(functionCall: DecodedFunctionCall): ContractDecodedData {
        val bucketIndex = functionCall.getParam("bucketIndex").value as? BigInteger
        val address = functionCall.getParam("voterAddress").value as? String
        return ContractDecodedData(
            listOf(
                OptionEntry(Utils.getApp().getString(R.string.method), Utils.getApp().getString(R.string.transfer_bucket)),
                OptionEntry(Utils.getApp().getString(R.string.bucket_index), Utils.getApp().getString(R.string.index_no, bucketIndex.toString())),
                OptionEntry(Utils.getApp().getString(R.string.stake_withdraw_receiption), address ?: ""),
            ),
            Utils.getApp().getString(R.string.index_no, bucketIndex.toString()),
            address?:"",
            ACTION_TYPE_TRANSFER_BUCKET,
            ""
        )
    }

    private fun decodeChangeCandidate(functionCall: DecodedFunctionCall): ContractDecodedData {
        val canName = functionCall.getParam("candName").value as? String
        val bucketIdx = functionCall.getParam("bucketIndex").value as? BigInteger
        return if (canName == null || bucketIdx == null) {
            throw Exception("invalid input")
        } else {
            ContractDecodedData(
                listOf(
                    OptionEntry(Utils.getApp().getString(R.string.method), Utils.getApp().getString(R.string.stake_change_candidate)),
                    OptionEntry(Utils.getApp().getString(R.string.candidate_name), canName),
                    OptionEntry(Utils.getApp().getString(R.string.bucket_index), Utils.getApp().getString(R.string.index_no, bucketIdx.toString()))
                ),
                Utils.getApp().getString(R.string.index_no, bucketIdx.toString()),
                "",
                ACTION_TYPE_DELEGATE,
                ""
            )
        }
    }

    private fun decodeUnstake(functionCall: DecodedFunctionCall): ContractDecodedData {
        val bucketIdx = functionCall.getParam("bucketIndex").value as? BigInteger
        return if (bucketIdx == null) {
            throw Exception("invalid input")
        } else {
            ContractDecodedData(
                listOf(
                    OptionEntry(Utils.getApp().getString(R.string.method), Utils.getApp().getString(R.string.stake_unstake)),
                    OptionEntry(Utils.getApp().getString(R.string.bucket_index), Utils.getApp().getString(R.string.index_no, bucketIdx.toString()))
                ),
                Utils.getApp().getString(R.string.index_no, bucketIdx.toString()),
                "",
                ACTION_TYPE_UNSTAKE,
              ""
            )
        }
    }

    private fun decodeWithdraw(functionCall: DecodedFunctionCall): ContractDecodedData {
        val bucketIdx = functionCall.getParam("bucketIndex").value as? BigInteger
        return if (bucketIdx == null) {
            throw Exception("invalid input")
        } else {
            ContractDecodedData(
                listOf(
                    OptionEntry(Utils.getApp().getString(R.string.method), Utils.getApp().getString(R.string.stake_withdraw)),
                    OptionEntry(Utils.getApp().getString(R.string.bucket_index), Utils.getApp().getString(R.string.index_no, bucketIdx.toString()))
                ),
                Utils.getApp().getString(R.string.index_no, bucketIdx.toString()),
                "",
                ACTION_TYPE_WITHDRAW,
                ""
            )
        }
    }

    private fun decodeMigrateStake(functionCall: DecodedFunctionCall): ContractDecodedData {
        val bucketIdx = functionCall.getParam("bucketIndex").value as? BigInteger
        return if (bucketIdx == null) {
            throw Exception("invalid input")
        } else {
            ContractDecodedData(
                listOf(
                    OptionEntry(Utils.getApp().getString(R.string.method), Utils.getApp().getString(R.string.stake_migrate)),
                    OptionEntry(Utils.getApp().getString(R.string.bucket_index), Utils.getApp().getString(R.string.index_no, bucketIdx.toString()))
                ),
                Utils.getApp().getString(R.string.index_no, bucketIdx.toString()),
                "",
                ACTION_TYPE_MIGRATE,
                ""
            )
        }
    }
}