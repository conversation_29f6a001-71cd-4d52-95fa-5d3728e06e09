package io.iotex.iopay.decoder

import com.blankj.utilcode.util.SPUtils
import com.blankj.utilcode.util.Utils
import io.iotex.iopay.R
import io.iotex.iopay.data.UserStore
import io.iotex.iopay.data.db.ACTION_TYPE_SWAP
import io.iotex.iopay.data.db.AppDatabase
import io.iotex.iopay.repo.TransferEntryMapRepo
import io.iotex.iopay.transaction.bean.OptionEntry
import io.iotex.iopay.util.Config
import io.iotex.iopay.util.IoPayConstant
import io.iotex.iopay.util.SPConstant
import io.iotex.iopay.util.TokenUtil
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.util.extension.asBigDecimal
import io.iotex.iopay.util.extension.cleanHexPrefix
import io.iotex.iopay.util.extension.toHexString
import io.iotex.iopay.wallet.web3.Web3Delegate
import net.osslabz.evm.abi.decoder.AbiDecoder
import net.osslabz.evm.abi.decoder.DecodedFunctionCall
import net.osslabz.evm.abi.util.HashUtil
import org.web3j.protocol.core.methods.request.Transaction
import java.math.BigDecimal
import java.math.BigInteger

class SwapContractDecoder(override val transaction: Transaction) : ContractDecoder {
    enum class Method(val signature: String) {
        swapETHForExactTokens("swapETHForExactTokens(uint256,address[],address,uint256)"),
        swapTokensForExactETH("swapTokensForExactETH(uint256,uint256,address[],address,uint256)"),
        swapTokensForExactTokens("swapTokensForExactTokens(uint256,uint256,address[],address,uint256)"),
        swapExactETHForTokens("swapExactETHForTokens(uint256,address[],address,uint256)"),
        swapExactTokensForETH("swapExactTokensForETH(uint256,uint256,address[],address,uint256)"),
        swapExactTokensForTokens("swapExactTokensForTokens(uint256,uint256,address[],address,uint256)"),
        swapExactETHForTokensSupportingFeeOnTransferTokens("swapExactETHForTokensSupportingFeeOnTransferTokens(uint256,address[],address,uint256)"),
        swapExactTokensForETHSupportingFeeOnTransferTokens("swapExactTokensForETHSupportingFeeOnTransferTokens(uint256,uint256,address[],address,uint256)"),
        swapExactTokensForTokensSupportingFeeOnTransferTokens("swapExactTokensForTokensSupportingFeeOnTransferTokens(uint256,uint256,address[],address,uint256)");
        fun signature(): String {
            return HashUtil.hashAsKeccak(signature.toByteArray()).toHexString().substring(0, 10)
        }
    }

    override fun decode(): ContractDecodedData? {
        val decoder = AbiDecoder(Utils.getApp().assets.open("swap.json"))
        val functionCall = decoder.decodeFunctionCall(transaction.data)
        return when (resolveMethod()) {
            Method.swapETHForExactTokens -> decodeSwapTokensForExactTokens(functionCall, "Swap ETH For Exact Tokens")
            Method.swapTokensForExactETH -> decodeSwapTokensForExactTokens(functionCall, "Swap Tokens For Exact ETH")
            Method.swapTokensForExactTokens -> decodeSwapTokensForExactTokens(functionCall, "Swap Tokens For Exact Tokens")
            Method.swapExactETHForTokens -> decodeSwapExactTokensForTokens(functionCall, "Swap ETH For Exact Tokens")
            Method.swapExactTokensForETH -> decodeSwapExactTokensForTokens(functionCall, "Swap Exact Tokens For ETH")
            Method.swapExactTokensForTokens -> decodeSwapExactTokensForTokens(functionCall, "Swap Exact Tokens For Tokens")
            Method.swapExactETHForTokensSupportingFeeOnTransferTokens -> decodeSwapExactTokensForTokens(functionCall, "Swap ETH For Exact Tokens Supporting Fee On Transfer Tokens")
            Method.swapExactTokensForETHSupportingFeeOnTransferTokens -> decodeSwapExactTokensForTokens(functionCall, "Swap Exact Tokens For ETH Supporting Fee On Transfer Tokens")
            Method.swapExactTokensForTokensSupportingFeeOnTransferTokens -> decodeSwapExactTokensForTokens(functionCall, "Swap Exact Tokens For Tokens Supporting Fee On Transfer Tokens")
            else -> null
        }
    }

    private fun resolveMethod(): Method? {
        return Method.values().firstOrNull {
            transaction.data.startsWith(it.signature())
        }
    }

    private fun decodeSwapTokensForExactTokens(
        functionCall: DecodedFunctionCall,
        methodName: String
    ): ContractDecodedData {
        val amount = TokenUtil.weiToTokenBN(
            transaction.value.cleanHexPrefix().toBigIntegerOrNull(16).toString()
        )
        var amountFrom = amount
        var symbolFrom = UserStore.getNetworkSymbol()
        val path = functionCall.getParam("path").value as? Array<*>
        val contractFrom = if (path?.isNotEmpty() == true) path[0].toString() else ""
        val amountOutMin =
            functionCall.getParam("amountOut").value as? BigInteger ?: BigInteger.ZERO
        var contractTo = ""
        if (amountFrom.asBigDecimal() == BigDecimal.ZERO) {
            //in token
            val amountIn =
                functionCall.getParam("amountInMax").value as? BigInteger ?: BigInteger.ZERO
            val decimals = resolveDecimals(contractFrom)
            symbolFrom = resolveSymbol(contractFrom)
            amountFrom = TokenUtil.weiToTokenBN(amountIn.toString(), decimals.toLong())
            //out token or iotex
            contractTo = if ((path?.size ?: 0) > 2) path?.get(2).toString() else ""
        } else {
            // in iotex
            // out token
            contractTo = if ((path?.size ?: 0) > 0) path?.last().toString() else ""
        }
        val decimalsTo = resolveDecimals(contractTo)
        val symbolTo = resolveSymbol(contractTo)
        val amountTo = TokenUtil.weiToTokenBN(amountOutMin.toString(), decimalsTo.toLong())

        val signature = Web3Delegate.resolveMethodCode(transaction.data)
        val method = TransferEntryMapRepo().querySignatures(signature, default = methodName)
        return ContractDecodedData(
            listOf(
                OptionEntry(Utils.getApp().getString(R.string.method), Utils.getApp().getString(R.string.xrc_swap)),
                OptionEntry(
                    Utils.getApp().getString(R.string.swap_from),
                    "-$amountFrom $symbolFrom"
                ),
                OptionEntry(
                    Utils.getApp().getString(R.string.minimum_receive),
                    "+$amountTo $symbolTo"
                ),
            ),
            amountFrom,
            transaction.to,
            ACTION_TYPE_SWAP,
            method,
            contractFrom
        )
    }

    private fun decodeSwapExactTokensForTokens(
        functionCall: DecodedFunctionCall,
        methodName: String
    ): ContractDecodedData {
        val amount = TokenUtil.weiToTokenBN(
            transaction.value.cleanHexPrefix().toBigIntegerOrNull(16).toString()
        )
        var amountFrom = amount
        var symbolFrom = UserStore.getNetworkSymbol()
        val path = functionCall.getParam("path").value as? Array<*>
        val contractFrom = if (path?.isNotEmpty() == true) path[0].toString() else ""
        val amountOutMin =
            functionCall.getParam("amountOutMin").value as? BigInteger ?: BigInteger.ZERO
        var contractTo = ""
        if (amountFrom.asBigDecimal() == BigDecimal.ZERO) {
            //in token
            val amountIn =
                functionCall.getParam("amountIn").value as? BigInteger ?: BigInteger.ZERO
            val decimals = resolveDecimals(contractFrom)
            symbolFrom = resolveSymbol(contractFrom)
            amountFrom = TokenUtil.weiToTokenBN(amountIn.toString(), decimals.toLong())
            //out token or iotex
            contractTo = if ((path?.size ?: 0) > 2) path?.get(2).toString() else ""
        } else {
            // in iotex
            // out token
            contractTo = if ((path?.size ?: 0) > 0) path?.last().toString() else ""
        }
        val decimalsTo = resolveDecimals(contractTo)
        val symbolTo = resolveSymbol(contractTo)
        val amountTo = TokenUtil.weiToTokenBN(amountOutMin.toString(), decimalsTo.toLong())

        val signature = Web3Delegate.resolveMethodCode(transaction.data)
        val method = TransferEntryMapRepo().querySignatures(signature, default = methodName)
        return ContractDecodedData(
            listOf(
                OptionEntry(Utils.getApp().getString(R.string.method), Utils.getApp().getString(R.string.xrc_swap)),
                OptionEntry(
                    Utils.getApp().getString(R.string.swap_from),
                    "-$amountFrom $symbolFrom"
                ),
                OptionEntry(
                    Utils.getApp().getString(R.string.minimum_receive),
                    "+$amountTo $symbolTo"
                ),
            ),
            amountFrom,
            transaction.to,
            ACTION_TYPE_SWAP,
            method,
            contractFrom
        )
    }
    private fun resolveDecimals(contract: String): Int {
        //wiotex
        if(contract.lowercase() == Config.CONTRACT_W_IOTEX) return 18
        val chainId = WalletHelper.getCurChainId()
        val token = AppDatabase.getInstance(Utils.getApp()).tokenDao().queryByAddress(chainId, contract)
        return token?.decimals ?: 18
    }

    private fun resolveSymbol(contract: String): String {
        //wiotex
        if(contract.lowercase() == Config.CONTRACT_W_IOTEX) return IoPayConstant.IOTX
        val chainId = WalletHelper.getCurChainId()
        val token = AppDatabase.getInstance(Utils.getApp()).tokenDao().queryByAddress(chainId, contract)
        return token?.symbol ?: SPUtils.getInstance()
            .getString(SPConstant.SP_RPC_NETWORK_NATIVE_CURRENCY, IoPayConstant.IOTX)
    }
}