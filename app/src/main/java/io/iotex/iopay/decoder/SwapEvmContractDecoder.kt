package io.iotex.iopay.decoder

import com.blankj.utilcode.util.LogUtils
import com.blankj.utilcode.util.SPUtils
import com.blankj.utilcode.util.Utils
import io.iotex.iopay.R
import io.iotex.iopay.data.UserStore
import io.iotex.iopay.data.db.ACTION_TYPE_SWAP
import io.iotex.iopay.data.db.AppDatabase
import io.iotex.iopay.repo.TransferEntryMapRepo
import io.iotex.iopay.transaction.bean.OptionEntry
import io.iotex.iopay.util.IoPayConstant
import io.iotex.iopay.util.SPConstant
import io.iotex.iopay.util.TokenUtil
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.util.extension.asBigDecimal
import io.iotex.iopay.util.extension.cleanHexPrefix
import io.iotex.iopay.util.extension.toHexString
import io.iotex.iopay.wallet.web3.Web3Delegate
import net.osslabz.evm.abi.decoder.AbiDecoder
import net.osslabz.evm.abi.decoder.DecodedFunctionCall
import net.osslabz.evm.abi.util.HashUtil
import org.web3j.abi.FunctionReturnDecoder
import org.web3j.abi.TypeReference
import org.web3j.abi.Utils.convert
import org.web3j.abi.datatypes.Address
import org.web3j.abi.datatypes.generated.Uint256
import org.web3j.protocol.core.methods.request.Transaction
import java.math.BigDecimal
import java.math.BigInteger

class SwapEvmContractDecoder(override val transaction: Transaction) : ContractDecoder {
    enum class Method(val signature: String) {
        swapExactTokensForTokens("swapExactTokensForTokens(uint256,uint256,address[],address)"),
        swapTokensForExactTokens("swapTokensForExactTokens(uint256,uint256,address[],address)"),
        execute("execute(bytes,bytes[],uint256)");


        fun signature(): String {
            return HashUtil.hashAsKeccak(signature.toByteArray()).toHexString().substring(0, 10)
        }
    }

    override fun decode(): ContractDecodedData? {
        return decode(transaction.data)
    }

    private fun decode(inputData: String): ContractDecodedData? {
        val decoder = AbiDecoder(Utils.getApp().assets.open("swap_evm.json"))
        val functionCall = decoder.decodeFunctionCall(inputData)
        return when (resolveMethod(inputData)) {
            Method.execute -> decodeExecute(functionCall)
            Method.swapExactTokensForTokens -> decodeSwapExactTokensForTokens(
                functionCall,
                "Swap Exact Tokens For Tokens"
            )

            Method.swapTokensForExactTokens -> decodeSwapTokensForExactTokens(
                functionCall,
                "Swap Tokens For Exact Tokens"
            )

            else -> null
        }
    }

    private fun resolveMethod(inputData: String): Method? {
        return Method.values().firstOrNull {
            inputData.startsWith(it.signature())
        }
    }

    private fun decodeExecute(
        functionCall: DecodedFunctionCall,
    ): ContractDecodedData {
        val byteArray =
            functionCall.getParam("inputs").value as? Array<*>
        //0
        val encodedData0 = byteArray?.get(0).toString()
        val contractFrom: String
        val returnTypes: ArrayList<TypeReference<*>> = ArrayList()
        returnTypes.add(object : TypeReference<Address>() {})
        returnTypes.add(object : TypeReference<Uint256>() {})
        val decodedData: List<*> = FunctionReturnDecoder.decode(encodedData0, convert(returnTypes))
        contractFrom = (decodedData[0] as Address).value // 第一个 uint256
        LogUtils.i("execute","0: "+contractFrom)
        //1
        val encodedData1 = byteArray?.get(1).toString()
        val valueFrom: BigInteger
        val returnTypes1: ArrayList<TypeReference<*>> = ArrayList()
        returnTypes1.add(object : TypeReference<Uint256?>() {})
        returnTypes1.add(object : TypeReference<Uint256?>() {})
        val decodedData1: List<*> = FunctionReturnDecoder.decode(encodedData1, convert(returnTypes1))
        val value11 = (decodedData1[0] as Uint256).value // 第二个返回值是 uint256
        valueFrom = (decodedData1[1] as Uint256).value // 第二个返回值是 uint256
        LogUtils.i("execute","1: "+value11+"  "+valueFrom)
        //2
        val encodedData2 = byteArray?.get(2).toString()
        val returnTypes2: ArrayList<TypeReference<*>> = ArrayList()
        returnTypes2.add(object : TypeReference<Address?>() {})
        returnTypes2.add(object : TypeReference<Address?>() {})
        returnTypes2.add(object : TypeReference<Uint256?>() {})
        val decodedData2: List<*> = FunctionReturnDecoder.decode(encodedData2, convert(returnTypes2))
        val value21: String = (decodedData2[0] as Address).value
        val value22 = (decodedData2[1] as Address).value
        val value23 = (decodedData2[2] as Uint256).value
        LogUtils.i("execute","2: "+value21+"  "+value22+" "+value23)
        //3
        val encodedData3 = byteArray?.get(3).toString()
        val contractTo: String
        val valueTo: BigInteger
        if(encodedData3.length == 194){
            val returnTypes3: ArrayList<TypeReference<*>> = ArrayList()
            returnTypes3.add(object : TypeReference<Address?>() {})
            returnTypes3.add(object : TypeReference<Address?>() {})
            returnTypes3.add(object : TypeReference<Uint256?>() {})

            val decodedData3: List<*> = FunctionReturnDecoder.decode(encodedData3, convert(returnTypes3))
            contractTo = (decodedData3[0] as Address).value
            val value32 = (decodedData3[1] as Address).value
            valueTo = (decodedData3[2] as Uint256).value
            LogUtils.i("execute","3: "+contractTo+"  "+value32 + "  " +valueTo)
        } else {
            val returnTypes3: ArrayList<TypeReference<*>> = ArrayList()
            returnTypes3.add(object : TypeReference<Address?>() {})
            returnTypes3.add(object : TypeReference<Uint256?>() {})

            val decodedData3: List<*> = FunctionReturnDecoder.decode(encodedData3, convert(returnTypes3))
            contractTo = (decodedData3[0] as Address).value
            valueTo = (decodedData3[1] as Uint256).value
            LogUtils.i("execute","3: "+contractTo+"  "+valueTo)
        }

        val decimalsFrom = resolveDecimals(contractFrom)
        val symbolFrom = resolveSymbol(contractFrom)
        val amountFrom = TokenUtil.weiToTokenBN(valueFrom.toString(), decimalsFrom.toLong())

        val decimalsTo = resolveDecimals(contractTo)
        val symbolTo = resolveSymbol(contractTo)
        val amountTo = TokenUtil.weiToTokenBN(valueTo.toString(), decimalsTo.toLong())

        val signature = Web3Delegate.resolveMethodCode(transaction.data)
        val method = TransferEntryMapRepo().querySignatures(signature)
        return ContractDecodedData(
            listOf(
                OptionEntry(Utils.getApp().getString(R.string.method), Utils.getApp().getString(R.string.xrc_swap)),
                OptionEntry(
                    Utils.getApp().getString(R.string.swap_from),
                    "-$amountFrom $symbolFrom"
                ),
                OptionEntry(
                    Utils.getApp().getString(R.string.minimum_receive),
                    "+$amountTo $symbolTo"
                ),
            ),
            amountFrom,
            transaction.to,
            ACTION_TYPE_SWAP,
            method,
            contractFrom
        )
    }

    private fun decodeSwapTokensForExactTokens(
        functionCall: DecodedFunctionCall,
        methodName: String
    ): ContractDecodedData {
        val amount = TokenUtil.weiToTokenBN(
            transaction.value.cleanHexPrefix().toBigIntegerOrNull(16).toString()
        )
        var amountFrom = amount
        var symbolFrom = UserStore.getNetworkSymbol()
        val path = functionCall.getParam("path").value as? Array<*>
        val contractFrom = if (path?.isNotEmpty() == true) path[0].toString() else ""
        val amountOutMin =
            functionCall.getParam("amountOut").value as? BigInteger ?: BigInteger.ZERO
        var contractTo = ""
        if (amountFrom.asBigDecimal() == BigDecimal.ZERO) {
            //in token
            val amountIn =
                functionCall.getParam("amountInMax").value as? BigInteger ?: BigInteger.ZERO
            val decimals = resolveDecimals(contractFrom)
            symbolFrom = resolveSymbol(contractFrom)
            amountFrom = TokenUtil.weiToTokenBN(amountIn.toString(), decimals.toLong())
            //out token or iotex
            contractTo = if ((path?.size ?: 0) > 2) path?.get(2).toString() else ""
        } else {
            // in iotex
            // out token
            contractTo = if ((path?.size ?: 0) > 0) path?.last().toString() else ""
        }
        val decimalsTo = resolveDecimals(contractTo)
        val symbolTo = resolveSymbol(contractTo)
        val amountTo = TokenUtil.weiToTokenBN(amountOutMin.toString(), decimalsTo.toLong())

        val signature = Web3Delegate.resolveMethodCode(transaction.data)
        val method = TransferEntryMapRepo().querySignatures(signature, default = methodName)
        return ContractDecodedData(
            listOf(
                OptionEntry(Utils.getApp().getString(R.string.method), Utils.getApp().getString(R.string.xrc_swap)),
                OptionEntry(
                    Utils.getApp().getString(R.string.swap_from),
                    "-$amountFrom $symbolFrom"
                ),
                OptionEntry(
                    Utils.getApp().getString(R.string.minimum_receive),
                    "+$amountTo $symbolTo"
                ),
            ),
            amountFrom,
            transaction.to,
            ACTION_TYPE_SWAP,
            method,
            contractFrom
        )
    }

    private fun decodeSwapExactTokensForTokens(
        functionCall: DecodedFunctionCall,
        methodName: String
    ): ContractDecodedData {
        val amount = TokenUtil.weiToTokenBN(
            transaction.value.cleanHexPrefix().toBigIntegerOrNull(16).toString()
        )
        var amountFrom = amount
        var symbolFrom = UserStore.getNetworkSymbol()
        val path = functionCall.getParam("path").value as? Array<*>
        val contractFrom = if (path?.isNotEmpty() == true) path[0].toString() else ""
        val amountOutMin =
            functionCall.getParam("amountOutMin").value as? BigInteger ?: BigInteger.ZERO
        var contractTo = ""
        if (amountFrom.asBigDecimal() == BigDecimal.ZERO) {
            //in token
            val amountIn =
                functionCall.getParam("amountIn").value as? BigInteger ?: BigInteger.ZERO
            val decimals = resolveDecimals(contractFrom)
            symbolFrom = resolveSymbol(contractFrom)
            amountFrom = TokenUtil.weiToTokenBN(amountIn.toString(), decimals.toLong())
            //out token or iotex
            contractTo = if ((path?.size ?: 0) > 2) path?.get(2).toString() else ""
        } else {
            // in iotex
            // out token
            contractTo = if ((path?.size ?: 0) > 0) path?.last().toString() else ""
        }
        val decimalsTo = resolveDecimals(contractTo)
        val symbolTo = resolveSymbol(contractTo)
        val amountTo = TokenUtil.weiToTokenBN(amountOutMin.toString(), decimalsTo.toLong())

        val signature = Web3Delegate.resolveMethodCode(transaction.data)
        val method = TransferEntryMapRepo().querySignatures(signature, default = methodName)
        return ContractDecodedData(
            listOf(
                OptionEntry(Utils.getApp().getString(R.string.method), Utils.getApp().getString(R.string.xrc_swap)),
                OptionEntry(
                    Utils.getApp().getString(R.string.swap_from),
                    "-$amountFrom $symbolFrom"
                ),
                OptionEntry(
                    Utils.getApp().getString(R.string.minimum_receive),
                    "+$amountTo $symbolTo"
                ),
            ),
            amountFrom,
            transaction.to,
            ACTION_TYPE_SWAP,
            method,
            contractFrom
        )
    }

    private fun resolveDecimals(contract: String): Int {
        val chainId = WalletHelper.getCurChainId()
        val token = AppDatabase.getInstance(Utils.getApp()).tokenDao()
            .queryByAddress(chainId, contract)
        return token?.decimals ?: 18
    }

    private fun resolveSymbol(contract: String): String {
        val chainId = WalletHelper.getCurChainId()
        val token = AppDatabase.getInstance(Utils.getApp()).tokenDao()
            .queryByAddress(chainId, contract)
        return token?.symbol ?: SPUtils.getInstance()
            .getString(SPConstant.SP_RPC_NETWORK_NATIVE_CURRENCY, IoPayConstant.IOTX)
    }
}