package io.iotex.iopay.decoder

import com.blankj.utilcode.util.SPUtils
import com.blankj.utilcode.util.Utils
import io.iotex.iopay.R
import io.iotex.iopay.data.db.ACTION_TYPE_SWAP
import io.iotex.iopay.data.db.AppDatabase
import io.iotex.iopay.transaction.bean.OptionEntry
import io.iotex.iopay.util.IoPayConstant
import io.iotex.iopay.util.SPConstant
import io.iotex.iopay.util.TokenUtil
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.util.extension.cleanHexPrefix
import io.iotex.iopay.util.extension.toHexString
import net.osslabz.evm.abi.decoder.AbiDecoder
import net.osslabz.evm.abi.decoder.DecodedFunctionCall
import net.osslabz.evm.abi.util.HashUtil
import org.web3j.protocol.core.methods.request.Transaction
import java.math.BigInteger

class WIOTXContractDecoder(override val transaction: Transaction) : ContractDecoder {
    enum class Method(val signature: String) {
        Deposit("deposit()"),
        Withdraw("withdraw(uint256)");

        fun signature(): String {
            return HashUtil.hashAsKeccak(signature.toByteArray()).toHexString().substring(0, 10)
        }
    }

    override fun decode(): ContractDecodedData? {
        val decoder = AbiDecoder(Utils.getApp().assets.open("wiotx.json"))
        val functionCall = decoder.decodeFunctionCall(transaction.data)
        return when(resolveMethod()) {
            Method.Deposit -> decodeDeposit()
            Method.Withdraw -> decodeWithdraw(functionCall)
            null -> null
        }
    }

    private fun resolveMethod(): Method? {
        return Method.values().firstOrNull {
            transaction.data.startsWith(it.signature())
        }
    }

    private fun decodeDeposit(): ContractDecodedData {
        val value = BigInteger(transaction.value.cleanHexPrefix(), 16).toString()
        val amount = TokenUtil.weiToTokenBN(value)
        val symbol = resolveSymbol("")
        return ContractDecodedData(
            listOf(
                OptionEntry(Utils.getApp().getString(R.string.method), Utils.getApp().getString(R.string.deposit)),
                OptionEntry(Utils.getApp().getString(R.string.amount), "$amount $symbol"),
            ),
            amount,
            transaction.to,
            ACTION_TYPE_SWAP,
            R.string.deposit.toString(),
        )
    }

    private fun decodeWithdraw(functionCall: DecodedFunctionCall): ContractDecodedData {
        val wad = functionCall.getParam("wad").value as? BigInteger
        val amount = TokenUtil.weiToTokenBN(wad.toString())
        val symbol = resolveSymbol("")
        return if (wad == null) {
            throw IllegalArgumentException("invalid input")
        } else {
            ContractDecodedData(
                listOf(
                    OptionEntry(Utils.getApp().getString(R.string.method), Utils.getApp().getString(R.string.withdraw)),
                    OptionEntry(Utils.getApp().getString(R.string.amount), "$amount W$symbol"),
                ),
                TokenUtil.weiToTokenBN(wad.toString()),
                transaction.to,
                ACTION_TYPE_SWAP,
                R.string.withdraw.toString(),
            )
        }
    }

    private fun resolveSymbol(contract: String): String {
        val chainId = WalletHelper.getCurChainId()
        val token = AppDatabase.getInstance(Utils.getApp()).tokenDao().queryByAddress(chainId, contract)
        return token?.symbol ?: SPUtils.getInstance()
            .getString(SPConstant.SP_RPC_NETWORK_NATIVE_CURRENCY, IoPayConstant.IOTX)
    }
}