package io.iotex.iopay.fragment.base

import android.content.DialogInterface
import android.content.Intent
import android.os.Bundle
import android.os.Parcelable
import android.util.SparseArray
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.FragmentActivity
import io.iotex.iopay.R
import java.io.Serializable

abstract class BaseDialogFragment : DialogFragment(), FragmentResultCallback {

    internal val tag = this::class.java.simpleName

    private var bundle: Bundle? = null
    private var callback: FragmentResultCallback? = null
    private var mRequestCode: Int = 0
    protected abstract val dialogLayoutID: Int

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        isCancelable = true
        setStyle(STYLE_NO_FRAME, R.style.Theme_dialog)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        val mBundle = arguments
        if (mBundle != null) {
            mRequestCode = mBundle.getInt("requestCode", 0)
        }
        return inflater.inflate(dialogLayoutID, container, false)
    }
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initView(view, savedInstanceState)
    }

    fun isShowing(): Boolean {
        if (dialog != null) {
            return dialog!!.isShowing
        } else {
            return false
        }
    }

    protected abstract fun initView(view: View, savedInstanceState: Bundle?)

    override fun onFragmentResult(requestCode: Int, resultCode: Int, data: Intent) {
    }

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        dismissAllowingStateLoss()
    }
    fun showAllowingStateLoss(activity: FragmentActivity?, tag: String) {
        if (activity == null || activity.isFinishing || this.isAdded) {
            return
        }
        try {
            val fm = activity.supportFragmentManager
            val ft = fm.beginTransaction()
            if (!fm.executePendingTransactions()) {
                ft.add(this, tag)
                ft.commitAllowingStateLoss()
            }
        } catch (e: Exception) {
        }
    }

    fun showAllowingStateLoss(activity: FragmentActivity?) {
        if (activity == null || activity.isFinishing || this.isAdded) {
            return
        }
        try {
            val fm = activity.supportFragmentManager
            val ft = fm.beginTransaction()
            if (!fm.executePendingTransactions()) {
                ft.add(this, this::class.java.name)
                ft.commitAllowingStateLoss()
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun fragmentCallbackResult(resultCode: Int, intent: Intent?) {
        if (callback != null) {
            callback!!.onFragmentResult(mRequestCode, resultCode, intent)
        }
    }

    fun setFragmentResultCallback(callback: FragmentResultCallback) {
        this.callback = callback
    }

    fun dismiss(resultCode: Int) {
        super.dismissAllowingStateLoss()
        fragmentCallbackResult(resultCode, null)
    }

    fun dismiss(resultCode: Int, intent: Intent) {
        super.dismissAllowingStateLoss()
        fragmentCallbackResult(resultCode, intent)
    }

    fun dismissAllowingStateLoss(resultCode: Int) {
        super.dismissAllowingStateLoss()
        fragmentCallbackResult(resultCode, null)
    }

    fun dismissAllowingStateLoss(resultCode: Int, intent: Intent) {
        super.dismissAllowingStateLoss()
        fragmentCallbackResult(resultCode, intent)
    }

    override fun onResume() {
        super.onResume()
        if (view != null)
            requireView().isClickable = true
    }

    override fun onPause() {
        super.onPause()
    }

    override fun onDestroy() {
        super.onDestroy()
    }

    fun addValues(key: String, value: Any) {
        if (bundle == null) {
            bundle = Bundle()
        }
        if (value is Boolean) {
            bundle!!.putBoolean(key, value)
        } else if (value is Byte) {
            bundle!!.putByte(key, value)
        } else if (value is Char) {
            bundle!!.putChar(key, value)
        } else if (value is Short) {
            bundle!!.putShort(key, value)
        } else if (value is Int) {
            bundle!!.putInt(key, value)
        } else if (value is Int) {
            bundle!!.putInt(key, value)
        } else if (value is Long) {
            bundle!!.putLong(key, value)
        } else if (value is Float) {
            bundle!!.putFloat(key, value)
        } else if (value is Double) {
            bundle!!.putDouble(key, value)
        } else if (value is String) {
            bundle!!.putString(key, value)
        } else if (value is CharSequence) {
            bundle!!.putCharSequence(key, value)
        } else if (value is Serializable) {
            bundle!!.putSerializable(key, value)
        } else if (value is Parcelable) {
            bundle!!.putParcelable(key, value)
        } else if (value is ArrayList<*>) {
            if (value[0] is Parcelable) {
                bundle!!.putParcelableArrayList(key, value as ArrayList<out Parcelable>?)
            } else if (value[0] is CharSequence) {
                bundle!!.putCharSequenceArrayList(key, value as ArrayList<CharSequence>?)
            } else if (value[0] is Int) {
                bundle!!.putIntegerArrayList(key, value as ArrayList<Int>?)
            } else if (value[0] is String) {
                bundle!!.putStringArrayList(key, value as ArrayList<String>?)
            } else {
                Exception("params error")
            }
        } else if (value is SparseArray<*>) {
            if (value.get(0) is Parcelable) {
                bundle!!.putSparseParcelableArray(key, value as SparseArray<out Parcelable>?)
            } else {
                Exception("params error")
            }
        } else if (value is Array<*>) {
            bundle!!.putParcelableArray(key, value as Array<Parcelable>?)
        } else if (value is BooleanArray) {
            bundle!!.putBooleanArray(key, value)
        } else if (value is ByteArray) {
            bundle!!.putByteArray(key, value)
        } else if (value is CharArray) {
            bundle!!.putCharArray(key, value)
        } else if (value is Array<*>) {
            bundle!!.putCharSequenceArray(key, value as Array<CharSequence>?)
        } else if (value is DoubleArray) {
            bundle!!.putDoubleArray(key, value)
        } else if (value is FloatArray) {
            bundle!!.putFloatArray(key, value)
        } else if (value is IntArray) {
            bundle!!.putIntArray(key, value)
        } else if (value is LongArray) {
            bundle!!.putLongArray(key, value)
        } else if (value is ShortArray) {
            bundle!!.putShortArray(key, value)
        } else if (value is Array<*>) {
            bundle!!.putStringArray(key, value as Array<String>?)
        } else {
            Exception("params error")
        }
    }

    fun removeValue(key: String) {
        bundle?.let {
            it.remove(key)
        }
    }

    fun commitAddValues() {
        if (bundle != null&&!isAdded) {
            arguments = bundle
        }
    }

    override fun onStart() {
        super.onStart()
        val dialogWindow = dialog?.window
        val lp = dialogWindow?.attributes
        dialogWindow?.setGravity(Gravity.BOTTOM or Gravity.CENTER_HORIZONTAL)
        lp?.width = ViewGroup.LayoutParams.MATCH_PARENT
        lp?.height = ViewGroup.LayoutParams.WRAP_CONTENT
        dialogWindow?.attributes = lp
    }
}
