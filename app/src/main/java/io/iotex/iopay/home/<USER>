package io.iotex.iopay.home

import com.blankj.utilcode.util.LogUtils
import io.iotex.base.BaseViewModel
import io.iotex.base.bindbase.BaseBindFragment
import io.iotex.iopay.R
import io.iotex.iopay.databinding.FragmentGiftBinding
import io.iotex.iopay.repo.GiftRepo
import io.iotex.iopay.support.eventbus.GiftUploadEvent
import io.iotex.iopay.support.eventbus.SwitchWalletEvent
import io.iotex.iopay.support.eventbus.WalletAvatarChangeEvent
import io.iotex.iopay.xapp.XAppFragment
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

class GiftFragment:BaseBindFragment<BaseViewModel,FragmentGiftBinding>(R.layout.fragment_gift) {
    companion object{
        fun newInstance(): GiftFragment{
            return GiftFragment()
        }
    }

    private val webViewFrag by lazy{
        val url = GiftRepo().getGiftCenterUrl()
        LogUtils.i("gift", url)
        XAppFragment.newInstance(url, "", "", false)
    }

    override fun initView() {
        EventBus.getDefault().register(this)

        childFragmentManager.beginTransaction().add(R.id.flLayout, webViewFrag).commit()
        mBinding.refreshLayout.setOnRefreshListener {
            mBinding.refreshLayout.finishRefresh()
            webViewFrag.firstPageFinished = false
            webViewFrag.webView.reload()
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onSwitchWalletEvent(event: SwitchWalletEvent) {
        val url = GiftRepo().getGiftCenterUrl()
        webViewFrag.webView.loadUrl(url)
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onWalletAvatarChangeEvent(event: WalletAvatarChangeEvent) {
        val url = GiftRepo().getGiftCenterUrl()
        webViewFrag.webView.loadUrl(url)
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onGiftUploadEvent(event: GiftUploadEvent) {
        kotlin.runCatching {
            webViewFrag.webView.evaluateJavascript("window.taskChange(${event.taskId});", null)
        }
    }

    override fun onDestroy() {
        EventBus.getDefault().unregister(this)
        super.onDestroy()
    }
}