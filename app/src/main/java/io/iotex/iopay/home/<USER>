package io.iotex.iopay.home

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import io.iotex.base.BaseViewModel
import io.iotex.iopay.R
import io.iotex.iopay.base.BaseBindToolbarActivity
import io.iotex.iopay.databinding.ActivitySwapBinding
import io.iotex.iopay.util.Config

class SwapActivity :
    BaseBindToolbarActivity<BaseViewModel, ActivitySwapBinding>(R.layout.activity_swap) {
    companion object {
        private const val KEY_CHAIN_ID = "key_chain_id"
        private const val KEY_FROM = "key_from"
        private const val KEY_TO = "key_to"
        fun startActivity(context: Context, chainId: Int, from: String?, to: String? = "") {
            val intent = Intent(context, SwapActivity::class.java)
            intent.putExtra(KEY_CHAIN_ID, chainId)
            intent.putExtra(KEY_FROM, from)
            intent.putExtra(KEY_TO, to)
            context.startActivity(intent)
        }
    }

    private val chainId by lazy {
        intent?.getIntExtra(KEY_CHAIN_ID, Config.IOTEX_CHAIN_ID) ?: Config.IOTEX_CHAIN_ID
    }
    private val from by lazy {
        intent?.getStringExtra(KEY_FROM)
    }
    private val to by lazy {
        intent?.getStringExtra(KEY_TO)
    }

    @SuppressLint("CommitTransaction")
    override fun initView() {
        setToolbarTitle(getString(R.string.xrc_swap))
        supportFragmentManager.beginTransaction()
            .replace(R.id.flLayout, SwapFragment.newInstance(chainId, from, to))
            .commitNowAllowingStateLoss()
    }
}