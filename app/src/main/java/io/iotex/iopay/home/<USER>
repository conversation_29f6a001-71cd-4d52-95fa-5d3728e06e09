package io.iotex.iopay.home

import android.annotation.SuppressLint
import io.iotex.base.BaseViewModel
import io.iotex.base.bindbase.BaseBindFragment
import io.iotex.iopay.R
import io.iotex.iopay.databinding.FragmentSwapHomeBinding
import io.iotex.iopay.reactnative.ReactNativeFragment
import io.iotex.iopay.reactnative.ReactScene
import io.iotex.iopay.util.FireBaseEvent
import io.iotex.iopay.util.FireBaseUtil

class SwapHomeFragment :
    BaseBindFragment<BaseViewModel, FragmentSwapHomeBinding>(R.layout.fragment_swap_home) {

    private val swapFragment by lazy {
        SwapFragment()
    }

    private val buyFragment by lazy {
        ReactNativeFragment.newInstance(ReactScene.BuyContainer.name)
    }

    @SuppressLint("CommitTransaction")
    override fun initView() {
        mBinding.tabView.setItem(
            arrayListOf(
                getString(R.string.xrc_swap),
                getString(R.string.buy)
            )
        )
        if (childFragmentManager.findFragmentByTag("swapFragment") == null) {
            childFragmentManager.beginTransaction()
                .add(R.id.flLayout, swapFragment, "swapFragment")
                .commitNow()
        }
        if (childFragmentManager.findFragmentByTag("buyFragment") == null) {
            childFragmentManager.beginTransaction()
                .add(R.id.flLayout, buyFragment, "buyFragment")
                .commitNow()
        }
        childFragmentManager.beginTransaction()
            .show(swapFragment)
            .hide(buyFragment)
            .commitNow()
        mBinding.tabView.onSelect = {
            when (it) {
                0 -> {
                    childFragmentManager.beginTransaction()
                        .show(swapFragment)
                        .hide(buyFragment)
                        .commitAllowingStateLoss()
                    FireBaseUtil.logFireBase(FireBaseEvent.ACTION_SWAP_TAB_SWAP)
                }

                1 -> {
                    childFragmentManager.beginTransaction()
                        .show(buyFragment)
                        .hide(swapFragment)
                        .commitAllowingStateLoss()
                    FireBaseUtil.logFireBase(FireBaseEvent.ACTION_SWAP_TAB_BUY)
                }
            }

        }
    }
}