package io.iotex.iopay.home

import android.annotation.SuppressLint
import com.blankj.utilcode.util.KeyboardUtils
import io.iotex.base.BaseViewModel
import io.iotex.base.bindbase.BaseBindFragment
import io.iotex.iopay.R
import io.iotex.iopay.data.UserStore
import io.iotex.iopay.databinding.FragmentBinoAiBinding
import io.iotex.iopay.support.eventbus.WebViewBinoAiRefreshErrorEvent
import io.iotex.iopay.util.Config
import io.iotex.iopay.util.PageEventUtil
import io.iotex.iopay.util.extension.dp2px
import io.iotex.iopay.xapp.XAppFragment
import org.greenrobot.eventbus.Subscribe

class BinoAiFragment :
    BaseBindFragment<BaseViewModel, FragmentBinoAiBinding>(R.layout.fragment_bino_ai) {
    companion object {
        fun newInstance(): BinoAiFragment {
            return BinoAiFragment()
        }
    }

    override fun useEventBus(): Boolean {
        return true
    }

    @SuppressLint("CommitTransaction")
    override fun initView() {
        val baseUrl = UserStore.getBinoAiUrl().ifEmpty {
            Config.BINO_AI_WEB_URL
        }
        val url = if (UserStore.isDarkTheme()) {
            "${baseUrl}?theme=dark"
        } else {
            "${baseUrl}?theme=light"
        }
        val webViewFrag = XAppFragment.newInstance(url, "", "", false)
        childFragmentManager.beginTransaction().add(R.id.flLayout, webViewFrag).commit()

        mBinding.refreshLayout.isEnabled = false
        mBinding.refreshLayout.setOnRefreshListener {
            mBinding.refreshLayout.isRefreshing = false
            mBinding.refreshLayout.isEnabled = false
            webViewFrag.webView.reload()
        }

        PageEventUtil.logEvent(PageEventUtil.MENUDEPINSCAN)
        activity?.let {
            KeyboardUtils.registerSoftInputChangedListener(it) { height ->
                if (height > 100) {
                    val layoutParams = mBinding.keyboardView.layoutParams
                    layoutParams.height = height - 50.dp2px()
                    mBinding.keyboardView.layoutParams = layoutParams
                } else {
                    val layoutParams = mBinding.keyboardView.layoutParams
                    layoutParams.height = 0
                    mBinding.keyboardView.layoutParams = layoutParams
                }
            }
        }
    }

    @Subscribe
    fun onWebViewBinoAiRefreshErrorEvent(event: WebViewBinoAiRefreshErrorEvent) {
        mBinding.refreshLayout.isEnabled = true
    }
}