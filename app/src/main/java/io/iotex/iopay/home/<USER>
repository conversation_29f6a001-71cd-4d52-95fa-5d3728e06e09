package io.iotex.iopay.home

import android.os.Bundle
import androidx.core.view.isInvisible
import androidx.core.view.isVisible
import androidx.core.widget.addTextChangedListener
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import com.blankj.utilcode.util.ToastUtils
import com.blankj.utilcode.util.Utils
import com.drakeet.multitype.MultiTypeAdapter
import com.robinhood.ticker.TickerUtils
import io.iotex.base.bindbase.BaseBindFragment
import io.iotex.iopay.R
import io.iotex.iopay.SchemeUtil
import io.iotex.iopay.data.UserStore
import io.iotex.iopay.data.bean.MethodParameters
import io.iotex.iopay.data.db.ACTION_TYPE_SWAP
import io.iotex.iopay.data.db.TokenEntry
import io.iotex.iopay.databinding.FragmentSwapBinding
import io.iotex.iopay.home.dialog.SmartRouterDialog
import io.iotex.iopay.home.dialog.SwapSlipDialog
import io.iotex.iopay.home.dialog.SwapTokenDialog
import io.iotex.iopay.home.dialog.SwapTokenViewModel
import io.iotex.iopay.home.dialog.TipsDialog
import io.iotex.iopay.home.item.TokenRouterBinder
import io.iotex.iopay.support.eventbus.ActionRefreshEvent
import io.iotex.iopay.support.eventbus.FavoriteOrDislikeERC20Event
import io.iotex.iopay.support.eventbus.MainPullRefresh
import io.iotex.iopay.support.eventbus.NetworkSwitchEvent
import io.iotex.iopay.support.eventbus.RefreshMainPriceEvent
import io.iotex.iopay.support.eventbus.SwitchWalletEvent
import io.iotex.iopay.support.eventbus.TokenLoadFinishEvent
import io.iotex.iopay.transaction.bean.OptionEntry
import io.iotex.iopay.util.Config
import io.iotex.iopay.util.EIP712Converter
import io.iotex.iopay.util.FireBaseEvent
import io.iotex.iopay.util.FireBaseUtil
import io.iotex.iopay.util.TokenUtil
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.util.extension.asBigDecimal
import io.iotex.iopay.util.extension.loadImage
import io.iotex.iopay.util.extension.setGone
import io.iotex.iopay.util.extension.setVisible
import io.iotex.iopay.util.extension.toJson
import io.iotex.iopay.wallet.solana.SolanaWeb3Dialog
import io.iotex.iopay.wallet.web3.FunctionEncodeUtil
import io.iotex.iopay.wallet.web3.Web3Repository
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.web3j.protocol.core.Response
import java.math.BigDecimal
import java.math.BigInteger

class SwapFragment : BaseBindFragment<SwapViewModel, FragmentSwapBinding>(R.layout.fragment_swap) {

    companion object {
        const val EXACT_INPUT = "EXACT_INPUT"
        const val EXACT_OUTPUT = "EXACT_OUTPUT"
        private const val KEY_CHAIN_ID = "key_chain_id"
        private const val KEY_FROM_ADDRESS = "key_from_address"
        private const val KEY_TO_ADDRESS = "key_to_address"
        fun newInstance(chainId:Int,from: String?, to: String?): SwapFragment {
            val args = Bundle()
            args.putInt(KEY_CHAIN_ID, chainId)
            args.putString(KEY_FROM_ADDRESS, from)
            args.putString(KEY_TO_ADDRESS, to)
            val fragment = SwapFragment()
            fragment.arguments = args
            return fragment
        }
    }

    private var defaultChainId = Config.IOTEX_CHAIN_ID

    private val defaultFromAddress by lazy {
        arguments?.getString(KEY_FROM_ADDRESS)
    }

    private val defaultToAddress by lazy {
        arguments?.getString(KEY_TO_ADDRESS)
    }

    private var showDialog = false

    override fun useEventBus(): Boolean {
        return true
    }

    private val mSwapTokenViewModel by lazy {
        ViewModelProvider(this)[SwapTokenViewModel::class.java]
            .apply {
                lifecycle.addObserver(this)
            }
    }

    private val mAdapterRouter = MultiTypeAdapter()
    private val binderTokenRouter = TokenRouterBinder()
    private var slipType = 0
    private var fromToken: TokenEntry? = null
    private var toToken: TokenEntry? = null
    private var slip = BigDecimal(0.5)
    private var tradeType = "EXACT_INPUT"
    private var router = OptionEntry("Best Trade", "v2,v3,mixed")
    private var max = false
    private var swapTokenDialog: SwapTokenDialog? = null
    override fun initView() {
        val chainId = arguments?.getInt(KEY_CHAIN_ID) ?: UserStore.getChainId()
        if(Config.SWAP_SUPPORT_CHAIN.contains(chainId)) defaultChainId = chainId
        mBinding.tvMax.setOnClickListener {
            max = true
            val balance = getFromBalance()
            mBinding.etFrom.requestFocus()
            mBinding.etFrom.setText(balance)
            mBinding.etFrom.setSelection(balance.length)
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_SWAP_MAX)
        }

        mBinding.recyclerViewRouter.layoutManager = LinearLayoutManager(requireContext())
        mAdapterRouter.register(binderTokenRouter)
        mBinding.recyclerViewRouter.adapter = mAdapterRouter

        mBinding.ivSlip.setOnClickListener {
            TipsDialog().apply {
                title = Utils.getApp().getString(R.string.point_of_slip)
                message = Utils.getApp().getString(R.string.slippage_is_when_the_transaction_price)
            }.show(childFragmentManager, System.currentTimeMillis().toString())
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_SWAP_SLIP_TIPS)
        }
        mBinding.ivRouter.setOnClickListener {
            TipsDialog().apply {
                title = Utils.getApp().getString(R.string.smart_router)
                message = Utils.getApp().getString(R.string.when_available_aggregate_v2_v3)
            }.show(childFragmentManager, System.currentTimeMillis().toString())
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_SWAP_ROUTER_TIPS)
        }
        mBinding.tvRouter.text = router.key
        mBinding.llRouter.setOnClickListener {
            SmartRouterDialog(
                router.key,
                fromToken?.chainId ?: toToken?.chainId ?: Config.IOTEX_CHAIN_ID
            ).apply {
                onSelectItem = {
                    router = it
                    mBinding.tvRouter.text = it.key
                    getSwapDetail()
                }
            }.show(childFragmentManager, System.currentTimeMillis().toString())
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_SWAP_ROUTER)
        }

        mBinding.llSlip.setOnClickListener {
            val oldSlip = mBinding.tvSlip.text.toString().replace("%", "")
            val fromValue = mBinding.etFrom.text.toString()
            val toValue = mBinding.etTo.text.toString()
            SwapSlipDialog(
                oldSlip,
                tradeType,
                fromValue,
                fromToken?.symbol ?: "",
                toValue,
                toToken?.symbol ?: ""
            ).apply {
                type = slipType
                onSlipChange = { type, slip ->
                    slipType = type
                    <EMAIL> = slip
                    mBinding.tvSlip.text = "$slip%"
                    getSwapDetail()
                }
            }.show(childFragmentManager, System.currentTimeMillis().toString())
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_SWAP_SLIP)
        }
        mBinding.llFrom.setOnClickListener {
            if (swapTokenDialog != null) return@setOnClickListener
            swapTokenDialog = SwapTokenDialog(fromToken?.chainId ?: toToken?.chainId ?: Config.IOTEX_CHAIN_ID).apply {
                onItemSelected = { token ->
                    if (token.chainId != toToken?.chainId) {
                        showToToken(null)
                    }
                    if (token.address == toToken?.address) {
                        switchToken()
                    } else {
                        showFromToken(token)
                    }
                }
                onDisMiss = {
                    swapTokenDialog = null
                }
            }
            swapTokenDialog?.show(childFragmentManager, System.currentTimeMillis().toString())
        }
        mBinding.llTo.setOnClickListener {
            if (swapTokenDialog != null) return@setOnClickListener
            swapTokenDialog = SwapTokenDialog(toToken?.chainId ?: fromToken?.chainId ?: Config.IOTEX_CHAIN_ID).apply {
                onItemSelected = { token ->
                    if (token.chainId != fromToken?.chainId) {
                        showFromToken(null)
                    }
                    if (token.address == fromToken?.address) {
                        switchToken()
                    } else {
                        showToToken(token)
                    }
                }
                onDisMiss = {
                    swapTokenDialog = null
                }
            }
            swapTokenDialog?.show(childFragmentManager, System.currentTimeMillis().toString())
        }

        mBinding.etFrom.addTextChangedListener {
            mViewModel.fromChangeText(it.toString())
        }

        mViewModel.fromChangeLiveData.observe(this) {
            clearPresent(it.toString())
            calculateBalance()
            calculateFromValue()
            if (tradeType == EXACT_INPUT) {
                getSwapDetail()
            }
        }

        mBinding.etFrom.setOnFocusChangeListener { _, hasFocus ->
            if (hasFocus) tradeType = EXACT_INPUT
        }

        mBinding.etTo.addTextChangedListener {
            mViewModel.toChangeText(it.toString())
        }

        mViewModel.toChangeLiveData.observe(this) {
            calculateToValue()
            if (tradeType == EXACT_OUTPUT) {
                getSwapDetail()
            }
        }

        mBinding.etTo.setOnFocusChangeListener { _, hasFocus ->
            if (hasFocus) tradeType = EXACT_OUTPUT
        }

        mBinding.ivShow.setOnClickListener {
            mBinding.llCover.setGone()
        }

        mBinding.ivSwitch.setOnClickListener {
            mBinding.ivSwitch.isEnabled = false
            switchToken()
            mBinding.ivSwitch.isEnabled = true
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_SWAP_SWITCH)
        }

        mBinding.tvConfirm.setOnClickListener {
            mBinding.tvConfirm.isEnabled = false
            if(fromToken?.chainId == Config.SOLANA_MAIN_CHAIN_ID){
                confirmSolana()
            } else {
                confirm()
            }
            mBinding.tvConfirm.isEnabled = true
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_SWAP_CONFIRM)
        }

        mBinding.refreshLayout.setOnRefreshListener {
            fromToken?.let {
                mViewModel.getDefaultToken(it.chainId, it.address, true)
            }
            toToken?.let {
                mViewModel.getDefaultToken(it.chainId, it.address, false)
            }
            mSwapTokenViewModel.loadSortToken()
            mBinding.refreshLayout.isRefreshing = false
        }

        itemIsVisible()
    }

    private fun confirmSolana(){
        val okxTransaction = mViewModel.okxParametersLiveData.value ?: return
        showLoading()
        val fromValue = mBinding.etFrom.text.toString()
        SolanaWeb3Dialog.sendRawTransaction(
            getOptions(),
            "",
            okxTransaction.transaction,
            okxTransaction.versionedTransaction,
            "ioPay",
            "Swap",
            type = ACTION_TYPE_SWAP,
            fromValue,
            fromToken?.symbol?:"",
            confirm = {
                cleanUi()
            }
        ){
            hideLoading()
            if (it != null) {
                mViewModel.hash = it
                mViewModel.checkResAndAddToken(toToken?.address ?: "")
            } else {
                mBinding.tvConfirm.isEnabled = true
                mBinding.tvConfirm.alpha = 1f
            }
        }
    }

    private fun confirm() {
        if (!fromToken?.address.isNullOrBlank()) {
            if (fromToken?.chainId == Config.IOTEX_CHAIN_ID) {
                val param =
                    mViewModel.methodParametersListLiveData.value ?: return
                mViewModel.checkApprove(fromToken?.address ?: "", param.to)
            } else {
                val value = getFromValue()
                mViewModel.checkUniApprove(
                    value.toString(),
                    fromToken?.address,
                    fromToken?.chainId,
                    toToken?.address
                )
            }
        } else {
            swap()
        }
    }

    private fun switchToken() {
        val empty = fromToken
        val emptyText = mBinding.etFrom.text.toString()
        mBinding.etFrom.setText(mBinding.etTo.text.toString())
        mBinding.etTo.setText(emptyText)
        showFromToken(toToken)
        showToToken(empty)
    }

    private fun clearPresent(from: String) {
        val amount = getFromBalance()
        if (amount != from) {
            max = false
        }
    }

    private fun getFromBalance():String{
        val balance = TokenUtil.weiToTokenBN(getFromValue().toString(),fromToken?.decimals?.toLong()?:18)
        return TokenUtil.displayBalance(balance)
    }

    private fun getFromValue(): BigInteger {
        var amount: BigInteger
        if (max) {
            if (fromToken?.address.isNullOrBlank()) {
                val gas =
                    mViewModel.gasLiveData.value?.get(fromToken?.chainId ?: Config.IOTEX_CHAIN_ID)
                        ?: BigInteger.ZERO
                amount =
                    ((fromToken?.balance?.toBigIntegerOrNull() ?: BigInteger.ZERO) - gas)
                if (amount < BigInteger.ZERO) {
                    amount = BigInteger.ZERO
                }
            } else {
                amount = fromToken?.balance?.toBigIntegerOrNull()?: BigInteger.ZERO
            }
        } else {
            val from = mBinding.etFrom.text.toString()
            amount = TokenUtil.toWei(from,fromToken?.decimals?:18)
        }
        return amount
    }

    private fun getSwapDetail() {
        mBinding.llResp.setGone()
        if (tradeType == EXACT_INPUT) {
            val bigValue = getFromValue()
            mBinding.etTo.setText("")
            when (fromToken?.chainId) {
                Config.IOTEX_CHAIN_ID -> {
                    mViewModel.getSwap(
                        fromToken?.chainId,
                        fromToken,
                        toToken,
                        bigValue.toString(),
                        slip,
                        tradeType,
                        router.value
                    )
                }
                Config.SOLANA_MAIN_CHAIN_ID -> {
                    mViewModel.getOkxSwap(
                        fromToken?.chainId,
                        fromToken,
                        toToken,
                        bigValue.toString(),
                        slip,
                        tradeType,
                    )
                }
                else -> {
                    mViewModel.getUniSwap(
                        fromToken?.chainId,
                        fromToken,
                        toToken,
                        bigValue.toString(),
                        slip,
                        tradeType
                    )
                }
            }
        } else {
            mBinding.etFrom.setText("")
            val to = mBinding.etTo.text.toString()
            val bigValue = TokenUtil.toWei(to, toToken?.decimals ?: 18)
            when (fromToken?.chainId) {
                Config.IOTEX_CHAIN_ID -> {
                    mViewModel.getSwap(
                        fromToken?.chainId,
                        toToken,
                        fromToken,
                        bigValue.toString(),
                        slip,
                        tradeType,
                        router.value
                    )
                }
                Config.SOLANA_MAIN_CHAIN_ID -> {
                    ToastUtils.showShort(R.string.exact_out_feature_currently_not_support_solana)
                }
                else -> {
                    mViewModel.getUniSwap(
                        fromToken?.chainId,
                        fromToken,
                        toToken,
                        bigValue.toString(),
                        slip,
                        tradeType
                    )
                }
            }
        }
    }

    private fun showFromToken(tokenEntry: TokenEntry?) {
        fromToken = tokenEntry
        if (tokenEntry == null) {
            mBinding.llFromToken.setGone()
            mBinding.tvSelectFrom.setVisible()
        } else {
            mBinding.llFromToken.setVisible()
            mBinding.tvSelectFrom.setGone()
            mBinding.tvFromBalance.setCharacterLists(TickerUtils.provideNumberList())
            mBinding.tvFromBalance.text = tokenEntry.displayAmount
            mBinding.ivFrom.loadImage(tokenEntry.logo, R.drawable.icon_token_default)
            val networks = mViewModel.networkListLiveData.value?.associateBy { it.chainId }
            mBinding.ivFromChain.loadImage(
                networks?.get(tokenEntry.chainId)?.logo,
                R.drawable.ic_network_default
            )
            mBinding.tvFrom.text = tokenEntry.symbol
            calculateFromValue()
            getSwapDetail()
        }
    }

    private fun showToToken(tokenEntry: TokenEntry?) {
        toToken = tokenEntry
        if (tokenEntry == null) {
            mBinding.llToToken.setGone()
            mBinding.tvSelectTo.setVisible()
        } else {
            mBinding.llToToken.setVisible()
            mBinding.tvSelectTo.setGone()
            mBinding.ivTo.loadImage(tokenEntry.logo, R.drawable.icon_token_default)
            val networks = mViewModel.networkListLiveData.value?.associateBy { it.chainId }
            mBinding.ivToChain.loadImage(
                networks?.get(tokenEntry.chainId)?.logo,
                R.drawable.ic_network_default
            )
            mBinding.tvTo.text = tokenEntry.symbol
            calculateToValue()
            getSwapDetail()
        }
    }

    private fun calculateFromValue() {
        val from = mBinding.etFrom.text.toString()
        if (fromToken == null || from.isBlank()) {
            mBinding.tvFromValue.text = "~$ --"
        } else {
            val value =
                from.asBigDecimal().multiply(fromToken?.price.asBigDecimal())
            if(value.compareTo(BigDecimal.ZERO) == 0){
                mBinding.tvFromValue.text = "~$ --"
            } else {
                mBinding.tvFromValue.text = "~$ " + TokenUtil.displayPrice(value.toString())
            }
        }
        confirmEnable()
    }

    private fun calculateToValue() {
        val to = mBinding.etTo.text.toString()
        if (toToken == null || to.isBlank()) {
            mBinding.tvToValue.text = "~$ --"
        } else {
            val value = to.asBigDecimal().multiply(toToken?.price.asBigDecimal())
            if(value.compareTo(BigDecimal.ZERO) == 0){
                mBinding.tvToValue.text = "~$ --"
            } else {
                mBinding.tvToValue.text = "~$ " + TokenUtil.displayPrice(value.toString())
            }
        }
        confirmEnable()
    }

    private fun confirmEnable() {
        val to = mBinding.etTo.text.toString()
        val from = mBinding.etFrom.text.toString()
        val input = TokenUtil.toWei(from, fromToken?.decimals ?: 18).toString().asBigDecimal()
        val error = when (fromToken?.chainId) {
            Config.IOTEX_CHAIN_ID -> {
                mViewModel.methodParametersListLiveData.value == null
            }
            Config.SOLANA_MAIN_CHAIN_ID -> {
                mViewModel.okxParametersLiveData.value == null
            }
            else -> {
                mViewModel.uniSwapTokenRespLiveData.value == null
            }
        }
        val enable = from.asBigDecimal().compareTo(BigDecimal.ZERO) != 0 &&
                to.asBigDecimal().compareTo(BigDecimal.ZERO) != 0 &&
                !error && input <= fromToken?.balance.asBigDecimal()
        mBinding.tvConfirm.isEnabled = enable
        mBinding.tvConfirm.alpha = if (!enable) 0.5f else 1f
    }

    private fun calculateBalance() {
        val from = mBinding.etFrom.text.toString()
        val input = TokenUtil.toWei(from, fromToken?.decimals ?: 18).toString().asBigDecimal()
        if (input > fromToken?.balance.asBigDecimal()) {
            ToastUtils.showShort(R.string.insufficient_balance)
        }
    }

    override fun initData() {
        mViewModel.tokenRouterListLiveData.observe(this) {
            mAdapterRouter.items = it
            mAdapterRouter.notifyDataSetChanged()
            itemIsVisible()
        }

        mViewModel.getDefaultToken(defaultChainId, defaultFromAddress, true)
        mViewModel.fromTokenLiveData.observe(this) {
            showFromToken(it)
        }

        if (!defaultToAddress.isNullOrEmpty()) {
            mViewModel.getDefaultToken(defaultChainId, defaultToAddress, false)
        }
        mViewModel.toTokenLiveData.observe(this) {
            showToToken(it)
        }

        mViewModel.getNetwork()
        mViewModel.networkListLiveData.observe(this) {
            fromToken?.let {
                showFromToken(it)
            }
            toToken?.let {
                showToToken(it)
            }
        }

        mViewModel.priceImpactWarningLiveData.observe(this) {
            mBinding.llPriceWarning.isVisible = it
        }
        mViewModel.fromValueLiveData.observe(this) {
            mBinding.etFrom.setText(it)
            mBinding.tvFromValue.text = "~$ --"
        }
        mViewModel.fromMaxLiveData.observe(this) {
            mBinding.tvMinMaxKey.text = Utils.getApp().getString(R.string.maximum_pay)
            mBinding.tvMinMaxValue.text = it
        }
        mViewModel.toValueLiveData.observe(this) {
            mBinding.etTo.setText(it)
            mBinding.tvToValue.text = "~$ --"
        }
        mViewModel.toMinLiveData.observe(this) {
            mBinding.tvMinMaxKey.text = Utils.getApp().getString(R.string.minimum_receive)
            mBinding.tvMinMaxValue.text = it
        }
        mViewModel.feeLiveData.observe(this) {
            mBinding.tvFee.text = it
        }
        mViewModel.priceLiveData.observe(this) {
            mBinding.tvPrice.text = it
        }
        mViewModel.loadingLiveData.observe(this) { load ->
            val error = when (fromToken?.chainId) {
                Config.IOTEX_CHAIN_ID -> {
                    mViewModel.methodParametersListLiveData.value == null
                }
                Config.SOLANA_MAIN_CHAIN_ID -> {
                    mViewModel.okxParametersLiveData.value == null
                }
                else -> {
                    mViewModel.uniSwapTokenRespLiveData.value == null
                }
            }
            mBinding.llResp.isVisible = !load && !error
            if (tradeType == EXACT_INPUT) {
                mBinding.shimmerTo.shimmerLayout.isVisible = load
                mBinding.llToEt.isInvisible = load
                if (error) mBinding.etTo.setText("")
            } else {
                mBinding.shimmerFrom.shimmerLayout.isVisible = load
                mBinding.llFromEt.isInvisible = load
                if (error) mBinding.etFrom.setText("")
            }
            confirmEnable()
        }

        mViewModel.approveAmountLiveData.observe(this) {
            val param = mViewModel.methodParametersListLiveData.value ?: return@observe
            val value = getFromValue()
            if (value <= it) {
                swap()
            } else {
                val data = FunctionEncodeUtil.approveData(param.to, value)
                approve(data)
            }
        }

        mViewModel.approveLiveData.observe(this) {
            if (it == null) {
                swap()
            } else {
                approve(it.data)
            }
        }

        mViewModel.defaultGasFee()

        mViewModel.uniSwapMethodParametersLiveData.observe(this) {
            swapTransaction(it)
        }

        mViewModel.actionLiveData.observe(this){
            fromToken?.let {
                mViewModel.getDefaultToken(it.chainId, it.address, true)
            }
            toToken?.let {
                mViewModel.getDefaultToken(it.chainId, it.address, false)
            }
        }
    }

    private fun approve(data: String) {
        SchemeUtil.checkChainId(fromToken?.chainId?:Config.IOTEX_CHAIN_ID){
            mBinding.tvConfirm.isEnabled = false
            mBinding.tvConfirm.alpha = 0.5f
            showLoading()
            if (showDialog) return@checkChainId
            showDialog = true
            Web3Repository.signTransaction(
                System.currentTimeMillis(),
                UserStore.getWalletAddress(),
                fromToken?.address ?: "",
                data,
                null,
                "",
                "",
                "",
                confirm = {
                    showDialog = false
                    hideLoading()
                },
            ) { _, res, _ ->
                showDialog = false
                hideLoading()
                if (res != null) {
                    swap()
                } else {
                    mBinding.tvConfirm.isEnabled = true
                    mBinding.tvConfirm.alpha = 1f
                }
            }
        }
    }

    private fun swap() {
        if (fromToken?.chainId == Config.IOTEX_CHAIN_ID) {
            val param = mViewModel.methodParametersListLiveData.value ?: return
            swapTransaction(param)
        } else {
            signTypeData()
        }
    }

    private fun signTypeData() {
        val resp = mViewModel.uniSwapTokenRespLiveData.value ?: return
        if (resp.permitData == null) {
            mViewModel.uniSwapApi(resp.quote, null, null)
            return
        }
        SchemeUtil.checkChainId(fromToken?.chainId?:Config.IOTEX_CHAIN_ID){
            runCatching {
                val json = EIP712Converter.convertToStandardFormat(resp.permitData.toJson())
                Web3Repository.handleSignTypedData(
                    System.currentTimeMillis(),
                    json,
                    appUrl = "",
                    appLogo = "",
                    response = { _: Long, res: String?, _: Response.Error? ->
                        if (res != null) {
                            mViewModel.uniSwapApi(resp.quote, resp.permitData, res)
                        } else {
                            mBinding.tvConfirm.isEnabled = true
                            mBinding.tvConfirm.alpha = 1f
                        }
                    }
                )
            }.onFailure {
                getSwapDetail()
            }
        }
    }

    private fun swapTransaction(param: MethodParameters) {
        SchemeUtil.checkChainId(fromToken?.chainId?:Config.IOTEX_CHAIN_ID){
            mBinding.tvConfirm.isEnabled = false
            mBinding.tvConfirm.alpha = 0.5f
            if (showDialog) return@checkChainId
            showDialog = true
            showLoading()
            Web3Repository.signTransaction(
                System.currentTimeMillis(),
                UserStore.getWalletAddress(),
                param.to,
                param.calldata,
                param.value,
                "",
                "",
                "",
                getOptions(),
                confirm = {
                    showDialog = false
                    cleanUi()
                }
            ) { _, res, _ ->
                showDialog = false
                hideLoading()
                if (res != null) {
                    mViewModel.hash = res
                    mViewModel.checkResAndAddToken(toToken?.address ?: "")
                } else {
                    mBinding.tvConfirm.isEnabled = true
                    mBinding.tvConfirm.alpha = 1f
                }
            }
        }
    }

    private fun getOptions(): ArrayList<OptionEntry> {
        val options = ArrayList<OptionEntry>()
        val to = mBinding.etTo.text.toString()
        val from = mBinding.etFrom.text.toString()
        options.add(OptionEntry(Utils.getApp().getString(R.string.method), Utils.getApp().getString(R.string.xrc_swap)))
        if(tradeType == EXACT_INPUT){
            options.add(OptionEntry(
                Utils.getApp().getString(R.string.swap_from),
                "-$from ${fromToken?.symbol}"
            ))
            val minMaxValue = mBinding.tvMinMaxValue.text.toString()
            options.add(OptionEntry(
                Utils.getApp().getString(R.string.minimum_receive),
                "+$minMaxValue"
            ))
        } else {
            val minMaxValue = mBinding.tvMinMaxValue.text.toString()
            options.add(OptionEntry(
                Utils.getApp().getString(R.string.swap_from),
                "-$minMaxValue"
            ))
            options.add(OptionEntry(
                Utils.getApp().getString(R.string.swap_to),
                "+$to ${toToken?.symbol}"
            ))
        }
        return options
    }

    private fun cleanUi(){
        hideLoading()
        mViewModel.methodParametersListLiveData.postValue(null)
        mViewModel.uniSwapTokenRespLiveData.postValue(null)
        mBinding.llResp.isVisible = false
        mBinding.etFrom.setText("")
        mBinding.etTo.setText("")
        mBinding.tvConfirm.isEnabled = true
        mBinding.tvConfirm.alpha = 1f
    }

    private fun itemIsVisible() {
        mBinding.llRouter.isVisible = WalletHelper.isIoTexNetWork(fromToken?.chainId ?: Config.IOTEX_CHAIN_ID)
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event: NetworkSwitchEvent) {
        if (!Config.SWAP_SUPPORT_CHAIN.contains(UserStore.getChainId())) return
        mViewModel.getDefaultToken(UserStore.getChainId(), defaultFromAddress, true)
        if (toToken?.chainId != UserStore.getChainId()) {
            showToToken(null)
        }
        itemIsVisible()
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event: SwitchWalletEvent) {
        mSwapTokenViewModel.loadSortToken()
        mViewModel.getDefaultToken(fromToken?.chainId ?: defaultChainId, fromToken?.address, true)
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event: RefreshMainPriceEvent) {
        if (fromToken?.chainId == event.chainId && fromToken?.address.isNullOrEmpty()) {
            fromToken?.price = event.price
        }
        if (toToken?.chainId == event.chainId && toToken?.address.isNullOrEmpty()) {
            toToken?.price = event.price
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onActionRefreshEvent(event: ActionRefreshEvent) {
        mViewModel.checkResAndAddToken(toToken?.address ?: "")
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event: TokenLoadFinishEvent) {
        mSwapTokenViewModel.loadSortToken()
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event: FavoriteOrDislikeERC20Event) {
        mSwapTokenViewModel.loadSortToken()
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event: MainPullRefresh) {
        mSwapTokenViewModel.loadSortToken()
    }
}