package io.iotex.iopay.home

import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.lifecycleScope
import com.blankj.utilcode.util.Utils
import com.drakeet.multitype.MultiTypeAdapter
import com.google.firebase.crashlytics.FirebaseCrashlytics
import io.iotex.base.bindbase.BaseBindFragment
import io.iotex.iopay.R
import io.iotex.iopay.data.db.AppDatabase
import io.iotex.iopay.data.db.NFT_TYPE_1155
import io.iotex.iopay.data.db.NFT_TYPE_721
import io.iotex.iopay.data.db.NftTokenEntry
import io.iotex.iopay.databinding.FragmentNftListBinding
import io.iotex.iopay.support.eventbus.MainPullRefresh
import io.iotex.iopay.support.eventbus.NetworkSwitchEvent
import io.iotex.iopay.support.eventbus.SwitchWalletEvent
import io.iotex.iopay.ui.binder.NftClassicBinder
import io.iotex.iopay.ui.binder.NftClassicItem
import io.iotex.iopay.util.Config
import io.iotex.iopay.util.Constant
import io.iotex.iopay.util.FireBaseEvent
import io.iotex.iopay.util.FireBaseUtil
import io.iotex.iopay.util.IoPayConstant
import io.iotex.iopay.util.PageEventUtil
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.util.extension.setGone
import io.iotex.iopay.util.extension.setVisible
import io.iotex.iopay.util.extension.updateItem
import io.iotex.iopay.viewmodel.token.NftViewModel
import io.iotex.iopay.wallet.NftListActivity
import io.iotex.iopay.wallet.NftTokenDetailActivity
import io.iotex.iopay.xapp.XAppsActivity
import kotlinx.coroutines.*
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.jetbrains.anko.startActivity

class NftListFragment: BaseBindFragment<NftViewModel, FragmentNftListBinding>(R.layout.fragment_nft_list) {

    private val mAdapter = MultiTypeAdapter()

    private val fetchSize = 80
    private val nftEmptyLiveData = MutableLiveData<Boolean>()

    val errorHandler = CoroutineExceptionHandler { _, e ->
        FirebaseCrashlytics.getInstance().recordException(e)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initRecycleView()
        fetchData()
    }

    private fun initRecycleView() {
        val binder = NftClassicBinder().apply {
            setItemClickCallback { classicItem ->
                gotoTarget(classicItem)
                val bundle = Bundle()
                bundle.putString("name", classicItem.nft.name)
                FireBaseUtil.logFireBase(FireBaseEvent.ACTION_HOME_NFT, bundle)
            }
        }
        mAdapter.register(NftClassicItem::class, binder)
        mBinding.mRvTokens.itemAnimator?.changeDuration = 0
        mBinding.mRvTokens.adapter = mAdapter
        nftEmptyLiveData.observe(viewLifecycleOwner){
            if (it) {
                mBinding.llEmpty.setVisible()
                mBinding.mRvTokens.setGone()
            } else {
                mBinding.llEmpty.setGone()
                mBinding.mRvTokens.setVisible()
            }
        }
        mBinding.tvMarket.setOnClickListener {
            val intent = Intent(requireContext(), XAppsActivity::class.java)
            intent.putExtra(
                IoPayConstant.BROWSER_URL, Config.MIMO_NFT_MARKET_PLACE_URL)
            startActivity(intent)
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_HOME_NFT_MARKET)
        }
    }

    private fun gotoTarget(classicItem: NftClassicItem) {
        lifecycleScope.launch {
            val chainId = WalletHelper.getCurChainId()
            val address = Constant.currentWallet?.address?.let {
                WalletHelper.convertWeb3Address(it)
            } ?: return@launch
            if (classicItem.nft.type == NFT_TYPE_721) {
                val nftList = withContext(Dispatchers.IO) {
                    AppDatabase.getInstance(Utils.getApp()).nftTokenDao()
                        .queryTokensByContract(address, chainId, classicItem.nft.contract)
                }
                requireActivity().startActivity<NftListActivity>(
                    NftListActivity.KEY_NFT_LIST to nftList
                )
                val map = HashMap<String,String>()
                map["name"] = classicItem.nft.name
                PageEventUtil.logEvent(PageEventUtil.NFTCOLLECTION, map)
            } else {
                val nft = withContext(Dispatchers.IO) {
                    AppDatabase.getInstance(Utils.getApp()).nftTokenDao()
                        .queryToken(address, chainId, classicItem.nft.contract, classicItem.nft.tokenId)
                }
                requireActivity().startActivity<NftTokenDetailActivity>(
                    NftTokenDetailActivity.KEY_NFT_TOKEN to nft
                )
            }
        }
    }

    private fun fetchData() {
        lifecycleScope.launch(errorHandler) {
            loadData()
            queryNftFromRemote()
        }
    }

    private suspend fun queryNftFromRemote() {
        val chainId = WalletHelper.getCurChainId()
        val address = Constant.currentWallet?.address?.let {
            WalletHelper.convertWeb3Address(it)
        } ?: return

        val list721 = queryData(NFT_TYPE_721)
        val list1155 = queryData(NFT_TYPE_1155)

        val tokenList = list721.toMutableList().apply {
            addAll(list1155)
        }

        flashData(tokenList, address, chainId)
    }

    private suspend fun queryData(type: String): List<NftTokenEntry> {
        return withContext(Dispatchers.IO + SupervisorJob() + errorHandler) {
            val chainId = WalletHelper.getCurChainId()
            val address = Constant.currentWallet?.address?.let {
                WalletHelper.convertWeb3Address(it)
            } ?: return@withContext emptyList<NftTokenEntry>()
            val tokenList = mutableListOf<NftTokenEntry>()
            var list: List<NftTokenEntry>
            var skip = 0
            do {
                list = mViewModel.getNftList(chainId, address, type, skip, fetchSize)
                tokenList.addAll(list)
                skip = tokenList.size - 1
            } while (list.size >= fetchSize)
            return@withContext tokenList
        }
    }

    private suspend fun loadData() {
        val chainId = WalletHelper.getCurChainId()
        val address = Constant.currentWallet?.address?.let {
            WalletHelper.convertWeb3Address(it)
        } ?: return
        val list = withContext(Dispatchers.IO) {
            val nft721List = AppDatabase.getInstance(Utils.getApp()).nftTokenDao()
                .queryTokensByType(address, chainId, NFT_TYPE_721)
            val nft721ClassicList = nft721List.groupBy {
                it.contract.lowercase()
            }.values.map {
                val nft = it[0]
                NftClassicItem(nft, it.size)
            }

            val nft1155List = AppDatabase.getInstance(Utils.getApp()).nftTokenDao()
                .queryTokensByType(address, chainId, NFT_TYPE_1155)
            val nft1155ClassicList = nft1155List.map {
                NftClassicItem(it, it.amount.toIntOrNull()?:0)
            }

            nft721ClassicList.toMutableList().apply {
                addAll(nft1155ClassicList)
            }
        }
        mAdapter.items = list
        mAdapter.notifyDataSetChanged()

        nftEmptyLiveData.postValue(mAdapter.items.isEmpty())

        val nftList = list.map { it.nft }
        queryNftLogo(nftList)
    }

    private fun queryNftLogo(tokenList: List<NftTokenEntry>) {
        val chainId = WalletHelper.getCurChainId()
        tokenList.forEach { nftToken ->
            if (nftToken.tokenUrl.isBlank()) {
                mViewModel.getNftLogo(chainId, nftToken)
            }
        }
    }

    private suspend fun flashData(list: List<NftTokenEntry>, address: String, chainId: Int) {
        withContext(Dispatchers.IO) {
            val localTokens = AppDatabase.getInstance(Utils.getApp()).nftTokenDao()
                .queryTokens(address, chainId)
            if (list.isNotEmpty()) {
                AppDatabase.getInstance(Utils.getApp()).nftTokenDao()
                    .deleteTokens(address, chainId)
            }
            list.map { nftToken ->
                nftToken.apply {
                    nftToken.walletAddress = address
                    nftToken.chainId = chainId
                    val localToken = localTokens.firstOrNull { localToken ->
                        nftToken.contract == localToken.contract && nftToken.tokenId == localToken.tokenId &&
                                nftToken.chainId == localToken.chainId
                    }
                    nftToken.tokenUrl = localToken?.tokenUrl ?: ""
                }
            }.also {
                AppDatabase.getInstance(Utils.getApp()).nftTokenDao()
                    .insert(*it.toTypedArray())
            }

        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onNetworkSwitchEvent(event: NetworkSwitchEvent) {
        lifecycleScope.launch(errorHandler) {
            nftEmptyLiveData.postValue(true)
            loadData()
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMainPullRefresh(event: MainPullRefresh) {
        fetchData()
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onWalletSwitchEvent(event: SwitchWalletEvent) {
        mViewModel.cancelAllJob()
        lifecycleScope.launch(errorHandler) {
            nftEmptyLiveData.postValue(true)
            fetchData()
        }
    }

    override fun initEvent() {
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this)
        }
        mViewModel.nftLiveData.observe(viewLifecycleOwner) { nft ->
            val item = mAdapter.items.firstOrNull { nftClassic ->
                (nftClassic as NftClassicItem).nft.contract == nft.contract &&
                        nftClassic.nft.tokenId == nft.tokenId &&
                        nftClassic.nft.chainId == nft.chainId
            } as? NftClassicItem ?: return@observe
            mAdapter.updateItem(item) { nftClassic ->
                nftClassic.nft.contract == nft.contract &&
                        nftClassic.nft.tokenId == nft.tokenId &&
                        nftClassic.nft.chainId == nft.chainId
            }
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this)
        }
    }
}