package io.iotex.iopay.home.item

import android.view.LayoutInflater
import android.view.ViewGroup
import com.youth.banner.adapter.BannerAdapter
import io.iotex.api.DiscoveryBannerQuery
import io.iotex.api.HomeBannerQuery
import io.iotex.base.bindbase.BaseBindVH
import io.iotex.iopay.R
import io.iotex.iopay.databinding.ItemDappBannerBinding
import io.iotex.iopay.util.extension.loadSvgOrImage

class HomeBannerAdapter(data: List<HomeBannerQuery.Home_banner>) :
    BannerAdapter<HomeBannerQuery.Home_banner, BaseBindVH<ItemDappBannerBinding>>(data) {

    override fun onCreateHolder(
        parent: ViewGroup,
        viewType: Int
    ): BaseBindVH<ItemDappBannerBinding> {
        return BaseBindVH(ItemDappBannerBinding.inflate(LayoutInflater.from(parent.context),parent,false))
    }

    override fun onBindView(
        holder: BaseBindVH<ItemDappBannerBinding>,
        data: HomeBannerQuery.Home_banner,
        position: Int,
        size: Int
    ) {
        holder.bind.ivImage.loadSvgOrImage(data.image(), R.drawable.default_banner)
    }

}