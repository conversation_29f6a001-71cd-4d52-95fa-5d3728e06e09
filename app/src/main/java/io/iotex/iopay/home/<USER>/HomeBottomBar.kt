package io.iotex.iopay.home.widget

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import androidx.core.view.isVisible
import androidx.databinding.DataBindingUtil
import com.blankj.utilcode.util.ColorUtils
import io.iotex.iopay.R
import io.iotex.iopay.data.UserStore
import io.iotex.iopay.databinding.ViewHomeBottomBarBinding
import io.iotex.iopay.util.extension.setGone
import io.iotex.iopay.util.extension.setVisible

class HomeBottomBar @JvmOverloads constructor(
    context: Context,
    attr: AttributeSet? = null,
    defStyle: Int = 0
) : FrameLayout(
    context,
    attr,
    defStyle
) {

    private var isVisitor = true
    private var lastPosition = 0

    private val mBinding = DataBindingUtil.inflate<ViewHomeBottomBarBinding>(
        LayoutInflater.from(context),
        R.layout.view_home_bottom_bar, this, true
    )

    private val llItem by lazy {
        arrayOf(
            mBinding.llWallet,
            mBinding.llDepin,
            mBinding.llSwap,
            mBinding.llStake,
            mBinding.llBino,
            mBinding.llDiscover
        )
    }
    private val ivItem by lazy {
        arrayOf(
            mBinding.ivWallet,
            mBinding.ivDepin,
            mBinding.ivSwap,
            mBinding.ivStake,
            mBinding.ivBino,
            mBinding.ivDiscover
        )
    }
    private val ivLottie by lazy {
        arrayOf(
            mBinding.lottieWallet,
            mBinding.lottieDepin,
            mBinding.lottieSwap,
            null,
            null,
            mBinding.lottieDiscover
        )
    }
    private val tvItem by lazy {
        arrayOf(
            mBinding.tvWallet,
            mBinding.tvDepin,
            mBinding.tvSwap,
            mBinding.tvStake,
            mBinding.tvBino,
            mBinding.tvDiscover
        )
    }

    private val selectIcon by lazy {
        arrayOf(
            R.drawable.icon_tab_wallet_selected,
            R.drawable.icon_tab_depin_select,
            R.drawable.icon_tab_swap_select,
            R.drawable.icon_tab_stake_select,
            R.drawable.icon_tab_bino_select,
            R.drawable.icon_tab_discover_select,
        )
    }

    private val icon by lazy {
        arrayOf(
            R.drawable.icon_tab_wallet,
            R.drawable.icon_tab_depin,
            R.drawable.icon_tab_swap,
            R.drawable.icon_tab_stake,
            R.drawable.icon_tab_bino,
            R.drawable.icon_tab_discover,
        )
    }

    fun setOnItemSelectedListener(back: (position: Int) -> Unit) {
        mBinding.llWallet.setOnClickListener {
            mBinding.lottieWallet.playAnimation()
            checkWallet()
            back.invoke(0)
        }
        mBinding.llStake.setOnClickListener {
            back.invoke(3)
            checkStake()
        }
        mBinding.llSwap.setOnClickListener {
            mBinding.lottieSwap.playAnimation()
            checkSwap()
            back.invoke(2)
        }
        mBinding.llBino.setOnClickListener {
            checkBino()
            if (isVisitor) {
                back.invoke(1)
            } else {
                back.invoke(3)
            }
        }
        mBinding.llDepin.setOnClickListener {
            mBinding.lottieDepin.playAnimation()
            checkDepin()
            back.invoke(1)
        }
        mBinding.llDiscover.setOnClickListener {
            mBinding.lottieDiscover.playAnimation()
            checkDiscover()
            if (isVisitor) {
                back.invoke(2)
            } else {
                back.invoke(4)
            }
        }
    }

    fun setVisitor(visitor: Boolean) {
        isVisitor = visitor
        mBinding.llBino.isVisible = UserStore.getSwitchBinoAi()
        if(visitor){
            mBinding.llDepin.isVisible = !UserStore.getSwitchBinoAi()
            mBinding.llStake.setGone()
            mBinding.llSwap.setGone()
        } else {
            mBinding.llDepin.setVisible()
            mBinding.llStake.isVisible = !UserStore.getSwitchBinoAi()
            mBinding.llSwap.setVisible()
        }
        checkItem(0)
    }

    fun checkItem(position: Int) {
        if (lastPosition == position) return
        when (position) {
            0 -> {
                checkWallet()
            }

            1 -> {
                if (isVisitor) {
                    checkBino()
                } else {
                    checkDepin()
                }
            }

            2 -> {
                if (isVisitor) {
                    checkDiscover()
                } else {
                    checkSwap()
                }
            }

            3 -> {
                if(UserStore.getSwitchBinoAi()){
                    checkBino()
                } else {
                    checkStake()
                }
            }

            4 -> {
                checkDiscover()
            }

            else -> {
                checkWallet()
            }
        }
        lastPosition = position
    }

    fun badgeItem(position: Int) {

    }

    private fun checkWallet() {
        checkItemImage(mBinding.ivWallet)
        checkItemText(mBinding.tvWallet)
        checkItemScale(mBinding.llWallet)
    }

    private fun checkStake() {
        checkItemImage(mBinding.ivStake)
        checkItemText(mBinding.tvStake)
        checkItemScale(mBinding.llStake)
    }

    private fun checkSwap() {
        checkItemImage(mBinding.ivSwap)
        checkItemText(mBinding.tvSwap)
        checkItemScale(mBinding.llSwap)
    }

    private fun checkBino() {
        checkItemImage(mBinding.ivBino)
        checkItemText(mBinding.tvBino)
        checkItemScale(mBinding.llBino)
    }

    private fun checkDiscover() {
        checkItemImage(mBinding.ivDiscover)
        checkItemText(mBinding.tvDiscover)
        checkItemScale(mBinding.llDiscover)
    }

    private fun checkDepin() {
        checkItemImage(mBinding.ivDepin)
        checkItemText(mBinding.tvDepin)
        checkItemScale(mBinding.llDepin)
    }

    private fun checkItemImage(view: ImageView) {
        ivItem.forEachIndexed { index, imageView ->
            val lottieView = ivLottie[index]
            if (view == imageView) {
                if(lottieView == null){
                    imageView.setVisible()
                    imageView.setImageResource(selectIcon[index])
                } else{
                    lottieView.setVisible()
                    imageView.setGone()
                }
            } else {
                lottieView?.setGone()
                imageView.setVisible()
                imageView.setImageResource(icon[index])
            }
        }

    }

    private fun checkItemText(view: TextView) {
        tvItem.forEach {
            if (view == it) {
                it.setTextColor(ColorUtils.getColor(R.color.color_855eff))
            } else {
                it.setTextColor(ColorUtils.getColor(R.color.menu_grey_color))
            }
        }
    }

    private fun checkItemScale(view: View) {
        llItem.forEach {
            if (view == it && view != mBinding.llSwap) {
                it.scaleX = 1.1f
                it.scaleY = 1.1f
            } else {
                it.scaleX = 1f
                it.scaleY = 1f
            }
        }
    }
}