package io.iotex.iopay.home.item

import android.view.LayoutInflater
import android.view.ViewGroup
import android.view.WindowManager.LayoutParams
import androidx.core.content.ContextCompat
import com.blankj.utilcode.util.ColorUtils
import com.drakeet.multitype.ItemViewBinder
import io.iotex.base.bindbase.BaseBindVH
import io.iotex.iopay.R
import io.iotex.iopay.databinding.ItemHomeTabBinding
import io.iotex.iopay.util.extension.setInvisible
import io.iotex.iopay.util.extension.setVisible

class HomeTabBinder(val match:Boolean = false) :
    ItemViewBinder<String, BaseBindVH<ItemHomeTabBinding>>() {

    var onItemClick: ((Int) -> Unit)? = null

    var select = 0

    override fun onCreateViewHolder(
        inflater: LayoutInflater,
        parent: ViewGroup
    ): BaseBindVH<ItemHomeTabBinding> {
        return BaseBindVH(
            ItemHomeTabBinding.inflate(
                LayoutInflater.from(parent.context),
                parent,
                false
            ).apply {
                val layout = mLlTab.layoutParams
                if(match){
                    layout.width = LayoutParams.MATCH_PARENT
                } else {
                    layout.width = LayoutParams.WRAP_CONTENT
                }
            }
        )
    }

    override fun onBindViewHolder(
        holder: BaseBindVH<ItemHomeTabBinding>,
        item: String
    ) {
        holder.bind.mTvTab.text = item
        if (holder.adapterPosition == select) {
            val color = ContextCompat.getColor(holder.bind.mTvTab.context, R.color.color_tab_select)
            holder.bind.mTvTab.setTextColor(color)
            holder.bind.mIndicator.setBackgroundColor(color)
            holder.bind.mIndicator.setVisible()
        } else {
            holder.bind.mTvTab.setTextColor(ColorUtils.getColor(R.color.color_tab_unselect))
            holder.bind.mIndicator.setInvisible()
        }
        if (holder.adapterPosition == 0) {
            holder.bind.mLlTab.tag = "token"
        }
        holder.bind.mLlTab.setOnClickListener {
            select = holder.adapterPosition
            onItemClick?.invoke(holder.adapterPosition)
        }
    }

}