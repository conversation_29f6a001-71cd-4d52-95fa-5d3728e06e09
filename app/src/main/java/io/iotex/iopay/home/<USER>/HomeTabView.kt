package io.iotex.iopay.home.widget

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.FrameLayout
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.LinearLayoutManager
import com.blankj.utilcode.util.StringUtils.getString
import com.drakeet.multitype.MultiTypeAdapter
import io.iotex.iopay.R
import io.iotex.iopay.databinding.ViewTabHomeBinding
import io.iotex.iopay.home.item.HomeTabBinder

class HomeTabView @JvmOverloads constructor(
    context: Context,
    attr: AttributeSet? = null,
    defStyle: Int = 0
) : FrameLayout(
    context,
    attr,
    defStyle
) {

    private val mBinding = DataBindingUtil.inflate<ViewTabHomeBinding>(
        LayoutInflater.from(context),
        R.layout.view_tab_home, this, true
    )

    private val mAdapter = MultiTypeAdapter()
    private val binder = HomeTabBinder()
    var onSelect: ((Int) -> Unit)? = null

    init {
        mBinding.apply {
            recyclerView.layoutManager = LinearLayoutManager(context,LinearLayoutManager.HORIZONTAL,false)
            mAdapter.register(String::class.java, binder.apply {
                onItemClick = {
                    mAdapter.notifyDataSetChanged()
                    onSelect?.invoke(it)
                }
            })
            recyclerView.adapter = mAdapter
            setItem(arrayListOf(getString(R.string.token)))
        }
    }

    fun setSelect(select: Int) {
        if (binder.select != select) {
            binder.select = select
            mAdapter.notifyDataSetChanged()
        }
    }

    fun getItemSize(): Int {
        return mAdapter.items.size
    }

    fun setItem(list: List<String>) {
        mAdapter.items = list
        mAdapter.notifyDataSetChanged()
    }

}