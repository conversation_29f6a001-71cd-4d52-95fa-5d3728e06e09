package io.iotex.iopay.home.item

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isVisible
import com.blankj.utilcode.util.SPUtils
import com.drakeet.multitype.ItemViewBinder
import io.iotex.base.bindbase.BaseBindVH
import io.iotex.iopay.R
import io.iotex.iopay.data.db.TokenEntry
import io.iotex.iopay.databinding.ItemHomeTokenBinding
import io.iotex.iopay.util.Constant
import io.iotex.iopay.util.SPConstant
import io.iotex.iopay.util.extension.loadSvgOrImage
import io.iotex.iopay.util.extension.toTokenBitmap

class HomeTokenBinder(val swipe: Boolean) :
    ItemViewBinder<TokenEntry, BaseBindVH<ItemHomeTokenBinding>>() {

    var onItemClickCallback: ((TokenEntry) -> Unit)? = null

    override fun onBindViewHolder(holder: BaseBindVH<ItemHomeTokenBinding>, item: TokenEntry) {
        val bind = holder.bind
        if (item.logo.isEmpty() && item.symbol.isNotEmpty()) {
            bind.ivLogo.setImageBitmap(item.symbol.toTokenBitmap(bind.ivLogo.context))
        } else {
            bind.ivLogo.loadSvgOrImage(item.logo, R.drawable.icon_token_default)
        }
        bind.ivDepin.isVisible = item.isDepinToken
        bind.ivOfficial.isVisible = item.isOfficial
        bind.ivRobot.isVisible = item.isRobotPump
        bind.tvSymbol.text = item.symbol
        bind.llStake.isVisible = item.isStake
        if(item.displayPrice == "0"){
            bind.tvPrice.text = "$ --"
        } else {
            bind.tvPrice.text = "$" + item.displayPrice
        }
        bind.tvChange.text = item.displayChange
        if (SPUtils.getInstance().getBoolean(SPConstant.SP_BALANCES_IS_HIDE)) {
            bind.tvAmount.text = Constant.balanceHide
            bind.tvValue.text = Constant.balanceHide
        } else {
            bind.tvAmount.text = item.displayAmount
            if(item.displayValue == "0"){
                bind.tvValue.text = "$ --"
            } else {
                bind.tvValue.text = "$" + item.displayValue
            }
        }

        bind.root.setOnClickListener {
            onItemClickCallback?.invoke(item)
        }
    }

    override fun onCreateViewHolder(
        inflater: LayoutInflater,
        parent: ViewGroup
    ): BaseBindVH<ItemHomeTokenBinding> {
        return BaseBindVH(
            ItemHomeTokenBinding.inflate(
                LayoutInflater.from(parent.context),
                parent,
                false
            )
        )
    }
}