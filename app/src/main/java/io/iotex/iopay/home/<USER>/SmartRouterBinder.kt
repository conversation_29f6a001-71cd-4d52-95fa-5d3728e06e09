package io.iotex.iopay.home.item

import android.view.LayoutInflater
import android.view.ViewGroup
import com.drakeet.multitype.ItemViewBinder
import io.iotex.base.bindbase.BaseBindVH
import io.iotex.iopay.R
import io.iotex.iopay.databinding.ItemSmartRouterBinding
import io.iotex.iopay.transaction.bean.OptionEntry

class SmartRouterBinder(private val oldRouter:String) : ItemViewBinder<OptionEntry, BaseBindVH<ItemSmartRouterBinding>>() {

    var onItemClick: ((OptionEntry) -> Unit)? = null

    override fun onBindViewHolder(holder: BaseBindVH<ItemSmartRouterBinding>, item: OptionEntry) {
        holder.bind.tvItem.text = item.key
        if (oldRouter == item.key) {
            holder.bind.tvItem.setBackgroundResource(R.drawable.shape_item_back_select)
        } else {
            holder.bind.tvItem.setBackgroundResource(R.drawable.shape_item_back)
        }
        holder.bind.tvItem.setOnClickListener {
            onItemClick?.invoke(item)
        }
    }

    override fun onCreateViewHolder(
        inflater: LayoutInflater,
        parent: ViewGroup
    ): BaseBindVH<ItemSmartRouterBinding> {
        return BaseBindVH(
            ItemSmartRouterBinding.inflate(
                LayoutInflater.from(parent.context),
                parent,
                false
            )
        )
    }
}