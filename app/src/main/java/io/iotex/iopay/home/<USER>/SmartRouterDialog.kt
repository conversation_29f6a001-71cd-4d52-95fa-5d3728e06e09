package io.iotex.iopay.home.dialog

import android.view.Gravity
import android.view.ViewGroup
import androidx.recyclerview.widget.LinearLayoutManager
import com.drakeet.multitype.MultiTypeAdapter
import io.iotex.base.BaseViewModel
import io.iotex.base.bindbase.BaseBindDialog
import io.iotex.iopay.R
import io.iotex.iopay.databinding.DialogSmartRouterBinding
import io.iotex.iopay.home.item.SmartRouterBinder
import io.iotex.iopay.transaction.bean.OptionEntry

class SmartRouterDialog(oldRouter:String,val chainId:Int) :
    BaseBindDialog<BaseViewModel, DialogSmartRouterBinding>(R.layout.dialog_smart_router) {

    private val mAdapter = MultiTypeAdapter()
    private val binder = SmartRouterBinder(oldRouter).apply {
        onItemClick = {
            dismiss()
            onSelectItem?.invoke(it)
        }
    }
    var onSelectItem: ((OptionEntry) -> Unit)? = null

    override fun initView() {
        mBinding.ivClose.setOnClickListener {
            dismiss()
        }
        mBinding.recyclerView.layoutManager = LinearLayoutManager(requireContext())
        if(chainId == 4689){
            mAdapter.items = arrayListOf(OptionEntry("Best Trade","v2,v3,mixed"),OptionEntry("Universal Router","v2,v3,mixed"),OptionEntry("Only v2","v2"),OptionEntry("Only v3","v3"))
        } else {
            mAdapter.items = arrayListOf(OptionEntry("Universal Router","v2,v3,mixed"))
        }
        mAdapter.register(OptionEntry::class.java,binder)
        mBinding.recyclerView.adapter = mAdapter
    }

    override fun onStart() {
        super.onStart()
        val dialogWindow = dialog?.window
        val lp = dialogWindow?.attributes
        dialogWindow?.setGravity(Gravity.BOTTOM)
        lp?.width = ViewGroup.LayoutParams.MATCH_PARENT
        lp?.height = ViewGroup.LayoutParams.WRAP_CONTENT
        dialogWindow?.attributes = lp
    }
}
