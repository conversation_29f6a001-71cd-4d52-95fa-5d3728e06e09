package io.iotex.iopay.home.item

import android.view.LayoutInflater
import android.view.ViewGroup
import com.drakeet.multitype.ItemViewBinder
import io.iotex.base.bindbase.BaseBindVH
import io.iotex.iopay.R
import io.iotex.iopay.data.db.RPCNetwork
import io.iotex.iopay.databinding.ItemSwapChainBinding
import io.iotex.iopay.util.extension.loadImage

class SwapChainBinder : ItemViewBinder<RPCNetwork, BaseBindVH<ItemSwapChainBinding>>() {

    var onItemClick: ((rpcNetwork:RPCNetwork) -> Unit)? = null

    var selectChainId = 0
    override fun onBindViewHolder(holder: BaseBindVH<ItemSwapChainBinding>, item: RPCNetwork) {
        holder.bind.ivLogo.loadImage(item.logo,R.drawable.ic_network_default)
        if (selectChainId == item.chainId) {
            holder.bind.flChain.setBackgroundResource(R.drawable.shape_swap_chain_select)
        } else {
            holder.bind.flChain.setBackgroundResource(R.drawable.shape_swap_chain_unselect)
        }
        holder.bind.flChain.setOnClickListener {
            selectChainId = item.chainId
            onItemClick?.invoke(item)
        }
    }

    override fun onCreateViewHolder(
        inflater: LayoutInflater,
        parent: ViewGroup
    ): BaseBindVH<ItemSwapChainBinding> {
        return BaseBindVH(
            ItemSwapChainBinding.inflate(
                LayoutInflater.from(parent.context),
                parent,
                false
            )
        )
    }
}