package io.iotex.iopay.home.dialog

import android.view.Gravity
import android.view.ViewGroup
import androidx.core.widget.addTextChangedListener
import com.apollographql.apollo.api.BigDecimal
import com.blankj.utilcode.util.ColorUtils
import com.blankj.utilcode.util.Utils
import com.machinefi.w3bstream.utils.extension.setForeTextColor
import io.iotex.base.BaseViewModel
import io.iotex.base.bindbase.BaseBindDialog
import io.iotex.iopay.R
import io.iotex.iopay.databinding.DialogSwapSlipBinding
import io.iotex.iopay.home.SwapFragment.Companion.EXACT_INPUT
import io.iotex.iopay.util.extension.asBigDecimal
import io.iotex.iopay.util.extension.setGone
import io.iotex.iopay.util.extension.setVisible

class SwapSlipDialog(
    private val oldSlip: String,
    private val tradeType: String,
    private val fromValue: String,
    private val fromSymbol: String,
    private val toValue: String,
    private val toSymbol: String
) :
    BaseBindDialog<BaseViewModel, DialogSwapSlipBinding>(R.layout.dialog_swap_slip) {
    var type = 0
    var onSlipChange: ((type: Int, slip: BigDecimal) -> Unit)? = null

    override fun initView() {
        mBinding.ivClose.setOnClickListener {
            dismiss()
        }

        mBinding.tvSlip.text = Utils.getApp().getString(R.string.optimal_slip_point_present, "0.5%")
            .setForeTextColor("0.5%", ColorUtils.getColor(R.color.color_00dc9c))
        mBinding.tvCustom1.setOnClickListener {
            mBinding.etCustom.setText("0.5")
        }
        mBinding.tvCustom2.setOnClickListener {
            mBinding.etCustom.setText("1")
        }
        mBinding.tvCustom3.setOnClickListener {
            mBinding.etCustom.setText("1.5")
        }
        mBinding.tvOptimal.setOnClickListener {
            type = 0
            mBinding.tvOptimal.setBackgroundResource(R.drawable.shape_swap_tab_select)
            mBinding.tvCustom.setBackgroundResource(0)
            mBinding.llOptimal.setVisible()
            mBinding.llCustom.setGone()
            calculateValue(calculateSlip())
        }

        mBinding.tvCustom.setOnClickListener {
            type = 1
            mBinding.tvOptimal.setBackgroundResource(0)
            mBinding.tvCustom.setBackgroundResource(R.drawable.shape_swap_tab_select)
            mBinding.llOptimal.setGone()
            mBinding.llCustom.setVisible()
            calculateValue(calculateSlip())
        }

        if (type == 1) {
            mBinding.etCustom.setText(oldSlip)
        }
        calculateValue(calculateSlip())

        mBinding.tvConfirm.setOnClickListener {
            onSlipChange?.invoke(type, calculateSlip())
            dismiss()
        }
        mBinding.etCustom.addTextChangedListener {
            calculateValue(calculateSlip())
        }
    }

    private fun calculateSlip(): BigDecimal {
        return if (type == 0) {
            BigDecimal(0.5)
        } else {
            val slip = mBinding.etCustom.text.toString()
            slip.asBigDecimal()
        }
    }

    private fun calculateValue(slip:BigDecimal){
        if(tradeType == EXACT_INPUT){
            mBinding.tvMinMaxKey.text = Utils.getApp().getString(R.string.minimum_receive)
            if(toValue.asBigDecimal().compareTo(BigDecimal.ZERO) == 0){
                mBinding.tvMinMaxValue.text = ""
            } else {
                val value = toValue.asBigDecimal()-toValue.asBigDecimal()*slip/BigDecimal(100)
                mBinding.tvMinMaxValue.text = value.toString() + toSymbol
            }
        } else {
            mBinding.tvMinMaxKey.text = Utils.getApp().getString(R.string.maximum_pay)
            if(fromValue.asBigDecimal().compareTo(BigDecimal.ZERO) == 0){
                mBinding.tvMinMaxValue.text = ""
            } else {
                val value = fromValue.asBigDecimal()+fromValue.asBigDecimal()*slip/BigDecimal(100)
                mBinding.tvMinMaxValue.text = value.toString() + fromSymbol
            }
        }
    }

    override fun onStart() {
        super.onStart()
        val dialogWindow = dialog?.window
        val lp = dialogWindow?.attributes
        dialogWindow?.setGravity(Gravity.BOTTOM)
        lp?.width = ViewGroup.LayoutParams.MATCH_PARENT
        lp?.height = ViewGroup.LayoutParams.WRAP_CONTENT
        dialogWindow?.attributes = lp
    }
}
