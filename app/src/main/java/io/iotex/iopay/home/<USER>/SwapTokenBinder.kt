package io.iotex.iopay.home.item

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isVisible
import com.drakeet.multitype.ItemViewBinder
import com.machinefi.w3bstream.utils.extension.ellipsis
import io.iotex.base.bindbase.BaseBindVH
import io.iotex.iopay.R
import io.iotex.iopay.data.db.RPCNetwork
import io.iotex.iopay.data.db.TokenEntry
import io.iotex.iopay.databinding.ItemSwapTokenBinding
import io.iotex.iopay.util.extension.loadImage

class SwapTokenBinder : ItemViewBinder<TokenEntry, BaseBindVH<ItemSwapTokenBinding>>() {

    var onItemClick: ((tokenEntry: TokenEntry) -> Unit)? = null

    var networks = HashMap<Int, RPCNetwork>()

    override fun onBindViewHolder(holder: BaseBindVH<ItemSwapTokenBinding>, item: TokenEntry) {
        holder.bind.ivLogo.loadImage(item.logo, R.drawable.icon_token_default)
        holder.bind.ivNetwork.loadImage(networks[item.chainId]?.logo, R.drawable.ic_network_default)
        holder.bind.tvName.text = item.symbol
        holder.bind.ivOfficial.isVisible = item.isOfficial
        holder.bind.tvBalance.text = item.displayAmount
        holder.bind.tvValue.text = item.displayValue
        if (item.address.isEmpty()) {
            holder.bind.tvAddress.text = item.name
        } else {
            holder.bind.tvAddress.text = item.address.ellipsis()
        }
        holder.bind.root.setOnClickListener {
            onItemClick?.invoke(item)
        }
    }

    override fun onCreateViewHolder(
        inflater: LayoutInflater,
        parent: ViewGroup
    ): BaseBindVH<ItemSwapTokenBinding> {
        return BaseBindVH(
            ItemSwapTokenBinding.inflate(
                LayoutInflater.from(parent.context),
                parent,
                false
            )
        )
    }
}