package io.iotex.iopay.home.dialog

import android.view.Gravity
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.core.widget.addTextChangedListener
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import com.blankj.utilcode.util.ScreenUtils
import com.blankj.utilcode.util.Utils
import com.drakeet.multitype.MultiTypeAdapter
import io.iotex.base.bindbase.BaseBindDialog
import io.iotex.iopay.R
import io.iotex.iopay.data.db.RPCNetwork
import io.iotex.iopay.data.db.TokenEntry
import io.iotex.iopay.databinding.DialogSwapTokenBinding
import io.iotex.iopay.home.item.HomeTabBinder
import io.iotex.iopay.home.item.SwapChainBinder
import io.iotex.iopay.home.item.SwapTokenBinder
import io.iotex.iopay.util.Config

class SwapTokenDialog(val chainId:Int) :
    BaseBindDialog<SwapTokenViewModel, DialogSwapTokenBinding>(R.layout.dialog_swap_token) {

    private val mAdapterChain = MultiTypeAdapter()
    private val mAdapterTab = MultiTypeAdapter()
    private val mAdapterToken = MultiTypeAdapter()
    private val swapTokenBinder = SwapTokenBinder().apply {
        onItemClick = {token->
            onItemSelected?.invoke(token)
            dismiss()
        }
    }

    private val swapChainBinder = SwapChainBinder()

    var onItemSelected:((tokenEntry: TokenEntry) -> Unit)? = null
    var onDisMiss:(() -> Unit)? = null

    override fun initView() {
        mBinding.ivClose.setOnClickListener {
            dismiss()
        }

        mBinding.flAllChain.setOnClickListener {
            mBinding.flAllChain.setBackgroundResource(R.drawable.shape_swap_chain_select)
            mBinding.tvNetwork.text = Utils.getApp().getString(R.string.all_networks)
            mBinding.recyclerViewTab.isVisible = false
            swapChainBinder.selectChainId = 0
            mAdapterChain.notifyDataSetChanged()
            mViewModel.chainId = 0
            mViewModel.getToken()
        }

        mBinding.etSearch.addTextChangedListener {
            mViewModel.search = it.toString()
            mViewModel.getToken()
        }

        mAdapterChain.register(RPCNetwork::class.java, swapChainBinder.apply {
            onItemClick = {
                checkNetwork(it)
            }
        })
        mBinding.recyclerViewChain.layoutManager =
            LinearLayoutManager(requireContext(), LinearLayoutManager.HORIZONTAL, false)
        mBinding.recyclerViewChain.adapter = mAdapterChain

        mAdapterTab.register(String::class.java, HomeTabBinder(true).apply {
            onItemClick = {
                when (it) {
                    0 -> {
                        mViewModel.isDepin = false
                        mViewModel.isMeMe = false
                    }
                    1 -> {
                        mViewModel.isDepin = true
                        mViewModel.isMeMe = false
                    }
                    else -> {
                        mViewModel.isDepin = false
                        mViewModel.isMeMe = true
                    }
                }
                mAdapterTab.notifyDataSetChanged()
                mViewModel.getToken()
            }
        })
        mAdapterTab.items = arrayListOf("All Tokens", "DePIN", "Meme")
        mBinding.recyclerViewTab.layoutManager = GridLayoutManager(requireContext(), 3)
        mBinding.recyclerViewTab.adapter = mAdapterTab

        mAdapterToken.register(TokenEntry::class.java, swapTokenBinder)
        mBinding.recyclerViewToken.layoutManager = LinearLayoutManager(requireContext())
        mBinding.recyclerViewToken.adapter = mAdapterToken
    }

    private fun checkNetwork(network:RPCNetwork){
        mBinding.tvNetwork.text = network.name
        mBinding.flAllChain.setBackgroundResource(R.drawable.shape_swap_chain_unselect)
        mBinding.recyclerViewTab.isVisible = network.chainId == Config.IOTEX_CHAIN_ID
        swapChainBinder.selectChainId = network.chainId
        mAdapterChain.notifyDataSetChanged()
        mViewModel.chainId = network.chainId
        mViewModel.getToken()
    }

    override fun initData() {
        mViewModel.getNetworkByChainId(chainId)
        mViewModel.defaultNetworkListLiveData.observe(this){
            checkNetwork(it)
        }

        mViewModel.getNetwork()
        mViewModel.networkListLiveData.observe(this) { networkList ->
            swapTokenBinder.networks.putAll(networkList.associateBy { it.chainId })
            mAdapterChain.items = networkList
            mAdapterChain.notifyDataSetChanged()
        }
        mViewModel.getToken()
        mViewModel.tokenListLiveData.observe(this) {
            mAdapterToken.items = it
            mAdapterToken.notifyDataSetChanged()
        }

        mViewModel.allTokenListLiveData.observe(this){
            mViewModel.getToken(false)
        }
    }

    override fun onStart() {
        super.onStart()
        val dialogWindow = dialog?.window
        val lp = dialogWindow?.attributes
        dialogWindow?.setGravity(Gravity.BOTTOM)
        lp?.width = ViewGroup.LayoutParams.MATCH_PARENT
        lp?.height = (ScreenUtils.getScreenHeight() * 0.8).toInt()
        dialogWindow?.attributes = lp
        dialogWindow?.setWindowAnimations(R.style.BottomSheetAnimation)
    }

    override fun dismiss() {
        onDisMiss?.invoke()
        super.dismiss()
    }
}
