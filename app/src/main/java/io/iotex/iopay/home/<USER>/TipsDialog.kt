package io.iotex.iopay.home.dialog

import android.content.DialogInterface
import android.view.Gravity
import android.view.ViewGroup
import com.blankj.utilcode.util.Utils
import io.iotex.base.BaseViewModel
import io.iotex.base.bindbase.BaseBindDialog
import io.iotex.iopay.R
import io.iotex.iopay.databinding.DialogTipsBinding

class TipsDialog :
    BaseBindDialog<BaseViewModel, DialogTipsBinding>(R.layout.dialog_tips) {

    var title: String = Utils.getApp().getString(R.string.note)
    var message: String = Utils.getApp().getString(R.string.dapp_not_support_bitcoin_network)


    var onCancel: (() -> Unit)? = null
    var onConfirm: (() -> Unit)? = null

    override fun initView() {
        mBinding.ivClose.setOnClickListener {
            dismiss()
        }
        mBinding.tvOk.setOnClickListener {
            onConfirm?.invoke()
            dismiss()
        }
        mBinding.tvTitle.text = title
        mBinding.tvMessage.text = message
    }

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        onCancel?.invoke()
    }

    override fun onStart() {
        super.onStart()
        val dialogWindow = dialog?.window
        val lp = dialogWindow?.attributes
        dialogWindow?.setGravity(Gravity.CENTER)
        lp?.width = ViewGroup.LayoutParams.MATCH_PARENT
        lp?.height = ViewGroup.LayoutParams.WRAP_CONTENT
        dialogWindow?.attributes = lp
    }
}
