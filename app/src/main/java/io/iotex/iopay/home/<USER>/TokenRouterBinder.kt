package io.iotex.iopay.home.item

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContentProviderCompat.requireContext
import androidx.core.view.isVisible
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import com.drakeet.multitype.ItemViewBinder
import com.drakeet.multitype.MultiTypeAdapter
import io.iotex.base.bindbase.BaseBindVH
import io.iotex.iopay.R
import io.iotex.iopay.data.db.TokenEntry
import io.iotex.iopay.databinding.ItemSmartRouterBinding
import io.iotex.iopay.databinding.ItemTokenRouterBinding
import io.iotex.iopay.databinding.ItemTokenRouterGridBinding
import io.iotex.iopay.util.extension.loadImage

class TokenRouterBinder : ItemViewBinder<List<TokenEntry>, BaseBindVH<ItemTokenRouterBinding>>() {

    override fun onBindViewHolder(holder: BaseBindVH<ItemTokenRouterBinding>, item: List<TokenEntry>) {
        val mAdapter = MultiTypeAdapter()
        val binder = TokenRouterItemBinder(holder.adapterPosition == 0)
        holder.bind.recyclerView.layoutManager = LinearLayoutManager(holder.bind.recyclerView.context,LinearLayoutManager.HORIZONTAL,false)
        mAdapter.register(TokenEntry::class.java,binder)
        mAdapter.items = item
        holder.bind.recyclerView.adapter = mAdapter
    }

    override fun onCreateViewHolder(
        inflater: LayoutInflater,
        parent: ViewGroup
    ): BaseBindVH<ItemTokenRouterBinding> {
        return BaseBindVH(
            ItemTokenRouterBinding.inflate(
                LayoutInflater.from(parent.context),
                parent,
                false
            )
        )
    }
    class TokenRouterItemBinder(private val first:Boolean) : ItemViewBinder<TokenEntry, BaseBindVH<ItemTokenRouterGridBinding>>() {

        override fun onBindViewHolder(holder: BaseBindVH<ItemTokenRouterGridBinding>, item: TokenEntry) {
            holder.bind.ivLogo.loadImage(item.logo,R.drawable.icon_token_default)
            holder.bind.tvName.text = item.symbol.uppercase()
            holder.bind.ivArrow.isVisible = !(holder.adapterPosition == 0 && first)
        }

        override fun onCreateViewHolder(
            inflater: LayoutInflater,
            parent: ViewGroup
        ): BaseBindVH<ItemTokenRouterGridBinding> {
            return BaseBindVH(
                ItemTokenRouterGridBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                )
            )
        }
    }
}