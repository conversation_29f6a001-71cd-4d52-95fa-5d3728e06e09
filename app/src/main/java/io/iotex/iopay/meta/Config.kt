package io.iotex.iopay.meta

const val META_PEBBLE_GRAPHQL = " https://api.iopay.me/v1/graphql"
const val CONTRACT_KEY_REGISTER = "register"

const val SP_KEY_GPS_CHECKED = "sp_key_sps_checked"
const val SP_KEY_SUBMIT_FREQUENCY = "sp_key_submit_frequency"
const val SP_KEY_GPS_PRECISION = "sp_key_gps_precision"

const val SP_KEY_META_SWITCH = "sp_key_meta_switch"
const val SP_KEY_W3B_STREAM_MENU_LOCAL = "sp_key_w3b_stream_menu_local"
const val SP_KEY_W3B_STREAM_MENU_SERVICE = "sp_key_w3b_stream_menu_service"
const val SP_KEY_W3B_STREAM_MENU_VISIBLE = "sp_key_w3b_stream_menu_visible"

const val SP_KEY_GEO_LOCATION_ERROR = "sp_key_geo_location_error"
const val SP_KEY_META_GEO_LOCATION_TIPS = "sp_key_meta_geo_location_tips"
const val SP_KEY_APP_DATA_INIT_FIRST_TIME = "sp_key_app_data_init_first_time"
const val SP_KEY_FIRST_SWITCH_TO_BITCOIN= "sp_key_first_switch_to_bitcoin"
const val SP_KEY_FIRST_SWITCH_TO_SOLANA= "sp_key_first_switch_to_solana_v2"

/**
 * location default
 */
const val INTERVAL_SEND_DATA = 5
const val GPS_PRECISION = 100

const val LEN_IMEI = 15
const val LEN_SN = 10

const val BUILD_APP_KEY = "QD/MHvIaPoqyTrB3JFzWdui3o8iGPStCgHKyNYzkjxfOog28AEce4qGfDjjRdsnG"

const val APP_KEY = """
                        KcgaALmI37JztNvVlj88ljqOl31QsgjubvShQBgrrDYZmyYNAkEA3e/A8/4YrYAD
                        faqtFrmlxhReE/8vUu3ArzB9o7K3HGpB7WfgFCNuG2NDJL54aUO/WADv06eQPMFQ
                        y0y6omZ1fwJBAMBKg9B8vzp96HypOIanXP++kFk36R0RueJw9B6xQmg9jgJbZhh9
                        k1QW1Szp81/0aNGlkF3bWpFn/qBdgEabRtcCQCpRHKlpOatbTU8YzAgZPdKW75lA
                        fvWA/8xnoo0j9mYknI130PIGD2iJdLP83Vi04jcVdqUUvhvXgGBDMRLmFmECQAY/
                        5bzW8Rgjk3TJwy6NLfaZ6PMdYBQzyUjUxvpgZHoi1gS5l73gBvPKsi79g41w0h9O
                        NDz4rh7ftGTd5RdmYI0CQQCq1/uMWEwQNAQohUq782p4vz7ARJsv/h/pv6xwTOEC
                        4X0ei9LM8FjebvMPb8YOqhsEMZ+A6IoNYsxHZYRmzdkW
                        """