package io.iotex.iopay.meta

import android.Manifest.permission
import android.app.Service
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.IBinder
import com.blankj.utilcode.util.*
import com.google.gson.Gson
import com.machinefi.w3bstream.utils.KeystoreUtil
import io.iotex.base.RetrofitClient
import io.iotex.iopay.IoPayApplication
import io.iotex.iopay.R
import io.iotex.iopay.api.GeoApi
import io.iotex.iopay.data.db.AppDatabase
import io.iotex.iopay.meta.bean.RecordEntry
import io.iotex.iopay.meta.bean.SensorData
import io.iotex.iopay.support.eventbus.MetaPermissionErrorEvent
import io.iotex.iopay.support.eventbus.MetaUploadErrorEvent
import io.iotex.iopay.support.eventbus.MetaUploadLocationErrorEvent
import io.iotex.iopay.util.Config.GEO_W3BSTREAM_URL
import io.iotex.iopay.util.GPSUtil
import io.iotex.iopay.util.RandomUtil
import io.iotex.iopay.wallet.dialog.PromptDialog
import kotlinx.coroutines.*
import org.greenrobot.eventbus.EventBus
import java.math.BigDecimal
import java.net.UnknownHostException
import kotlin.math.log10

class MetaUploadService : Service() {

    private var recordId = 0L

    private val geoApiService by lazy {
        RetrofitClient.createApiService(GEO_W3BSTREAM_URL, GeoApi::class.java)
    }

    companion object {
        fun startService(context: Context, showPermissionError: Boolean = false) {
            val intent = Intent(context, MetaUploadService::class.java)
            intent.putExtra("showPermissionError", showPermissionError)
            context.startService(intent)
        }

        fun stopService(context: Context) {
            val intent = Intent(context, MetaUploadService::class.java)
            context.stopService(intent)
        }
    }

    override fun onBind(intent: Intent?): IBinder? {
        return null
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        val exceptionHandler = CoroutineExceptionHandler { _, exception ->
            ToastUtils.showShort(exception.message)
        }
        CoroutineScope(Dispatchers.IO).launch(exceptionHandler) {
            val queryDevice = AppDatabase.getInstance(this@MetaUploadService).deviceDao()
                .queryDevice()
            if (queryDevice != null) {
                requestPermission(
                    queryDevice.imei,
                    intent?.getBooleanExtra("showPermissionError", false) ?: false
                )
            }
        }
        return super.onStartCommand(intent, flags, startId)
    }

    private fun requestPermission(imei: String, showPermissionError: Boolean) {
        if (SPUtils.getInstance().getBoolean(SP_KEY_GPS_CHECKED, true)) {
            PermissionUtils
                .permission(permission.ACCESS_FINE_LOCATION, permission.ACCESS_COARSE_LOCATION)
                .callback(object : PermissionUtils.SimpleCallback {
                    override fun onGranted() {
                        startUpload(imei)
                    }

                    override fun onDenied() {
                        if (showPermissionError) {
                            EventBus.getDefault().post(MetaPermissionErrorEvent())
                            PromptDialog(ActivityUtils.getTopActivity())
                                .setTitle(Utils.getApp().getString(R.string.error))
                                .setContent(
                                    Utils.getApp().getString(R.string.open_location_permission)
                                )
                                .setPositiveButton(Utils.getApp().getString(R.string.ok)) {
                                    PermissionUtils.launchAppDetailsSettings()
                                }
                                .show()
                        }
                    }
                })
                .request()
        }

    }

    /**
     * upload data
     */
    fun startUpload(imei: String) {
        polling {
            if (System.currentTimeMillis() - recordId < 50 * 1000) {
                return@polling
            }
            recordId = System.currentTimeMillis()
            val location = GPSUtil.getLocation() ?: kotlin.run {
                recordError(imei, "0", "0")
                EventBus.getDefault().postSticky(MetaUploadLocationErrorEvent(true))
                return@polling
            }
            EventBus.getDefault().postSticky(MetaUploadLocationErrorEvent(false))
            val gpsPrecision = SPUtils.getInstance().getInt(SP_KEY_GPS_PRECISION, GPS_PRECISION)
            val origin = 7 - log10(gpsPrecision.toDouble()).toInt()
            val decimal = if (origin >= 0) {
                origin
            } else {
                0
            }
            var lat = 0L
            var lng = 0L
            if (SPUtils.getInstance().getBoolean(SP_KEY_GPS_CHECKED, true)) {
                lat = GPSUtil.encodeLocation(location.latitude, decimal)
                lng = GPSUtil.encodeLocation(location.longitude, decimal)
            }

            val random = RandomUtil.integer(10000, 99999)
            val bd = TimeUtils.getNowMills().toBigDecimal().div(BigDecimal.TEN.pow(3))
            val timestampStr = bd.setScale(0, BigDecimal.ROUND_DOWN)
            val sensorData = SensorData(
                imei,
                1024,
                lat.toString(),
                lng.toString(),
                random.toString(),
                timestampStr.toLong()
            )
            var rawLat = "0"
            var rawLng = "0"
            if (SPUtils.getInstance().getBoolean(SP_KEY_GPS_CHECKED, true)) {
                rawLat = GPSUtil.decodeLocation(lat)
                rawLng = GPSUtil.decodeLocation(lng)
            }

            upload(Gson().toJson(sensorData), success = {
                val timestamp = TimeUtils.getNowMills().toString()
                RecordEntry(
                    recordId.toString(),
                    imei,
                    rawLng,
                    rawLat,
                    timestamp, "1"
                ).also {
                    AppDatabase.getInstance(this).recordDao().insertIfNonExist(it)
                }
            }, errorHandler = {
                recordError(imei, rawLng, rawLat)
            })
        }
    }

    private fun recordError(imei: String, rawLng: String, rawLat: String) {
        CoroutineScope(Dispatchers.IO).launch{
            val timestamp = TimeUtils.getNowMills().toString()
            RecordEntry(
                recordId.toString(),
                imei,
                rawLng,
                rawLat,
                timestamp, "0"
            ).also {
                AppDatabase.getInstance(IoPayApplication.getInstance()).recordDao().insertIfNonExist(it)
            }
            EventBus.getDefault().post(MetaUploadErrorEvent())
            SPUtils.getInstance().put(SP_KEY_GEO_LOCATION_ERROR, true)
        }
    }

    private fun upload(
        json: String,
        success: ((data: String) -> Unit)? = null,
        errorHandler: (() -> Unit)? = null
    ) {
        val exceptionHandler = CoroutineExceptionHandler { _, exception ->
            if (exception is UnknownHostException) {
                ToastUtils.showShort(R.string.no_network)
            } else {
                ToastUtils.showShort(exception.message)
            }
            errorHandler?.invoke()
        }
        CoroutineScope(Dispatchers.IO).launch(SupervisorJob() + exceptionHandler) {
            val signature = KeystoreUtil.signData(json.toByteArray())
            val pubKey = KeystoreUtil.getPubKey()
            val map = HashMap<String, String>()
            map["pubKey"] = pubKey
            map["signature"] = signature
            map["data"] = json
            val result = geoApiService.upload(map).toString()
            success?.invoke(result)
        }
    }


    /**
     * repeat upload
     */
    private fun polling(callback: () -> Unit) {
        val exceptionHandler = CoroutineExceptionHandler { _, _ ->
            EventBus.getDefault().post(MetaUploadErrorEvent())
            SPUtils.getInstance().put(SP_KEY_GEO_LOCATION_ERROR, true)
        }
        var interval =
            SPUtils.getInstance().getInt(SP_KEY_SUBMIT_FREQUENCY, INTERVAL_SEND_DATA).toLong()
        if (interval == 0L) interval = 5L
        CoroutineScope(Dispatchers.IO).launch(SupervisorJob() + exceptionHandler) {
            callback.invoke()//open upload first
            while (true) {
                if (System.currentTimeMillis() % (interval * 60 * 1000) == 0L) {
                    callback.invoke()
                }
            }
        }
    }
}
