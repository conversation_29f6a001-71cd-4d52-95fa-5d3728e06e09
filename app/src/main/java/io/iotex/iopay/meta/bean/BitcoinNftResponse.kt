package io.iotex.iopay.meta.bean

import java.io.Serializable

data class BitcoinNftResponse(
    val code: Int,
    val `data`: Pagination,
    val msg: String
): Serializable

data class Pagination(
    val `data`: List<BitcoinNft>,
    val page: Int,
    val size: Int,
    val total: Int
): Serializable

data class BitcoinNft(
    val content: String,
    val contentLink: String,
    val contentType: String,
    val createdTime: String,
    val genesisHeight: String,
    val genesisTransaction: String,
    val inscriptionId: String,
    val inscriptionNum: String,
    val location: String,
    val locationHeight: Int,
    val output: String,
    val outputValue: String,
    val ownerAddress: String
): Serializable