package io.iotex.iopay.meta.bean

import org.bitcoinj.core.UTXO
import java.io.Serializable


data class UnspentUTXO(
    val status: UTXOStatus,
    val txid: String,
    val value: Long,
    val vout: Long
): Serializable

data class UTXOStatus(
    val block_hash: String,
    val block_height: Long,
    val block_time: Long,
    val confirmed: Boolean
): Serializable

data class BitcoinTransactionReceipt(
    val fee: Long,
    val locktime: Long,
    val sigops: Long,
    val size: Long,
    val status: TransactionStatus,
    val txid: String,
    val version: Long,
    val vin: List<TransactionVin>,
    val vout: List<TransactionVout>,
    val weight: Long
): Serializable

data class TransactionStatus(
    val block_hash: String,
    val block_height: Long,
    val block_time: Long,
    val confirmed: Boolean
): Serializable

data class TransactionVin(
    val is_coinbase: Boolean,
    val prevout: Prevout,
    val scriptsig: String,
    val scriptsig_asm: String,
    val sequence: Long,
    val txid: String,
    val vout: Long,
    val witness: List<String>
): Serializable

data class TransactionVout(
    val scriptpubkey: String,
    val scriptpubkey_address: String,
    val scriptpubkey_asm: String,
    val scriptpubkey_type: String,
    val value: Long
): Serializable

data class Prevout(
    val scriptpubkey: String,
    val scriptpubkey_address: String,
    val scriptpubkey_asm: String,
    val scriptpubkey_type: String,
    val value: Long
): Serializable

data class BitcoinFee(
    val economyFee: Int,
    val fastestFee: Int,
    val halfHourFee: Int,
    val hourFee: Int,
    val minimumFee: Int
): Serializable

data class UTXOBundle(
    val confirmedUTXOs: List<UTXO>,
    val availableUTXOs: List<UTXO>
): Serializable

data class RPCResult(
    val code: Int,
    val message: String
): Serializable

