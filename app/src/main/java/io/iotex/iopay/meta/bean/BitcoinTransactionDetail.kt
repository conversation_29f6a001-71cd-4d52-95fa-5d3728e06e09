package io.iotex.iopay.meta.bean

import java.io.Serializable

data class BitcoinTransactionDetail(
    val blockchainSpecific: BlockchainSpecific,
    val fee: Fee,
    val index: Int,
    val isConfirmed: Boolean,
    val minedInBlockHash: String,
    val minedInBlockHeight: Int,
    val recipients: List<Recipient>,
    val senders: List<Sender>,
    val timestamp: Int,
    val transactionHash: String,
    val transactionId: String
): Serializable

data class BlockchainSpecific(
    val locktime: Int,
    val size: Int,
    val vSize: Int,
    val version: Int,
    val vin: List<Vin>,
    val vout: List<Vout>
): Serializable

data class Fee(
    val amount: String,
    val unit: String
): Serializable

data class Recipient(
    val address: String,
    val amount: String
): Serializable

data class Sender(
    val address: String,
    val amount: String
): Serializable

data class Vin(
    val addresses: List<String>,
    val scriptSig: ScriptSig,
    val sequence: Long,
    val txid: String,
    val txinwitness: List<String>,
    val value: String,
    val vout: Int
): Serializable

data class Vout(
    val isSpent: Boolean,
    val scriptPubKey: ScriptPubKey,
    val value: String
): Serializable

data class ScriptSig(
    val asm: String,
    val hex: String,
    val type: String
): Serializable

data class ScriptPubKey(
    val addresses: List<String>,
    val asm: String,
    val hex: String,
    val reqSigs: Int,
    val type: String
): Serializable