package io.iotex.iopay.meta.bean

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import java.io.Serializable

/**
 * device for register meta
 */

@Entity(tableName = "DeviceEntry")
data class DeviceEntry(
    @PrimaryKey
    val imei: String,

    val sn: String,

    @ColumnInfo(name = "pub_key")
    val pubKey: String,

    var owner: String = "", // wallet address

    var power: Int = 0,//user SP_KEY_META_SWITCH

    var registered: Boolean = false,

    var activated: Boolean = false
) : Serializable