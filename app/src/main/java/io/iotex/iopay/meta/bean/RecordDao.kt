package io.iotex.iopay.meta.bean

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query

/**
 * meta upload data history record
 */

@Dao
interface RecordDao {

    @Insert(onConflict = OnConflictStrategy.IGNORE)
    fun insertIfNonExist(record: RecordEntry)

    @Query("select * from RecordEntry where (imei = :imei AND success == '1') order by timestamp desc limit ((:page - 1)*:size), (:page*:size)")
    fun queryByImei(imei: String, page: Int, size: Int): List<RecordEntry>

    @Query("select * from RecordEntry where (imei = :imei AND success == '0') order by timestamp desc limit ((:page - 1)*:size), (:page*:size)")
    fun queryErrorByImei(imei: String, page: Int, size: Int): List<RecordEntry>
}