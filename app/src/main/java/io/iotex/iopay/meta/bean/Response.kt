package io.iotex.iopay.meta.bean

import java.io.Serializable

class ResponseWrapper<T>(val result: Response<T>?, val error: Error?): Serializable

data class Error(
    val code: Int,
    val message: String
): Serializable

data class Response<T>(
    val type: String,
    val data: T
): Serializable

data class OwnerBean(
    val owner:String,
): Serializable

data class JsonAddressBean(
    val json:AddressBean,
): Serializable{
    inner class AddressBean(
        val address:String
    ): Serializable
}

data class JsonTokenRiskBean(
    val json:TokenRiskBean,
): Serializable{
    inner class TokenRiskBean(
        val code:String,
        val token:TokenRisk,
    ): Serializable{
        inner class TokenRisk(
            val riskStatus:String,
        ): Serializable
    }
}

data class ChainIdResult(
    val result:String?
): Serializable

data class GasTrackerResponse(
    val message: String,
    val result: GasTracker,
    val status: String
): Serializable

data class GasTimeResponse(
    val message: String,
    val result: String,
    val status: String
): Serializable

data class GasTracker(
    val FastGasPrice: String,
    val LastBlock: String,
    val ProposeGasPrice: String,
    val SafeGasPrice: String,
    val gasUsedRatio: String,
    val suggestBaseFee: String
): Serializable

data class ActionResponse(
    val exist:Boolean,
    val actions:ArrayList<ActionResult>?,
)
data class ActionResult(
    val actHash:String?,
    val actType:String?,
    val sender:String?,
    val recipient:String?,
    val contract:String?,
    val amount:String?,
    val timestamp:String?,
    val gasFee:String?,
)

data class ActionXRC20Response(
    val exist:Boolean,
    val xrc20:ArrayList<ActionResult>?,
)

data class ActionXRC721Response(
    val exist:Boolean,
    val xrc721:ArrayList<ActionResult>?,
)