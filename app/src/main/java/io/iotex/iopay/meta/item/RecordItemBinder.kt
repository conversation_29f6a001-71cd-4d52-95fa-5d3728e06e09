package io.iotex.iopay.meta.item

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.blankj.utilcode.util.TimeUtils
import com.drakeet.multitype.ItemViewBinder
import io.iotex.iopay.R
import io.iotex.iopay.meta.bean.RecordEntry
import io.iotex.iopay.util.extension.setGone
import io.iotex.iopay.util.extension.setVisible

/**
 * meta history item view
 */

class RecordItemBinder(val success: String) : ItemViewBinder<RecordEntry, RecordItemBinder.VH>() {

    override fun onCreateViewHolder(inflater: LayoutInflater, parent: ViewGroup): VH {
        val view = inflater.inflate(R.layout.item_record, parent, false)
        return VH(view)
    }

    override fun onBindViewHolder(holder: VH, item: RecordEntry) {
        holder.mTvLat.text = "Lat: " + item.lat
        holder.mTvLong.text = "Long: " + item.lng
        holder.mTvTime.text = TimeUtils.millis2String(item.timestamp.toLong())
        if (success == "0") {
            holder.mIvError.setVisible()
        } else {
            holder.mIvError.setGone()
        }
    }

    inner class VH(view: View) : RecyclerView.ViewHolder(view) {
        val mTvLat: TextView = view.findViewById(R.id.mTvLat)
        val mTvLong: TextView = view.findViewById(R.id.mTvLong)
        val mTvTime: TextView = view.findViewById(R.id.mTvTime)
        val mIvError: ImageView = view.findViewById(R.id.iv_error)
    }

}