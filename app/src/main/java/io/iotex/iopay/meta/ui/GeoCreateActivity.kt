package io.iotex.iopay.meta.ui

import com.blankj.utilcode.util.ColorUtils
import com.machinefi.w3bstream.utils.extension.setForeTextColor
import io.iotex.base.bindbase.BaseBindActivity
import io.iotex.iopay.R
import io.iotex.iopay.base.BaseBindToolbarActivity
import io.iotex.iopay.databinding.ActivityGeoCreateBinding
import org.jetbrains.anko.startActivity

class GeoCreateActivity :
    BaseBindToolbarActivity<GeoCreateViewModel, ActivityGeoCreateBinding>(R.layout.activity_geo_create) {

    override fun initView() {
        setToolbarTitle(getString(R.string.geo_location))

        mBinding.tvMessage.text = getString(R.string.connect_mobile_device).setForeTextColor(
            getString(R.string.setting_w3bstream), ColorUtils.getColor(R.color.color_617AFF)
        )

        mBinding.tvConnect.setOnClickListener {
            mViewModel.createGeoDevice()
        }
    }

    override fun initData() {
        mViewModel.deviceLiveData.observe(this) {
            GeoLoadingDialog()
                .apply {
                    onComplete = {
                        startActivity<GeoRegisterActivity>()
                        finish()
                    }
                }
                .show(supportFragmentManager, System.currentTimeMillis().toString())

        }
    }

}