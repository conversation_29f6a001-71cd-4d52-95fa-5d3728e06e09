package io.iotex.iopay.meta.ui

import android.app.Application
import android.content.Context
import androidx.lifecycle.MutableLiveData
import com.machinefi.w3bstream.utils.ImeiUtil
import com.machinefi.w3bstream.utils.KeystoreUtil
import io.iotex.iopay.base.BaseLaunchVM
import io.iotex.iopay.data.db.AppDatabase
import io.iotex.iopay.meta.LEN_SN
import io.iotex.iopay.meta.bean.DeviceEntry
import io.iotex.iopay.util.RandomUtil

class GeoCreateViewModel(application: Application) : BaseLaunchVM(application) {

    val deviceLiveData = MutableLiveData<DeviceEntry?>()

    fun createGeoDevice() {
        addLaunch {
            var deviceEntry = AppDatabase.getInstance(iView as Context).deviceDao()
                .queryDevice()
            if (deviceEntry == null) {
                val pubKey = KeystoreUtil.getPubKey()
                val imei = ImeiUtil.getImei()
                deviceEntry = DeviceEntry(imei, RandomUtil.string(LEN_SN), pubKey)
                AppDatabase.getInstance(iView as Context).deviceDao()
                    .insertIfNonExist(deviceEntry)
            }

            deviceLiveData.postValue(deviceEntry)
        }
    }
}