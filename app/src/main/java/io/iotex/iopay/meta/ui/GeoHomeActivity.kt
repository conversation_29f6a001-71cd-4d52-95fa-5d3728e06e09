package io.iotex.iopay.meta.ui

import android.content.Intent
import android.view.View
import com.blankj.utilcode.util.SPUtils
import io.iotex.iopay.R
import io.iotex.iopay.base.BaseBindToolbarActivity
import io.iotex.iopay.databinding.ActivityGeoHomeBinding
import io.iotex.iopay.meta.MetaUploadService
import io.iotex.iopay.meta.SP_KEY_GEO_LOCATION_ERROR
import io.iotex.iopay.meta.SP_KEY_META_SWITCH
import io.iotex.iopay.meta.ui.dialog.GeoLocationErrorDialog
import io.iotex.iopay.meta.ui.dialog.GeoLocationTipsDialog
import io.iotex.iopay.meta.ui.dialog.MetaWalletDialog
import io.iotex.iopay.support.eventbus.MetaPermissionErrorEvent
import io.iotex.iopay.support.eventbus.MetaUploadErrorEvent
import io.iotex.iopay.support.eventbus.MetaUploadLocationErrorEvent
import io.iotex.iopay.util.Constant
import io.iotex.iopay.util.TokenUtil
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.util.extension.setGone
import io.iotex.iopay.util.extension.setVisible
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.jetbrains.anko.startActivity

class GeoHomeActivity :
    BaseBindToolbarActivity<GeoHomeViewModel, ActivityGeoHomeBinding>(R.layout.activity_geo_home) {

    override fun initView() {
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this)
        }
        setToolbarTitle(getString(R.string.geo_location))
        setToolbarSubmitText(getString(R.string.history))

        setToolbarSubmitClick {
            val intent = Intent(this, MetaHistoryActivity::class.java)
            intent.putExtra("success", "1")
            startActivity(intent)
        }

        mBinding.llError.setOnClickListener {
            SPUtils.getInstance().put(SP_KEY_GEO_LOCATION_ERROR, false)
            activityImage()
            val intent = Intent(this, MetaHistoryActivity::class.java)
            intent.putExtra("success", "0")
            startActivity(intent)
        }

        mBinding.ivSetting.setOnClickListener {
            startActivity(Intent(this, MetaSettingActivity::class.java))
        }

        mBinding.mSwitch.isChecked =
            SPUtils.getInstance().getBoolean(SP_KEY_META_SWITCH, false)
        activityImage()

        mBinding.mSwitch.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked) {
                SPUtils.getInstance().put(SP_KEY_META_SWITCH, true)
                MetaUploadService.startService(this, true)
            } else {
                SPUtils.getInstance().put(SP_KEY_META_SWITCH, false)
                MetaUploadService.stopService(this)
            }
            activityImage()
        }

        mBinding.ivAddressWarn.setOnClickListener {
            MetaWalletDialog(mViewModel.deviceLiveData.value?.owner ?: "")
                .show(supportFragmentManager, System.currentTimeMillis().toString())
        }

    }

    override fun initData() {
        mViewModel.deviceLiveData.observe(this) {
            if (it == null) {
                startActivity<GeoCreateActivity>()
                return@observe
            } else if (!it.activated) {
                startActivity<GeoRegisterActivity>()
                return@observe
            }
            mBinding.tvImei.text = "IMEI: " + it.imei
            mBinding.tvSn.text = "SN: " + it.sn
            if (it.owner.isNotBlank()) {
                val address = it.owner.lowercase()
                mBinding.tvAddress.text = TokenUtil.textEllipsis(address, 6, 6)
                val currentWallet =
                    WalletHelper.convertWeb3Address(Constant.currentWallet?.address ?: "")
                if (!address.equals(currentWallet, true)) {
                    mBinding.ivAddressWarn.setVisible()
                } else {
                    mBinding.ivAddressWarn.setGone()
                }
            }
        }
    }

    private fun activityImage() {
        val meta = SPUtils.getInstance().getBoolean(SP_KEY_META_SWITCH, false)
        if (meta) {
            val error = SPUtils.getInstance().getBoolean(SP_KEY_GEO_LOCATION_ERROR)
            if (error) {
                mBinding.llError.visibility = View.VISIBLE
                mBinding.llLoading.visibility = View.GONE
            } else {
                mBinding.llError.visibility = View.GONE
                mBinding.llLoading.visibility = View.VISIBLE
            }
            mBinding.ivImage.setImageResource(R.drawable.icon_meta_activity_on)
        } else {
            mBinding.llError.visibility = View.GONE
            mBinding.llLoading.visibility = View.INVISIBLE
            mBinding.ivImage.setImageResource(R.drawable.icon_meta_activity_off)
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEventMainThread(event: MetaUploadErrorEvent) {
        val meta = SPUtils.getInstance().getBoolean(SP_KEY_META_SWITCH, false)
        if (meta) {
            mBinding.llError.visibility = View.VISIBLE
            mBinding.llLoading.visibility = View.GONE
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEventMainThread(event: MetaPermissionErrorEvent) {
        GeoLocationTipsDialog().show(
            supportFragmentManager,
            System.currentTimeMillis().toString()
        )
        mBinding.mSwitch.isChecked = false
        SPUtils.getInstance().put(SP_KEY_META_SWITCH, false)
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEventMainThread(event: MetaUploadLocationErrorEvent) {
        if(event.weakGps){
            mBinding.ivGps.setImageResource(R.drawable.icon_gps_red)
            mBinding.ivGps.setOnClickListener {
                GeoLocationErrorDialog().show(supportFragmentManager,System.currentTimeMillis().toString())
            }
        }else{
            mBinding.ivGps.setImageResource(R.drawable.icon_gps_green)
            mBinding.ivGps.setOnClickListener {
                //just cover click.
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this)
        }
    }
}