package io.iotex.iopay.meta.ui

import android.app.Application
import android.content.Context
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.MutableLiveData
import io.iotex.iopay.base.BaseLaunchVM
import io.iotex.iopay.data.db.AppDatabase
import io.iotex.iopay.meta.bean.DeviceEntry

class GeoHomeViewModel(application: Application) : BaseLaunchVM(application) {

    val deviceLiveData = MutableLiveData<DeviceEntry?>()

    override fun onCreate(owner: LifecycleOwner) {
        super.onCreate(owner)
        getGeoDevice()
    }

    private fun getGeoDevice() {
        addLaunch {
            val queryDevice = AppDatabase.getInstance(iView as Context).deviceDao()
                .queryDevice()
            deviceLiveData.postValue(queryDevice)
        }
    }
}