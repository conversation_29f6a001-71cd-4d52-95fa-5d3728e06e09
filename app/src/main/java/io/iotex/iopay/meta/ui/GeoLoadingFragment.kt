package io.iotex.iopay.meta.ui

import android.view.WindowManager
import com.blankj.utilcode.util.ColorUtils
import com.bumptech.glide.Glide
import com.machinefi.w3bstream.utils.extension.setForeTextColor
import io.iotex.base.BaseViewModel
import io.iotex.base.bindbase.BaseBindDialog
import io.iotex.iopay.R
import io.iotex.iopay.databinding.DialogGeoLoadingBinding
import io.reactivex.Flowable
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.Disposable
import java.util.concurrent.TimeUnit

class GeoLoadingDialog :
    BaseBindDialog<BaseViewModel, DialogGeoLoadingBinding>(R.layout.dialog_geo_loading) {

    private var dis: Disposable? = null

    var onComplete: (() -> Unit)? = null

    override fun initView() {
        mBinding.toolbarTitle.setText(R.string.geo_location)

        mBinding.tvMessage.text = getString(R.string.connect_w3bstream).setForeTextColor(
            getString(R.string.setting_w3bstream), ColorUtils.getColor(R.color.color_617AFF)
        )

        Glide.with(mBinding.ivImage).load(R.drawable.icon_meta_progess).into(mBinding.ivImage)

        startProgress()
    }

    private fun startProgress() {
        if (dis != null) return
        dis = Flowable.intervalRange(1, 100, 0, 30, TimeUnit.MILLISECONDS)
            .observeOn(AndroidSchedulers.mainThread())
            .doOnNext {
                mBinding.mLoadingProgress.setProgress(it.toInt())
            }
            .doFinally {
                onComplete?.invoke()
            }
            .subscribe()
    }

    override fun onStart() {
        super.onStart()
        val attributes = dialog?.window?.attributes
        attributes?.width = WindowManager.LayoutParams.MATCH_PARENT
        attributes?.height = WindowManager.LayoutParams.MATCH_PARENT
        dialog?.window?.attributes = attributes
    }

    override fun onDestroy() {
        super.onDestroy()
        if (dis?.isDisposed == false) dis?.dispose()
    }

}