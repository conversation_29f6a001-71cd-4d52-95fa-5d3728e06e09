package io.iotex.iopay.meta.ui

import com.blankj.utilcode.util.ColorUtils
import com.machinefi.w3bstream.utils.extension.setForeTextColor
import io.iotex.iopay.R
import io.iotex.iopay.base.BaseBindToolbarActivity
import io.iotex.iopay.databinding.ActivityGeoRegisterBinding
import io.iotex.iopay.transaction.TransactionDialog
import io.iotex.iopay.util.Constant
import io.iotex.iopay.util.FireBaseUtil
import io.iotex.iopay.util.TokenUtil
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.wallet.dialog.PromptDialog
import org.jetbrains.anko.startActivity
import java.math.BigInteger

class GeoRegisterActivity :
    BaseBindToolbarActivity<GeoRegisterViewModel, ActivityGeoRegisterBinding>(R.layout.activity_geo_register) {

    private var transactionDialog: TransactionDialog? = null

    private val mErrorWalletDialog by lazy {
        PromptDialog(this)
            .setTitle(getString(R.string.error))
            .setCloseable(false)
            .setContent(
                getString(
                    R.string.select_specific_wallet,
                    TokenUtil.textEllipsis(mViewModel.deviceLiveData.value?.owner ?: "", 8, 8)
                )
            )
            .setPositiveButton(getString(R.string.ok)) {
                finish()
            }
    }

    private val mWatchWalletDialog by lazy {
        PromptDialog(this)
            .setTitle(getString(R.string.error))
            .setCloseable(false)
            .setContent(
                getString(
                    R.string.watch_wallet_warning,
                )
            )
            .setPositiveButton(getString(R.string.ok)) {
                finish()
            }
    }

    override fun initView() {
        setToolbarTitle(getString(R.string.geo_location))

        mBinding.tvMessage.text =
            getString(R.string.you_have_successfully_connected_w3bstream).setForeTextColor(
                getString(R.string.connected_w3bstream), ColorUtils.getColor(R.color.color_617AFF)
            )


        mBinding.tvRegister.setOnClickListener {
            if (mViewModel.watchLiveData.value == true) {
                mWatchWalletDialog.show()
                return@setOnClickListener
            }
            mViewModel.queryRegisterResult()
            FireBaseUtil.logFireBase("action_setting_w3bstream_register")
        }
    }

    override fun initData() {
        mViewModel.deviceLiveData.observe(this) {
            if (it?.activated == true) {
                mBinding.tvRegister.text = getString(R.string.activate_geo_location)
                mBinding.tvMessage.text =
                    getString(R.string.register_success)
                mBinding.tvRegister.setOnClickListener {
                    startActivity<GeoHomeActivity>()
                    finish()
                    FireBaseUtil.logFireBase("action_access_geolocation")
                }
            } else if (it?.registered == true) {
                mViewModel.retrievePubKey(it.imei)
            } else {
                mViewModel.register()
            }

            val curWalletAddress = Constant.currentWallet?.address ?: ""
            val web3Address = WalletHelper.convertWeb3Address(curWalletAddress)
            if (it?.owner?.isNotBlank() == true && !it.owner.equals(web3Address, true)) {
                mErrorWalletDialog.show()
            }
        }

        mViewModel.transactionDialogData.observe(this) {
            if (transactionDialog != null && transactionDialog?.showsDialog == true) return@observe
            transactionDialog = TransactionDialog(it.contract, BigInteger.ZERO,it.signData,it.options).apply {
                onCancel = {
                    transactionDialog = null
                }

                onTransactionConfirm = { gasLimit: Long, gasPrice: String, maxPriorityFeePerGas, maxFeePerGas ->
                    transactionDialog?.dismiss()
                    transactionDialog = null
                    mViewModel.executeActive(it.contract, it.signData, gasLimit, gasPrice)
                }
            }
            transactionDialog?.show(supportFragmentManager,System.currentTimeMillis().toString())
        }
    }
}