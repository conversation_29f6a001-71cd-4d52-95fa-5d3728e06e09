package io.iotex.iopay.meta.ui

import android.app.Application
import android.content.Context
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.MutableLiveData
import com.apollographql.apollo.ApolloClient
import com.apollographql.apollo.coroutines.await
import com.blankj.utilcode.util.ToastUtils
import com.blankj.utilcode.util.Utils
import com.machinefi.metapebble.utils.SecurityUtil
import com.machinefi.w3bstream.bean.AuthRequest
import com.machinefi.w3bstream.bean.AuthResult
import com.machinefi.w3bstream.utils.KeystoreUtil
import com.machinefi.w3bstream.utils.TypeEncoder
import io.iotex.base.RetrofitClient
import io.iotex.base.okHttpClient
import io.iotex.iopay.R
import io.iotex.iopay.api.GeoApi
import io.iotex.iopay.base.BaseLaunchVM
import io.iotex.iopay.data.db.ACTION_TYPE_DAPP
import io.iotex.iopay.data.db.AppDatabase
import io.iotex.iopay.meta.APP_KEY
import io.iotex.iopay.meta.BUILD_APP_KEY
import io.iotex.iopay.meta.CONTRACT_KEY_REGISTER
import io.iotex.iopay.meta.META_PEBBLE_GRAPHQL
import io.iotex.iopay.meta.bean.DeviceEntry
import io.iotex.iopay.meta.bean.RequestWrapper
import io.iotex.iopay.meta.bean.RetrievePubKeyBody
import io.iotex.iopay.meta.bean.TransactionData
import io.iotex.iopay.repo.TransferEntryMapRepo
import io.iotex.iopay.util.Config.GEO_W3BSTREAM_URL
import io.iotex.iopay.util.Constant
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.transaction.bean.OptionEntry
import io.iotex.iopay.util.extension.asBigDecimal
import io.iotex.iopay.util.extension.toHexByteArray
import io.iotex.iopay.wallet.web3.FunctionSignData
import io.iotex.iopay.wallet.web3.Web3Delegate
import io.iotex.iopay.wallet.web3.Web3Repository
import io.iotex.metapebble.ContractQuery
import org.web3j.abi.datatypes.Address
import org.web3j.abi.datatypes.generated.Uint256
import org.web3j.utils.Numeric
import java.math.BigInteger
import kotlin.coroutines.suspendCoroutine

class GeoRegisterViewModel(application: Application) : BaseLaunchVM(application) {

    val confirmLiveData = MutableLiveData<String>()
    val deviceLiveData = MutableLiveData<DeviceEntry?>()
    val watchLiveData = MutableLiveData<Boolean>()

    val transactionDialogData = MutableLiveData<TransactionData>()

    //wallet web3 address
    var address = WalletHelper.convertWeb3Address(
        Constant.currentWallet?.address
            ?: ""
    )

    private val mApolloClient = ApolloClient.builder()
        .serverUrl(META_PEBBLE_GRAPHQL)
        .okHttpClient(okHttpClient)
        .build()

    private val geoApiService by lazy {
        RetrofitClient.createApiService(GEO_W3BSTREAM_URL, GeoApi::class.java)
    }

    override fun onCreate(owner: LifecycleOwner) {
        super.onCreate(owner)
        queryWallet()
    }

    private fun queryWallet(){
        addLaunch {
            val wallet  = AppDatabase.getInstance(iView as Context).walletDao().queryWalletByAddress(address)
            watchLiveData.postValue(wallet?.isWatch == true)
        }
    }

    fun queryRegisterResult() {
        addLaunch(true) {
            val device = AppDatabase.getInstance(iView as Context).deviceDao()
                .queryDevice()
            val map = HashMap<String, String>()
            map["imei"] = device?.imei ?: ""
            val responseWrapper = geoApiService.requestRegisterResult(map)
            val owner = responseWrapper.result?.data?.owner ?: ""
            val registered = WalletHelper.isValidAddress(owner)
            if (registered) {
                device?.let {
                    device.owner = owner
                    device.registered = true
                    AppDatabase.getInstance(iView as Context).deviceDao().update(device)
                }
            }
            deviceLiveData.postValue(device)
        }
    }

    fun register() {
        addLaunch(true) {
            val pubKey = KeystoreUtil.getPubKey()
            val data = "${deviceLiveData.value?.imei}${deviceLiveData.value?.sn}${pubKey}"
            val key = "${SecurityUtil.key()}$BUILD_APP_KEY$APP_KEY"
            val signature = KeystoreUtil.signKeyData(key, data)
            val authRequest = AuthRequest(
                deviceLiveData.value?.imei ?: "",
                deviceLiveData.value?.sn ?: "",
                pubKey,
                signature
            )
            val responseWrapper = geoApiService.sign(authRequest)
            responseWrapper.result?.data?.let {
                activateMetaPebble(it)
            }
        }
    }

    private suspend fun getContract(name: String): String {
        val registerContract = mApolloClient.query(ContractQuery()).await().data
            ?.metaPebble_pebble_contract_mainnet()
            ?.firstOrNull {
                it?.name() == name
            }
        return WalletHelper.convertWeb3Address(registerContract?.address() ?: "")
    }

    private fun activateMetaPebble(authResult: AuthResult) {
        addLaunch(true) {
            val contract = getContract(CONTRACT_KEY_REGISTER)
            val pubKey = KeystoreUtil.getPubKey()
            val msg = Numeric.prependHexPrefix(
                TypeEncoder.encodePacked(Address(address)) +
                    TypeEncoder.encodePacked(Uint256(authResult.timestamp.toBigInteger()))
            )
            val signature = KeystoreUtil.signData(msg.toHexByteArray())
            val signData = FunctionSignData.getRegistrationData(
                authResult.imei,
                pubKey,
                authResult.sn,
                authResult.timestamp.toString(),
                signature,
                authResult.authentication
            )
            val nonce = Web3Delegate.transactionNonce()
            val code = Web3Delegate.resolveMethodCode(signData)
            val method = TransferEntryMapRepo().querySignatures(code)
            val options = arrayListOf(
                OptionEntry(Utils.getApp().getString(R.string.method), method),
                OptionEntry("Nonce", nonce.toString())
            )
            options.add(
                OptionEntry(
                    Utils.getApp().getString(R.string.amount),
                    "0"
                )
            )
            transactionDialogData.postValue(
                TransactionData(
                    contract,
                    signData, options
                )
            )
        }
    }

    fun executeActive(contract: String, data: String, gasLimit: Long,gasPrice: String) {
        addLaunch(true) {
            Web3Repository.doTransaction(
                0,
                contract,
                gasPrice.asBigDecimal().toBigInteger(),
                gasLimit.toBigInteger(),
                data,
                BigInteger.ZERO,
                ACTION_TYPE_DAPP,
                "",
                "",
                "",
                "MetaPebble",
                {
                    confirmLiveData.postValue("")
                }
            ){ _, res, _ ->
                if (!res.isNullOrBlank()) {
                    val receipt = Web3Delegate.queryTransactionReceipt(res)
                    if (receipt?.isStatusOK == true) {
                        deviceLiveData.value?.let {
                            it.activated = true
                            it.owner = address
                            AppDatabase.getInstance(Utils.getApp()).deviceDao().update(it)
                            deviceLiveData.postValue(it)
                        }
                        ToastUtils.showShort(R.string.success)
                    }
                }
            }
        }
    }

    fun retrievePubKey(imei: String) {
        addLaunch(true) {
            val data = "Retrieve device $imei"
            val signature = signPebbleMessage(data)
            val newPubKey = KeystoreUtil.getPubKey()
            val requestWrapper = RequestWrapper(signature, RetrievePubKeyBody(imei, newPubKey))
            val responseWrapper = geoApiService.retrieve(requestWrapper)
            if (responseWrapper.result?.data == true) {
                val device = deviceLiveData.value
                device?.let {
                    device.activated = true
                    device.owner = address
                    AppDatabase.getInstance(Utils.getApp()).deviceDao().update(device)
                    deviceLiveData.postValue(device)
                }
                ToastUtils.showShort(R.string.success)
            } else {
                ToastUtils.showShort(R.string.failure)
            }
        }
    }

    private suspend fun signPebbleMessage(data: String): String {
        return suspendCoroutine { continuation ->
            val requestId = System.currentTimeMillis()
            Web3Repository.handleSignMessage(
                requestId,
                data.toByteArray(),
                true,
                null,
                "MetaPebble",
                "https://iotexproject.iotex.io/ecosystem/main/img/Pebble.png"
            )
            { responseId, result, error ->
                if (responseId == requestId && !result.isNullOrBlank()) {
                    continuation.resumeWith(Result.success(result))
                } else {
                    continuation.resumeWith(Result.failure(Throwable(error?.message?: "Sign failure")))
                }
            }
        }
    }

}