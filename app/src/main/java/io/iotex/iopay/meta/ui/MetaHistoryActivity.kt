package io.iotex.iopay.meta.ui

import android.view.View
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.LinearLayoutManager
import com.drakeet.multitype.MultiTypeAdapter
import io.iotex.base.bindbase.BaseBindActivity
import io.iotex.iopay.R
import io.iotex.iopay.base.BaseBindToolbarActivity
import io.iotex.iopay.databinding.ActivityMetaHistoryBinding
import io.iotex.iopay.meta.item.RecordItemBinder
import io.iotex.iopay.meta.bean.RecordEntry

/**
 * meta upload data history
 */
class MetaHistoryActivity : BaseBindToolbarActivity<MetaHistoryViewModel,
    ActivityMetaHistoryBinding>(R.layout.activity_meta_history) {

    private val mAdapter = MultiTypeAdapter()

    val success by lazy {
        intent.getStringExtra("success")?:""
    }

    override fun initView() {
        setToolbarTitle(getString(R.string.history))

        val recordBinder = RecordItemBinder(success)
        mAdapter.register(RecordEntry::class.java, recordBinder)
        mBinding.mRvContent.adapter = mAdapter
        mBinding.mRvContent.layoutManager = LinearLayoutManager(this)

        mBinding.mSrfRefresh.setOnRefreshListener {
            mViewModel.page = 1
            mViewModel.queryRecordByImei(mViewModel.deviceLiveData.value?.imei,success)
        }
        mBinding.mSrfRefresh.setOnLoadMoreListener {
            mViewModel.page = mViewModel.page + 1
            mViewModel.queryRecordByImei(mViewModel.deviceLiveData.value?.imei,success)
        }
    }

    override fun initEvent() {
        mViewModel.deviceLiveData.observe(this, Observer {
            if (it == null) {
                mBinding.mRvContent.visibility = View.GONE
                mBinding.mLlEmpty.visibility = View.VISIBLE
            } else {
                mViewModel.queryRecordByImei(it.imei,success)
            }
        })
        mViewModel.recordLiveData.observe(this, Observer {
            mBinding.mSrfRefresh.finishRefresh()
            mBinding.mSrfRefresh.finishLoadMore()
            if (it.size < mViewModel.pageSize) {
                mBinding.mSrfRefresh.setEnableLoadMore(false)
            } else {
                mBinding.mSrfRefresh.setEnableLoadMore(true)
            }
            if (mViewModel.page <= 1) {
                mAdapter.items = it
                mAdapter.notifyDataSetChanged()
            } else {
                val list = mAdapter.items.toMutableList()
                list.addAll(it)
                mAdapter.items = list
                mAdapter.notifyItemRangeInserted(list.size - it.size, it.size)
            }
            if (mAdapter.itemCount <= 0) {
                mBinding.mRvContent.visibility = View.GONE
                mBinding.mLlEmpty.visibility = View.VISIBLE
            } else {
                mBinding.mRvContent.visibility = View.VISIBLE
                mBinding.mLlEmpty.visibility = View.GONE
            }
        })
    }
}