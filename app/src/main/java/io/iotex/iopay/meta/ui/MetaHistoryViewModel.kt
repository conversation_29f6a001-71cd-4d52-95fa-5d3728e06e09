package io.iotex.iopay.meta.ui

import android.app.Application
import android.content.Context
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.MutableLiveData
import io.iotex.iopay.base.BaseLaunchVM
import io.iotex.iopay.data.db.AppDatabase
import io.iotex.iopay.meta.bean.DeviceEntry
import io.iotex.iopay.meta.bean.RecordEntry

/**
 * meta history
 */
class MetaHistoryViewModel(application: Application) : BaseLaunchVM(application) {

    var page = 1
    val pageSize = 20
    val deviceLiveData = MutableLiveData<DeviceEntry?>()//register device
    val recordLiveData = MutableLiveData<List<RecordEntry>>()//history

    override fun onCreate(owner: LifecycleOwner) {
        super.onCreate(owner)
        page = 1
        queryDevice()
    }

    /**
     * history list dao
     */
    fun queryRecordByImei(imei: String?,success:String) {
        if (imei == null) return
        if(success=="1"){
            addLaunch {
                val record = AppDatabase.getInstance(iView as Context).recordDao()
                    .queryByImei(imei, page, pageSize)
                recordLiveData.postValue(record)
            }
        }else{
            addLaunch {
                val record = AppDatabase.getInstance(iView as Context).recordDao()
                    .queryErrorByImei(imei, page, pageSize)
                recordLiveData.postValue(record)
            }
        }

    }


    /**
     * register device dao
     */
    private fun queryDevice() {
        addLaunch {
            val device = AppDatabase.getInstance(iView as Context).deviceDao()
                .queryDevice()
            deviceLiveData.postValue(device)
        }
    }
}