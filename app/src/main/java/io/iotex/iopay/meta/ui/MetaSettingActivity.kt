package io.iotex.iopay.meta.ui

import com.blankj.utilcode.util.SPUtils
import io.iotex.base.BaseViewModel
import io.iotex.base.bindbase.BaseBindActivity
import io.iotex.iopay.R
import io.iotex.iopay.base.BaseBindToolbarActivity
import io.iotex.iopay.databinding.ActivityMetaSettingBinding
import io.iotex.iopay.meta.*
import io.iotex.iopay.ui.widget.PickerDialog
import io.iotex.iopay.ui.widget.PickerItemData

/**
 * meta setting location
 */
class MetaSettingActivity : BaseBindToolbarActivity<BaseViewModel,
    ActivityMetaSettingBinding>(R.layout.activity_meta_setting) {

    private val mSubmitFrequencyList = listOf(
        PickerItemData("1 min", 1),
        PickerItemData("2 mins", 2),
        PickerItemData("3 mins", 3),
        PickerItemData("4 mins", 4),
        PickerItemData("5 mins", 5),
        PickerItemData("6 mins", 6),
        PickerItemData("7 mins", 7),
        PickerItemData("8 mins", 8),
        PickerItemData("9 mins", 9),
        PickerItemData("10 mins", 10)
    )

    private val mGpsPrecisionList = listOf(
        PickerItemData("Fine (~100M)", 100),
        PickerItemData("Medium (~1KM)", 1000),
        PickerItemData("Coarse (~10KM)", 10000)
    )

    override fun initView() {
        setToolbarTitle(getString(R.string.meta_setting))

        //location
        mBinding.mSbGps.isChecked = SPUtils.getInstance().getBoolean(SP_KEY_GPS_CHECKED, true)
        mBinding.mSbGps.setOnCheckedChangeListener { _, isChecked ->
            SPUtils.getInstance().put(SP_KEY_GPS_CHECKED, isChecked)
        }

        //Frequency
        getFrequencyCurrentItem()?.also {
            mBinding.mTvFrequency.text = it.label
        }
        mBinding.mRlFrequency.setOnClickListener {
            val picker = PickerDialog(this, mSubmitFrequencyList)

            val curItem = getFrequencyCurrentItem()
            if (curItem != null) {
                picker.setCurrentItem(curItem)
            }
            picker.setTitle(getString(R.string.meta_gps_interval))
                .setPositiveButton(getString(R.string.confirm)) {
                    mBinding.mTvFrequency.text = it.label
                    SPUtils.getInstance().put(SP_KEY_SUBMIT_FREQUENCY, it.value)
                }
                .show()
        }

        //GpsPrecision
        getPrecisionCurrentItem()?.let {
            mBinding.mTvGpsPrecision.text = it.label
        }
        mBinding.mRlGpsPrecision.setOnClickListener {
            val picker = PickerDialog(this, mGpsPrecisionList)

            val curItem = getPrecisionCurrentItem()
            if (curItem != null) {
                picker.setCurrentItem(curItem)
            }
            picker.setTitle(getString(R.string.meta_gps_precision))
                .setPositiveButton(getString(R.string.confirm)) {
                    mBinding.mTvGpsPrecision.text = it.label
                    SPUtils.getInstance().put(SP_KEY_GPS_PRECISION, it.value)
                }
                .show()
        }
    }

    /**
     * picker current item
     */
    private fun getFrequencyCurrentItem(): PickerItemData? {
        return mSubmitFrequencyList.firstOrNull {
            it.value == SPUtils.getInstance().getInt(SP_KEY_SUBMIT_FREQUENCY, INTERVAL_SEND_DATA)
        }
    }

    /**
     * picker current item
     */
    private fun getPrecisionCurrentItem(): PickerItemData? {
        return mGpsPrecisionList.firstOrNull {
            it.value == SPUtils.getInstance().getInt(SP_KEY_GPS_PRECISION, GPS_PRECISION)
        }
    }
}