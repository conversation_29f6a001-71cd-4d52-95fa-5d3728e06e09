package io.iotex.iopay.meta.ui.dialog

import io.iotex.base.BaseViewModel
import io.iotex.base.bindbase.BaseBindDialog
import io.iotex.iopay.R
import io.iotex.iopay.databinding.DialogGeoLocationErrorBinding

class GeoLocationErrorDialog:BaseBindDialog<BaseViewModel,DialogGeoLocationErrorBinding>(R.layout.dialog_geo_location_error) {

    override fun initView() {

        mBinding.ivClose.setOnClickListener {
            dismiss()
        }

        mBinding.tvGetIt.setOnClickListener {
            dismiss()
        }
    }
}