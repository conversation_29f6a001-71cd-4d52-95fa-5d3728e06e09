package io.iotex.iopay.meta.ui.dialog

import android.content.Intent
import android.net.Uri
import android.provider.Settings
import io.iotex.base.BaseViewModel
import io.iotex.base.bindbase.BaseBindDialog
import io.iotex.iopay.IoPayApplication
import io.iotex.iopay.R
import io.iotex.iopay.databinding.DialogGeoLocationTipsBinding

class GeoLocationTipsDialog :
    BaseBindDialog<BaseViewModel, DialogGeoLocationTipsBinding>(R.layout.dialog_geo_location_tips) {

    override fun initView() {

        mBinding.ivClose.setOnClickListener {
            dismiss()
        }

        mBinding.tvGetIt.setOnClickListener {
            dismiss()
            val intent = Intent()
            intent.action = Settings.ACTION_APPLICATION_DETAILS_SETTINGS
            intent.data = Uri.fromParts("package", IoPayApplication.getInstance().packageName, null)
            startActivity(intent)
        }
    }
}