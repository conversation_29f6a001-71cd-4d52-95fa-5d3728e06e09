package io.iotex.iopay.meta.ui.dialog

import io.iotex.base.BaseViewModel
import io.iotex.base.bindbase.BaseBindDialog
import io.iotex.iopay.R
import io.iotex.iopay.databinding.DialogMetaWalletBinding

class MetaWalletDialog @JvmOverloads constructor(val wallet:String? = null):BaseBindDialog<BaseViewModel,DialogMetaWalletBinding>(R.layout.dialog_meta_wallet) {

    override fun initView() {

        mBinding.tvAddress.text = wallet

        mBinding.ivClose.setOnClickListener {
            dismiss()
        }

        mBinding.tvGetIt.setOnClickListener {
            dismiss()
        }
    }
}