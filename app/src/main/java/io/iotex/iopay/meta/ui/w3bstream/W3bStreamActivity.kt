package io.iotex.iopay.meta.ui.w3bstream

import android.view.View
import com.blankj.utilcode.util.ColorUtils
import com.blankj.utilcode.util.SPUtils
import com.blankj.utilcode.util.ToastUtils
import io.iotex.base.bindbase.BaseBindActivity
import io.iotex.iopay.R
import io.iotex.iopay.base.BaseBindToolbarActivity
import io.iotex.iopay.databinding.ActivityW3bStreamBinding
import io.iotex.iopay.meta.SP_KEY_GEO_LOCATION_ERROR
import io.iotex.iopay.meta.SP_KEY_META_SWITCH
import io.iotex.iopay.meta.ui.GeoCreateActivity
import io.iotex.iopay.meta.ui.GeoHomeActivity
import io.iotex.iopay.meta.ui.GeoRegisterActivity
import io.iotex.iopay.util.*
import org.jetbrains.anko.startActivity

class W3bStreamActivity : BaseBindToolbarActivity<W3bStreamViewModel,
    ActivityW3bStreamBinding>(R.layout.activity_w3b_stream) {

    override fun initView() {
        setToolbarTitle(getString(R.string.setting_w3bstream))

        mBinding.llMetaPebble.setOnClickListener {
            if (WalletHelper.isIoTexNetWork()) {
                mViewModel.getGeoDevice()
            } else {
                ToastUtils.showShort(getString(R.string.supported_main_network))
            }

            FireBaseUtil.logFireBase(FireBaseEvent.W3BSTREAM_ENTER)
        }
    }

    override fun initData() {
        mViewModel.deviceLiveData.observe(this){
            if (it != null) {
                if(it.activated){
                    startActivity<GeoHomeActivity>()
                    FireBaseUtil.logFireBase(FireBaseEvent.W3BSTREAM_ACTIVATED)
                }else{
                    startActivity<GeoRegisterActivity>()
                }
            } else {
                startActivity<GeoCreateActivity>()
            }
        }

    }

    override fun onResume() {
        super.onResume()
        val meta = SPUtils.getInstance().getBoolean(SP_KEY_META_SWITCH, false)
        if (meta) {
            mBinding.tvMeta.text = "ON"
            mBinding.tvMeta.setTextColor(ColorUtils.getColor(R.color.color_617AFF))
        } else {
            mBinding.tvMeta.text = "OFF"
            mBinding.tvMeta.setTextColor(ColorUtils.getColor(R.color.gray_B4B8CB))
        }

        val error = SPUtils.getInstance().getBoolean(SP_KEY_GEO_LOCATION_ERROR)
        if(error){
            mBinding.vError.visibility = View.VISIBLE
        }else{
            mBinding.vError.visibility = View.GONE
        }
    }
}