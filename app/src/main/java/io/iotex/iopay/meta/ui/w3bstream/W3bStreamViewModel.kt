package io.iotex.iopay.meta.ui.w3bstream

import android.app.Application
import android.content.Context
import androidx.lifecycle.MutableLiveData
import io.iotex.iopay.base.BaseLaunchVM
import io.iotex.iopay.data.db.AppDatabase
import io.iotex.iopay.meta.bean.DeviceEntry

class W3bStreamViewModel(application: Application) : BaseLaunchVM(application) {

    val deviceLiveData = MutableLiveData<DeviceEntry?>()

    fun getGeoDevice() {
        addLaunch {
            val queryDevice = AppDatabase.getInstance(iView as Context).deviceDao()
                .queryDevice()
            deviceLiveData.postValue(queryDevice)
        }
    }
}