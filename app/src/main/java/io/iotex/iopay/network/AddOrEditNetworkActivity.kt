package io.iotex.iopay.network

import android.content.Context
import android.content.Intent
import androidx.lifecycle.lifecycleScope
import com.blankj.utilcode.util.RegexUtils
import com.blankj.utilcode.util.Utils
import io.iotex.iopay.R
import io.iotex.iopay.base.BaseBindToolbarActivity
import io.iotex.iopay.databinding.ActivityAddEditNetworkBinding
import io.iotex.iopay.network.viewmodel.AddOrEditNetworkViewModel
import io.iotex.iopay.util.FireBaseUtil
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.util.extension.isNumeric
import io.iotex.iopay.util.extension.setGone
import io.iotex.iopay.util.extension.trimSpace
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.util.Locale

class AddOrEditNetworkActivity : BaseBindToolbarActivity<AddOrEditNetworkViewModel,ActivityAddEditNetworkBinding>(R.layout.activity_add_edit_network) {

    companion object{
        const val ACTION_ADD_NETWORK = 1
        const val ACTION_EDIT_NETWORK = 2
        fun startActivity(context:Context,action:Int){
            val intent = Intent(context,AddOrEditNetworkActivity::class.java)
            intent.putExtra("action",action)
            context.startActivity(intent)
        }
    }

    private val mAction by lazy {
        intent?.getIntExtra("action", ACTION_ADD_NETWORK) ?: ACTION_ADD_NETWORK
    }

    override fun initView() {
        setToolbarSubmitText(getString(R.string.confirm))
        setToolbarSubmitClick {
            if (mAction == ACTION_ADD_NETWORK) {
                addNetwork()
            } else {
                editNetwork()
            }
        }

        if (mAction == ACTION_ADD_NETWORK) {
            initAddNetwork()
        } else {
            renderNetworkUI()
        }

        mBinding.mEtRpcUrl.editText().requestFocus()
    }

    override fun initEvent() {
        mViewModel.completeLiveData.observe(this){
            finish()
        }

        mViewModel.curNetworkLiveData.observe(this){ network->
            mBinding.mEtNetworkName.setText(network.networkName)
            mBinding.tvChainId.text = network.chainId.toString()
            mBinding.mEtCurrencySymbol.setText(network.currencySymbol)
            mBinding.mEtExplorerUrl.setText(network.explorer)
        }

        mViewModel.curNetworkNoteLiveData.observe(this){
            var nodeString = ""
            it?.forEach {node->
                if(nodeString.isNotEmpty()){
                    nodeString += "\n"
                }
                nodeString += node.rpcUrl
            }
            mBinding.tvRpc.text = nodeString
        }
    }

    private fun initAddNetwork() {
        setToolbarTitle(getString(R.string.add_custom_network))
        mBinding.mTvDelete.setGone()
        mBinding.llRpc.setGone()
        mBinding.mEtChainId.isInputEnable = false
        mBinding.mEtRpcUrl.addTextChangedListener {
            mViewModel.queryNodeStateByRpc(it.trimSpace())
        }

        mBinding.mEtChainId.addTextChangedListener {
            mViewModel.isValidChainId(it.toIntOrNull())
        }

        mViewModel.isValidChainIdLiveData.observe(this){
            isValidChainId()
        }

        mViewModel.noteLiveData.observe(this){
            if (it.chainId != -1){
                mBinding.mEtChainId.setText(it.chainId.toString())
                mBinding.mEtNetworkName.setText(it.name)
                mViewModel.querySymbol(it.chainId)
                mBinding.mEtRpcUrl.notifyNormal()
            } else {
                mBinding.mEtRpcUrl.notifyError(getString(R.string.rpc_url_error))
            }
        }

        mViewModel.noteSymbolLiveData.observe(this){
            mBinding.mEtCurrencySymbol.setText(it)
        }
    }

    private fun renderNetworkUI() {
        setToolbarTitle(getString(R.string.edit_custom_network))
        mBinding.llEditRpc.setGone()
        mBinding.mEtRpcUrl.isInputEnable = false
        mBinding.mTvDelete.setOnClickListener {
            mViewModel.deleteNetWork()
            FireBaseUtil.logFireBase("action_customized_network_edit_delete")
        }
        mViewModel.getCurNetwork()
        mViewModel.getCurNetworkNode()
    }

    private fun addNetwork() {
        if (!isValidNetworkName()) return
        if (!isValidRpcUrl()) return
        if (!isValidChainId()) return
        if (!isValidCurrencySymbol()) return

        val chainId = mBinding.mEtChainId.text().trim().toInt()
        val name = mBinding.mEtNetworkName.text().trim()
        val rpcUrl = RegexUtils.getReplaceAll(mBinding.mEtRpcUrl.text().trim(), "/*$", "")
            .lowercase(Locale.getDefault())
        val explorerUrl = mBinding.mEtExplorerUrl.text().trim()
        val symbol = mBinding.mEtCurrencySymbol.text().trim()
        mViewModel.addNetwork(chainId,name,rpcUrl,explorerUrl, symbol)
    }

    private fun editNetwork() {
        lifecycleScope.launch {
            if (!isValidNetworkName()) return@launch
            if (!isValidCurrencySymbol()) return@launch
            val network = withContext(Dispatchers.IO) {
                WalletHelper.getCurNetwork()
            } ?: return@launch
            network.networkName = mBinding.mEtNetworkName.text().trim()
            network.name = mBinding.mEtNetworkName.text().trim()
            network.shortName = mBinding.mEtNetworkName.text().trim()
            network.currencySymbol = mBinding.mEtCurrencySymbol.text().trim()
            network.explorer = mBinding.mEtExplorerUrl.text().trim()
            mViewModel.editNetwork(network)
        }
    }

    private fun isValidNetworkName(): Boolean {
        if (mBinding.mEtNetworkName.text().trim().isBlank()) {
            mBinding.mEtNetworkName.notifyError(getString(R.string.network_name_error))
            return false
        }
        mBinding.mEtNetworkName.notifyNormal()
        return true
    }

    private fun isValidCurrencySymbol(): Boolean {
        if (mBinding.mEtCurrencySymbol.text().trim().isBlank()) {
            mBinding.mEtCurrencySymbol.notifyError(getString(R.string.currency_symbol_error))
            return false
        }
        mBinding.mEtCurrencySymbol.notifyNormal()
        return true
    }

    private fun isValidChainId(): Boolean {
        val chainId = mBinding.mEtChainId.text().trim()
        if (!chainId.isNumeric()) {
            mBinding.mEtChainId.notifyError(getString(R.string.chain_id_error))
            return false
        }

        if(mViewModel.isValidChainIdLiveData.value == false){
            mBinding.mEtChainId.notifyError(getString(R.string.network_exists_error))
            return false
        }

        mBinding.mEtChainId.notifyNormal()
        return true
    }

    private fun isValidRpcUrl(): Boolean {
        val rpcUrl = mBinding.mEtRpcUrl.text().trim().lowercase(Locale.getDefault())
        if (!RegexUtils.isURL(rpcUrl)) {
            mBinding.mEtRpcUrl.notifyError(Utils.getApp().getString(R.string.rpc_url_error))
            return false
        }

        if(mViewModel.noteLiveData.value?.chainId == -1){
            mBinding.mEtRpcUrl.notifyError(getString(R.string.rpc_url_error))
            return false
        }
        mBinding.mEtRpcUrl.notifyNormal()
        return true
    }

}