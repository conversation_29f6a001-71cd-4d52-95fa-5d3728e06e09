package io.iotex.iopay.network

import android.os.Bundle
import androidx.recyclerview.widget.LinearLayoutManager
import com.blankj.utilcode.util.SPUtils
import com.drakeet.multitype.MultiTypeAdapter
import io.iotex.base.bindbase.BaseBindActivity
import io.iotex.iopay.MainActivity
import io.iotex.iopay.R
import io.iotex.iopay.base.BaseBindToolbarActivity
import io.iotex.iopay.databinding.ActivityFirstSwitchNetworkBinding
import io.iotex.iopay.network.item.NetworkSwitchBinder
import io.iotex.iopay.network.viewmodel.NetworkSwitchViewModel
import io.iotex.iopay.util.FireBaseEvent
import io.iotex.iopay.util.FireBaseUtil
import io.iotex.iopay.util.SPConstant

class FirstNetworkSwitchActivity :
    BaseBindToolbarActivity<NetworkSwitchViewModel, ActivityFirstSwitchNetworkBinding>(R.layout.activity_first_switch_network) {

    private val adapter = MultiTypeAdapter()

    override fun initView() {
        hideToolbarBack()
        setToolbarTitle(getString(R.string.network))
        SPUtils.getInstance().put(SPConstant.SP_FIRST_SWITCH_NET, false)

        mBinding.recyclerView.layoutManager = LinearLayoutManager(this)
        adapter.register(NetworkSwitchBinder().apply {
            itemClick = {
                val bundle = Bundle()
                bundle.putString("name", it.shortName)
                bundle.putString("selectedChainId", it.chainId.toString())
                FireBaseUtil.logFireBase(FireBaseEvent.ACTION_START_CREATE_WALLET_VIEW_MAINNET, bundle)
            }
        })
        mBinding.recyclerView.adapter = adapter

        mBinding.enterWallet.setOnClickListener {
            MainActivity.startActivity(this@FirstNetworkSwitchActivity)
            FireBaseUtil.logFireBase(
                "action_select_mainnet"
            )
            finish()
        }

        mBinding.refreshLayout.setOnRefreshListener {
            mViewModel.fetchNetworkList()
            mViewModel.loadMainNetwork()
        }
    }

    override fun initData() {
        mViewModel.loadMainNetwork()
    }

    override fun initEvent() {
        mViewModel.networkLiveData.observe(this) {
            mBinding.refreshLayout.finishRefresh()
            adapter.items = it
            adapter.notifyDataSetChanged()
        }
    }

    override fun onBackPressed() {
    }

}