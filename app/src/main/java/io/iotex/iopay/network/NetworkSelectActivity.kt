package io.iotex.iopay.network

import androidx.core.widget.addTextChangedListener
import androidx.recyclerview.widget.LinearLayoutManager
import com.blankj.utilcode.util.SPUtils
import com.blankj.utilcode.util.ToastUtils
import com.drakeet.multitype.MultiTypeAdapter
import io.iotex.iopay.R
import io.iotex.iopay.base.BaseBindToolbarActivity
import io.iotex.iopay.data.db.NetworkChain
import io.iotex.iopay.databinding.ActivityNetworkSelectBinding
import io.iotex.iopay.util.FireBaseUtil
import io.iotex.iopay.util.SPConstant.SP_FIRST_SELECT_CUSTOM_NETWORK
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.network.dialog.NetworkSelectDialog
import io.iotex.iopay.network.item.NetworkChainBinder
import io.iotex.iopay.network.viewmodel.NetworkSelectViewModel
import io.iotex.iopay.util.FireBaseEvent
import io.iotex.iopay.util.PageEventUtil

class NetworkSelectActivity :
    BaseBindToolbarActivity<NetworkSelectViewModel, ActivityNetworkSelectBinding>(R.layout.activity_network_select) {

    private val mAdapter = MultiTypeAdapter()

    override fun initView() {
        setToolbarTitle(getString(R.string.add_network))
        setToolbarSubmitText(getString(R.string.custom))

        setToolbarSubmitClick {
            AddOrEditNetworkActivity.startActivity(this, AddOrEditNetworkActivity.ACTION_ADD_NETWORK)
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_ADD_CUSTOM_NETWORK)
            PageEventUtil.logEvent(PageEventUtil.CUSTOMNETWORK)
        }


        mBinding.recyclerView.layoutManager = LinearLayoutManager(this)
        val binder = NetworkChainBinder().apply {
            itemClick = {
                mViewModel.getRpcStatus(it)
            }
            check = WalletHelper.getCurChainId().toString()
        }
        mAdapter.register(NetworkChain::class.java, binder)
        mBinding.recyclerView.adapter = mAdapter

        mBinding.etSearch.addTextChangedListener {
            mViewModel.getChainList(it.toString())
        }

        if (SPUtils.getInstance().getBoolean(SP_FIRST_SELECT_CUSTOM_NETWORK, true)) {
            SPUtils.getInstance().put(SP_FIRST_SELECT_CUSTOM_NETWORK, false)
            NetworkSelectDialog().show(
                supportFragmentManager,
                System.currentTimeMillis().toString()
            )
        }

    }

    override fun initData() {
        mViewModel.chanListLiveData.observe(this) {
            if (it != null) {
                mAdapter.items = it
                mAdapter.notifyDataSetChanged()
            }
        }

        mViewModel.rpcAvail.observe(this) {
            if (it == null) {
                ToastUtils.showShort(R.string.rpc_url_error)
            } else {
                finish()
            }
        }
    }
}