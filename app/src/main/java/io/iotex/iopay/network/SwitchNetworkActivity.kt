package io.iotex.iopay.network

import androidx.core.view.isVisible
import androidx.lifecycle.lifecycleScope
import com.blankj.utilcode.util.SPUtils
import com.blankj.utilcode.util.Utils
import com.drakeet.multitype.MultiTypeAdapter
import io.iotex.iopay.R
import io.iotex.iopay.base.BaseBindToolbarActivity
import io.iotex.iopay.data.UserStore
import io.iotex.iopay.data.db.AppDatabase
import io.iotex.iopay.data.db.RPCNetwork
import io.iotex.iopay.databinding.ActivitySwitchNetworkBinding
import io.iotex.iopay.ui.binder.NetworkListBinder
import io.iotex.iopay.util.SPConstant
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.util.extension.setVisible
import io.iotex.iopay.setting.GeneralActivity
import io.iotex.iopay.support.eventbus.RefreshNetworkListEvent
import io.iotex.iopay.util.Config
import io.iotex.iopay.util.Constant
import io.iotex.iopay.util.PageEventUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.jetbrains.anko.startActivity

class SwitchNetworkActivity :
    BaseBindToolbarActivity<Nothing, ActivitySwitchNetworkBinding>(R.layout.activity_switch_network) {

    private val mAdapter = MultiTypeAdapter()

    private fun setupActionBar() {
        setToolbarTitle(getString(R.string.network))
        setToolbarSubmitImage(R.drawable.icon_add_blue)
        setToolbarSubmitClick {
            startActivity<NetworkSelectActivity>()
            PageEventUtil.logEvent(PageEventUtil.ADDNETWORK)
        }
    }

    override fun onResume() {
        super.onResume()
        initData()
    }

    override fun initView() {
        setupActionBar()
        setupRecycleView()
        val devMode = SPUtils.getInstance().getBoolean(SPConstant.SP_DEVELOPER_MODE, false)
        if (!devMode) {
            mBinding.tvDevelop.setVisible()
            mBinding.tvDevelop.setOnClickListener {
                startActivity<GeneralActivity>()
            }
        }
        mBinding.llAllNetwork.isVisible = UserStore.getAllNetwork()
    }

    private fun setupRecycleView() {
        val binder = NetworkListBinder().apply {
            mItemClickCallback = {
                finish()
            }
            mItemRemoveListener = {
                deleteNetwork(it)
            }
        }
        mAdapter.register(RPCNetwork::class.java, binder)
        mBinding.rvNetwork.adapter = mAdapter
    }

    private fun deleteNetwork(network: RPCNetwork) {
        if (!network.immutable) {
            lifecycleScope.launch {
                withContext(Dispatchers.IO) {
                    if (UserStore.getChainId() == network.chainId) {
                        AppDatabase.getInstance(Utils.getApp()).rpcNetworkDao().queryRPCNetworkByChainId(Config.IOTEX_CHAIN_ID)?.let {
                            WalletHelper.switchNetwork(it)
                        }
                    }
                    AppDatabase.getInstance(Utils.getApp()).rpcNetworkDao().delete(network)
                    EventBus.getDefault().post(RefreshNetworkListEvent())
                    initData()
                }
            }
        }
    }

    private suspend fun loadData() {
        val list = withContext(Dispatchers.IO) {
            val devMode = SPUtils.getInstance().getBoolean(SPConstant.SP_DEVELOPER_MODE, false)
            val list = if (devMode) {
                AppDatabase.getInstance(Utils.getApp()).rpcNetworkDao().queryAllRPCNetwork()
            } else {
                AppDatabase.getInstance(Utils.getApp()).rpcNetworkDao().queryMainNetwork()
            }
            val defList = list.filter { it.immutable }
            val customList = list.filter { !it.immutable } as ArrayList

            val networks =mutableListOf<RPCNetwork>().apply {
                addAll(defList)
                addAll(customList)
            }.also {
                val current = WalletHelper.getCurNetwork() ?: return@also
                it.remove(current)
                it.add(0, current)
            }
            filterAANetworks(networks).filter {
                val wallet = Constant.currentWallet ?: return@filter true
                if (wallet.isSolanaPrivateWallet() || wallet.isSolanaWatchWallet()) {
                    WalletHelper.isSolanaNetwork(it.chainId)
                } else if (wallet.isBitcoinWatchWallet()) {
                    WalletHelper.isBitcoinNetwork(it.chainId)
                } else if (wallet.isEvmWatchWallet()) {
                    !WalletHelper.isSolanaNetwork(it.chainId) && !WalletHelper.isBitcoinNetwork(it.chainId)
                } else if (wallet.isEvmPrivateWallet()) {
                    !WalletHelper.isSolanaNetwork(it.chainId)
                } else {
                    true
                }
            }
        }

        mAdapter.items = list
        mAdapter.notifyDataSetChanged()
    }

    private fun filterAANetworks(networks: List<RPCNetwork>): List<RPCNetwork> {
        val wallet = WalletHelper.getCurWallet()
        if (wallet?.isAAWallet() == true) {
            val aaNetworkList = AppDatabase.getInstance(Utils.getApp()).networkAAConfigDao().queryAll()
            return networks.filter { network ->
                aaNetworkList.firstOrNull { config ->
                    config.chainId == network.chainId
                } != null
            }
        }
        return networks
    }

    override fun initData() {
        lifecycleScope.launch {
            loadData()
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onRefreshNetworkListEvent(event: RefreshNetworkListEvent) {
        lifecycleScope.launch {
            loadData()
        }
    }

}