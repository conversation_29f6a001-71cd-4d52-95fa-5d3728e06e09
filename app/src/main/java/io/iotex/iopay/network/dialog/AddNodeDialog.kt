package io.iotex.iopay.network.dialog

import android.view.Gravity
import android.view.WindowManager
import com.blankj.utilcode.util.RegexUtils
import com.blankj.utilcode.util.Utils
import io.iotex.base.bindbase.BaseBindDialog
import io.iotex.iopay.R
import io.iotex.iopay.data.db.RPCNetworkNode
import io.iotex.iopay.databinding.DialogAddNodeBinding
import io.iotex.iopay.network.viewmodel.NetWorkNodeViewModel
import io.iotex.iopay.support.eventbus.RefreshRpcNodeListEvent
import io.iotex.iopay.util.FireBaseUtil
import io.iotex.iopay.util.extension.trimSpace
import org.greenrobot.eventbus.EventBus
import java.util.*

class AddNodeDialog(val node: RPCNetworkNode? = null): BaseBindDialog<NetWorkNodeViewModel,DialogAddNodeBinding>(R.layout.dialog_add_node) {

    override fun initView() {

        mBinding.mBtnCancel.setOnClickListener {
            dismiss()
            FireBaseUtil.logFireBase("action_custom_rpc_confirmation_cancel")
        }
        mBinding.mBtnConfirm.setOnClickListener {
            verifyRpcUrl()
            FireBaseUtil.logFireBase("action_custom_rpc_confirmation_confirm")
        }
    }

    override fun initData() {
        mViewModel.getCurNetwork()
        mViewModel.curNetworkLiveData.observe(this){
            mBinding.mTvNetworkName.text = it.name
            mBinding.mTvChainId.text = it.chainId.toString()
        }
        mBinding.mEtRpcUrl.setText(node?.rpcUrl)
        mViewModel.noteLiveData.observe(this){
            if(it == null){
                mBinding.mEtRpcUrl.notifyError(Utils.getApp().getString(R.string.rpc_url_exists_error))
                return@observe
            }
            if (it.chainId == -1) {
                mBinding.mEtRpcUrl.notifyError(Utils.getApp().getString(R.string.rpc_url_error))
                return@observe
            }
            if (mViewModel.curNetworkLiveData.value?.chainId != it.chainId) {
                mBinding.mEtRpcUrl.notifyError(Utils.getApp().getString(R.string.network_node_error))
                return@observe
            }
            if (node != null) {
                node.rpcUrl = it.rpcUrl
                mViewModel.insertNetWorkNode(node)
            } else {
                mViewModel.insertNetWorkNode(it)
            }
        }

        mViewModel.noteInsetLiveData.observe(this){
            EventBus.getDefault().post(RefreshRpcNodeListEvent())
            dismiss()
        }
    }

    private fun verifyRpcUrl() {
        val rpcUrl = RegexUtils.getReplaceAll(mBinding.mEtRpcUrl.text().trim(), "/*$", "")
            .lowercase(Locale.getDefault()).trimSpace()
        if (!RegexUtils.isURL(rpcUrl)) {
            mBinding.mEtRpcUrl.notifyError(Utils.getApp().getString(R.string.rpc_url_error))
            return
        }
        mViewModel.queryNodeStateByRpc(rpcUrl)
    }

    override fun onStart() {
        super.onStart()
        val attributes = dialog?.window?.attributes
        attributes?.width = WindowManager.LayoutParams.MATCH_PARENT
        attributes?.gravity = Gravity.BOTTOM
        dialog?.window?.attributes = attributes
    }
}