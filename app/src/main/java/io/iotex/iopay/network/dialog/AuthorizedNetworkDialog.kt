package io.iotex.iopay.network.dialog

import android.content.res.Configuration
import android.view.Gravity
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import com.blankj.utilcode.util.ScreenUtils
import com.drakeet.multitype.MultiTypeAdapter
import io.iotex.base.bindbase.BaseBindDialog
import io.iotex.iopay.R
import io.iotex.iopay.data.db.RPCNetwork
import io.iotex.iopay.databinding.DialogAuthorizedNetworkBinding
import io.iotex.iopay.network.item.AuthorizedNetworkBinder
import io.iotex.iopay.network.viewmodel.NetworkSwitchViewModel

class AuthorizedNetworkDialog(
    private val items: List<RPCNetwork>,
) :
    BaseBindDialog<NetworkSwitchViewModel, DialogAuthorizedNetworkBinding>(R.layout.dialog_authorized_network) {

    var onItemClick: ((RPCNetwork) -> Unit)? = null

    private val adapter = MultiTypeAdapter()

    override fun initView() {
        mBinding.recyclerView.layoutManager = GridLayoutManager(context,5)
        adapter.register(AuthorizedNetworkBinder())
        adapter.items = items
        mBinding.recyclerView.adapter = adapter
    }

    override fun onStart() {
        super.onStart()
        val dialogWindow = dialog?.window
        val lp = dialogWindow?.attributes
        dialogWindow?.setGravity(Gravity.BOTTOM)
        val orientation = resources.configuration.orientation
        if (orientation == Configuration.ORIENTATION_LANDSCAPE) {
            lp?.width = ScreenUtils.getScreenWidth()
            lp?.height = ScreenUtils.getScreenWidth()
        } else {
            lp?.width = ScreenUtils.getScreenWidth()
            lp?.height = (ScreenUtils.getScreenHeight() * 0.6).toInt()
        }
        dialogWindow?.attributes = lp
    }
}