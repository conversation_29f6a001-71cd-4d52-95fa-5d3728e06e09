package io.iotex.iopay.network.dialog

import io.iotex.base.BaseViewModel
import io.iotex.base.bindbase.BaseBindDialog
import io.iotex.iopay.R
import io.iotex.iopay.databinding.DialogNetworkSelectBinding

class NetworkSelectDialog :
    BaseBindDialog<BaseViewModel, DialogNetworkSelectBinding>(R.layout.dialog_network_select) {
    override fun initView() {

        mBinding.ivClose.setOnClickListener {
            dismiss()
        }

        mBinding.tvGetIt.setOnClickListener {
            dismiss()
        }
    }
}