package io.iotex.iopay.network.dialog

import android.content.res.Configuration
import android.view.Gravity
import androidx.recyclerview.widget.LinearLayoutManager
import com.blankj.utilcode.util.ScreenUtils
import com.blankj.utilcode.util.Utils
import com.drakeet.multitype.MultiTypeAdapter
import io.iotex.base.bindbase.BaseBindDialog
import io.iotex.iopay.R
import io.iotex.iopay.data.db.RPCNetwork
import io.iotex.iopay.databinding.DialogNetworkSwitchBinding
import io.iotex.iopay.network.item.NetworkSwitchBinder
import io.iotex.iopay.network.viewmodel.NetworkSwitchViewModel

class NetworkSwitchDialog(
    private val title: String = Utils.getApp().getString(R.string.switch_network),
    private val items: List<RPCNetwork>? = null,
) :
    BaseBindDialog<NetworkSwitchViewModel, DialogNetworkSwitchBinding>(R.layout.dialog_network_switch) {

    var onItemClick: ((RPCNetwork) -> Unit)? = null

    private val adapter = MultiTypeAdapter()

    override fun initView() {
        mBinding.tvTitle.text = title
        mBinding.ivClose.setOnClickListener {
            dismiss()
        }
        mBinding.recyclerView.layoutManager = LinearLayoutManager(context)
        adapter.register(NetworkSwitchBinder(true).apply {
            itemClick = {
                dismiss()
                onItemClick?.invoke(it)
            }
        })
        mBinding.recyclerView.adapter = adapter
    }


    override fun initData() {
        if (items != null) {
            adapter.items = items
            adapter.notifyDataSetChanged()
            return
        }
        mViewModel.loadAllNetwork()
        mViewModel.networkLiveData.observe(this) {
            adapter.items = it
            adapter.notifyDataSetChanged()
        }
    }

    override fun onStart() {
        super.onStart()
        val dialogWindow = dialog?.window
        val lp = dialogWindow?.attributes
        dialogWindow?.setGravity(Gravity.BOTTOM)
        val orientation = resources.configuration.orientation
        if (orientation == Configuration.ORIENTATION_LANDSCAPE) {
            lp?.width = ScreenUtils.getScreenWidth()
            lp?.height = ScreenUtils.getScreenWidth()
        } else {
            lp?.width = ScreenUtils.getScreenWidth()
            lp?.height = (ScreenUtils.getScreenHeight() * 0.6).toInt()
        }
        dialogWindow?.attributes = lp
    }
}