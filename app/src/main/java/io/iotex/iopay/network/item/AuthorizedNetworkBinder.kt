package io.iotex.iopay.network.item

import android.view.LayoutInflater
import android.view.ViewGroup
import com.drakeet.multitype.ItemViewBinder
import io.iotex.base.bindbase.BaseBindVH
import io.iotex.iopay.R
import io.iotex.iopay.data.db.RPCNetwork
import io.iotex.iopay.databinding.ItemAuthorizedNetworkBinding
import io.iotex.iopay.util.extension.loadSvgOrImage

class AuthorizedNetworkBinder : ItemViewBinder<RPCNetwork, BaseBindVH<ItemAuthorizedNetworkBinding>>() {

    override fun onCreateViewHolder(
        inflater: LayoutInflater,
        parent: ViewGroup
    ): BaseBindVH<ItemAuthorizedNetworkBinding> {
        val bind = ItemAuthorizedNetworkBinding.inflate(inflater, parent, false)
        return BaseBindVH(bind)
    }

    override fun onBindViewHolder(holder: BaseBindVH<ItemAuthorizedNetworkBinding>, item: RPCNetwork) {
        holder.bind.ivLogo.loadSvgOrImage(item.logo, R.drawable.ic_network_default)
        holder.bind.tvName.text = item.name
    }
}