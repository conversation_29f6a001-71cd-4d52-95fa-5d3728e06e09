package io.iotex.iopay.network.item

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.databinding.DataBindingUtil
import com.blankj.utilcode.util.ColorUtils
import com.drakeet.multitype.ItemViewBinder
import io.iotex.base.bindbase.BaseBindVH
import io.iotex.iopay.R
import io.iotex.iopay.data.db.NetworkChain
import io.iotex.iopay.databinding.ItemNetworkChainBinding
import io.iotex.iopay.util.extension.loadSvgOrImage
import io.iotex.iopay.util.extension.setGone
import io.iotex.iopay.util.extension.setVisible

class NetworkChainBinder : ItemViewBinder<NetworkChain, BaseBindVH<ItemNetworkChainBinding>>() {

    var itemClick: ((NetworkChain) -> Unit)? = null
    var check: String? = null

    override fun onBindViewHolder(holder: BaseBindVH<ItemNetworkChainBinding>, item: NetworkChain) {
        val bind = holder.bind
        bind.tvName.text = item.name
        bind.tvChainId.text = "ChainID:" + item.chainId
        bind.tvChain.text = "currency:" + item.chain
        bind.ivImage.loadSvgOrImage(item.icon,R.drawable.ic_network_default)
        if (check == item.chainId) {
            bind.tvName.setTextColor(ColorUtils.getColor(R.color.color_617AFF))
            bind.tvChainId.setTextColor(ColorUtils.getColor(R.color.color_617AFF))
            bind.tvChain.setTextColor(ColorUtils.getColor(R.color.color_617AFF))
            bind.ivCheck.setVisible()
        } else {
            bind.tvName.setTextColor(ContextCompat.getColor(bind.tvName.context, R.color.color_title_sub))
            bind.tvChainId.setTextColor(ContextCompat.getColor(bind.tvChainId.context, R.color.color_title_thr))
            bind.tvChain.setTextColor(ContextCompat.getColor(bind.tvChain.context, R.color.color_title_thr))
            bind.ivCheck.setGone()
        }

        bind.root.setOnClickListener {
            itemClick?.invoke(item)
        }

    }

    override fun onCreateViewHolder(inflater: LayoutInflater, parent: ViewGroup): BaseBindVH<ItemNetworkChainBinding> {
        val bind = DataBindingUtil.inflate<ItemNetworkChainBinding>(
            inflater,
            R.layout.item_network_chain,
            parent,
            false
        )
        return BaseBindVH(bind)
    }
}