package io.iotex.iopay.network.viewmodel

import android.app.Application
import androidx.lifecycle.MutableLiveData
import com.blankj.utilcode.util.TimeUtils
import com.blankj.utilcode.util.Utils
import io.iotex.iopay.base.BaseLaunchVM
import io.iotex.iopay.data.db.AppDatabase
import io.iotex.iopay.data.db.MODE_MAIN
import io.iotex.iopay.data.db.NETWORK_GENERAL
import io.iotex.iopay.data.db.RPCNetwork
import io.iotex.iopay.data.db.RPCNetworkNode
import io.iotex.iopay.support.eventbus.RefreshNetworkListEvent
import io.iotex.iopay.util.Config
import io.iotex.iopay.util.FireBaseUtil
import io.iotex.iopay.util.WalletHelper
import org.greenrobot.eventbus.EventBus
import org.web3j.protocol.Web3j
import org.web3j.protocol.http.HttpService

class AddOrEditNetworkViewModel(application: Application) : BaseLaunchVM(application) {

    val completeLiveData = MutableLiveData<Boolean>()
    val curNetworkLiveData = MutableLiveData<RPCNetwork>()
    val curNetworkNoteLiveData = MutableLiveData<List<RPCNetworkNode>>()
    val noteLiveData = MutableLiveData<RPCNetworkNode>()
    val noteSymbolLiveData = MutableLiveData<String>()
    val isValidChainIdLiveData = MutableLiveData<Boolean>()

    fun deleteNetWork() {
        addLaunch {
            val network = WalletHelper.getCurNetwork()
            network?.let {
                AppDatabase.getInstance(Utils.getApp()).rpcNetworkDao()
                    .delete(network)
                val nodeList = AppDatabase.getInstance(Utils.getApp()).rpcNetworkNode()
                    .queryRPCNodesByChainId(network.chainId)
                nodeList.forEach { node ->
                    AppDatabase.getInstance(Utils.getApp()).rpcNetworkNode().delete(node)
                }
                val ioNetwork = AppDatabase.getInstance(Utils.getApp()).rpcNetworkDao()
                    .queryRPCNetworkByChainId(Config.IOTEX_CHAIN_ID)
                ioNetwork?.let {
                    WalletHelper.switchNetwork(ioNetwork)
                    EventBus.getDefault().post(RefreshNetworkListEvent())
                    completeLiveData.postValue(true)
                }
            }

        }
    }

    fun getCurNetwork() {
        addLaunch {
            val network = WalletHelper.getCurNetwork()
            network?.let {
                curNetworkLiveData.postValue(it)
            }
        }
    }

    fun getCurNetworkNode() {
        addLaunch {
            val network = WalletHelper.getCurNetwork()
            network?.let {
                val nodeList = AppDatabase.getInstance(Utils.getApp()).rpcNetworkNode()
                    .queryRPCNodesByChainId(network.chainId)
                curNetworkNoteLiveData.postValue(nodeList)
            }

        }
    }

    fun addNetwork(
        chainId: Int,
        name: String,
        rpcUrl: String,
        explorerUrl: String,
        symbol: String
    ) {
        addLaunch {
            val id = TimeUtils.getNowMills().toString()
            val order = TimeUtils.getNowMills().toInt()
            val network = RPCNetwork(
                id, chainId, name, name, "", rpcUrl, explorerUrl, "",
                symbol, symbol, "", 18, "0",
                "", "", explorerUrl, MODE_MAIN, "#617AFF", "#617AFF",
                name, order, immutable = false, bg_color_start = "#617480",
                bg_color_end = "#3D477E"
            )
            AppDatabase.getInstance(Utils.getApp()).rpcNetworkDao()
                .insertRPCNetworkDao(network)

            noteLiveData.value?.let {
                AppDatabase.getInstance(Utils.getApp()).rpcNetworkNode()
                    .insertRPCNetworkDao(it)
            }

            EventBus.getDefault().post(RefreshNetworkListEvent())
            WalletHelper.switchNetwork(network)
            FireBaseUtil.logFireBase("action_custom_network_add_confirm")
            completeLiveData.postValue(true)
        }
    }

    fun editNetwork(network: RPCNetwork) {
        addLaunch {
            AppDatabase.getInstance(Utils.getApp()).rpcNetworkDao()
                .update(network)
            EventBus.getDefault().post(RefreshNetworkListEvent())
            WalletHelper.switchNetwork(network)
            completeLiveData.postValue(true)
        }
    }

    fun isValidChainId(chainId: Int?) {
        addLaunch {
            val allNetwork = AppDatabase.getInstance(Utils.getApp()).rpcNetworkDao()
                .queryAllRPCNetwork()
            val network = allNetwork.firstOrNull {
                it.chainId == chainId
            }
            if (network != null) {
                isValidChainIdLiveData.postValue(false)
            } else {
                isValidChainIdLiveData.postValue(true)
            }
        }
    }


    fun queryNodeStateByRpc(rpcUrl: String) {
        addLaunch(onError = {
            val node =
                RPCNetworkNode(-1, rpcUrl, rpcStatus = NETWORK_GENERAL, immutable = false, active = true)
            noteLiveData.postValue(node)
        }) {
            val chainIdBig = Web3j.build(HttpService(rpcUrl)).ethChainId().send().chainId
            val chainId = chainIdBig.toInt()
            val custom = AppDatabase.getInstance(Utils.getApp()).networkChainDao()
                .queryNetworkByChainId(chainId)
            val node =
                RPCNetworkNode(chainId, rpcUrl, rpcStatus = NETWORK_GENERAL, immutable = false, active = true)
            node.name = custom?.name?:""
            noteLiveData.postValue(node)
        }
    }

    fun querySymbol(chainId: Int) {
        addLaunch {
            val network =
                AppDatabase.getInstance(Utils.getApp()).networkChainDao()
                    .queryNetworkByChainId(chainId)
            network?.let {
                noteSymbolLiveData.postValue(network.symbol)
            }
        }
    }
}