package io.iotex.iopay.network.viewmodel

import android.app.Application
import androidx.lifecycle.MutableLiveData
import com.blankj.utilcode.util.TimeUtils
import com.blankj.utilcode.util.Utils
import io.iotex.base.RetrofitClient
import io.iotex.iopay.api.UrlApi
import io.iotex.iopay.base.BaseLaunchVM
import io.iotex.iopay.data.db.*
import io.iotex.iopay.util.Config.IOPAY_URL
import io.iotex.iopay.util.DateTimeUtils
import io.iotex.iopay.util.WalletHelper
import org.web3j.utils.Numeric
import java.math.BigInteger

class NetWorkNodeViewModel(application: Application) : BaseLaunchVM(application) {

    private val apiService by lazy {
        RetrofitClient.createApiService(IOPAY_URL, UrlApi::class.java)
    }

    val curNetworkLiveData = MutableLiveData<RPCNetwork>()
    val noteLiveData = MutableLiveData<RPCNetworkNode?>()
    val noteInsetLiveData = MutableLiveData<Boolean>()

    fun getCurNetwork() {
        addLaunch {
            val network = WalletHelper.getCurNetwork()
            network?.let {
                curNetworkLiveData.postValue(it)
            }
        }
    }

    fun queryNodeStateByRpc(rpcUrl: String) {
        addLaunch(true, onError = {
            noteLiveData.postValue(RPCNetworkNode(-1, ""))
        }) {
            val curChainId = WalletHelper.getCurChainId()
            val nodeOld = AppDatabase.getInstance(Utils.getApp()).rpcNetworkNode()
                .queryRPCNodeByUrl(curChainId, rpcUrl)
            if (nodeOld != null) {
                noteLiveData.postValue(null)
            } else {
                if(WalletHelper.isSolanaNetwork()){
                    val node =
                        RPCNetworkNode(WalletHelper.getCurChainId(), rpcUrl, NETWORK_GENERAL, immutable = false, active = false)
                    noteLiveData.postValue(node)
                    return@addLaunch
                }
                val id = TimeUtils.getNowMills().toString()
                val map = HashMap<String, String>().apply {
                    put("id", id)
                    put("jsonrpc", "2.0")
                    put("method","eth_chainId")
                }
                val start = TimeUtils.getNowMills()
                val result = apiService.requestEthChainId(rpcUrl,map).result?:""
                val end = TimeUtils.getNowMills()
                val chainId = BigInteger(Numeric.cleanHexPrefix(result), 16).toInt()
                val rpcStatus = DateTimeUtils.getRpcState(end-start)
                val node =
                    RPCNetworkNode(chainId, rpcUrl, rpcStatus, immutable = false, active = false)
                noteLiveData.postValue(node)
            }
        }
    }

    fun insertNetWorkNode(node: RPCNetworkNode) {
        addLaunch {
            AppDatabase.getInstance(Utils.getApp()).rpcNetworkNode()
                .insertRPCNetworkDao(node)
            noteInsetLiveData.postValue(true)
        }
    }

}