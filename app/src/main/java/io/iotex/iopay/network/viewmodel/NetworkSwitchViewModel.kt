package io.iotex.iopay.network.viewmodel

import android.app.Application
import android.os.Bundle
import androidx.lifecycle.MutableLiveData
import com.apollographql.apollo.ApolloClient
import com.apollographql.apollo.coroutines.await
import com.blankj.utilcode.util.GsonUtils
import com.blankj.utilcode.util.RegexUtils
import com.blankj.utilcode.util.SPUtils
import com.blankj.utilcode.util.Utils
import io.iotex.base.RetrofitClient
import io.iotex.base.okHttpClient
import io.iotex.iopay.api.IoPayRestApi
import io.iotex.iopay.base.BaseLaunchVM
import io.iotex.iopay.data.UserStore
import io.iotex.iopay.data.bean.NetworkListResult
import io.iotex.iopay.data.db.AppDatabase
import io.iotex.iopay.data.db.MODE_DEV
import io.iotex.iopay.data.db.MODE_MAIN
import io.iotex.iopay.data.db.NETWORK_GENERAL
import io.iotex.iopay.data.db.NetworkAAConfig
import io.iotex.iopay.data.db.RPCNetwork
import io.iotex.iopay.data.db.RPCNetworkNode
import io.iotex.iopay.util.Config
import io.iotex.iopay.util.DateTimeUtils
import io.iotex.iopay.util.FileUtils
import io.iotex.iopay.util.FireBaseEvent
import io.iotex.iopay.util.FireBaseUtil
import io.iotex.iopay.util.SPConstant
import io.iotex.iopay.util.WalletHelper
import io.iotex.network.NetworkAAConfigQuery

class NetworkSwitchViewModel(application: Application) : BaseLaunchVM(application) {

    val networkLiveData = MutableLiveData<List<RPCNetwork>>()

    private val apiService by lazy {
        RetrofitClient.createApiService(Config.IOPAY_REST_API_URL, IoPayRestApi::class.java)
    }

    private val apolloClient by lazy {
        ApolloClient.builder()
            .serverUrl(Config.IoPayUrl)
            .okHttpClient(okHttpClient)
            .build()
    }

    fun loadMainNetwork() {
        addLaunch {
            val result =
                AppDatabase.getInstance(Utils.getApp()).rpcNetworkDao().queryMainNetworkImmutable()
            val wallet = WalletHelper.getCurWallet()
            val list = result.filter {
                if (wallet?.isSolanaPrivateWallet() == true || wallet?.isSolanaWatchWallet() == true) {
                    WalletHelper.isSolanaNetwork(it.chainId)
                } else if (wallet?.isBitcoinWatchWallet() == true) {
                    WalletHelper.isBitcoinNetwork(it.chainId)
                } else if (wallet?.isEvmWatchWallet() == true) {
                    !WalletHelper.isSolanaNetwork(it.chainId) && !WalletHelper.isBitcoinNetwork(it.chainId)
                } else if (wallet?.isEvmPrivateWallet() == true) {
                    !WalletHelper.isSolanaNetwork(it.chainId)
                } else {
                    true
                }
            }
            networkLiveData.postValue(list)
        }
    }

    fun fetchNetworkListOneDay() {
        if (DateTimeUtils.formatYMD(UserStore.getNetworkListTime()) != DateTimeUtils.formatYMD(System.currentTimeMillis())) {
            UserStore.setNetworkListTime(System.currentTimeMillis())
            fetchNetworkList()
        }
    }

    fun fetchNetworkList() {
        addLaunchNoCancel("fetchNetworkList",onError = {
            val bundleEvent = Bundle()
            bundleEvent.putString("api", "network_list")
            bundleEvent.putString("msg", it.message)
            FireBaseUtil.logFireBase(
                FireBaseEvent.API_ERROR_CATCH,
                bundleEvent
            )
        }) {
            apiService.networkList().network_list.let {
                resolveNetworkList(it)
            }
        }
    }

    private fun resolveNetworkList(networkList: List<NetworkListResult.NetWork>?) {
        networkList?.forEach { network ->
            val rpcList = network.rpc
            var activatedNode = AppDatabase.getInstance(Utils.getApp()).rpcNetworkNode()
                .queryRPCNodeActivated(network.chainId)
            val oldNodes = AppDatabase.getInstance(Utils.getApp()).rpcNetworkNode()
                .queryImmutableNodes(network.chainId)
            AppDatabase.getInstance(Utils.getApp()).rpcNetworkNode()
                .deleteImmutableNodes(network.chainId)
            rpcList?.mapIndexed { index, url ->
                val rpcUrl = RegexUtils.getReplaceAll(url, "/*$", "")
                val rpcStatus = oldNodes.firstOrNull { node ->
                    node.rpcUrl == rpcUrl
                }?.rpcStatus ?: NETWORK_GENERAL
                if (index == 0 && activatedNode == null) {
                    RPCNetworkNode(
                        network.chainId,
                        rpcUrl,
                        immutable = true,
                        active = true,
                        rpcStatus = rpcStatus
                    ).also {
                        activatedNode = it
                    }
                } else if (activatedNode?.rpcUrl?.equals(rpcUrl, false) == true) {
                    RPCNetworkNode(
                        network.chainId,
                        rpcUrl,
                        immutable = true,
                        active = true,
                        rpcStatus = rpcStatus
                    )
                } else {
                    RPCNetworkNode(network.chainId, rpcUrl, immutable = true, rpcStatus = rpcStatus)
                }
            }?.forEach { node ->
                AppDatabase.getInstance(Utils.getApp()).rpcNetworkNode()
                    .insertRPCNetworkDao(node)
            }

            val devMode = if (network.devMode) MODE_DEV else MODE_MAIN

            val networkEntry = RPCNetwork(
                network.id.toString(),
                network.chainId,
                network.name ?: "",
                network.shortName ?: "",
                network.platform,
                activatedNode?.rpcUrl ?: "",
                network.explorer ?: "",
                network.network_chain_theme?.logo_image ?: "",
                network.nativeCurrencyMarket?.token?.symbol?.uppercase() ?: "",
                network.nativeCurrencyMarket?.token?.name ?: "",
                network.nativeCurrencyMarket?.token?.logo ?: "",
                network.nativeCurrencyMarket?.token?.decimals?.toIntOrNull() ?: 18,
                network.nativeCurrencyMarket?.current_price ?: "0",
                network.tokensAlias ?: "",
                "",
                network.swapUrl ?: "",
                devMode,
                "",
                "",
                network.networkName ?: "",
                network.order ?: 0,
                network.network_chain_theme?.back_image ?: "",
                "",
                "",
                true,
                network.network_config?.token_approval_checker ?: "",
                network.network_config?.chain_icon ?: "",
                network.network_config?.chain_icon_selected ?: "",
                network.network_config?.gas_station ?: "",
                network.network_chain_theme?.theme_color ?: "",
                network.nativeCurrencyMarket?.price_change_24h ?: "",
                network.nativeCurrencyMarket?.sparkline_in_7d ?: "",
                network.network_config?.chain_icon_light ?: "",
                network.nativeCurrencyMarket?.token?.id?:"",
                network.network_config?.dapp_chain?:false,
                network.network_config?.token_category?:"",
                network.network_config?.frequency?:30,
            )
            AppDatabase.getInstance(Utils.getApp()).rpcNetworkDao()
                .insertRPCNetworkDao(networkEntry)
            UserStore.setDefaultGasLimit(network.chainId, network.network_config?.gas_limit ?: "0")
            UserStore.setDefaultGasPrice(network.chainId, network.network_config?.gas_price ?: "0")
        }
        val chainId = WalletHelper.getCurChainId()
        val rpcNode = AppDatabase.getInstance(Utils.getApp()).rpcNetworkNode()
            .queryRPCNodeActivated(chainId)
        rpcNode?.let {
            SPUtils.getInstance().put(SPConstant.SP_RPC_NETWORK_URL, rpcNode.rpcUrl)
        }
    }

    fun loadAllNetwork(search: String? = null) {
        addLaunch {
            val result = if (search.isNullOrEmpty()) {
                AppDatabase.getInstance(Utils.getApp()).rpcNetworkDao().queryAllRPCNetwork()
            } else {
                AppDatabase.getInstance(Utils.getApp()).rpcNetworkDao().queryRPCNetworkBySearch(search)
            }
            if (result.isEmpty()) {
                fetchNetworkList()
            }
            val dev = SPUtils.getInstance().getBoolean(SPConstant.SP_DEVELOPER_MODE, false)
            val defList = if (dev) {
                result.filter { it.immutable }
            } else {
                result.filter { it.devMode == MODE_MAIN && it.immutable }
            }
            val customList = result.filter { !it.immutable } as ArrayList

            mutableListOf<RPCNetwork>().apply {
                addAll(defList)
                addAll(customList)
                networkLiveData.postValue(filterAANetworks(this))
            }
        }
    }

    fun loadNetworkFromJson() {
        addLaunch {
            val result =
                AppDatabase.getInstance(Utils.getApp()).rpcNetworkDao().queryMainNetworkImmutable()
            if (result.isEmpty()) {
                val json = FileUtils.readJsonFromAssets("network_list.json")
                val networkList = GsonUtils.fromJson(json, NetworkListResult::class.java)
                resolveNetworkList(networkList.network_list)
            }
        }
    }

    private fun filterAANetworks(networks: List<RPCNetwork>): List<RPCNetwork> {
        val wallet = WalletHelper.getCurWallet()
        if (wallet?.isAAWallet() == true) {
            val aaNetworkList =
                AppDatabase.getInstance(Utils.getApp()).networkAAConfigDao().queryAll()
            return networks.filter { network ->
                aaNetworkList.firstOrNull { config ->
                    config.chainId == network.chainId
                } != null
            }
        }
        return networks
    }

    fun loadAAConfig() {
        addLaunch {
            val networkAAConfigQuery = NetworkAAConfigQuery.builder().build()
            apolloClient
                .query(networkAAConfigQuery)
                .await().data?.network_aa_config()?.map {
                    NetworkAAConfig(
                        it?.chain_id() ?: Config.IOTEX_CHAIN_ID,
                        it.entry_point() ?: Config.CONTRACT_ENTRY_POINT,
                        it.factory() ?: Config.CONTRACT_FACTORY,
                        it.bound_email() ?: Config.CONTRACT_BOUND_EMAIL,
                        it.email_service() ?: Config.EMAIL_SERVICE,
                        it.bundler_service() ?: Config.BUNDLER_SERVICE,
                        it.paymaster_service() ?: Config.PAYMASTER_SERVICE,
                        it.subgraph() ?: Config.SUBGRAPH,
                        it.forceUsePaymaster() ?: false
                    )
                }?.also {
                    AppDatabase.getInstance(Utils.getApp()).networkAAConfigDao()
                        .insert(*it.toTypedArray())
                }
        }
    }
}