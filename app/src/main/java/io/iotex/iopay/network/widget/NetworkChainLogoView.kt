package io.iotex.iopay.network.widget

import android.content.Context
import android.graphics.Rect
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.viewbinding.ViewBinding
import com.blankj.utilcode.util.Utils
import com.drakeet.multitype.ItemViewBinder
import com.drakeet.multitype.MultiTypeAdapter
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.machinefi.walletconnect2.WC2Config
import io.iotex.iopay.R
import io.iotex.iopay.data.db.AppDatabase
import io.iotex.iopay.data.db.RPCNetwork
import io.iotex.iopay.databinding.ItemNetworkLogoBinding
import io.iotex.iopay.databinding.ViewDappChainTagBinding
import io.iotex.iopay.util.Config
import io.iotex.iopay.util.Constant
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.util.extension.loadSvgOrImage
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class NetworkChainLogoView @JvmOverloads constructor(
    context: Context,
    attr: AttributeSet? = null,
    defStyle: Int = 0
) : FrameLayout(
    context,
    attr,
    defStyle
) {
    private val mBinding = DataBindingUtil.inflate<ViewDappChainTagBinding>(
        LayoutInflater.from(context),
        R.layout.view_dapp_chain_tag, this, true
    )

    var onClick: (() -> Unit)? = null

    private val adapter = MultiTypeAdapter()

    fun setNetworkChain(chains: String) {
        kotlin.runCatching {
            val type = object : TypeToken<ArrayList<String>>() {}.type
            val chainList =
                Gson().fromJson<ArrayList<String>>(chains, type)
            parseChainNetwork(chainList) { networks ->
                setNetwork(networks)
            }
        }
    }

    fun setNetwork(networks: List<RPCNetwork>) {
        var logoList = networks.map {
            it.logo
        }.toMutableList()
        if (logoList.size > 6) {
            logoList = logoList.subList(0, 6)
            logoList.add(WC2Config.CONTRACT_MORE)
        }
        setNetworkLogo(logoList)
    }

    private fun setNetworkLogo(logoList: List<String>) {
        //recycler will cut padding,so remove and add.
        mBinding.llRoot.removeAllViews()
        val recyclerView = RecyclerView(context)
        mBinding.llRoot.addView(recyclerView)

        recyclerView.addItemDecoration(object : RecyclerView.ItemDecoration() {
            override fun getItemOffsets(
                outRect: Rect,
                view: View,
                parent: RecyclerView,
                state: RecyclerView.State
            ) {
                super.getItemOffsets(outRect, view, parent, state)
                if (parent.getChildAdapterPosition(view) != (logoList.size - 1)) {
                    outRect.right = -25
                }
            }
        })
        adapter.register(NetworkLogoItemBinder())
        adapter.items = logoList
        recyclerView.layoutManager =
            LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
        recyclerView.adapter = adapter
    }

    override fun onTouchEvent(event: MotionEvent?): Boolean {
        (parent as View).performClick()
        return false
    }

    inner class NetworkLogoItemBinder() : ItemViewBinder<String, VH>() {
        override fun onBindViewHolder(holder: VH, item: String) {
            val bind = holder.bind as ItemNetworkLogoBinding
            if (item == WC2Config.CONTRACT_MORE) {
                bind.ivChain.setImageResource(R.drawable.icon_network_wc_more)
            } else {
                bind.ivChain.loadSvgOrImage(item, R.drawable.ic_network_default)
            }
            bind.root.setOnClickListener {
                onClick?.invoke()
            }
        }

        override fun onCreateViewHolder(
            inflater: LayoutInflater,
            parent: ViewGroup
        ): VH {
            return VH(ItemNetworkLogoBinding.inflate(inflater))
        }

    }

    class VH(viewBinding: ViewBinding) : RecyclerView.ViewHolder(viewBinding.root) {
        val bind = viewBinding
    }


    companion object {
        fun parseChainNetwork(chains: List<String>?, callback: (ArrayList<RPCNetwork>) -> Unit) {
            MainScope().launch {
                val networks = ArrayList<RPCNetwork>()
                val wallet = Constant.currentWallet ?: return@launch
                withContext(Dispatchers.IO){
                    chains?.forEach { chain ->
                        if (chain.contains(WC2Config.CONTRACT_SOLANA)) {
                            if (wallet.isEvmPrivateWallet() || wallet.isEvmWatchWallet() || wallet.isAAWallet()) return@forEach
                            val network = AppDatabase.getInstance(Utils.getApp()).rpcNetworkDao()
                                .queryRPCNetworkByChainId(Config.SOLANA_MAIN_CHAIN_ID)
                            network?.let {
                                if (!networks.contains(it)) networks.add(it)
                            }
                        } else {
                            if (wallet.isSolanaPrivateWallet() || wallet.isSolanaWatchWallet()) return@forEach
                            val chainId = chain.replace(WC2Config.CONTRACT_EIP155_DOT, "").toIntOrNull()
                                ?: return@forEach
                            val network =
                                AppDatabase.getInstance(Utils.getApp()).rpcNetworkDao()
                                    .queryRPCNetworkByChainId(chainId)
                            network?.let {
                                if (!networks.contains(it)) networks.add(it)
                            }
                        }
                    }
                    if (networks.isEmpty()) {
                        val network = WalletHelper.getCurNetwork()
                        network?.let {
                            if (!networks.contains(it)) networks.add(it)
                        }
                    }
                }
                callback.invoke(networks)
            }
        }
    }
}