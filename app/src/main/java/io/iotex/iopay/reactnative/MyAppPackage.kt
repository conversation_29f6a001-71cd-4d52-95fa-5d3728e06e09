package io.iotex.iopay.reactnative

import com.facebook.react.ReactPackage
import com.facebook.react.bridge.NativeModule
import com.facebook.react.bridge.ReactApplicationContext
import io.iotex.iopay.reactnative.views.NavigationHeaderSubViewManager
import io.iotex.iopay.reactnative.views.NavigationHeaderViewManager
import io.iotex.iopay.reactnative.views.StakePickerViewManager

class MyAppPackage : ReactPackage {

    override fun createNativeModules(reactContext: ReactApplicationContext): List<NativeModule> {
        return listOf(
            ReactBridge(reactContext),
            ReactNativeWeb3Provider(reactContext),
            NativeEventEmitter(reactContext),
            RNCacheLoader(reactContext)
        )
    }

    override fun createViewManagers(reactContext: ReactApplicationContext) = listOf(
        NavigationHeaderViewManager(),
        NavigationHeaderSubViewManager(),
        StakePickerViewManager()
    )
}