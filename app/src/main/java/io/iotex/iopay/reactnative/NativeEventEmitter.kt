package io.iotex.iopay.reactnative

import androidx.core.os.bundleOf
import com.facebook.react.bridge.Arguments
import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.bridge.ReactContextBaseJavaModule
import com.facebook.react.bridge.WritableMap
import com.facebook.react.module.annotations.ReactModule
import com.facebook.react.modules.core.DeviceEventManagerModule.RCTDeviceEventEmitter
import io.iotex.iopay.data.UserStore
import io.iotex.iopay.util.WalletHelper

@ReactModule(name = NativeEventEmitter.ModuleName)
class NativeEventEmitter(val reactContext: ReactApplicationContext): ReactContextBaseJavaModule(reactContext) {

    companion object {
        const val ModuleName = "NativeEventEmitter"
//        const val LOCALE_CHANGE_EVENT = "LOCALE_CHANGE_EVENT"
        const val STATUS_CHANGE_EVENT = "STATUS_CHANGE_EVENT"
        const val DATA_CHANGE_EVENT = "DATA_CHANGE_EVENT"
        const val COLOR_MODE_CHANGE_EVENT = "COLOR_MODE_CHANGE_EVENT"
    }

    override fun getName(): String = ModuleName

    fun publishNativeStatus(chainId: Int, address: String, rpc: String, showWeb3Subject: Boolean) {
        val event = Arguments.createMap()
        event.putInt("chainId", chainId)
        event.putString("address", address)
        event.putString("rpc", rpc)
        event.putString("addressMode", if (showWeb3Subject) "0x" else "io")
        emit(STATUS_CHANGE_EVENT, event)
    }

    fun publishRefreshBalanceEvent() {
        WalletHelper.getCurNetwork()?.let {
            val chainId = if (UserStore.getAllNetwork()) 0 else it.chainId
            emit(DATA_CHANGE_EVENT, Arguments.fromBundle(bundleOf("type" to "balance", "chainId" to chainId)))
            emit(DATA_CHANGE_EVENT, Arguments.fromBundle(bundleOf("type" to "tokenBalance", "chainId" to chainId)))
        }
    }

    fun publishTokenListChanged() {
        WalletHelper.getCurNetwork()?.let {
            val chainId = if(UserStore.getAllNetwork()) 0 else it.chainId
            emit(DATA_CHANGE_EVENT, Arguments.fromBundle(bundleOf("type" to "tokenList", "chainId" to chainId)))
        }
    }

    fun publishColorModeChanged() {
        val mode = if (UserStore.isDarkTheme())  "dark" else "light"
        emit(COLOR_MODE_CHANGE_EVENT, Arguments.fromBundle(bundleOf("colorMode" to mode)))
    }

    private fun emit(eventName: String, data: WritableMap) {
        val emitter: RCTDeviceEventEmitter = reactContext.getJSModule(
            RCTDeviceEventEmitter::class.java
        )
        emitter.emit(eventName, data)
    }
}