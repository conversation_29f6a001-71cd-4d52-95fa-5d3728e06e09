package io.iotex.iopay.reactnative

data class RNBucket (
    val index: Long,
    val owner: String,
    val candidate: String,
    val stakeStartTime: Long,
    val stakedDuration: Int,
    val autoStake: Boolean,
    val unstakeStartTime: Long,
    val createTime: Long,
    val stakedAmount: String,
//    val selfStakingBucket: Boolean,
    // withdrawWaitUntil,
    // status: this.getStatus(withdrawWaitUntil, unstakeStartTime, stakeStartTime),
    val canName: String
//    val logo: String
)