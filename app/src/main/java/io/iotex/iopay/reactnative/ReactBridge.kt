package io.iotex.iopay.reactnative

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import androidx.core.os.bundleOf
import androidx.fragment.app.FragmentActivity
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.SPUtils
import com.blankj.utilcode.util.TimeUtils
import com.blankj.utilcode.util.ToastUtils
import com.blankj.utilcode.util.Utils
import com.facebook.react.bridge.ActivityEventListener
import com.facebook.react.bridge.Arguments
import com.facebook.react.bridge.BaseActivityEventListener
import com.facebook.react.bridge.Promise
import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.bridge.ReactContextBaseJavaModule
import com.facebook.react.bridge.ReactMethod
import com.facebook.react.bridge.ReadableArray
import com.facebook.react.bridge.ReadableMap
import com.facebook.react.bridge.ReadableType
import com.facebook.react.bridge.WritableNativeMap
import com.google.firebase.crashlytics.FirebaseCrashlytics
import com.google.gson.Gson
import com.machinefi.walletconnect2.WC2Config
import io.iotex.iopay.IoPayApplication
import io.iotex.iopay.MainActivity
import io.iotex.iopay.R
import io.iotex.iopay.SchemeUtil
import io.iotex.iopay.dapp.dialog.DAppEnterTipsDialog
import io.iotex.iopay.data.UserStore
import io.iotex.iopay.data.db.ACTION_TYPE_COMPOUND_UNREGISTER
import io.iotex.iopay.data.db.AppDatabase
import io.iotex.iopay.data.db.LIKE_STATUS_LIKE
import io.iotex.iopay.data.db.LIKE_STATUS_UNLIKE
import io.iotex.iopay.data.db.TokenCacheEntry
import io.iotex.iopay.data.db.TokenEntry
import io.iotex.iopay.reactnative.ReactDestinationScene.AddressBook
import io.iotex.iopay.reactnative.ReactDestinationScene.Dapp
import io.iotex.iopay.reactnative.ReactDestinationScene.Scan
import io.iotex.iopay.reactnative.ReactDestinationScene.Staking
import io.iotex.iopay.reactnative.ReactDestinationScene.Web
import io.iotex.iopay.reactnative.dialog.BuyEnterTipsDialog
import io.iotex.iopay.setting.book.AddressBookActivity
import io.iotex.iopay.support.eventbus.ActionRefreshEvent
import io.iotex.iopay.support.eventbus.FavoriteOrDislikeERC20Event
import io.iotex.iopay.support.eventbus.MainPullRefresh
import io.iotex.iopay.support.eventbus.MessageRedDotEvent
import io.iotex.iopay.support.eventbus.NewsRedDotEvent
import io.iotex.iopay.ui.NoticePopupWindow
import io.iotex.iopay.util.Constant
import io.iotex.iopay.util.FireBaseUtil
import io.iotex.iopay.util.IoPayConstant
import io.iotex.iopay.util.SPConstant
import io.iotex.iopay.util.StakeConstant
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.util.extension.i
import io.iotex.iopay.wallet.qrcode.IoScanQRCodeActivity
import io.iotex.iopay.wallet.web3.Web3Delegate
import io.iotex.iopay.xapp.XAppsActivity
import io.iotex.iopay.xapp.trust.AddChainsUtil
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus
import org.jetbrains.anko.doAsync
import org.jetbrains.anko.uiThread
import java.math.BigInteger
import java.util.Timer
import kotlin.concurrent.schedule

class ReactBridge(context: ReactApplicationContext) : ReactContextBaseJavaModule(context) {

    private val CHOOSE_DELEGATE_CODE = 2
    private var mPromise: Promise? = null
    private val promiseMap = mutableMapOf<String, Promise>()

    private val mActivityEventListener: ActivityEventListener =
        object : BaseActivityEventListener() {
            override fun onActivityResult(
                activity: Activity?,
                requestCode: Int,
                resultCode: Int,
                data: Intent?
            ) {
                if (requestCode == CHOOSE_DELEGATE_CODE && resultCode == Activity.RESULT_OK) {
                    val registerName = data?.getStringExtra(StakeConstant.DELEGATE_NAME) ?: ""
                    val name = data?.getStringExtra(StakeConstant.DELEGATE_NAME_DISPLAY) ?: ""
                    val logo = data?.getStringExtra(StakeConstant.DELEGATE_LOGO) ?: ""
                    val json = mapOf("registerName" to registerName, "name" to name, "logo" to logo)
                    mPromise?.resolve(Gson().toJson(json))
                    mPromise = null
                }
            }
        }

    init {
        context.addActivityEventListener(mActivityEventListener)
    }

    override fun getName(): String {
        return "ReactBridge"
    }

    @ReactMethod
    fun navigateTo(sceneName: String, params: ReadableMap) {
        if (ReactDestinationScene.values().map { it.name }.contains(sceneName)) {
            when (enumValueOf<ReactDestinationScene>(sceneName)) {
                Staking -> handleStaking()
                Dapp -> handleDapp(params.toHashMap())
                Web -> handleDapp(params.toHashMap())
                else -> {}
            }
        }
        if (ReactScene.values().map { it.name }.contains(sceneName)) {
            handleReactScene(sceneName, params)
        }
    }

    @ReactMethod
    fun sendAnalyticEvent(event: String, params: ReadableMap) {
        if (event == "action_api_error") {
            params.getString("error")?.let {
                FirebaseCrashlytics.getInstance().recordException(Throwable(it))
            }
        } else {
            val bundle = bundleOf(*params.toHashMap().toList().toTypedArray())
            FireBaseUtil.logFireBase(event, bundle)
        }
    }

    @ReactMethod
    fun navigateBack(skip: Int) {
        val app = IoPayApplication.getInstance()
        val count = app.lifecycleHandler.store.count()

        if (skip > count - 1) {
            return
        }
        app.lifecycleHandler.store.slice(count - 1 - skip until count)
            .forEach {
                it.finish()
            }
    }

    @ReactMethod
    fun navigateBackTo(scene: String) {
        val app = IoPayApplication.getInstance()
        val count = app.lifecycleHandler.store.count()
        val index = app.lifecycleHandler.store.indexOfFirst {
            if (it::class == ReactNativeActivity::class) {
                val rn = it as ReactNativeActivity
                return@indexOfFirst rn.getReactComponentName() == scene
            }
            return@indexOfFirst false
        }
        if (index == -1) {
            return
        }
        app.lifecycleHandler.store.slice(index + 1 until count)
            .forEach {
                it.finish()
            }
    }

    @ReactMethod
    fun navigateToSafari(uri: String, screenName: String, inner: Boolean) {
        val param = HashMap<String, Any>()
        param["url"] = uri
        param["screenName"] = screenName
        handleDapp(param)
    }

    @ReactMethod
    fun insertTransaction(
        from: String,
        to: String,
        amount: String,
        hash: String?,
        gasPrice: String,
        gasLimit: String,
        nonce: String,
        method: String,
        chain: Int,
        symbol: String,
        address: String,
        extra: String
    ) {
        // ios use. empty for android.
    }

    @ReactMethod
    fun popWaiting(hash: String?) {
        // ios use. empty for android.
    }

    @ReactMethod
    fun updateUnreadMessageCount(count: Int) {
        doAsync {
            uiThread {
                UserStore.setMessageCount(count)
                EventBus.getDefault().post(MessageRedDotEvent())
            }
        }
    }

    @ReactMethod
    fun updateHasNewNews() {
        doAsync {
            uiThread {
                EventBus.getDefault().post(NewsRedDotEvent())
            }
        }
    }

    @ReactMethod
    fun requestAlert(title: String, content: String, promise: Promise) {
        NoticePopupWindow(title, content, null, confirmAction = {
            promise.resolve(true)
        }, cancelAction = {
            promise.resolve(false)
        }).show()
    }

    @ReactMethod
    fun requestDappEnterAlert(id: String, title: String, promise: Promise) {
        if (currentActivity is FragmentActivity) {
            val activity = currentActivity as FragmentActivity
            if (SPUtils.getInstance().getBoolean("${SPConstant.SP_BEDROCK_ENTER_TIPS_UNDERSTOOD}_$id")) {
                promise.resolve(true)
            } else {
                DAppEnterTipsDialog(title).apply {
                    onConfirmClick = {
                        if (it) SPUtils.getInstance()
                            .put("${SPConstant.SP_BEDROCK_ENTER_TIPS_UNDERSTOOD}_$id", true)
                        promise.resolve(true)
                    }
                    onCancelClick = {
                        promise.resolve(false)
                    }
                }.show(activity.supportFragmentManager, System.currentTimeMillis().toString())
            }
        }
    }

    @ReactMethod
    fun requestBuyEnterAlert(promise: Promise) {
        if (currentActivity is FragmentActivity) {
            val activity = currentActivity as FragmentActivity
            if (SPUtils.getInstance().getBoolean(SPConstant.SP_BUY_ENTER_TIPS_UNDERSTOOD)) {
                promise.resolve(true)
            } else {
                BuyEnterTipsDialog().apply {
                    onConfirmClick = {
                        if (it) SPUtils.getInstance()
                            .put(SPConstant.SP_BUY_ENTER_TIPS_UNDERSTOOD, true)
                        promise.resolve(true)
                    }
                    onCancelClick = {
                        promise.resolve(false)
                    }
                }.show(activity.supportFragmentManager, System.currentTimeMillis().toString())
            }
        }
    }

    private fun handleReactScene(scene: String, params: ReadableMap) {
        val intent = Intent(currentActivity, ReactNativeActivity::class.java)
        intent.putExtra(ReactNativeActivity.REACT_COMPONENT_NAME, scene)
        val it = params.keySetIterator()
        while (it.hasNextKey()) {
            val key = it.nextKey()
            when (params.getType(key)) {
                ReadableType.String -> intent.putExtra(key, params.getString(key))
                ReadableType.Number -> intent.putExtra(key, params.getInt(key))
                ReadableType.Boolean -> intent.putExtra(key, params.getBoolean(key))
                else -> {}
            }
        }
        currentActivity?.startActivity(intent)
    }

    private fun handleStaking() {
        currentActivity?.let {
            SchemeUtil.goto(it, SchemeUtil.SCHEME_STAKE_PAGE)
        }
    }

    private fun handleDapp(params: HashMap<String, Any>) {
        val intent = Intent(currentActivity, XAppsActivity::class.java)
        intent.putExtra(IoPayConstant.BROWSER_URL, params["url"] as String?)
        intent.putExtra(IoPayConstant.BROWSER_DAPP_NAME, params["name"] as String?)
        intent.putExtra(IoPayConstant.BROWSER_DAPP_LOGO, params["icon"] as String?)
        intent.putExtra(IoPayConstant.BROWSER_SCREEN_NAME, params["screenName"] as String?)
        currentActivity?.startActivity(intent)
    }

    @ReactMethod
    fun checkTokenNeedAddToMain(tokens: ReadableArray, promise: Promise) {
        val walletAddress = Constant.currentWallet?.address
        val list = walletAddress?.let {
            AppDatabase.getInstance(Utils.getApp()).tokenCacheDao().queryByStatus(
                walletAddress, WalletHelper.getCurChainId(),
                LIKE_STATUS_LIKE
            )
        }

        val topActivity = ActivityUtils.getTopActivity()
        if (topActivity is MainActivity) {
            if (topActivity.isSwapPage()) {
                // filter out tokens that not in the list
                tokens.toArrayList().filter { token ->
                    val tokenAddress = (token as Map<*, *>)["address"] as String
                    list?.find { it.address == tokenAddress } == null
                }.let { it ->
                    promise.resolve(Arguments.makeNativeArray(it.map { Arguments.makeNativeMap(it as Map<String, Object>) }))
                }
            }
        }
        promise.resolve(Arguments.makeNativeArray(emptyList<WritableNativeMap>()))
    }

    @ReactMethod
    fun addTokensToMain(tokens: ReadableArray) {
        val walletAddress = Constant.currentWallet?.address
        doAsync {
            val tokenList = ArrayList<TokenEntry>()
            val tokenCacheList = ArrayList<TokenCacheEntry>()
            tokens.toArrayList().forEach { token ->
                val t = token as Map<*, *>
                val tokenAddress = t["address"] as String
                val tokenName = t["name"] as String
                val tokenSymbol = t["symbol"] as String
                val tokenDecimals = (t["decimals"] as Double).toInt()
                val tokenLogo = t["logoURI"] as String
                val chainId = (t["chainId"] as Double).toInt()
                if (walletAddress != null) {
                    var oldERC20 =
                        AppDatabase.getInstance(Utils.getApp()).tokenDao().queryByAddress(chainId, tokenAddress)
                    if (oldERC20 == null) {
                        oldERC20 = TokenEntry(
                            TimeUtils.getNowMills().toString(),
                            chainId,
                            tokenAddress.lowercase(),
                            tokenName,
                            tokenSymbol,
                            tokenDecimals,
                            tokenLogo,
                            "",
                            isCustomToken = true
                        )
                    }
                    tokenList.add(oldERC20)
                    var tokenCache = AppDatabase.getInstance(Utils.getApp()).tokenCacheDao()
                        .queryByAddress(walletAddress, chainId, tokenAddress)
                    if (tokenCache == null) {
                        tokenCache = TokenCacheEntry(
                            walletAddress,
                            chainId,
                            tokenAddress,
                            ""
                        )
                    } else {
                        tokenCache.likeStatus = LIKE_STATUS_LIKE
                    }
                    tokenCacheList.add(tokenCache)
                }
            }
            AppDatabase.getInstance(Utils.getApp()).tokenDao().insertOrReplace(*tokenList.toTypedArray())
            AppDatabase.getInstance(Utils.getApp()).tokenCacheDao().insertOrReplace(*tokenCacheList.toTypedArray())
            EventBus.getDefault().post(FavoriteOrDislikeERC20Event())
        }
    }

    @ReactMethod
    fun share(title: String, content: String) {
        val intent = Intent(Intent.ACTION_SEND)
        intent.type = "text/plain"
        intent.putExtra(Intent.EXTRA_SUBJECT, title)
        intent.putExtra(Intent.EXTRA_TEXT, content)
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
        currentActivity?.startActivity(Intent.createChooser(intent, title))
    }

    @ReactMethod
    fun navigateWithCallback(scene: String, key: String, params: ReadableMap, promise: Promise) {
        if (ReactDestinationScene.values().map { it.name }.contains(scene)) {
            when (enumValueOf<ReactDestinationScene>(scene)) {
                AddressBook -> {
                    currentActivity?.let {
                        AddressBookActivity.startActivity(it) { address, _, _ ->
                            promise.resolve(WalletHelper.formatWalletAddress(address))
                        }
                    }
                }
                Scan -> {
                    currentActivity?.let { activity ->
                        IoScanQRCodeActivity.startActivity(activity) {
                            if (WalletHelper.isValidAddress(it)) {
                                promise.resolve(it)
                            }
                        }
                    }
                }
                else -> {
                }
            }
        }

        if (ReactScene.values().map { it.name }.contains(scene)) {
            promiseMap[key] = promise
            this.handleReactScene(scene, params)
        }
    }

    @ReactMethod
    fun callback(key: String, params: ReadableMap) {
        currentActivity?.finish()
        promiseMap[key]?.resolve(Arguments.makeNativeMap(params.toHashMap()))
        promiseMap.remove(key)
    }

    @ReactMethod
    fun updateActionHistory(hash: String, columns: ReadableArray, values: ReadableArray) {
        val map = mutableMapOf<String, String>()
        for (i in 0 until columns.size()) {
            map[columns.getString(i)] = values.getString(i)
        }
        val dao = AppDatabase.getInstance(Utils.getApp()).actionRecordDao()
        val chainId = WalletHelper.getCurChainId()
        WalletHelper.getCurWallet()?.let {
            dao.queryByHash(chainId, hash)
        }?.let {
            map["to"]?.apply {
                if (it.type == ACTION_TYPE_COMPOUND_UNREGISTER) {
                    it.value = Utils.getApp()
                        .getString(R.string.index_no, this)
                } else {
                    it.to = this
                }
            }
            dao.insertOrReplace(it)
            EventBus.getDefault().post(ActionRefreshEvent())
        }
    }

    @ReactMethod(isBlockingSynchronousMethod = true)
    fun currentWalletIsAbstract(): Boolean {
        return Constant.currentWallet?.isAAWallet() ?: false
    }

    @ReactMethod
    fun requestTokenList(chainId: Int, promise: Promise) {
        CoroutineScope(Dispatchers.IO).launch {
            val list = kotlin.runCatching {
                AppDatabase.getInstance(Utils.getApp()).tokenDao().queryByChain(chainId)
            }.getOrNull()
            val tokenList = ArrayList<Bundle>()
            val network = AppDatabase.getInstance(Utils.getApp())
                .rpcNetworkDao().queryRPCNetworkByChainId(chainId)
            tokenList.add(bundleOf(
                "id" to System.currentTimeMillis().toString(),
                "logo" to (network?.currencyLogo ?: ""),
                "name" to (network?.currencyName ?: ""),
                "address" to "0x00000000000000000000000000000000000000000",
                "symbol" to (network?.currencySymbol ?: ""),
                "decimals" to (network?.currencyDecimals ?: 18),
                "isDepinToken" to false,
                "tags" to arrayOf<String>(),
                "isOfficial" to true,
                "price" to (network?.currencyPrice ?: "0"),
                "isMeme" to false
            ))
            list?.forEach {
                tokenList.add(bundleOf(
                    "id" to it.id,
                    "logo" to it.logo,
                    "name" to it.name,
                    "address" to it.address,
                    "symbol" to it.symbol,
                    "decimals" to it.decimals,
                    "isDepinToken" to it.isDepinToken,
                    "tags" to it.tags,
                    "isOfficial" to it.isOfficial,
                    "price" to it.price,
                    "isMeme" to it.isMeme,
                    "isRobotpump" to it.isRobotPump
                ))
            }

            promise.resolve(Arguments.fromList(tokenList))
        }
    }

    /**
     * @param chainIds: 需要查找的 chainId 列表，目前固定为 [1, 56, 137]
     */
    @ReactMethod
    fun requestETHPrice(chainIds: ReadableArray, promise: Promise) {
        doAsync {
            val list = chainIds.toArrayList()
            val result = list.fold(bundleOf()) { acc, value ->
                if (value is String) {
                    val network = AppDatabase.getInstance(Utils.getApp())
                        .rpcNetworkDao().queryRPCNetworkByChainId(value.toInt())
                    acc.putString(value.toString(), network?.currencyPrice ?: "0")
                }
                return@fold acc
            }
            promise.resolve(Arguments.fromBundle(result))
        }
    }

    /**
     * @param tokens: token address array list
     */
    @ReactMethod
    fun requestRefreshTokenBalance(chainId: Int, tokens: ReadableArray) {
        if (chainId != WalletHelper.getCurChainId()) return
        doAsync {
            val list = tokens.toArrayList()
            if (list.size == 0) {
                EventBus.getDefault().post(MainPullRefresh())
            } else {
                val contractList = ArrayList<String>()
                list.forEach {
                    if (it != null && it is HashMap<*, *>) {
                        val address = it["address"] as? String
                        address?.let {
                            contractList.add(address)
                        }
                    }
                }
                val balanceList = Web3Delegate.getErc20Balance(*contractList.toTypedArray())
                if (balanceList.size == contractList.size) {
                    contractList.forEachIndexed { index, contract ->
                        val address = UserStore.getWalletAddress()
                        val erc20 = AppDatabase.getInstance(Utils.getApp()).tokenCacheDao().queryByAddress(address,chainId, contract)
                        if (address == erc20?.walletAddress && erc20.chainId == WalletHelper.getCurChainId()) {
                            erc20.balance = balanceList[index].toString()
                            if ((balanceList[index]
                                    ?: BigInteger.ZERO) > BigInteger.ZERO && erc20.likeStatus != LIKE_STATUS_UNLIKE
                            ) {
                                erc20.likeStatus = LIKE_STATUS_LIKE
                            }
                            AppDatabase.getInstance(Utils.getApp()).tokenCacheDao().insertOrReplace(erc20)
                        }
                    }
                }
                EventBus.getDefault().post(ActionRefreshEvent())
            }
        }
    }

    @ReactMethod
    fun requestChangeChain(chainId: String, promise: Promise) {
        doAsync {
            val network = AppDatabase.getInstance(Utils.getApp()).rpcNetworkDao()
                .queryRPCNetworkByChainId(chainId.toInt(16))
            if (network == null) {
                ToastUtils.showShort(
                    chainId + " " + chainId.toInt(16) + " " + Utils.getApp()
                        .getString(R.string.chain_id_error)
                )
                promise.resolve(false)
                return@doAsync
            }
            AddChainsUtil.showSwitchNetworkDialog(
                WC2Config.IOPAY_LOGO,
                WC2Config.IOPAY_URL,
                network
            ) {
                Timer().schedule(1000) {
                    promise.resolve(it)
                }
            }
        }
    }

    @ReactMethod
    fun navigatePointTask(id: Int, link: String) {
        "id: $id link: $link".i()
        currentActivity?.let {
            SchemeUtil.goto(it, link)
        }
    }
}