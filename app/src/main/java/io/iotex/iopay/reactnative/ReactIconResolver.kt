package io.iotex.iopay.reactnative

import android.content.Context
import android.graphics.BitmapFactory
import android.graphics.drawable.BitmapDrawable
import android.graphics.drawable.Drawable
import android.net.Uri
import android.os.StrictMode
import com.facebook.react.ReactApplication
import com.facebook.react.views.imagehelper.ResourceDrawableIdHelper
import java.io.FileNotFoundException
import java.io.IOException
import java.io.InputStream
import java.net.URL

fun Context.isDebug(): Bo<PERSON>an {
    return (applicationContext as ReactApplication).reactNativeHost.useDeveloperSupport
}

class ReactIconResolver {
    interface ImagesLoadingListener {
        fun onComplete(drawable: List<Drawable>)
        fun onComplete(drawable: Drawable)
        fun onError(error: Throwable?)
    }

    fun loadIcon(context: Context, uri: String?): Drawable? {
        if (uri == null) return null
        try {
            return getDrawable(context, uri)
        } catch (e: IOException) {
            e.printStackTrace()
        }
        return null
    }

    fun loadIcon(context: Context, uri: String, listener: ImagesLoadingListener) {
        try {
            listener.onComplete(getDrawable(context, uri))
        } catch (e: IOException) {
            listener.onError(e)
        }
    }

    @Throws(IOException::class)
    private fun getDrawable(context: Context, source: String): Drawable {
        var drawable: Drawable?
        if (isLocalFile(Uri.parse(source))) {
            drawable = loadFile(context, source)
        } else {
            drawable = loadResource(context, source)
            if (drawable == null && context.isDebug()) {
                drawable = readJsDevImage(context, source)
            }
        }
        if (drawable == null) throw RuntimeException("Could not load image $source")
        return drawable.mutate()
    }

    @Throws(IOException::class)
    private fun readJsDevImage(context: Context, source: String): Drawable {
        val threadPolicy = adjustThreadPolicyDebug(context)
        val `is` = openStream(context, source)
        val bitmap = BitmapFactory.decodeStream(`is`)
        restoreThreadPolicyDebug(context, threadPolicy)
        return BitmapDrawable(context.resources, bitmap)
    }

    private fun isLocalFile(uri: Uri): Boolean {
        return FILE_SCHEME == uri.scheme
    }

    private fun loadFile(context: Context, uri: String): Drawable {
        val bitmap = BitmapFactory.decodeFile(Uri.parse(uri).path)
        return BitmapDrawable(context.resources, bitmap)
    }

    private fun adjustThreadPolicyDebug(context: Context): StrictMode.ThreadPolicy? {
        var threadPolicy: StrictMode.ThreadPolicy? = null
        if (context.isDebug()) {
            threadPolicy = StrictMode.getThreadPolicy()
            StrictMode.setThreadPolicy(StrictMode.ThreadPolicy.Builder().permitNetwork().build())
        }
        return threadPolicy
    }

    private fun restoreThreadPolicyDebug(context: Context, threadPolicy: StrictMode.ThreadPolicy?) {
        if (context.isDebug() && threadPolicy != null) {
            StrictMode.setThreadPolicy(threadPolicy)
        }
    }

    companion object {
        private const val FILE_SCHEME = "file"
        private fun loadResource(context: Context, iconSource: String): Drawable? {
            return ResourceDrawableIdHelper.getInstance().getResourceDrawable(context, iconSource)
        }

        @Throws(IOException::class)
        private fun openStream(context: Context, uri: String): InputStream? {
            return if (uri.contains("http")) remoteUrl(uri) else localFile(context, uri)
        }

        @Throws(IOException::class)
        private fun remoteUrl(uri: String): InputStream {
            return URL(uri).openStream()
        }

        @Throws(FileNotFoundException::class)
        private fun localFile(context: Context, uri: String): InputStream? {
            return context.contentResolver.openInputStream(Uri.parse(uri))
        }
    }
}