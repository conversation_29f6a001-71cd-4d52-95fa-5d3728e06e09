package io.iotex.iopay.reactnative

import android.content.Intent
import android.os.Bundle
import androidx.appcompat.widget.Toolbar
import com.facebook.react.ReactFragment
import com.facebook.react.ReactRootView
import com.facebook.react.modules.core.DefaultHardwareBackBtnHandler
import io.iotex.base.BaseViewModel
import io.iotex.base.bindbase.BaseBindActivity
import io.iotex.base.language.MultiLanguage
import io.iotex.iopay.BuildConfig
import io.iotex.iopay.IoPayApplication
import io.iotex.iopay.R
import io.iotex.iopay.data.UserStore
import io.iotex.iopay.data.db.Wallet
import io.iotex.iopay.databinding.ActivityReactNativeBinding
import io.iotex.iopay.support.enum.Web3AddressType
import io.iotex.iopay.util.Constant
import io.iotex.iopay.util.WalletHelper

class ReactNativeActivity : BaseBindActivity<BaseViewModel, ActivityReactNativeBinding>(R.layout.activity_react_native), DefaultHardwareBackBtnHandler, ReactNavigateContainer {
    companion object {
        const val REACT_COMPONENT_NAME = "component_name"
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        val component = intent.getStringExtra(REACT_COMPONENT_NAME)
            ?.let { enumValueOf<ReactScene>(it) }

        if (component != null) {
            val bundle = intent.extras ?: Bundle()
            bundle.putString("locale", getLocale())
            getWallet()?.let {
                bundle.putString("address", WalletHelper.convertWeb3Address(it.address))
                val chain = intent.getStringExtra("chain_id")
                if(chain.isNullOrEmpty()){
                    val chainId = WalletHelper.getCurChainId()
                    bundle.putInt("chain_id", chainId)
                }
                if (UserStore.getWeb3Type() == Web3AddressType.WEB3.type) {
                    bundle.putBoolean("showWeb3", true)
                } else {
                    bundle.putBoolean("showWeb3", false)
                }
                bundle.putBoolean("watch", it.isWatch)
            }
            val mode = if (UserStore.isDarkTheme())  "dark" else "light"
            bundle.putString("colorMode", mode)

            bundle.putBoolean("test", packageName == "io.iotex.iopay" || BuildConfig.DEBUG)

            val props = Bundle()
            props.putBundle("passProps", bundle)

            val fragment = ReactFragment.Builder()
                .setComponentName(component.name)
                .setLaunchOptions(props)
                .build()

            supportFragmentManager
                .beginTransaction()
                .add(R.id.reactNativeFragment, fragment)
                .commit()
        }
    }

    fun getReactComponentName(): String? {
        return intent.getStringExtra(REACT_COMPONENT_NAME)
    }

    override fun setToolbar(toolbar: Toolbar) {
        setSupportActionBar(toolbar)
    }

    private fun getReactTag(): Int {
        val frag = supportFragmentManager.findFragmentById(R.id.reactNativeFragment)
        val view = frag?.view as ReactRootView
        return view.rootViewTag
    }

    fun getLocale(): String{
        return MultiLanguage.getSetLanguageType(this).getLanguage()
    }

    fun getWallet(): Wallet? {
        return Constant.currentWallet
    }

    override fun invokeDefaultOnBackPressed() {
        super.onBackPressed()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        IoPayApplication.getInstance()
            .reactNativeHost
            .reactInstanceManager
            .onActivityResult(this, requestCode, resultCode, data)
    }
}
