package io.iotex.iopay.reactnative

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.widget.Toolbar
import androidx.core.os.bundleOf
import androidx.core.view.isVisible
import androidx.lifecycle.lifecycleScope
import com.blankj.utilcode.util.SPUtils
import com.blankj.utilcode.util.Utils
import com.facebook.react.ReactFragment
import com.google.firebase.crashlytics.FirebaseCrashlytics
import io.iotex.base.language.MultiLanguage
import io.iotex.iopay.BuildConfig
import io.iotex.iopay.IoPayApplication
import io.iotex.iopay.R
import io.iotex.iopay.data.UserStore
import io.iotex.iopay.data.db.AppDatabase
import io.iotex.iopay.fragment.base.BaseFragment
import io.iotex.iopay.support.enum.Web3AddressType
import io.iotex.iopay.support.eventbus.NetworkSwitchEvent
import io.iotex.iopay.support.eventbus.RefreshBalanceFinishEvent
import io.iotex.iopay.support.eventbus.SwitchAllNetworkEvent
import io.iotex.iopay.support.eventbus.SwitchWalletEvent
import io.iotex.iopay.support.eventbus.TokenLoadFinishEvent
import io.iotex.iopay.util.Config
import io.iotex.iopay.util.Constant
import io.iotex.iopay.util.Constant.AVATAR_THEME_BLOCKIES
import io.iotex.iopay.util.Constant.AVATAR_THEME_JAZZICONS
import io.iotex.iopay.util.Constant.AVATAR_THEME_ROBOTS
import io.iotex.iopay.util.SPConstant
import io.iotex.iopay.util.SPConstant.SP_WALLET_AVATAR
import io.iotex.iopay.util.WalletHelper
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.jetbrains.anko.doAsync

class ReactNativeFragment : BaseFragment(), ReactNavigateContainer {

    private val component by lazy {
        arguments?.getString(REACT_COMPONENT_NAME)?.let { enumValueOf<ReactScene>(it) }
    }

    private val showStatusBar by lazy {
        arguments?.getBoolean(SHOW_STATUS_BAR)?:false
    }

    private val showBack by lazy {
        arguments?.getBoolean(SHOW_BACK)?:false
    }

    companion object {

        const val REACT_COMPONENT_NAME = "component_name"
        const val SHOW_STATUS_BAR = "showStatusBar"
        const val SHOW_BACK = "showBack"
        fun newInstance(name: String,showStatusBar:Boolean = false,showBack:Boolean = false): ReactNativeFragment {
            val args = Bundle()
            args.putString(REACT_COMPONENT_NAME, name)
            args.putBoolean(SHOW_STATUS_BAR, showStatusBar)
            args.putBoolean(SHOW_BACK, showBack)
            val fragment = ReactNativeFragment()
            fragment.arguments = args
            return fragment
        }
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        EventBus.getDefault().register(this)
        val view = inflater.inflate(R.layout.fragment_react_native, container, false)
        addFragment()
        return view
    }

    private fun addFragment() {
        val errorHandler = CoroutineExceptionHandler { _, e ->
            FirebaseCrashlytics.getInstance().recordException(e)
        }
        lifecycleScope.launch(errorHandler) {
            if (component != null) {
                val bundle = Bundle()
                bundle.putString("locale",
                    context?.let { MultiLanguage.getSetLanguageType(it).getLanguage() })
                Constant.currentWallet?.let {
                    bundle.putString("address", WalletHelper.convertWeb3Address(it.address))
                    val chainId = if(UserStore.getAllNetwork()) 0 else WalletHelper.getCurChainId()
                    bundle.putInt("chain_id", chainId)
                    if (UserStore.getWeb3Type() == Web3AddressType.WEB3.type
                    ) {
                        bundle.putBoolean("showWeb3", true)
                    } else {
                        bundle.putBoolean("showWeb3", false)
                    }
                    bundle.putBoolean("watch", it.isWatch)
                }
                val mode = if (UserStore.isDarkTheme())  "dark" else "light"
                bundle.putString("colorMode", mode)

                bundle.putBoolean("test", requireActivity().packageName == "io.iotex.iopay" || BuildConfig.DEBUG)

                bundle.putBoolean("fake_depin", UserStore.getSwitchRNFakeDepin())

                val avatar = when (SPUtils.getInstance().getInt(SP_WALLET_AVATAR, AVATAR_THEME_ROBOTS)) {
                        AVATAR_THEME_BLOCKIES -> "Blockies"
                        AVATAR_THEME_JAZZICONS -> "Jazzicons"
                        else -> "Robots"
                    }
                bundle.putString("avatar", avatar)
                bundle.putBoolean("showBack", showBack)

                val activatedRpcNode = withContext(Dispatchers.IO) {
                    AppDatabase.getInstance(Utils.getApp()).rpcNetworkNode()
                        .queryRPCNodeActivated(UserStore.getChainId())
                }
                bundle.putString("rpc", activatedRpcNode?.rpcUrl)
                val activatedRpcNodes = withContext(Dispatchers.IO) {
                    AppDatabase.getInstance(Utils.getApp()).rpcNetworkNode()
                        .queryAllRPCNodeActivated()
                }

                bundle.putBundle("rpcBundle", bundleOf(
                    "1" to (activatedRpcNodes.find { it.chainId == 1 }?.rpcUrl ?: ""),
                    "56" to (activatedRpcNodes.find { it.chainId == 56 }?.rpcUrl ?: ""),
                    "137" to (activatedRpcNodes.find { it.chainId == 137 }?.rpcUrl ?: ""),
                    "4689" to (activatedRpcNodes.find { it.chainId == 4689 }?.rpcUrl ?: ""),
                    "4691" to (activatedRpcNodes.find { it.chainId == 4691 }?.rpcUrl ?: ""),
                ))

                val props = Bundle()
                props.putBundle("passProps", bundle)

                val fragment = ReactFragment.Builder()
                    .setComponentName(component?.name)
                    .setLaunchOptions(props)
                    .build()

                childFragmentManager
                    .beginTransaction()
                    .add(R.id.reactNativeFragment, fragment)
                    .commit()
            }
        }
    }

    override fun setToolbar(toolbar: Toolbar) {

    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onNetworkSwitchEvent(event: NetworkSwitchEvent) {
        publishNativeStatusChanged()
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onSwitchAllNetworkEvent(event: SwitchAllNetworkEvent) {
        publishNativeStatusChanged()
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onWalletSwitchEvent(event: SwitchWalletEvent) {
        publishNativeStatusChanged()
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onRefreshBalanceFinishEvent(event: RefreshBalanceFinishEvent) {
        publishRefreshBalanceOrDataEvent()
    }
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event: TokenLoadFinishEvent) {
        publishRefreshBalanceOrDataEvent()
    }

    override fun onDestroy() {
        EventBus.getDefault().unregister(this)
        super.onDestroy()
    }


    private fun publishNativeStatusChanged() {
        doAsync {
            val wallet = WalletHelper.getCurWallet()
            val net = WalletHelper.getCurNetwork()
            val context = IoPayApplication.getInstance().reactNativeHost.reactInstanceManager.currentReactContext
            val eventEmitter = context?.getNativeModule(NativeEventEmitter::class.java)
            val rpc = SPUtils.getInstance().getString(
                SPConstant.SP_RPC_NETWORK_URL,
                Config.IOTEX_RPC_URL
            )
            val showWeb3 = UserStore.getWeb3Type() == Web3AddressType.WEB3.type
            if (net != null && wallet != null) {
                val chainId = if(UserStore.getAllNetwork()) 0 else net.chainId
                eventEmitter?.publishNativeStatus(chainId, wallet.address, rpc, showWeb3)
            }
        }
    }

    private fun publishRefreshBalanceOrDataEvent(){
        doAsync {
            val context = IoPayApplication.getInstance().reactNativeHost.reactInstanceManager.currentReactContext
            val eventEmitter = context?.getNativeModule(NativeEventEmitter::class.java)
            eventEmitter?.publishRefreshBalanceEvent()
        }
    }
}