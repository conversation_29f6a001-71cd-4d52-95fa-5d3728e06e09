package io.iotex.iopay.reactnative

import com.facebook.react.bridge.Promise
import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.bridge.ReactContextBaseJavaModule
import com.facebook.react.bridge.ReactMethod
import com.facebook.react.bridge.ReadableArray
import com.facebook.react.bridge.WritableNativeArray
import io.iotex.iopay.R
import io.iotex.iopay.util.Constant
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.util.extension.i
import io.iotex.iopay.util.extension.toJson
import io.iotex.iopay.wallet.web3.Web3Repository
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import org.web3j.protocol.core.Response

class ReactNativeWeb3Provider(context: ReactApplicationContext) :
    ReactContextBaseJavaModule(context) {
    override fun getName(): String = "NativeWeb3Provider"

    @ReactMethod
    fun jsonRpcFetchFunc(name: String, params: ReadableArray, promise: Promise) {

        "jsonRpcFetchFunc --> $name".i()
        params.toArrayList().toJson().i()

        when (name) {
            "eth_accounts" -> {
                ethAccounts(promise)
            }

            "eth_sendTransaction" -> {
                MainScope().launch {
                    ethSendTransaction(params, promise)
                }
            }

            "eth_signTypedData_v4" -> {
                ethSignTypedData(params, promise)
            }
        }
    }

    private fun ethAccounts(promise: Promise) {
        Constant.currentWallet?.let { wallet ->
            val web3Address = WalletHelper.convertWeb3Address(wallet.address)
            val array = WritableNativeArray().apply {
                pushString(web3Address)
            }
            promise.resolve(array)
        } ?: promise.reject(
            "0",
            reactApplicationContext.getString(R.string.error_get_account)
        )
    }

    private fun ethSendTransaction(params: ReadableArray, promise: Promise) {
        if (WalletHelper.checkWalletExpired()) {
            promise.reject("0", reactApplicationContext.getString(R.string.failure_alert))
            return
        }
        Constant.currentWallet?.let { wallet ->
            val paramsMap = params.getMap(0)
            val from = WalletHelper.convertWeb3Address(wallet.address)
            val to = paramsMap.getString("to") ?: ""
            val data = paramsMap.getString("data") ?: "0x"
            val value = paramsMap.getString("value")
            val gas = paramsMap.getString("gasPrice") ?: ""
            val id = System.currentTimeMillis()
            Web3Repository.signTransaction(
                id,
                from,
                to,
                data,
                value,
                "",
                "",
                "",
                null,
                gas,
                "",
                null
            ){ _, res, error ->
                handleResponse(res, error, promise)
            }
        } ?: promise.reject("0", reactApplicationContext.getString(R.string.failure_alert))
    }

    private fun ethSignTypedData(params: ReadableArray, promise: Promise) {
        if (WalletHelper.checkWalletExpired()) {
            promise.reject("0", reactApplicationContext.getString(R.string.failure_alert))
            return
        }
        val id = System.currentTimeMillis()
        val data = params.getString(1)
        Web3Repository.handleSignTypedData(
            id,
            data,
            "",
            "",
            response = { _: Long, res: String?, _: Response.Error? ->
                if (res != null) {
                    promise.resolve(res)
                } else {
                    promise.reject(IllegalArgumentException("Signed error"))
                }
            }
        )
    }

    private fun handleResponse(res: String?, error: Response.Error?, promise: Promise) {
        MainScope().launch {
            if (res != null) {
                promise.resolve(res)
            } else {
                promise.reject(
                    "0",
                    error?.message ?: reactApplicationContext.getString(R.string.error_get_account)
                )
            }
        }
    }
}
