package io.iotex.iopay.reactnative

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.facebook.react.ReactFragment
import com.google.firebase.crashlytics.FirebaseCrashlytics
import io.iotex.iopay.R
import io.iotex.iopay.fragment.base.BaseFragment
import io.iotex.base.language.MultiLanguage
import io.iotex.iopay.data.UserStore

class ReactNewsFragment: BaseFragment() {
    companion object {
        fun newInstance() = ReactNewsFragment()
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        val view = inflater.inflate(R.layout.fragment_news_react, container, false)

        try {
            val bundle = Bundle()
            bundle.putString("locale",
                context?.let { MultiLanguage.getSetLanguageType(it).getLanguage() })
            val mode = if (UserStore.isDarkTheme())  "dark" else "light"
            bundle.putString("colorMode", mode)
            val props = Bundle()
            props.putBundle("passProps", bundle)
            val fragment = ReactFragment.Builder()
                .setComponentName(ReactScene.News.name)
                .setLaunchOptions(props)
                .build()

            childFragmentManager
                .beginTransaction()
                .add(R.id.reactNativeFragment, fragment)
                .commit()
        }catch (e:Exception){
            e.printStackTrace()
            FirebaseCrashlytics.getInstance().recordException(e)
        }


        return view
    }
}