package io.iotex.iopay.reactnative

enum class ReactScene {
    <PERSON>arningList,
    BuyIOTX,
    SwapToken,
    BuyContainer,
    BuyPage,
    DelegateProfile,
    News,
    CreateStake,
    Settings,
    Notification,
    NotificationDetail,
    StakeRoot,
    DelegateList,
    StakeBucketDetail,
    CreateStakeNew,
    TransferBucket,
    AddDeposit,
    ReStakeBucket,
    CreateStakeNative,
    ChangeCandidate,
    Compound,
    CreateStakeContract,
    CreateStakeContractConfirm,
    WithdrawContractBucket,
    ReStakeContractBucket,
    ReStakeContractBucketV2,
    CreateStakeDelegate,
    CreateStakeAmount,
    CreateStakeDuration,
    CreateStakeConfirm,
    Exchange,
    GiftCenter
}

enum class ReactDestinationScene {
    Staking,
    Dapp,
    Web,
    AddressBook,
    Scan
}