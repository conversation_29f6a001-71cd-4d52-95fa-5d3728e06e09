package io.iotex.iopay.reactnative.dialog

import android.content.DialogInterface
import io.iotex.base.BaseViewModel
import io.iotex.base.bindbase.BaseBindDialog
import io.iotex.iopay.R
import io.iotex.iopay.databinding.DialogBuyEnterTipsBinding

class BuyEnterTipsDialog :
    BaseBindDialog<BaseViewModel, DialogBuyEnterTipsBinding>(R.layout.dialog_buy_enter_tips) {

    var onConfirmClick: ((Boolean) -> Unit)? = null
    var onCancelClick: (() -> Unit)? = null
    override fun initView() {
        mBinding.ivClose.setOnClickListener {
            dismiss()
        }

        mBinding.tvGetIt.setOnClickListener {
            onConfirmClick?.invoke(mBinding.checkbox.isChecked)
            dismiss()
        }
    }

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        onCancelClick?.invoke()
    }

}