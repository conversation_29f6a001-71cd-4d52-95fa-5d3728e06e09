package io.iotex.iopay.reactnative.views

import com.facebook.react.bridge.Arguments
import com.facebook.react.bridge.WritableMap
import com.facebook.react.uimanager.events.Event
import com.facebook.react.uimanager.events.RCTEventEmitter
import com.google.firebase.crashlytics.FirebaseCrashlytics

class NavigationBarEvent(viewTag: Int): Event<NavigationBarEvent>(viewTag) {
    companion object {
        const val EVENT_NAME = "topClick"
    }

    override fun getEventName() = EVENT_NAME

    @Deprecated("Deprecated in Java")
    override fun dispatch(rctEventEmitter: RCTEventEmitter) {
        try {
            rctEventEmitter.receiveEvent(viewTag, eventName, serializeEventData())
        }catch (e:Exception){
            e.printStackTrace()
            FirebaseCrashlytics.getInstance().recordException(e)
        }
    }

    private fun serializeEventData(): WritableMap {
        return Arguments.createMap()
    }
}