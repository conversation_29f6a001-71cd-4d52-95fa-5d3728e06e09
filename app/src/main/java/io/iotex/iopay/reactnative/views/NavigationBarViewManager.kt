package io.iotex.iopay.reactnative.views;

import android.app.Activity
import android.content.ContextWrapper
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.widget.Toolbar
import androidx.core.view.doOnLayout
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import com.facebook.react.bridge.JSApplicationIllegalArgumentException
import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.uimanager.*
import com.facebook.react.uimanager.annotations.ReactProp
import io.iotex.iopay.R
import io.iotex.iopay.reactnative.ReactNativeActivity
import io.iotex.iopay.reactnative.ReactNavigateContainer

class NavigationBarViewManager(private val reactContext: ReactApplicationContext) :
    ViewGroupManager<Toolbar>() {

    companion object {
        private const val COMMAND_CREATE = 1
        private const val RIGHT_BUTTON_CLICK_EVENT = "onRightItemPress"
    }

    override fun getName(): String = "ReactNavigationBarView"

    override fun getCommandsMap() = mapOf("create" to COMMAND_CREATE)

    override fun createViewInstance(reactContext: ThemedReactContext): Toolbar {
        return Toolbar(reactContext)
    }

//    @ReactProp(name = "title")
//    fun setTitle(view: Toolbar, value: String) {
//        view.doOnLayout {
//            getReactContainer(view)?.setTitle(value)
//        }
//    }
//
//    @ReactProp(name = "rightItemTitle")
//    fun setRightItemTitle(view: Toolbar, value: String) {
//        view.doOnLayout {
//            getReactContainer(view)?.setRightItemTitle(value, view.id)
//        }
//    }

    override fun createViewInstance(
        reactTag: Int,
        reactContext: ThemedReactContext,
        initialProps: ReactStylesDiffMap?,
        stateWrapper: StateWrapper?
    ): Toolbar {
        return super.createViewInstance(reactTag, reactContext, initialProps, stateWrapper)
    }

    override fun addView(parent: Toolbar?, child: View?, index: Int) {
        if (index != 0) {
            throw JSApplicationIllegalArgumentException("The only valid indices for child is 0. Got $index instead.")
        }
        child?.layoutParams = ViewGroup.LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.MATCH_PARENT,
        )
//        parent?.doOnLayout {
//            getReactContainer(parent!!)?.setRightItemView(child!!, parent.id)
//        }
    }

    override fun getExportedCustomDirectEventTypeConstants(): Map<String, Any> {
        return mapOf(
            "topClick" to mapOf(
                "registrationName" to RIGHT_BUTTON_CLICK_EVENT
            )
        )
    }

    private fun getReactContainer(view: View): ReactNavigateContainer? {
        try {
            val frag = FragmentManager.findFragment<Fragment>(view).parentFragment
            if (frag is ReactNavigateContainer) {
                return frag
            }
        } catch (_: Exception) {

        }
        val context = view.context as? Activity ?: (view.context as? ContextWrapper)?.baseContext
        return context as? ReactNativeActivity
    }

}