package io.iotex.iopay.reactnative.views

import android.annotation.SuppressLint
import android.view.MotionEvent
import android.view.View
import android.view.ViewParent
import com.facebook.react.bridge.ReactContext
import com.facebook.react.uimanager.JSTouchDispatcher
import com.facebook.react.uimanager.RootViewUtil
import com.facebook.react.uimanager.UIManagerHelper
import com.facebook.react.uimanager.events.EventDispatcher
import com.facebook.react.views.view.ReactViewGroup

@SuppressLint("ViewConstructor")
class NavigationHeaderSubView(context: ReactContext) : ReactViewGroup(context) {
    private var reactWidth = 0
    private var reactHeight = 0
    var type = Type.RIGHT
    var bar: NavigationHeaderView? = null

    val config: NavigationHeaderView?
        get() = (parent as? NavigationHeaderView.CustomToolbar)?.config

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        if (MeasureSpec.getMode(widthMeasureSpec) == MeasureSpec.EXACTLY &&
            MeasureSpec.getMode(heightMeasureSpec) == MeasureSpec.EXACTLY
        ) {
            // dimensions provided by react
            reactWidth = MeasureSpec.getSize(widthMeasureSpec)
            reactHeight = MeasureSpec.getSize(heightMeasureSpec)
            val parent = parent
            if (parent != null) {
                forceLayout()
                (parent as View).requestLayout()
            }
        }
        setMeasuredDimension(reactWidth, reactHeight)
    }

    enum class Type {
        LEFT, CENTER, RIGHT, BACK, SEARCH_BAR
    }
}