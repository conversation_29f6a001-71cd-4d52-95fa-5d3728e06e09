package io.iotex.iopay.reactnative.views

import com.facebook.react.bridge.JSApplicationIllegalArgumentException
import com.facebook.react.module.annotations.ReactModule
import com.facebook.react.touch.JSResponderHandler
import com.facebook.react.uimanager.ReactStylesDiffMap
import com.facebook.react.uimanager.StateWrapper
import com.facebook.react.uimanager.ThemedReactContext
import com.facebook.react.uimanager.ViewGroupManager
import com.facebook.react.uimanager.annotations.ReactProp

@ReactModule(name = NavigationHeaderSubViewManager.REACT_CLASS)
class NavigationHeaderSubViewManager: ViewGroupManager<NavigationHeaderSubView>() {
    override fun getName() = REACT_CLASS

    override fun createViewInstance(context: ThemedReactContext) = NavigationHeaderSubView(context)

    @ReactProp(name = "type")
    fun setType(view: NavigationHeaderSubView, type: String?) {
        view.type = when (type) {
            "left" -> NavigationHeaderSubView.Type.LEFT
            "center" -> NavigationHeaderSubView.Type.CENTER
            "right" -> NavigationHeaderSubView.Type.RIGHT
            "back" -> NavigationHeaderSubView.Type.BACK
            "searchBar" -> NavigationHeaderSubView.Type.SEARCH_BAR
            else -> throw JSApplicationIllegalArgumentException("Unknown type $type")
        }
    }

    companion object {
        const val REACT_CLASS = "ReactNavigationSubView"
    }
}