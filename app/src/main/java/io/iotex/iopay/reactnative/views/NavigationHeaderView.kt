package io.iotex.iopay.reactnative.views

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.content.ContextWrapper
import android.view.Gravity
import android.view.View
import android.widget.TextView
import androidx.appcompat.widget.Toolbar
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import com.facebook.react.bridge.ReactContext
import com.facebook.react.views.view.ReactViewGroup
import com.google.android.material.appbar.AppBarLayout
import io.iotex.iopay.R
import io.iotex.iopay.reactnative.ReactNativeActivity
import io.iotex.iopay.reactnative.ReactNavigateContainer

@SuppressLint("ViewConstructor")
class NavigationHeaderView(context: ReactContext) : ReactViewGroup(context) {
    open class CustomToolbar(context: Context, val config: NavigationHeaderView) : Toolbar(context)

    private val headerSubViews = mutableListOf<NavigationHeaderSubView>()
    var toolbar: Toolbar =
        View.inflate(context, R.layout.react_native_toolbar, null) as Toolbar
    private var isDestroyed = false
    private var isAttachedToWindow = false
    private var title: String? = null


    fun destroy() {
        isDestroyed = true
    }

    override fun onLayout(changed: Boolean, l: Int, t: Int, r: Int, b: Int) {
        if (toolbar.parent != null) {
            toolbar.layout(l, t, r, b)
            toolbar.findViewById<TextView>(R.id.toolbar_title).layout(l, 78, r, b)
        }
    }

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        isAttachedToWindow = true
        onUpdate()
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        isAttachedToWindow = false
    }

    fun addHeaderSubView(subView: NavigationHeaderSubView) {
        headerSubViews.add(subView)
        subView.bar = this
        maybeUpdate()
    }

    fun getHeaderSubView(index: Int): NavigationHeaderSubView {
        return headerSubViews[index]
    }

    val headerSubViewCount get() = headerSubViews.size

    fun removeHeaderSubView(index: Int) {
        headerSubViews.removeAt(index)
        maybeUpdate()
    }

    fun removeAllHeaderSubViews() {
        headerSubViews.clear()
        maybeUpdate()
    }

    fun addHeaderSubView(index: Int, subView: NavigationHeaderSubView) {
        headerSubViews.add(index, subView)
        maybeUpdate()
    }

    fun maybeUpdate() {
        if (parent != null && !isDestroyed) {
            onUpdate()
        }
    }

    fun onUpdate() {
        if (!isAttachedToWindow || isDestroyed) {
            return
        }
        val container = getReactContainer(this) ?: return
//        val activity = (context as? ContextWrapper)?.baseContext as? ReactNativeActivity ?: return
//        activity.setSupportActionBar(toolbar)
        container.setToolbar(toolbar)
        if (toolbar.parent == null) {

            val layout  = LayoutParams(
                LayoutParams.MATCH_PARENT, LayoutParams.WRAP_CONTENT
            )
            toolbar.layoutParams = layout
            this.addView(toolbar)
        }
//        val actionBar = requireNotNull(activity.supportActionBar)
        toolbar.findViewById<TextView>(R.id.toolbar_title).text = title

        toolbar.titleMarginStart = 0
        for (i in toolbar.childCount - 1 downTo 0) {
            if (toolbar.getChildAt(i) is NavigationHeaderSubView) {
                toolbar.removeViewAt(i)
            }
        }
        for (subView in headerSubViews) {
            val params = Toolbar.LayoutParams(AppBarLayout.LayoutParams.WRAP_CONTENT, AppBarLayout.LayoutParams.MATCH_PARENT)
            when (subView.type) {
                NavigationHeaderSubView.Type.LEFT -> {
                    params.gravity = Gravity.START
                }
                NavigationHeaderSubView.Type.CENTER -> {
                    toolbar.findViewById<TextView>(R.id.toolbar_title).text = null
                    params.width = LayoutParams.MATCH_PARENT
                    params.gravity = Gravity.CENTER_HORIZONTAL
                }
                NavigationHeaderSubView.Type.RIGHT -> {
                    params.gravity = Gravity.END
                    params.marginEnd= 22
                }
                else -> {}
            }
            subView.layoutParams = params
            toolbar.addView(subView)
        }
    }

    fun setTitle(title: String?) {
        this.title = title
    }

    private fun getReactContainer(view: View): ReactNavigateContainer? {
        try {
            val frag = FragmentManager.findFragment<Fragment>(view).parentFragment
            if (frag is ReactNavigateContainer) {
                return frag
            }
        } catch (_: Exception) {

        }
        val context = view.context as? Activity ?: (view.context as? ContextWrapper)?.baseContext
        return context as? ReactNativeActivity
    }

    private fun manuallyLayoutChildren(view: View) {
        measure(
            MeasureSpec.makeMeasureSpec(width, MeasureSpec.EXACTLY),
            MeasureSpec.makeMeasureSpec(height, MeasureSpec.EXACTLY)
        )
        toolbar.layout(left, top, right, bottom)
    }
}