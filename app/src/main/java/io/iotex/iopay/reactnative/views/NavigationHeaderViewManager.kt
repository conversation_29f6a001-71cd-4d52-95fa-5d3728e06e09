package io.iotex.iopay.reactnative.views

import android.view.View
import android.view.View.MeasureSpec
import com.facebook.react.module.annotations.ReactModule
import com.facebook.react.uimanager.LayoutShadowNode
import com.facebook.react.uimanager.ThemedReactContext
import com.facebook.react.uimanager.ViewGroupManager
import com.facebook.react.uimanager.annotations.ReactProp
import com.facebook.yoga.YogaMeasureFunction
import com.facebook.yoga.YogaMeasureMode
import com.facebook.yoga.YogaMeasureOutput
import com.facebook.yoga.YogaNode

@ReactModule(name = NavigationHeaderViewManager.REACT_CLASS)
class NavigationHeaderViewManager: ViewGroupManager<NavigationHeaderView>() {
    override fun getName() = REACT_CLASS

    override fun createViewInstance(context: ThemedReactContext) = NavigationHeaderView(context)

    override fun addView(parent: <PERSON>Header<PERSON>ie<PERSON>, child: View, index: Int) {
        if (child !is NavigationHeaderSubView) {
            throw IllegalArgumentException("Children should be of type NavigationHeaderSubView")
        }
        parent.addHeaderSubView(child)
    }

    override fun onDropViewInstance(view: NavigationHeaderView) {
        view.destroy()
    }

    override fun removeAllViews(parent: NavigationHeaderView) {
        parent.removeAllHeaderSubViews()
    }

    override fun removeViewAt(parent: NavigationHeaderView?, index: Int) {
        parent?.removeHeaderSubView(index)
    }

    override fun getChildCount(parent: NavigationHeaderView): Int = parent.headerSubViewCount

    override fun getChildAt(parent: NavigationHeaderView, index: Int): View = parent.getHeaderSubView(index)

//    override fun needsCustomLayoutForChildren() = true

    override fun onAfterUpdateTransaction(parent: NavigationHeaderView) {
        super.onAfterUpdateTransaction(parent)
        parent.onUpdate()
    }

    @ReactProp(name = "title")
    fun setTitle(parent: NavigationHeaderView, title: String?) {
        parent.setTitle(title)
    }

    companion object {
        const val REACT_CLASS = "ReactNavigationBarView"
    }

    class NavigationHeaderShadowNode: LayoutShadowNode(), YogaMeasureFunction {
        init {
            initMeasureFunction()
        }

        private fun initMeasureFunction() {
            this.setMeasureFunction(this)
        }

        override fun measure(
            node: YogaNode?,
            width: Float,
            widthMode: YogaMeasureMode?,
            height: Float,
            heightMode: YogaMeasureMode?
        ): Long {
            return YogaMeasureOutput.make(width, 200.toFloat())
        }
    }
}