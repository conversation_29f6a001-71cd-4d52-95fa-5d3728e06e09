package io.iotex.iopay.reactnative.views

import com.facebook.react.bridge.Arguments
import com.facebook.react.bridge.ReadableArray
import com.facebook.react.bridge.WritableMap
import com.facebook.react.uimanager.SimpleViewManager
import com.facebook.react.uimanager.ThemedReactContext
import com.facebook.react.uimanager.UIManagerHelper
import com.facebook.react.uimanager.annotations.ReactProp
import com.facebook.react.uimanager.events.Event
import com.facebook.react.uimanager.events.RCTEventEmitter
import com.google.firebase.crashlytics.FirebaseCrashlytics
import io.iotex.iopay.ui.widget.StakePickerView
import io.iotex.iopay.util.TokenUtil

class StakePickerViewManager : SimpleViewManager<StakePickerView>() {

    override fun getName(): String {
        return "ReactStakePickerView"
    }

    override fun createViewInstance(context: ThemedReactContext): StakePickerView {
        val pickerView = StakePickerView(context)
        pickerView.setOnSelectedCallback { amountIndex, daysIndex ->
            val data = Arguments.createMap().apply {
                putInt("amount", amountIndex)
                putInt("duration", daysIndex)
            }
            UIManagerHelper.getEventDispatcherForReactTag(context, pickerView.id)
                ?.dispatchEvent(
                    PickerViewEvent(pickerView.id, data)
                )
        }
        return pickerView
    }

    @ReactProp(name = "data")
    fun setData(view: StakePickerView, data: ReadableArray) {
        if (data.size() == 0) return
        val dataMap = mutableMapOf<String, List<String>>()
        data.toArrayList().forEach {
            if (it != null && it is HashMap<*, *>) {
                val key = TokenUtil.weiToTokenBN(it["amount"]?.toString() ?: "0")
                val entry = it["duration"] as? ArrayList<*>
                if (key.isNotBlank() && entry != null) {
                    dataMap[key] = entry.toList() as List<String>
                }
            }
        }
        view.setData(dataMap)
    }

    override fun getExportedCustomBubblingEventTypeConstants(): Map<String, Any> {
        return mapOf(
            "topChange" to mapOf(
                "phasedRegistrationNames" to mapOf(
                    "bubbled" to "onChange"
                )
            )
        )
    }
}

class PickerViewEvent(viewTag: Int, val data: WritableMap) : Event<NavigationBarEvent>(viewTag) {

    override fun getEventName(): String {
        return "topChange"
    }

    @Deprecated("Deprecated in Java")
    override fun dispatch(rctEventEmitter: RCTEventEmitter) {
        try {
            rctEventEmitter.receiveEvent(viewTag, eventName, data)
        } catch (e: Exception) {
            e.printStackTrace()
            FirebaseCrashlytics.getInstance().recordException(e)
        }
    }

}