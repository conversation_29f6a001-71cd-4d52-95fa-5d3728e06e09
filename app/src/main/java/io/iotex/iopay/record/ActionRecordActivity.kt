package io.iotex.iopay.record

import android.annotation.SuppressLint
import androidx.lifecycle.lifecycleScope
import io.iotex.base.BaseViewModel
import io.iotex.iopay.R
import io.iotex.iopay.base.BaseBindToolbarActivity
import io.iotex.iopay.data.UserStore
import io.iotex.iopay.databinding.ActivityActionRecordBinding
import io.iotex.iopay.util.Constant
import io.iotex.iopay.util.FireBaseEvent
import io.iotex.iopay.util.FireBaseUtil
import io.iotex.iopay.util.IoPayConstant
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.xapp.XAppsActivity
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.jetbrains.anko.startActivity

class ActionRecordActivity :
    BaseBindToolbarActivity<BaseViewModel, ActivityActionRecordBinding>(R.layout.activity_action_record) {

    @SuppressLint("CommitTransaction")
    override fun initView() {
        setToolbarTitle(getString(R.string.activities))
        if (!UserStore.getAllNetwork()) {
            setToolbarSubmitImage(R.drawable.icon_action_explorer)
            setToolbarSubmitClick {
                gotoExplorer()
                FireBaseUtil.logFireBase(FireBaseEvent.ACTION_ACTIVITIES_PAGE_CLICK_EXPLORER)
            }
        }
        supportFragmentManager.beginTransaction().add(R.id.flLayout, ActionRecordFragment())
            .commitAllowingStateLoss()
    }

    private fun gotoExplorer() {
        lifecycleScope.launch(Dispatchers.IO) {
            val network = WalletHelper.getCurNetwork() ?: return@launch
            val explorerUrl =
                "${network.explorer}address/${Constant.currentWallet?.getCurNetworkAddress() ?: WalletHelper.getWeb3Address()}"
            startActivity<XAppsActivity>(
                IoPayConstant.BROWSER_URL to explorerUrl
            )
        }
    }
}