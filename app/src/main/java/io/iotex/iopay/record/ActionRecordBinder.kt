package io.iotex.iopay.record

import android.os.Bundle
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isVisible
import com.blankj.utilcode.util.ColorUtils
import com.blankj.utilcode.util.DeviceUtils
import com.blankj.utilcode.util.TimeUtils
import com.blankj.utilcode.util.Utils
import com.drakeet.multitype.ItemViewBinder
import io.iotex.base.bindbase.BaseBindVH
import io.iotex.iopay.R
import io.iotex.iopay.data.UserStore
import io.iotex.iopay.data.db.*
import io.iotex.iopay.databinding.ItemTransactionRecordBinding
import io.iotex.iopay.repo.BitcoinRepo
import io.iotex.iopay.repo.GiftRepo
import io.iotex.iopay.util.Config
import io.iotex.iopay.util.Constant
import io.iotex.iopay.util.DateTimeUtils
import io.iotex.iopay.util.FireBaseEvent
import io.iotex.iopay.util.FireBaseUtil
import io.iotex.iopay.util.TokenUtil
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.util.extension.loadSvgOrImage
import io.iotex.iopay.util.extension.setVisible
import io.iotex.iopay.util.extension.toEvmAddress
import io.iotex.iopay.wallet.solana.SolanaWeb3
import io.iotex.iopay.wallet.web3.Web3Delegate
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import org.web3j.crypto.WalletUtils
import java.math.BigInteger
import java.text.SimpleDateFormat
import java.util.Locale

class ActionRecordBinder :
    ItemViewBinder<ActionRecordEntry, BaseBindVH<ItemTransactionRecordBinding>>() {

    var onShareClick: ((ActionRecordEntry) -> Unit)? = null
    var onEmptyHashClick: ((ActionRecordEntry) -> Unit)? = null
    var onSpeedUpClick: ((ActionRecordEntry) -> Unit)? = null
    var onCancelClick: ((ActionRecordEntry) -> Unit)? = null

    override fun onBindViewHolder(
        holder: BaseBindVH<ItemTransactionRecordBinding>,
        item: ActionRecordEntry
    ) {
        val bind = holder.bind
        setupTime(holder, item)
        setStatus(bind, item)
        setSpeedUp(bind, item)
        when (item.type) {
            ACTION_TYPE_TRANSFER,
            ACTION_TYPE_BITCOIN_TRANSFER,
            ACTION_TYPE_NFT -> {
                setSendValue(bind, item)
            }

            ACTION_TYPE_SWAP -> {
                setSwap(bind, item)
            }

            ACTION_TYPE_UNSTAKE,
            ACTION_TYPE_RESTAKE,
            ACTION_TYPE_RESTAKE_LOCK,
            ACTION_TYPE_RESTAKE_UNLOCK,
            ACTION_TYPE_STAKE,
            ACTION_TYPE_DELEGATE,
            ACTION_TYPE_DEPOSIT_STAKE,
            ACTION_TYPE_TRANSFER_BUCKET,
            ACTION_TYPE_COMPOUND_REGISTER,
            ACTION_TYPE_COMPOUND_UNREGISTER,
            ACTION_TYPE_MIGRATE,
            ACTION_TYPE_WITHDRAW -> {
                setBucket(bind, item)
            }

            else -> {
                setExecution(bind, item)
            }
        }

        bind.ivShare.isVisible =
            item.status == STATUS_SUCCESS && (item.type == ACTION_TYPE_TRANSFER || item.type == ACTION_TYPE_NFT)
        bind.ivShare.setOnClickListener {
            onShareClick?.invoke(item)
        }

        bind.ivChain.isVisible = UserStore.getAllNetwork()
        if (UserStore.getAllNetwork()) {
            item.getChainImage(item.chainId) {
                bind.ivChain.loadSvgOrImage(it, R.drawable.ic_network_default)
            }
        }

        bind.root.setOnClickListener {
            if (item.hash.isNotBlank()) {
                WalletHelper.gotoExplorer(item.hash, item.chainId)
            } else if (item.status == STATUS_FAILED) {
                onEmptyHashClick?.invoke(item)
            }
        }
    }

    override fun onCreateViewHolder(
        inflater: LayoutInflater,
        parent: ViewGroup
    ): BaseBindVH<ItemTransactionRecordBinding> {
        val bind = ItemTransactionRecordBinding.inflate(inflater, parent, false)
        return BaseBindVH(bind)
    }

    private fun setSendValue(bind: ItemTransactionRecordBinding, item: ActionRecordEntry) {
        val address = Constant.currentWallet?.getCurNetworkAddress() ?: UserStore.getWalletAddress()
        if (item.from == address) {
            bind.ivType.setImageResource(R.drawable.icon_action_send)
            bind.tvSendTitle.text = Utils.getApp().getString(R.string.send_symbol, item.symbol)
            if (item.type == ACTION_TYPE_NFT) {
                bind.tvValue.text = if (item.nftTokenType == NFT_TYPE_721)
                    item.symbol + " No." + item.nftTokenId
                else
                    TokenUtil.displayBalance(item.value) + " " + item.symbol + " No." + item.nftTokenId
            } else {
                bind.tvValue.text = if (item.value == "0")
                    TokenUtil.displayBalance(item.value) + " " + item.symbol
                else Utils.getApp().getString(
                    R.string.value_symbol,
                    TokenUtil.displayBalance(item.value), item.symbol
                )
            }
            val toAddress = TokenUtil.textEllipsis(WalletHelper.formatWalletAddress(item.to), 6, 8)
            bind.tvToAddress.text = Utils.getApp().getString(R.string.to_address_format, toAddress)
        } else {
            bind.ivType.setImageResource(R.drawable.icon_action_receive)
            bind.tvSendTitle.text = Utils.getApp().getString(R.string.receive) + " " + item.symbol
            if (item.type == ACTION_TYPE_NFT) {
                bind.tvValue.text = if (item.nftTokenType == NFT_TYPE_721)
                    item.symbol + " No." + item.nftTokenId
                else
                    TokenUtil.displayBalance(item.value) + " " + item.symbol + " No." + item.nftTokenId
            } else {
                bind.tvValue.text = TokenUtil.displayBalance(item.value) + " " + item.symbol
            }
            val fromAddress =
                TokenUtil.textEllipsis(WalletHelper.formatWalletAddress(item.from), 6, 8)
            bind.tvToAddress.text = Utils.getApp().getString(R.string.from) + ":" + fromAddress
        }

    }

    private fun setSwap(bind: ItemTransactionRecordBinding, item: ActionRecordEntry) {
        bind.ivType.setImageResource(R.drawable.icon_action_swap)
        bind.tvSendTitle.text = Utils.getApp().getString(R.string.xrc_swap)
        bind.tvValue.text = Utils.getApp().getString(
            R.string.value_symbol,
            TokenUtil.displayBalance(item.value), item.symbol
        )
        bind.tvToAddress.text = item.origin.ifBlank { "ioPay swap" }
    }

    private fun setBucket(bind: ItemTransactionRecordBinding, item: ActionRecordEntry) {
        bind.ivType.setImageResource(item.getImageResource())
        item.getMethodName {
            bind.tvSendTitle.text = it
        }
        val hasValue = item.value.isNotBlank() && item.value != BigInteger.ZERO.toString()
        bind.tvValue.text = if (hasValue) item.value else ""
        bind.tvToAddress.text = if (WalletUtils.isValidAddress(item.to)) {
            val toAddress = TokenUtil.textEllipsis(WalletHelper.formatWalletAddress(item.to), 6, 8)
            Utils.getApp().getString(R.string.to_address_format, toAddress)
        } else item.to
    }

    private fun setExecution(bind: ItemTransactionRecordBinding, item: ActionRecordEntry) {
        bind.ivType.setImageResource(item.getImageResource())
        item.getMethodName {
            bind.tvSendTitle.text = it
        }
        val hasValue = item.value.isNotBlank() && item.value != BigInteger.ZERO.toString()
        bind.tvValue.text = if (hasValue) Utils.getApp().getString(
            R.string.value_symbol,
            TokenUtil.displayBalance(item.value), item.symbol
        ) else item.name
        bind.tvToAddress.text = if (WalletUtils.isValidAddress(item.to)) {
            val toAddress = TokenUtil.textEllipsis(WalletHelper.formatWalletAddress(item.to), 6, 8)
            Utils.getApp().getString(R.string.to_address_format, toAddress)
        } else item.to.ifBlank { item.origin }
    }

    private fun setStatus(bind: ItemTransactionRecordBinding, item: ActionRecordEntry) {
        when (item.status) {
            STATUS_WAITING -> {
                bind.tvStatus.setBackgroundResource(R.drawable.stroke_shape_action_wait)
                bind.tvStatus.setTextColor(ColorUtils.getColor(R.color.color_fe9020))
                bind.tvStatus.setText(R.string.waiting)
                questStatus(bind, item)
            }

            STATUS_FAILED -> {
                bind.tvStatus.setBackgroundResource(R.drawable.stroke_shape_action_fail)
                bind.tvStatus.setTextColor(ColorUtils.getColor(R.color.color_fb4040))
                bind.tvStatus.setText(R.string.failed)
            }

            STATUS_SUCCESS -> {
                if (item.cancel) {
                    bind.tvStatus.setBackgroundResource(R.drawable.stroke_shape_action_cancel)
                    bind.tvStatus.setTextColor(ColorUtils.getColor(R.color.color_808080))
                    bind.tvStatus.setText(R.string.canceled)
                } else {
                    bind.tvStatus.setBackgroundResource(R.drawable.stroke_shape_action_success)
                    bind.tvStatus.setTextColor(ColorUtils.getColor(R.color.color_00dc9c))
                    bind.tvStatus.setText(R.string.success)
                    val address = Constant.currentWallet?.getCurNetworkAddress() ?: UserStore.getWalletAddress()
                    if (DateTimeUtils.formatYMD(item.timestamp.toLongOrNull()) == DateTimeUtils.formatYMD(TimeUtils.getNowMills())
                        && address == item.from
                    ) {
                        CoroutineScope(Dispatchers.IO).launch {
                            giftTask(item)
                        }
                    }
                }
            }
        }
    }

    private fun questStatus(bind: ItemTransactionRecordBinding, item: ActionRecordEntry) {
        CoroutineScope(Dispatchers.IO).launch {
            val success = if (WalletHelper.isBitcoinNetwork()) {
                val transactionDetail = BitcoinRepo().waitForTransactionDetail(item.hash)
                transactionDetail?.status?.confirmed
            } else if (WalletHelper.isSolanaNetwork()) {
                SolanaWeb3.getTransaction(item.hash)
            } else {
                val receipt = Web3Delegate.queryTransactionReceipt(item.hash)
                receipt?.isStatusOK
            }

            val action = AppDatabase.getInstance(Utils.getApp()).actionRecordDao()
                .queryByHash(WalletHelper.getCurChainId(), item.hash)
            action?.let {
                MainScope().launch {
                    if (success == true) {
                        action.status = STATUS_SUCCESS
                        setStatus(bind, action)
                        giftTask(item)
                    }
                    if (!WalletHelper.isBitcoinNetwork() && success == false) {
                        action.status = STATUS_FAILED
                        setStatus(bind, action)
                    }
                }
            }
        }
    }

    private suspend fun giftTask(item: ActionRecordEntry) {
        if (item.chainId != Config.IOTEX_CHAIN_ID) {
            return
        }
        CoroutineScope(Dispatchers.IO).launch {
            var pointTaskEntry: PointTaskEntry? = null
            if (item.type == ACTION_TYPE_TRANSFER) {
                pointTaskEntry = GiftRepo().hasSendTask()
            }
            if (item.type == ACTION_TYPE_SWAP) {
                pointTaskEntry = GiftRepo().hasSwapTask()
            }
            if (item.type == ACTION_TYPE_STAKE) {
                pointTaskEntry = GiftRepo().hasStakeTask()
            }
            if (pointTaskEntry?.released == true) {
                val result = GiftRepo().updateEvent(pointTaskEntry.id, item.from.toEvmAddress())
                if (result?.ok == true) {
                    logEvent(item)
                }
            }
        }
    }

    private fun logEvent(item: ActionRecordEntry){
        val bundle = Bundle()
        bundle.putString("address", item.from.toEvmAddress())
        bundle.putString("device_id", DeviceUtils.getUniqueDeviceId())
        bundle.putString("amount", item.value)
        if (item.type == ACTION_TYPE_TRANSFER) {
            FireBaseUtil.logFireBase(FireBaseEvent.POINT_TASK_TRANSACTION, bundle)
        }
        if (item.type == ACTION_TYPE_SWAP) {
            FireBaseUtil.logFireBase(FireBaseEvent.POINT_TASK_SWAP, bundle)
        }
        if (item.type == ACTION_TYPE_STAKE) {
            FireBaseUtil.logFireBase(FireBaseEvent.POINT_TASK_STAKE, bundle)
        }
    }

    private fun setupTime(
        holder: BaseBindVH<ItemTransactionRecordBinding>,
        item: ActionRecordEntry
    ) {
        val curFormatTime =
            TimeUtils.millis2String(
                item.timestamp.toLong(),
                SimpleDateFormat("yyyy/MM/dd", Locale.ENGLISH)
            )
        holder.bind.tvTime.text = curFormatTime
        var timestamp = ""
        val lastPosition = holder.adapterPosition - 1
        if (lastPosition >= 0) {
            if (adapterItems[lastPosition] is ActionRecordEntry) {
                timestamp = (adapterItems[lastPosition] as ActionRecordEntry).timestamp
            }
            val lastFormatTime =
                TimeUtils.millis2String(
                    timestamp.toLong(),
                    SimpleDateFormat("yyyy/MM/dd", Locale.ENGLISH)
                )
            holder.bind.tvTime.isVisible = curFormatTime != lastFormatTime
        } else {
            holder.bind.tvTime.setVisible()
        }
        holder.bind.vDivider.isVisible = holder.adapterPosition != adapterItems.size - 1
    }

    private fun setSpeedUp(bind: ItemTransactionRecordBinding, item: ActionRecordEntry) {
        val speed = item.type == ACTION_TYPE_TRANSFER || item.type == ACTION_TYPE_NFT
        bind.llSpeedUp.isVisible = item.hash.isNotBlank() &&
            item.status == STATUS_WAITING && speed && Constant.currentWallet?.isAAWallet() == false
            && item.from == UserStore.getWalletAddress() && !WalletHelper.isSolanaNetwork(item.chainId)
        bind.tvSpeedUp.isVisible = !item.cancel
        bind.tvCancel.isVisible = !item.cancel
        bind.tvSpeedUpCancel.isVisible = item.cancel

        bind.tvSpeedUp.setOnClickListener {
            onSpeedUpClick?.invoke(item)
        }
        bind.tvCancel.setOnClickListener {
            onCancelClick?.invoke(item)
        }
        bind.tvSpeedUpCancel.setOnClickListener {
            onSpeedUpClick?.invoke(item)
        }
    }
}