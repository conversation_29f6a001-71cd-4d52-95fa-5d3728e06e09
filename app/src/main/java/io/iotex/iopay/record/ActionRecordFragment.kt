package io.iotex.iopay.record

import android.os.Bundle
import androidx.core.view.isVisible
import androidx.recyclerview.widget.LinearLayoutManager
import com.drakeet.multitype.MultiTypeAdapter
import io.iotex.base.bindbase.BaseBindFragment
import io.iotex.iopay.R
import io.iotex.iopay.data.UserStore
import io.iotex.iopay.data.db.ActionRecordEntry
import io.iotex.iopay.databinding.FragmentRecordBinding
import io.iotex.iopay.record.dialog.CancelUpDialog
import io.iotex.iopay.record.dialog.SpeedUpDialog
import io.iotex.iopay.support.eventbus.ActionRefreshEvent
import io.iotex.iopay.support.eventbus.NetworkSwitchEvent
import io.iotex.iopay.support.eventbus.SwitchAllNetworkEvent
import io.iotex.iopay.support.eventbus.SwitchWalletEvent
import io.iotex.iopay.util.FireBaseEvent
import io.iotex.iopay.util.FireBaseUtil
import io.iotex.iopay.util.PageEventUtil
import io.iotex.iopay.wallet.dialog.TransferEmptyHashDialog
import io.iotex.iopay.wallet.dialog.TransferShareDialog
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

class ActionRecordFragment :
    BaseBindFragment<ActionRecordViewModel, FragmentRecordBinding>(R.layout.fragment_record) {

    companion object {

        private const val KEY_CHAIN_ID = "chainId"
        private const val KEY_CONTRACT = "contract"
        fun newInstance(chainId:Int = UserStore.getChainId(),contract: String = ""): ActionRecordFragment {
            val args = Bundle()
            args.putInt(KEY_CHAIN_ID, chainId)
            args.putString(KEY_CONTRACT, contract)
            val fragment = ActionRecordFragment()
            fragment.arguments = args
            return fragment
        }
    }

    private val chainId by lazy {
        arguments?.getInt(KEY_CHAIN_ID) ?: 1
    }

    private val contract by lazy {
        arguments?.getString(KEY_CONTRACT) ?: ""
    }

    override fun useEventBus(): Boolean {
        return true
    }

    private val mAdapter = MultiTypeAdapter()

    override fun initView() {
        mAdapter.register(ActionRecordEntry::class.java, ActionRecordBinder()
            .apply {
                onShareClick = {
                    TransferShareDialog(it).show(
                        childFragmentManager,
                        System.currentTimeMillis().toString()
                    )
                    FireBaseUtil.logFireBase(FireBaseEvent.ACTION_ACTIVITIES_TXN_INFO_SHARE)
                }
                onEmptyHashClick = {
                    TransferEmptyHashDialog(it).show(
                        childFragmentManager,
                        System.currentTimeMillis().toString()
                    )
                    FireBaseUtil.logFireBase(FireBaseEvent.ACTION_ACTIVITIES_TXN_INFO)
                }

                onSpeedUpClick = {
                    SpeedUpDialog(it).show(
                        childFragmentManager,
                        System.currentTimeMillis().toString()
                    )
                    FireBaseUtil.logFireBase(FireBaseEvent.ACTION_ACTIVITIES_PENDING_TXN_SPEED_UP)
                }

                onCancelClick = {
                    CancelUpDialog(it).show(
                        childFragmentManager,
                        System.currentTimeMillis().toString()
                    )
                    FireBaseUtil.logFireBase(FireBaseEvent.ACTION_ACTIVITIES_PENDING_TXN_CANCEL)
                }
            })
        mBinding.recyclerView.layoutManager = LinearLayoutManager(requireContext())
        mBinding.recyclerView.adapter = mAdapter

        PageEventUtil.logEvent(PageEventUtil.ACTIVITIES)
    }

    override fun initData() {
        mViewModel.getRecord(chainId, contract)
        mViewModel.recordLiveData.observe(this) {
            mBinding.srfRefresh.isRefreshing = false
            mBinding.llEmpty.isVisible = it.isNullOrEmpty()
            mAdapter.items = it
            mAdapter.notifyDataSetChanged()
        }
        mBinding.srfRefresh.setOnRefreshListener {
            mViewModel.getRecord(chainId, contract)
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onRefreshActionListEvent(event: ActionRefreshEvent) {
        mViewModel.getRecord(chainId, contract)
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onRefreshActionListEvent(event: SwitchWalletEvent) {
        mViewModel.cancelAllJob()
        mViewModel.getRecord(chainId, contract)
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onRefreshActionListEvent(event: NetworkSwitchEvent) {
        mViewModel.getRecord(chainId, contract)
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onSwitchAllNetworkEvent(event: SwitchAllNetworkEvent) {
        mViewModel.getRecord(chainId, contract)
    }

}