package io.iotex.iopay.record

import android.app.Application
import android.os.Bundle
import androidx.lifecycle.MutableLiveData
import com.blankj.utilcode.util.Utils
import io.iotex.base.RetrofitClient
import io.iotex.iopay.api.AnalyserApi
import io.iotex.iopay.base.BaseLaunchVM
import io.iotex.iopay.data.UserStore
import io.iotex.iopay.data.db.ACTION_TYPE_NFT
import io.iotex.iopay.data.db.ACTION_TYPE_TRANSFER
import io.iotex.iopay.data.db.ActionRecordEntry
import io.iotex.iopay.data.db.AppDatabase
import io.iotex.iopay.data.db.NFT_TYPE_721
import io.iotex.iopay.data.db.STATUS_FAILED
import io.iotex.iopay.data.db.STATUS_SUCCESS
import io.iotex.iopay.data.db.STATUS_WAITING
import io.iotex.iopay.support.eventbus.ActionRefreshEvent
import io.iotex.iopay.util.Config
import io.iotex.iopay.util.FireBaseEvent
import io.iotex.iopay.util.FireBaseUtil
import io.iotex.iopay.util.TokenUtil
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.util.extension.toEvmAddress
import io.iotex.iopay.util.extension.toIoAddress
import io.iotex.iopay.wallet.web3.Web3Delegate
import org.greenrobot.eventbus.EventBus

class ActionRecordViewModel(application: Application) : BaseLaunchVM(application) {

    val recordLiveData = MutableLiveData<List<ActionRecordEntry>>()

    fun getRecord(chainId:Int, contract: String) {
        addLaunch {
            val address =
                WalletHelper.getCurWallet()?.getCurNetworkAddress() ?: UserStore.getWalletAddress()
            val recordList = if (contract.isNotBlank()) {
                if (contract.isEmpty()) {
                    AppDatabase.getInstance(Utils.getApp()).actionRecordDao().queryAllByChainId(
                        address, chainId
                    )
                } else {
                    AppDatabase.getInstance(Utils.getApp()).actionRecordDao().queryByContract(
                        address, WalletHelper.getCurChainId(), contract
                    )
                }
            } else {
                if (UserStore.getAllNetwork()) {
                    AppDatabase.getInstance(Utils.getApp()).actionRecordDao().queryAll(
                        address
                    )
                } else {
                    AppDatabase.getInstance(Utils.getApp()).actionRecordDao().queryAllByChainId(
                        address, WalletHelper.getCurChainId()
                    )
                }
            }
            recordLiveData.postValue(recordList)
            updateFireBaseEvent(recordList)
        }
    }

    private fun updateFireBaseEvent(recordList: List<ActionRecordEntry>) {
        addLaunch {
            recordList.forEach { action ->
                val actionTime = action.timestamp.toLongOrNull() ?: System.currentTimeMillis()
                val time = UserStore.getRecordEventTime()
                if (time > actionTime) return@forEach
                if (action.status == STATUS_WAITING) return@forEach
                UserStore.setRecordEventTime(actionTime)
                action.getMethodNameEn {
                    val bundle = Bundle()
                    bundle.putString("method", it)
                    if (action.status == STATUS_FAILED) {
                        FireBaseUtil.logFireBase(FireBaseEvent.ACTION_TRANSFER_METHOD_FAILED, bundle)
                    } else {
                        FireBaseUtil.logFireBase(FireBaseEvent.ACTION_TRANSFER_METHOD, bundle)
                    }

                }
            }
        }
    }

    fun getRecordByApi() {
        val apiService =
            RetrofitClient.createApiService(Config.ACTION_BY_ADDRESS_URL, AnalyserApi::class.java)
        addLaunch {
            val pagination = mutableMapOf(
                "skip" to 0,
                "first" to 100,
            )
            val map = mutableMapOf(
                "address" to UserStore.getWalletAddress().toIoAddress(),
                "pagination" to pagination,
            )
            val actionResponse = apiService.actionByAccount(map)
            val wallet = WalletHelper.getCurWallet()
            val list = actionResponse.actions?.filter {
                it.recipient?.toEvmAddress() == UserStore.getWalletAddress()
            }?.filter {
                (it.timestamp?.toLongOrNull() ?: 0L) * 1000 > (wallet?.timestamp?.toLongOrNull()
                    ?: 0L)
            }
            if (list.isNullOrEmpty()) return@addLaunch
            val actionList = ArrayList<ActionRecordEntry>()
            list.forEach {
                val local = AppDatabase.getInstance(Utils.getApp()).actionRecordDao().queryByHash(
                    WalletHelper.getCurChainId(),
                    it.actHash ?: ""
                )
                if (local == null) {
                    if (WalletHelper.getCurChainId() != Config.IOTEX_CHAIN_ID) return@addLaunch
                    val action = getAction(
                        ((it.timestamp?.toLongOrNull() ?: 0L) * 1000).toString(),
                        it.actHash ?: "",
                        "",
                        it.sender ?: "",
                        it.recipient ?: "",
                        TokenUtil.weiToTokenBN(it.amount ?: ""),
                        type = ACTION_TYPE_TRANSFER,
                        symbol = TokenUtil.getNativeCurrencySymbol(),
                        status = STATUS_SUCCESS,
                    )
                    actionList.add(action)
                }
            }
            AppDatabase.getInstance(Utils.getApp()).actionRecordDao().insertOrReplace(*actionList.toTypedArray())
            EventBus.getDefault().post(ActionRefreshEvent())
        }
        addLaunch {
            val pagination = mutableMapOf(
                "skip" to 0,
                "first" to 100,
            )
            val map = mutableMapOf(
                "address" to UserStore.getWalletAddress().toIoAddress(),
                "pagination" to pagination,
            )
            val actionResponse = apiService.actionXRC20ByAccount(map)
            val wallet = WalletHelper.getCurWallet()
            val list = actionResponse.xrc20?.filter {
                it.recipient?.toEvmAddress() == UserStore.getWalletAddress()
            }?.filter {
                (it.timestamp?.toLongOrNull() ?: 0L) * 1000 > (wallet?.timestamp?.toLongOrNull()
                    ?: 0L)
            }
            if (list.isNullOrEmpty()) return@addLaunch
            val actionList = ArrayList<ActionRecordEntry>()
            list.forEach {
                val local = AppDatabase.getInstance(Utils.getApp()).actionRecordDao().queryByHash(
                    WalletHelper.getCurChainId(),
                    it.actHash ?: ""
                )
                if (local == null) {
                    if (WalletHelper.getCurChainId() != Config.IOTEX_CHAIN_ID) return@addLaunch
                    val erc20 = AppDatabase.getInstance(Utils.getApp()).tokenDao()
                        .queryByAddress(
                            WalletHelper.getCurChainId(),
                            it.contract?.toEvmAddress() ?: ""
                        )
                        ?: return@addLaunch
                    val action = getAction(
                        ((it.timestamp?.toLongOrNull() ?: 0L) * 1000).toString(),
                        it.actHash ?: "",
                        "",
                        it.sender ?: "",
                        it.recipient ?: "",
                        TokenUtil.weiToTokenBN(it.amount ?: "", erc20.decimals.toLong()),
                        type = ACTION_TYPE_TRANSFER,
                        symbol = erc20.symbol,
                        status = STATUS_SUCCESS,
                    )
                    actionList.add(action)
                }
            }
            AppDatabase.getInstance(Utils.getApp()).actionRecordDao().insertOrReplace(*actionList.toTypedArray())
            EventBus.getDefault().post(ActionRefreshEvent())
        }

        addLaunch {
            val pagination = mutableMapOf(
                "skip" to 0,
                "first" to 100,
            )
            val map = mutableMapOf(
                "address" to UserStore.getWalletAddress().toIoAddress(),
                "pagination" to pagination,
            )
            val actionResponse = apiService.actionXRC721ByAccount(map)
            val wallet = WalletHelper.getCurWallet()
            val list = actionResponse.xrc721?.filter {
                it.recipient?.toEvmAddress() == UserStore.getWalletAddress()
            }?.filter {
                (it.timestamp?.toLongOrNull() ?: 0L) * 1000 > (wallet?.timestamp?.toLongOrNull()
                    ?: 0L)
            }
            if (list.isNullOrEmpty()) return@addLaunch
            val actionList = ArrayList<ActionRecordEntry>()
            list.forEach {
                val local = AppDatabase.getInstance(Utils.getApp()).actionRecordDao().queryByHash(
                    WalletHelper.getCurChainId(),
                    it.actHash ?: ""
                )
                if (local == null) {
                    if (WalletHelper.getCurChainId() != Config.IOTEX_CHAIN_ID) return@addLaunch
                    val name = Web3Delegate.erc20Name(it.contract?.toEvmAddress() ?: "")
                    val symbol = Web3Delegate.erc20Symbol(it.contract?.toEvmAddress() ?: "")
                    val action = getAction(
                        ((it.timestamp?.toLongOrNull() ?: 0L) * 1000).toString(),
                        it.actHash ?: "",
                        "",
                        it.sender ?: "",
                        it.recipient ?: "",
                        "",
                        type = ACTION_TYPE_NFT,
                        symbol = symbol,
                        tokenId = it.amount ?: "",
                        name = name,
                        tokenType = NFT_TYPE_721,
                        status = STATUS_SUCCESS,
                    )
                    actionList.add(action)
                }
            }
            AppDatabase.getInstance(Utils.getApp()).actionRecordDao().insertOrReplace(*actionList.toTypedArray())
            EventBus.getDefault().post(ActionRefreshEvent())
        }
    }

    private fun getAction(
        timestamp: String,
        hash: String? = "",
        nonce: String = "",
        from: String = "",
        to: String = "",
        value: String = "",
        data: String = "",
        gasPrice: String = "",
        gasLimit: String = "",
        maxPriorityFee: String = "",
        maxFeePerGas: String = "",
        status: Int = STATUS_WAITING,
        type: Int = ACTION_TYPE_TRANSFER,
        contract: String = "",
        method: String = "",
        name: String = "",
        tokenId: String = "",
        tokenType: String = "",
        symbol: String = "",
        decimals: String = "18",
        origin: String = "",
        cancel: Boolean = false,
    ):ActionRecordEntry{
        val chainId = WalletHelper.getCurChainId()
       return ActionRecordEntry(
            timestamp,
            chainId,
            hash ?: "",
            nonce,
            from.toEvmAddress(),
            to.toEvmAddress(),
            value,
            data,
            gasPrice,
            gasLimit,
            maxPriorityFee,
            maxFeePerGas,
            status,
            type,
            contract,
            method,
            name,
            tokenId,
            tokenType,
            symbol,
            decimals,
            origin,
            cancel
        )
    }
}