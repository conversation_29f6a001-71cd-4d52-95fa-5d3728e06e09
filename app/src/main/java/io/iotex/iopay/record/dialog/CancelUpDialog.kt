package io.iotex.iopay.record.dialog

import android.graphics.Paint
import android.view.Gravity
import android.view.ViewGroup
import io.iotex.base.bindbase.BaseBindDialog
import io.iotex.iopay.R
import io.iotex.iopay.data.UserStore
import io.iotex.iopay.data.db.ACTION_TYPE_NFT
import io.iotex.iopay.data.db.ActionRecordEntry
import io.iotex.iopay.data.db.NFT_TYPE_721
import io.iotex.iopay.databinding.DialogCancelUpBinding
import io.iotex.iopay.util.TokenUtil
import io.iotex.iopay.util.extension.asBigDecimal
import io.iotex.iopay.wallet.viewmodel.TransferViewModel
import java.math.BigDecimal
import java.math.BigInteger


class CancelUpDialog(private val actionRecordEntry: ActionRecordEntry) :
    BaseBindDialog<TransferViewModel, DialogCancelUpBinding>(R.layout.dialog_cancel_up) {

    override fun initView() {
        val maxFeePerGas = (actionRecordEntry.maxFeePerGas.toBigIntegerOrNull() ?: BigInteger.ZERO)
        val maxPriorityFee =
            (actionRecordEntry.maxPriorityFee.toBigIntegerOrNull() ?: BigInteger.ZERO)
        var fee = maxFeePerGas + maxPriorityFee
        if (fee == BigInteger.ZERO) fee =
            actionRecordEntry.gasPrice.asBigDecimal().toBigInteger()

        val afterMaxFeePerGas = maxFeePerGas.toBigDecimal() * BigDecimal(1.25)
        val afterMaxPriorityFee = maxPriorityFee.toBigDecimal() * BigDecimal(1.25)
        var afterFee = afterMaxFeePerGas + afterMaxPriorityFee
        if (afterFee.toBigInteger() == BigInteger.ZERO) afterFee = actionRecordEntry.gasPrice.asBigDecimal() * BigDecimal(1.25)
        showGasFree(afterFee.toBigInteger(), fee)
        mBinding.ivClose.setOnClickListener {
            dismiss()
        }
        mBinding.tvGasOldValue.paint.flags = Paint.STRIKE_THRU_TEXT_FLAG or Paint.ANTI_ALIAS_FLAG
        mBinding.mBtnConfirm.setOnClickListener {
            if (actionRecordEntry.type == ACTION_TYPE_NFT) {
                if (actionRecordEntry.nftTokenType == NFT_TYPE_721) {
                    mViewModel.transferERC721(
                        actionRecordEntry.timestamp,
                        actionRecordEntry.to,
                        actionRecordEntry.contract,
                        actionRecordEntry.name,
                        actionRecordEntry.symbol,
                        actionRecordEntry.value.toBigIntegerOrNull()?: BigInteger.ZERO,
                        actionRecordEntry.gasLimit.toBigIntegerOrNull() ?: BigInteger.ZERO,
                        actionRecordEntry.gasPrice.asBigDecimal().toBigInteger(),
                        actionRecordEntry.maxPriorityFee.toBigIntegerOrNull() ?: BigInteger.ZERO,
                        actionRecordEntry.maxFeePerGas.toBigIntegerOrNull() ?: BigInteger.ZERO,
                        actionRecordEntry.nonce.toBigIntegerOrNull() ?: BigInteger.ZERO,
                    true
                    )
                } else {
                    mViewModel.transferERC1155(
                        actionRecordEntry.timestamp,
                        actionRecordEntry.to,
                        actionRecordEntry.contract,
                        actionRecordEntry.name,
                        actionRecordEntry.symbol,
                        actionRecordEntry.nftTokenId.toBigIntegerOrNull() ?: BigInteger.ZERO,
                        actionRecordEntry.value.toBigIntegerOrNull()?: BigInteger.ZERO,
                        actionRecordEntry.gasLimit.toBigIntegerOrNull() ?: BigInteger.ZERO,
                        actionRecordEntry.gasPrice.asBigDecimal().toBigInteger(),
                        actionRecordEntry.maxPriorityFee.toBigIntegerOrNull() ?: BigInteger.ZERO,
                        actionRecordEntry.maxFeePerGas.toBigIntegerOrNull() ?: BigInteger.ZERO,
                        actionRecordEntry.nonce.toBigIntegerOrNull() ?: BigInteger.ZERO,
                        true
                    )
                }
            } else {
                if (actionRecordEntry.contract.isNotBlank()) {
                    mViewModel.transferERC20(
                        actionRecordEntry.timestamp,
                        actionRecordEntry.to,
                        actionRecordEntry.contract,
                        "",
                        actionRecordEntry.value,
                        actionRecordEntry.decimals.toLongOrNull()?:18L,
                        actionRecordEntry.symbol,
                        actionRecordEntry.gasLimit.toBigIntegerOrNull() ?: BigInteger.ZERO,
                        actionRecordEntry.gasPrice.asBigDecimal().toBigInteger(),
                        actionRecordEntry.maxPriorityFee.toBigIntegerOrNull() ?: BigInteger.ZERO,
                        actionRecordEntry.maxFeePerGas.toBigIntegerOrNull() ?: BigInteger.ZERO,
                        actionRecordEntry.nonce.toBigIntegerOrNull() ?: BigInteger.ZERO,
                        true
                    )
                } else {
                    mViewModel.transferCurrency(
                        actionRecordEntry.timestamp,
                        actionRecordEntry.to,
                        actionRecordEntry.value,
                        actionRecordEntry.data,
                        actionRecordEntry.gasLimit.toBigIntegerOrNull() ?: BigInteger.ZERO,
                        actionRecordEntry.gasPrice.asBigDecimal().toBigInteger(),
                        actionRecordEntry.maxPriorityFee.toBigIntegerOrNull() ?: BigInteger.ZERO,
                        actionRecordEntry.maxFeePerGas.toBigIntegerOrNull() ?: BigInteger.ZERO,
                        actionRecordEntry.nonce.toBigIntegerOrNull(),
                        true
                    )
                }
            }
        }

        mViewModel.confirmLiveData.observe(this){
            dismiss()
        }
    }

    private fun showGasFree(fee: BigInteger,oldFee: BigInteger) {
        val gasFee = fee * (actionRecordEntry.gasLimit.toBigIntegerOrNull() ?: BigInteger.ZERO)
        val oldGasFee = oldFee * (actionRecordEntry.gasLimit.toBigIntegerOrNull() ?: BigInteger.ZERO)
        val money = gasFee.toBigDecimal() * UserStore.getNetWorkPrice().asBigDecimal()
        val moneyOld = oldGasFee.toBigDecimal() * UserStore.getNetWorkPrice().asBigDecimal()
        mBinding.tvGasValue.text =
            TokenUtil.weiToTokenBN(gasFee.toString()) + " " + UserStore.getNetworkSymbol() +
                " ≈ $ " + TokenUtil.weiToTokenBN(money.toString())
        mBinding.tvGasSub.text = getString(
            R.string.gas_price_and_limit, TokenUtil.weiToTokenBN(
                fee.toString()
            ), actionRecordEntry.gasLimit
        )
        mBinding.tvGasOldValue.text = TokenUtil.weiToTokenBN(oldGasFee.toString()) + " " + UserStore.getNetworkSymbol() +
            " ≈ $ " + TokenUtil.weiToTokenBN(moneyOld.toString())
    }

    override fun onStart() {
        super.onStart()
        val attributes = dialog?.window?.attributes
        attributes?.width = ViewGroup.LayoutParams.MATCH_PARENT
        attributes?.height = ViewGroup.LayoutParams.WRAP_CONTENT
        attributes?.gravity = Gravity.BOTTOM
        dialog?.window?.attributes = attributes
    }
}