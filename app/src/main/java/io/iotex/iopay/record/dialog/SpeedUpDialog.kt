package io.iotex.iopay.record.dialog

import android.view.Gravity
import android.view.ViewGroup
import android.widget.TextView
import androidx.lifecycle.ViewModelProvider
import com.blankj.utilcode.util.Utils
import io.iotex.base.bindbase.BaseBindDialog
import io.iotex.iopay.R
import io.iotex.iopay.data.UserStore
import io.iotex.iopay.data.db.ACTION_TYPE_NFT
import io.iotex.iopay.data.db.ActionRecordEntry
import io.iotex.iopay.data.db.NFT_TYPE_721
import io.iotex.iopay.databinding.DialogSpeedUpBinding
import io.iotex.iopay.transaction.EditGas1559Dialog
import io.iotex.iopay.transaction.bean.GasLevel.GAS_LEVEL_MARKET
import io.iotex.iopay.transaction.gas.GasViewModel
import io.iotex.iopay.util.Config
import io.iotex.iopay.util.TokenUtil
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.util.extension.asBigDecimal
import io.iotex.iopay.wallet.dialog.EditGasDialog
import io.iotex.iopay.wallet.viewmodel.TransferViewModel
import java.math.BigDecimal
import java.math.BigInteger

class SpeedUpDialog(private val actionRecordEntry: ActionRecordEntry) :
    BaseBindDialog<TransferViewModel, DialogSpeedUpBinding>(R.layout.dialog_speed_up) {

    private val gasViewModel by lazy {
        ViewModelProvider(this)[GasViewModel::class.java]
            .apply {
                lifecycle.addObserver(this)
            }
    }

    private var level = GAS_LEVEL_MARKET
    private val maxFeePerGas = actionRecordEntry.maxFeePerGas.toBigIntegerOrNull() ?: BigInteger.ZERO
    private val maxPriorityFee = actionRecordEntry.maxPriorityFee.toBigIntegerOrNull() ?: BigInteger.ZERO
    private var afterMaxFeePerGas = maxFeePerGas.toBigDecimal() * BigDecimal(1.25)
    private var afterMaxPriorityFee = maxPriorityFee.toBigDecimal() * BigDecimal(1.25)


    override fun initView() {
        mBinding.tvTitle.text = if(actionRecordEntry.cancel) Utils.getApp().getString(R.string.speed_up_cancellation)
        else Utils.getApp().getString(R.string.speed_up)
        var fee = maxFeePerGas + maxPriorityFee
        if (fee == BigInteger.ZERO) fee =
            actionRecordEntry.gasPrice.asBigDecimal().toBigInteger()
        showGasFree(mBinding.tvGasValue, mBinding.tvGasSub, fee)

        var afterGasPrice = afterMaxFeePerGas + afterMaxPriorityFee
        if (afterGasPrice.toBigInteger() == BigInteger.ZERO) afterGasPrice = actionRecordEntry.gasPrice.asBigDecimal() * BigDecimal(1.25)
        showGasFree(mBinding.tvAfterValue, mBinding.tvAfterGas, afterGasPrice.toBigInteger())
        mBinding.ivClose.setOnClickListener {
            dismiss()
        }
        mBinding.ivEdit.setOnClickListener {
            editGas()
        }
        mBinding.mBtnConfirm.setOnClickListener {
            actionRecordEntry.gasPrice = afterGasPrice.toString()
            if (actionRecordEntry.type == ACTION_TYPE_NFT) {
                if (actionRecordEntry.nftTokenType == NFT_TYPE_721) {
                    mViewModel.transferERC721(
                        actionRecordEntry.timestamp,
                        actionRecordEntry.to,
                        actionRecordEntry.contract,
                        actionRecordEntry.name,
                        actionRecordEntry.symbol,
                        actionRecordEntry.nftTokenId.toBigIntegerOrNull() ?: BigInteger.ZERO,
                        actionRecordEntry.gasLimit.toBigIntegerOrNull() ?: BigInteger.ZERO,
                        actionRecordEntry.gasPrice.asBigDecimal().toBigInteger(),
                        afterMaxPriorityFee.toBigInteger() ?: BigInteger.ZERO,
                        afterMaxFeePerGas.toBigInteger() ?: BigInteger.ZERO,
                        actionRecordEntry.nonce.toBigIntegerOrNull() ?: BigInteger.ZERO
                    )
                } else {
                    mViewModel.transferERC1155(
                        actionRecordEntry.timestamp,
                        actionRecordEntry.to,
                        actionRecordEntry.contract,
                        actionRecordEntry.name,
                        actionRecordEntry.symbol,
                        actionRecordEntry.nftTokenId.toBigIntegerOrNull() ?: BigInteger.ZERO,
                        actionRecordEntry.value.toBigIntegerOrNull() ?: BigInteger.ZERO,
                        actionRecordEntry.gasLimit.toBigIntegerOrNull() ?: BigInteger.ZERO,
                        actionRecordEntry.gasPrice.asBigDecimal().toBigInteger(),
                        afterMaxPriorityFee.toBigInteger() ?: BigInteger.ZERO,
                        afterMaxFeePerGas.toBigInteger() ?: BigInteger.ZERO,
                        actionRecordEntry.nonce.toBigIntegerOrNull() ?: BigInteger.ZERO
                    )
                }
            } else {
                if (actionRecordEntry.contract.isNotBlank()) {
                    mViewModel.transferERC20(
                        actionRecordEntry.timestamp,
                        actionRecordEntry.to,
                        actionRecordEntry.contract,
                        "",
                        actionRecordEntry.value,
                        actionRecordEntry.decimals.toLongOrNull() ?: 18L,
                        actionRecordEntry.symbol,
                        actionRecordEntry.gasLimit.toBigIntegerOrNull() ?: BigInteger.ZERO,
                        actionRecordEntry.gasPrice.asBigDecimal().toBigInteger(),
                        afterMaxPriorityFee.toBigInteger() ?: BigInteger.ZERO,
                        afterMaxFeePerGas.toBigInteger() ?: BigInteger.ZERO,
                        actionRecordEntry.nonce.toBigIntegerOrNull() ?: BigInteger.ZERO
                    )
                } else {
                    mViewModel.transferCurrency(
                        actionRecordEntry.timestamp,
                        actionRecordEntry.to,
                        actionRecordEntry.value,
                        actionRecordEntry.data,
                        actionRecordEntry.gasLimit.toBigIntegerOrNull() ?: BigInteger.ZERO,
                        actionRecordEntry.gasPrice.asBigDecimal().toBigInteger(),
                        afterMaxPriorityFee.toBigInteger() ?: BigInteger.ZERO,
                        afterMaxFeePerGas.toBigInteger() ?: BigInteger.ZERO,
                        actionRecordEntry.nonce.toBigIntegerOrNull()
                    )
                }
            }
        }

        mViewModel.confirmLiveData.observe(this) {
            dismiss()
        }
    }

    override fun initData() {
        gasViewModel.getGasTracker(actionRecordEntry.gasLimit)
    }

    private fun showGasFree(tvValue: TextView, tvGas: TextView, gasPrice: BigInteger) {
        val gasFee = gasPrice * (actionRecordEntry.gasLimit.toBigIntegerOrNull() ?: BigInteger.ZERO)
        val money = gasFee.toBigDecimal() * UserStore.getNetWorkPrice().asBigDecimal()
        tvValue.text =
            TokenUtil.weiToTokenBN(gasFee.toString()) + " " + UserStore.getNetworkSymbol() +
                " ≈ $ " + TokenUtil.weiToTokenBN(money.toString())

        val chainId = WalletHelper.getCurChainId()
        if (chainId == Config.IOTEX_CHAIN_ID || chainId == Config.IOTEX_TEST_CHAIN_ID) {
            tvGas.text = getString(
                R.string.gas_price_and_limit_qev,
                TokenUtil.weiToTokenBN(gasPrice.toString(), 12),
                actionRecordEntry.gasLimit
            )
        } else {
            tvGas.text = getString(
                R.string.gas_price_and_limit,
                TokenUtil.weiToTokenBN(gasPrice.toString(), 9),
                actionRecordEntry.gasLimit
            )
        }
    }

    private fun editGas() {
        if (WalletHelper.isSupport1559Gas()) {
            val maxFeePerGas =
                (actionRecordEntry.maxFeePerGas.toBigIntegerOrNull() ?: BigInteger.ZERO)
            val maxPriorityFee =
                (actionRecordEntry.maxPriorityFee.toBigIntegerOrNull() ?: BigInteger.ZERO)
            EditGas1559Dialog(actionRecordEntry.gasLimit, maxPriorityFee, maxFeePerGas, level)
                .apply {
                    onConfirmCallback = { maxPriorityFeePerGas, maxFeePerGas, level ->
                        afterMaxFeePerGas = maxFeePerGas.toBigDecimal()
                        afterMaxPriorityFee = maxPriorityFeePerGas.toBigDecimal()
                        val afterFee = maxPriorityFeePerGas + maxFeePerGas
                        showGasFree(mBinding.tvAfterValue, mBinding.tvAfterGas, afterFee)
                    }
                }
                .show(requireActivity().supportFragmentManager, EditGas1559Dialog::class.java.name)
        } else {
            val dialog = EditGasDialog()
            dialog.addValues("defGasLimit", actionRecordEntry.gasLimit.toLong())
            dialog.addValues("gasLimit", actionRecordEntry.gasLimit.toLong())
            dialog.addValues("gasPrice", actionRecordEntry.gasPrice)
            dialog.setOnTextChangeCallback { gasLimit, gasPrice ->
                actionRecordEntry.gasLimit = gasLimit.toString()
                actionRecordEntry.gasPrice = gasPrice
                showGasFree(
                    mBinding.tvAfterValue,
                    mBinding.tvAfterGas,
                    gasPrice.asBigDecimal().toBigInteger()
                )
            }
            dialog.commitAddValues()
            dialog.showAllowingStateLoss(activity)
        }
    }

    override fun initEvent() {
        gasViewModel.gasTracker.observe(this) {
            if (afterMaxFeePerGas < it.marketGasFee.toBigDecimal()) {
                afterMaxFeePerGas = it.marketGasFee.toBigDecimal()
                afterMaxPriorityFee = it.marketGasFee.toBigDecimal() - it.baseFee.toBigDecimal()
            } else if (afterMaxFeePerGas < it.highGasFee.toBigDecimal()) {
                afterMaxFeePerGas = it.highGasFee.toBigDecimal()
                afterMaxPriorityFee = it.highGasFee.toBigDecimal() - it.baseFee.toBigDecimal()
            }
            var afterFee = afterMaxFeePerGas + afterMaxPriorityFee
            if (afterFee.toBigInteger() == BigInteger.ZERO) afterFee = actionRecordEntry.gasPrice.asBigDecimal() * BigDecimal(1.25)
            showGasFree(mBinding.tvAfterValue, mBinding.tvAfterGas, afterFee.toBigInteger())
        }
    }

    override fun onStart() {
        super.onStart()
        val attributes = dialog?.window?.attributes
        attributes?.width = ViewGroup.LayoutParams.MATCH_PARENT
        attributes?.height = ViewGroup.LayoutParams.WRAP_CONTENT
        attributes?.gravity = Gravity.BOTTOM
        dialog?.window?.attributes = attributes
    }
}