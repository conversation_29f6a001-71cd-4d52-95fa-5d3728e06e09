package io.iotex.iopay.repo

import com.blankj.utilcode.util.Utils
import com.google.gson.Gson
import io.iotex.base.RetrofitClient
import io.iotex.iopay.R
import io.iotex.iopay.api.BitcoinApi
import io.iotex.iopay.base.BaseLaunchRepo
import io.iotex.iopay.data.db.AppDatabase
import io.iotex.iopay.data.db.STATUS_SUCCESS
import io.iotex.iopay.data.db.WalletCache
import io.iotex.iopay.meta.bean.BitcoinFee
import io.iotex.iopay.meta.bean.BitcoinNft
import io.iotex.iopay.meta.bean.BitcoinTransactionReceipt
import io.iotex.iopay.meta.bean.RPCResult
import io.iotex.iopay.meta.bean.UTXOBundle
import io.iotex.iopay.support.eventbus.ActionRefreshEvent
import io.iotex.iopay.util.Config
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.util.extension.toast
import io.iotex.iopay.wallet.bitcoin.BitcoinHelper
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.withContext
import okhttp3.RequestBody.Companion.toRequestBody
import org.bitcoinj.core.Coin
import org.bitcoinj.core.Sha256Hash
import org.bitcoinj.core.UTXO
import org.bitcoinj.params.TestNet3Params
import org.bitcoinj.script.Script
import org.bouncycastle.util.encoders.Hex
import org.greenrobot.eventbus.EventBus
import java.math.BigInteger

class BitcoinRepo : BaseLaunchRepo() {

    private val waitTimeoutMs = 30000L
    private val waitIntervalMs = 10000L

    private val apiService: BitcoinApi
        get() {
            var url = WalletHelper.getCurNetwork()?.rpc ?: Config.BITCOIN_QUERY_API
            if (!url.endsWith("/")) {
                url = "$url/"
            }
            return RetrofitClient.createApiService(url, BitcoinApi::class.java)
        }

    private val originalApiService: BitcoinApi
        get() {
            var url = WalletHelper.getCurNetwork()?.rpc ?: Config.BITCOIN_QUERY_API
            if (!url.endsWith("/")) {
                url = "$url/"
            }
            return RetrofitClient.createOriginalApiService(url, BitcoinApi::class.java)
        }

    suspend fun getBitcoinBalance(address: String): BigInteger? {
        return kotlin.runCatching {
            val chainId = WalletHelper.getCurChainId()
            val utxoBundle = getUTXOBundle(address)
            val balance = utxoBundle.confirmedUTXOs.sumOf { it.value.value }.toBigInteger()
            val availableBalance = utxoBundle.availableUTXOs.sumOf { it.value.value }.toBigInteger()
            var walletCache = AppDatabase.getInstance(Utils.getApp()).walletCacheDao()
                .queryWalletCache(address, chainId)
            if (walletCache == null) {
                walletCache = WalletCache(chainId, address, balance.toString(), availableBalance.toString())
            } else {
                walletCache.balance = balance.toString()
                walletCache.availableBalance = availableBalance.toString()
            }
            if (chainId != WalletHelper.getCurChainId()) return null
            AppDatabase.getInstance(Utils.getApp()).walletCacheDao().insertOrUpdate(walletCache)
            balance
        }.getOrNull()
    }

    suspend fun getUTXOBundle(address: String): UTXOBundle {
        return kotlin.runCatching {
            val unspentUTOXList = apiService.getUtxoList(address)
            val confirmedList = unspentUTOXList.filter {
                it.status.confirmed
            }.map {
                UTXO(
                    Sha256Hash.wrap(it.txid),
                    it.vout,
                    Coin.valueOf(it.value),
                    it.status.block_height.toInt(),
                    false,
                    Script(Hex.decode(""))
                )
            }.sortedByDescending { it.value.value }
            val nftList = getAllNftList(address)
            val availableUTXOList = confirmedList.filter {utxo ->
                val nft = nftList.firstOrNull { nft ->
                    nft.output.contains(utxo.hash.toString(), true)
                }
                nft == null
            }
            UTXOBundle(confirmedList, availableUTXOList)
        }.onFailure {
            it.printStackTrace()
        }.getOrNull() ?: UTXOBundle(emptyList(), emptyList())
    }

    private suspend fun getAllNftList(address: String): List<BitcoinNft> {
        if (BitcoinHelper.getBitcoinNetworkParams() == TestNet3Params.get()) return emptyList()
        return kotlin.runCatching {
            val list = mutableListOf<BitcoinNft>()
            var page = 1
            do {
                val response = apiService.getNftList(address, page)
                page++
                if (response.code == 1) {
                    list.addAll(response.data.data)
                }
                val `continue` = response.code == 1 && response.data.page * response.data.size < response.data.total
            } while (`continue`)
            list
        }.getOrNull() ?: emptyList()
    }

    suspend fun getBitcoinFee(): BitcoinFee? {
        return kotlin.runCatching {
            apiService.getBitcoinFee()
        }.getOrNull()
    }

    suspend fun broadcastTx(txHex: String): String? {
        return kotlin.runCatching {
            val response = originalApiService.broadcastTx(txHex.toRequestBody())
            if (response.isSuccessful) {
                response.body()?.string()
            } else {
                val originMsg = response.errorBody()?.string() ?: return@runCatching null
                val msg = "{" + originMsg.substringAfter("{")
                val error = Gson().fromJson(msg, RPCResult::class.java)
                if (error?.code == -26) {
                    Utils.getApp().getString(R.string.gas_fee_too_low).toast()
                } else {
                    msg.toast()
                }
                null
            }
        }.onFailure {
            it.printStackTrace()
        }.getOrNull()
    }

    suspend fun getTransactionDetail(transactionId: String): BitcoinTransactionReceipt? {
        return kotlin.runCatching {
            apiService.getTransactionDetail(transactionId)
        }.onFailure {
            it.printStackTrace()
        }.getOrNull()
    }

    suspend fun waitForTransactionDetail(transactionId: String): BitcoinTransactionReceipt? {
        val exceptionHandler = CoroutineExceptionHandler { _, exception ->
            exception.printStackTrace()
        }
        return withContext(Dispatchers.IO + exceptionHandler) {
            val end = System.currentTimeMillis() + waitTimeoutMs
            var transactionReceipt: BitcoinTransactionReceipt? = null
            outer@ while (System.currentTimeMillis() < end) {
                delay(waitIntervalMs)
                transactionReceipt = getTransactionDetail(transactionId)
                if (transactionReceipt?.status?.confirmed == true) {
                    break@outer
                }
            }
            val bitcoinWallet = WalletHelper.getCurWallet()?.getBitcoinWallet()
            if (bitcoinWallet != null) {
                val action = AppDatabase.getInstance(Utils.getApp()).actionRecordDao()
                    .queryByHash(WalletHelper.getCurChainId(), transactionId)
                if (action != null && transactionReceipt?.status?.confirmed == true) {
                    action.status = STATUS_SUCCESS
                    AppDatabase.getInstance(Utils.getApp()).actionRecordDao().insertOrReplace(action)
                    EventBus.getDefault().post(ActionRefreshEvent())
                }
            }
            return@withContext transactionReceipt
        }
    }
}