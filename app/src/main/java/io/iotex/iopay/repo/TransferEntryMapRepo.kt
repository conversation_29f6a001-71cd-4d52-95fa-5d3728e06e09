package io.iotex.iopay.repo

import com.apollographql.apollo.ApolloClient
import com.apollographql.apollo.coroutines.await
import com.blankj.utilcode.util.Utils
import io.iotex.api.CertifiedContractQuery
import io.iotex.base.okHttpClient
import io.iotex.iopay.R
import io.iotex.iopay.base.BaseLaunchRepo
import io.iotex.iopay.data.UserStore
import io.iotex.iopay.data.db.AppDatabase
import io.iotex.iopay.data.db.CertifiedContractEntry
import io.iotex.iopay.data.db.SignatureEntry
import io.iotex.iopay.util.Config
import io.iotex.iopay.util.Constant
import io.iotex.iopay.util.TokenUtil
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.util.extension.asBigDecimal
import io.iotex.iopay.util.extension.toEvmAddress
import io.iotex.iopay.util.extension.toIoAddress
import io.iotex.iopay.wallet.web3.crypto.Numeric
import io.iotex.signature.SignatureQuery
import io.iotex.stake.DelegateQuery
import io.iotex.stake.type.Order_by
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch

class TransferEntryMapRepo : BaseLaunchRepo() {
    private val apolloClient by lazy {
        ApolloClient.builder()
            .serverUrl(Config.IoPayUrl)
            .okHttpClient(okHttpClient)
            .build()
    }

    fun getContractName(contract: String, callBack: ((CertifiedContractEntry) -> Unit)? = null) {
        addLaunch {
            var entry = AppDatabase.getInstance(Utils.getApp()).certifiedContractDao()
                .queryByContract(contract.lowercase())
            if (entry == null) {
                entry = CertifiedContractEntry(contract, "", "", "")
                getCertifiedContract(contract, callBack)
            }
            MainScope().launch {
                callBack?.invoke(entry)
            }
        }
    }

    private fun getCertifiedContract(
        contract: String = "",
        callBack: ((CertifiedContractEntry) -> Unit)? = null
    ) {
        val categoriesQuery = CertifiedContractQuery.builder()
            .build()

        addLaunch {
            apolloClient.query(categoriesQuery).await().data
                ?.certified_contract()?.let { data ->
                    data.forEach {
                        val entry =
                            CertifiedContractEntry(
                                it.contract().lowercase(),
                                it.name(),
                                "",
                                it.option() ?: ""
                            )
                        AppDatabase.getInstance(Utils.getApp()).certifiedContractDao()
                            .insertIfNonExist(entry)
                        MainScope().launch {
                            if (entry.contract == contract) callBack?.invoke(entry)
                        }
                    }
                }
        }
    }

    fun getStakeName(address: String, callBack: ((String?) -> Unit)? = null) {
        addLaunch {
            var name: String? = null
            Constant.delegateList?.forEach {
                if (it.owner_address().equals(address.toIoAddress(), true)) {
                    name = it.name()
                }
            }
            MainScope().launch {
                name?.let {
                    callBack?.invoke(name)
                }
            }
            val contractActionsQuery = DelegateQuery.builder()
                .order_by(Order_by.DESC)
                .build()

            ApolloClient.builder()
                .serverUrl(Config.stakeGatewayUrl)
                .okHttpClient(okHttpClient)
                .build().query(contractActionsQuery)
                .await().data?.iopay_delegate()?.let { list ->
                    Constant.delegateList = list
                    list.forEach {
                        if (it.owner_address().equals(address.toIoAddress(), true)) {
                            name = it.name()
                        }
                    }
                    MainScope().launch {
                        callBack?.invoke(name)
                    }
                }
        }
    }

    fun getWalletOrBookName(address: String,callBack: ((String?) -> Unit)? = null){
        addLaunch {
            val wallet = AppDatabase.getInstance(Utils.getApp()).walletDao().queryWalletByAddress(address.toEvmAddress())
            if(wallet == null){
                val book = AppDatabase.getInstance(Utils.getApp()).addressBookDao().queryByAddress(address.toEvmAddress())
                MainScope().launch {
                    callBack?.invoke(book?.name)
                }
            }else{
                MainScope().launch {
                    callBack?.invoke(wallet.alias)
                }
            }
        }
    }

    fun getTokenBySymbol(
        amount: String,
        symbol: String,
        callBack: ((logo: String?, money: String?) -> Unit)? = null
    ) {
        addLaunch {
            val amountString = amount.replace("-", "").replace("+", "")
            if (symbol == UserStore.getNetworkSymbol()) {
                val network = AppDatabase.getInstance(Utils.getApp()).rpcNetworkDao()
                    .queryRPCNetworkByChainId(WalletHelper.getCurChainId())
                val money =
                    amountString.asBigDecimal().multiply(network?.currencyPrice.asBigDecimal())
                val logo = if(network?.chainId == Config.IOTEX_CHAIN_ID){
                    network.logo
                } else {
                    network?.currencyLogo
                }
                MainScope().launch {
                    callBack?.invoke(
                        logo,
                        "$" + TokenUtil.displayPrice(money.toString())
                    )
                }
            } else {
                AppDatabase.getInstance(Utils.getApp()).tokenDao().queryBySymbol(symbol)
                    .let {
                        val tokenWithLogo =
                            TokenUtil.sortToken(it).firstOrNull { token -> token.logo.isNotEmpty() }
                        val token = tokenWithLogo ?: TokenUtil.sortToken(it).firstOrNull()
                        val money =
                            amountString.asBigDecimal().multiply(token?.price.asBigDecimal())
                        MainScope().launch {
                            callBack?.invoke(
                                token?.logo,
                                "$" + TokenUtil.displayPrice(money.toString())
                            )
                        }
                    }
            }
        }
    }

    fun querySignatures(
        code: String,
        default: String = Utils.getApp().getString(R.string.other_method)
    ): String {
        if (Utils.getApp().getString(R.string.transfer_send) == code) return code
        if (!Numeric.containsHexPrefix(code)) return default

        val signatureEntry = AppDatabase.getInstance(Utils.getApp()).signatureDao()
            .queryById(code)

        return if (signatureEntry != null) {
            when (signatureEntry.signature) {
                "Deposit" -> {
                    Utils.getApp().getString(R.string.deposit)
                }

                "add Liquidity" -> {
                    Utils.getApp().getString(R.string.add_liquidity)
                }

                "Withdraw" -> {
                    Utils.getApp().getString(R.string.withdraw)
                }

                else -> {
                    signatureEntry.signature
                }
            }
        } else {
            querySignaturesRemote(code)
            default
        }
    }

    private fun querySignaturesRemote(code: String) {
        addLaunch {
            val signatureCode = if (code.startsWith("0x")) {
                code.substring(2)
            } else {
                code
            }
            val signatureQuery = SignatureQuery.builder()
                .args(listOf(signatureCode))
                .build()
            apolloClient
                .query(signatureQuery).await().data?.signature()?.forEach {
                    AppDatabase.getInstance(Utils.getApp()).signatureDao()
                        .insert(
                            SignatureEntry(
                                Numeric.toHexString(it.id()),
                                it.en() ?: ""
                            )
                        )
                }
        }
    }

}