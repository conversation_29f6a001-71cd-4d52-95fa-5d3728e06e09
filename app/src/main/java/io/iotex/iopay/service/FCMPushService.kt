package io.iotex.iopay.service

import com.blankj.utilcode.util.AppUtils
import com.blankj.utilcode.util.LogUtils
import com.google.firebase.messaging.FirebaseMessagingService
import com.google.firebase.messaging.RemoteMessage
import io.iotex.iopay.MainActivity
import io.iotex.iopay.util.NotificationHelper

class FCMPushService : FirebaseMessagingService() {
    private val tag = FCMPushService::class.java.simpleName
    override fun onDeletedMessages() {
        super.onDeletedMessages()
        LogUtils.i(tag, "onDeletedMessages")
    }

    override fun onMessageReceived(p0: RemoteMessage) {
        val title = p0.notification?.title ?: AppUtils.getAppName()
        val content = p0.notification?.body ?: AppUtils.getAppName()
        if (!p0.data["link"].isNullOrEmpty() || !p0.data["msg_id"].isNullOrEmpty() || !p0.data["actHash"].isNullOrEmpty()) {
            MainActivity.getIsVisitor {
                NotificationHelper.showNotification(this, title, content, p0.data, it)
            }
        } else {
            super.onMessageReceived(p0)
        }
        LogUtils.i(tag, "onMessageReceived:" + p0.data.toString())
    }

    override fun onNewToken(p0: String) {
        super.onNewToken(p0)
        LogUtils.i(tag, "onNewToken")
    }

    override fun onCreate() {
        super.onCreate()
        LogUtils.i(tag, "onCreate")
    }
}