package io.iotex.iopay.service

import io.iotex.base.okHttpClient
import io.iotex.iopay.util.Config
import okhttp3.Call
import okhttp3.Callback
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.Request
import okhttp3.RequestBody.Companion.toRequestBody
import okhttp3.Response
import org.json.JSONObject
import java.io.IOException
import java.math.BigInteger

object HttpRequestManager {

    fun getRpcBlockTime(rpcUrl: String, block: String, callback: HttpResultCallBack) {
        val hex = BigInteger(block).toString(16)
        val json =
            "{\"jsonrpc\":\"2.0\",\"method\":\"eth_getBlockByNumber\",\"params\": [\"${hex}\",false],\"id\":1}"
        val request = Request.Builder()
            .addHeader("content-type", "application/json")
            .url(rpcUrl)
            .post(
                json.toRequestBody("application/json; charset=utf-8".toMediaTypeOrNull())
            )
            .build()
        okHttpClient.newCall(request).enqueue(object : Callback {
            override fun onFailure(call: Call, e: IOException) {
                callback.failure(e.message ?: "")
            }

            override fun onResponse(call: Call, response: Response) {
                val content = JSONObject(response.body?.string() ?: "")
                val result = content.optJSONObject("result")
                val timestamp = result?.get("timestamp").toString()
                val time = timestamp.substring(2).toBigInteger(16)
                callback.success(time.toString())
            }

        })
    }

    fun getFioAddress(
        fio_address: String,
        chain_code: String,
        token_code: String,
        callback: HttpResultCallBack
    ) {
        val json = JSONObject()
        json.put("fio_address", fio_address)
        json.put("chain_code", chain_code)
        json.put("token_code", token_code)
        val request = Request.Builder()
            .addHeader("content-type", "application/json")
            .url(Config.FioApi + Config.FIO_METHOD_GET_PUB_ADDRESS)
            .post(
                json.toString().toRequestBody("application/json; charset=utf-8".toMediaTypeOrNull())
            )
            .build()
        okHttpClient.newCall(request).enqueue(object : Callback {
            override fun onFailure(call: Call, e: IOException) {
                callback.failure(e.toString())
            }

            override fun onResponse(call: Call, response: Response) {
                try {
                    if (response.code == 200) {
                        response.body?.string()?.let {
                            val content = JSONObject(it)
                            val address = content.optString("public_address")
                            if (address.isNotBlank()) {
                                callback.success(address)
                            } else {
                                callback.failure("error")
                            }
                        } ?: callback.failure("error")
                    } else {
                        callback.failure("error")
                    }
                } catch (e: Exception) {
                    callback.failure("error")
                }
            }
        })
    }

    interface HttpResultCallBack {
        fun failure(error: String)
        fun success(result: String)
    }
}

data class PolygonPriorityFee(
    val blockNumber: Long,
    val blockTime: Long,
    val estimatedBaseFee: Double,
    val fast: PriorityFee,
    val safeLow: PriorityFee,
    val standard: PriorityFee
)
data class PriorityFee(
    val maxFee: Double,
    val maxPriorityFee: Double
)
