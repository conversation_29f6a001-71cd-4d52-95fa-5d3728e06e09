package io.iotex.iopay.setting

import android.content.Intent
import android.net.Uri
import com.blankj.utilcode.util.AppUtils
import com.blankj.utilcode.util.ToastUtils
import io.iotex.iopay.R
import io.iotex.iopay.base.BaseBindToolbarActivity
import io.iotex.iopay.data.UserStore
import io.iotex.iopay.databinding.ActivityAboutIopayBinding
import io.iotex.iopay.util.Config
import io.iotex.iopay.util.Config.FEEDBACK_EMAIL
import io.iotex.iopay.util.FireBaseEvent
import io.iotex.iopay.util.FireBaseUtil
import io.iotex.iopay.util.IoPayConstant
import io.iotex.iopay.util.extension.setVisible
import io.iotex.iopay.viewmodel.setting.WalletSettingViewModel
import io.iotex.iopay.xapp.XAppsActivity
import io.iotex.iopay.wallet.dialog.CommunityDialog
import io.iotex.iopay.wallet.dialog.UpdaterDialog

class AboutIoPayActivity : BaseBindToolbarActivity<WalletSettingViewModel,ActivityAboutIopayBinding>(R.layout.activity_about_iopay) {

    private var updaterDialog:UpdaterDialog? = null

    override fun initView() {
        setToolbarTitle(getString(R.string.about_ioPay))
        mBinding.llCommunity.setOnClickListener {
            val dialog = CommunityDialog()
            dialog.commitAddValues()
            dialog.showAllowingStateLoss(this@AboutIoPayActivity)
        }

        mBinding.llFunctionRequest.setOnClickListener {
            val intent = Intent(this, XAppsActivity::class.java)
            intent.putExtra(IoPayConstant.BROWSER_URL, Config.FEATURE_REQUESTS_URL)
            startActivity(intent)
            FireBaseUtil.logFireBase("action_about_click_feature_requests")
        }

        mBinding.llPprivacyPolicy.setOnClickListener {
            val intent = Intent(this, XAppsActivity::class.java)
            intent.putExtra(IoPayConstant.BROWSER_URL, Config.POLICY_URL)
            startActivity(intent)
        }
        mBinding.llTermsServices.setOnClickListener {
            val intent = Intent(this, XAppsActivity::class.java)
            intent.putExtra(IoPayConstant.BROWSER_URL, Config.TERM_URL)
            startActivity(intent)
        }
        mBinding.mLlFeedback.setOnClickListener {
            val uri: Uri = Uri.parse("mailto:$FEEDBACK_EMAIL")
            val intent = Intent(Intent.ACTION_SENDTO, uri)
            startActivity(Intent.createChooser(intent, getString(R.string.ioPay)))
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_ABOUT_CLICK_FEEDBACK)
        }

        mBinding.mLlTranslation.setOnClickListener {
            val intent = Intent(this, XAppsActivity::class.java)
            intent.putExtra(IoPayConstant.BROWSER_URL, Config.TRANSLATION_CONTRIBUTORS_URL)
            startActivity(intent)
        }

        mBinding.mLlGoogle.setOnClickListener {
            val intent = Intent(this, XAppsActivity::class.java)
            intent.putExtra(IoPayConstant.BROWSER_URL, Config.GOOGLE_PLAY_OPEN_TEST_URL)
            startActivity(intent)
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_SETTINGS_ABOUT_JOIN_BETA)
        }

        var numIoPay = 0
        mBinding.tvIopay.setOnClickListener {
            numIoPay += 1
            if (numIoPay >= 5) {
                val intent = Intent(this, XAppsActivity::class.java)
                intent.putExtra(IoPayConstant.BROWSER_URL, "file:///android_asset/test.html")
                startActivity(intent)
                numIoPay = 0
            }
        }
        mBinding.tvVersion.text = AppUtils.getAppVersionName()
        mBinding.llUpdate.setOnClickListener {
            val update = UserStore.getUpdateApp()
            if (!update) {
                ToastUtils.showShort(R.string.iopay_is_already_the_latest_version)
                return@setOnClickListener
            }
            if(updaterDialog == null){
                mViewModel.versionCodeLiveData.value?.let {
                    updaterDialog = UpdaterDialog(it)
                        .apply {
                            onDismiss = {
                                updaterDialog = null
                            }
                        }
                    updaterDialog?.show(supportFragmentManager,System.currentTimeMillis().toString())
                }
            }
            FireBaseUtil.logFireBase("action_setting_about_update_app")
        }

        mViewModel.fetchAppVersion()
        mViewModel.versionCodeLiveData.observe(this){
            val update = AppUtils.getAppVersionCode() < it.target_version_code()
            UserStore.setUpdateApp(update)
            if (update) {
                mBinding.ivUpdateTag.setVisible()
            }
        }
    }

}