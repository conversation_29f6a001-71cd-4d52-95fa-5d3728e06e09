package io.iotex.iopay.setting

import androidx.appcompat.app.AppCompatDelegate
import com.blankj.utilcode.util.ActivityUtils
import io.iotex.base.BaseViewModel
import io.iotex.iopay.MainActivity
import io.iotex.iopay.R
import io.iotex.iopay.base.BaseBindToolbarActivity
import io.iotex.iopay.data.UserStore
import io.iotex.iopay.databinding.ActivityAppearanceBinding

class AppearanceActivity :
    BaseBindToolbarActivity<BaseViewModel, ActivityAppearanceBinding>(R.layout.activity_appearance) {

    override fun initView() {
        setToolbarTitle(getString(R.string.appearance))
        if (UserStore.isDarkTheme()) {
            mBinding.tvDark.setBackgroundResource(R.drawable.shape_card_back_stroke_gradient)
            mBinding.tvLight.setBackgroundResource(R.drawable.shape_card_back)
        } else {
            mBinding.tvDark.setBackgroundResource(R.drawable.shape_card_back)
            mBinding.tvLight.setBackgroundResource(R.drawable.shape_card_back_stroke_gradient)
        }
        mBinding.tvDark.setOnClickListener {
            mBinding.tvDark.setBackgroundResource(R.drawable.shape_card_back_stroke_gradient)
            mBinding.tvLight.setBackgroundResource(R.drawable.shape_card_back)
            UserStore.setDarkTheme(AppCompatDelegate.MODE_NIGHT_YES)
            AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_YES)
            MainActivity.getIsVisitor {
                ActivityUtils.getActivityList().forEach { activity -> activity.finish() }
                MainActivity.startActivity(this, isVisitor = it)
            }
        }

        mBinding.tvLight.setOnClickListener {
            mBinding.tvDark.setBackgroundResource(R.drawable.shape_card_back)
            mBinding.tvLight.setBackgroundResource(R.drawable.shape_card_back_stroke_gradient)
            UserStore.setDarkTheme(AppCompatDelegate.MODE_NIGHT_NO)
            AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_NO)
            MainActivity.getIsVisitor {
                ActivityUtils.getActivityList().forEach { activity -> activity.finish() }
                MainActivity.startActivity(this, isVisitor = it)
            }
        }
    }
}