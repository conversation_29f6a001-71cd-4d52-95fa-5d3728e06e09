package io.iotex.iopay.setting

import com.blankj.utilcode.util.SPUtils
import com.machinefi.lockscreen.Config.SP_AUTH_BIOMETRICS
import com.machinefi.lockscreen.Config.SP_AUTH_LOGIN
import com.machinefi.lockscreen.Config.SP_AUTH_TRANSFER
import com.machinefi.lockscreen.LockAuthHelper
import io.iotex.base.BaseViewModel
import io.iotex.iopay.R
import io.iotex.iopay.base.BaseBindToolbarActivity
import io.iotex.iopay.databinding.ActivityAuthenticationBinding
import io.iotex.iopay.util.FireBaseEvent
import io.iotex.iopay.util.FireBaseUtil

class AuthenticationActivity :
    BaseBindToolbarActivity<BaseViewModel, ActivityAuthenticationBinding>(R.layout.activity_authentication) {
    override fun initView() {
        setToolbarTitle(getString(R.string.authentication))
        mBinding.switchLogin.isChecked = SPUtils.getInstance().getBoolean(SP_AUTH_LOGIN, true)
        mBinding.switchTransfer.isChecked = SPUtils.getInstance().getBoolean(SP_AUTH_TRANSFER, true)
        mBinding.switchBiometrics.isChecked =
            SPUtils.getInstance().getBoolean(SP_AUTH_BIOMETRICS, true)
        mBinding.switchLogin.setOnClickListener {
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_AUTH_CLICK_BEFORE_LOGIN)
            LockAuthHelper.showAuthLock(this, onCancel = {
                mBinding.switchLogin.isChecked =
                    SPUtils.getInstance().getBoolean(SP_AUTH_LOGIN, true)
            }) {
                val authLogin = !SPUtils.getInstance().getBoolean(SP_AUTH_LOGIN, true)
                SPUtils.getInstance().put(SP_AUTH_LOGIN, authLogin)
            }
        }
        mBinding.switchTransfer.setOnClickListener {
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_AUTH_CLICK_BEFORE_TRANSACTION)
            LockAuthHelper.showAuthLock(this, onCancel = {
                mBinding.switchTransfer.isChecked =
                    SPUtils.getInstance().getBoolean(SP_AUTH_TRANSFER, true)
            }) {
                val authTransfer = !SPUtils.getInstance().getBoolean(SP_AUTH_TRANSFER, true)
                SPUtils.getInstance().put(SP_AUTH_TRANSFER, authTransfer)
            }
        }
        mBinding.switchBiometrics.setOnClickListener {
            LockAuthHelper.showAuthLock(this, onCancel = {
                mBinding.switchBiometrics.isChecked =
                    SPUtils.getInstance().getBoolean(SP_AUTH_BIOMETRICS, true)
            }) {
                val authTransfer = !SPUtils.getInstance().getBoolean(SP_AUTH_BIOMETRICS, true)
                SPUtils.getInstance().put(SP_AUTH_BIOMETRICS, authTransfer)
            }
        }
    }
}