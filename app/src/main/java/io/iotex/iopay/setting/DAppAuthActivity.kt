package io.iotex.iopay.setting

import android.view.View
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.blankj.utilcode.util.Utils
import io.iotex.base.BaseViewModel
import io.iotex.iopay.R
import io.iotex.iopay.base.BaseBindToolbarActivity
import io.iotex.iopay.data.db.AppDatabase
import io.iotex.iopay.data.db.TrustDapp
import io.iotex.iopay.databinding.ActivityDappAuthBinding
import io.iotex.iopay.ui.DappAuthAdapter
import io.iotex.iopay.ui.NoticePopupWindow
import io.iotex.iopay.util.WalletHelper
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.jetbrains.anko.doAsync
import org.jetbrains.anko.uiThread

class DAppAuthActivity :
    BaseBindToolbarActivity<BaseViewModel, ActivityDappAuthBinding>(R.layout.activity_dapp_auth) {

    override fun initView() {
        setToolbarTitle(getString(R.string.clear_auth_cache))
        setToolbarSubmitText(getString(R.string.clear_all))
        setToolbarSubmitClick {
            NoticePopupWindow(
                null,
                getString(R.string.clear_trust_confirm),
                null,
                confirmAction = {
                    doAsync {
                        AppDatabase.getInstance(this@DAppAuthActivity).trustDapp().deleteAll()
                        uiThread {
                            setData()
                        }
                    }
                }).show()
        }
        val linearLayoutManager = LinearLayoutManager(this)
        linearLayoutManager.orientation = LinearLayoutManager.VERTICAL
        mBinding.recyclerView.layoutManager = linearLayoutManager
        setData()
    }

    private fun setData() {
        lifecycleScope.launch {
            val list = withContext(Dispatchers.IO) {
                val wallet = WalletHelper.getCurWallet() ?: return@withContext emptyArray()
                if (WalletHelper.isBitcoinNetwork()) {
                    AppDatabase.getInstance(Utils.getApp()).trustDapp()
                        .queryAllTrustDapp(wallet.getBitcoinWallet()?.bitcoinAddress ?: "")
                } else {
                    AppDatabase.getInstance(Utils.getApp()).trustDapp().queryAllTrustDapp(wallet.address)
                }
            }

            if (list.isNotEmpty()) {
                mBinding.recyclerView.visibility = View.VISIBLE
                mBinding.noHistory.visibility = View.GONE
                val adapter = DappAuthAdapter(list)
                mBinding.recyclerView.adapter = adapter
                adapter.setOnKotlinItemDelClickListener(object : DappAuthAdapter.IKotlinItemDelClickListener {
                    override fun onItemDelClickListener(trustDapp: TrustDapp) {
                        doAsync {
                            AppDatabase.getInstance(Utils.getApp()).trustDapp().delete(trustDapp)
                            setData()
                        }
                    }
                })
            } else {
                mBinding.recyclerView.visibility = View.GONE
                mBinding.noHistory.visibility = View.VISIBLE
            }
        }
    }
}