package io.iotex.iopay.setting

import android.app.Activity
import android.os.Bundle
import android.view.WindowManager
import androidx.core.view.isVisible
import androidx.lifecycle.lifecycleScope
import com.drakeet.multitype.MultiTypeAdapter
import io.iotex.base.BaseViewModel
import io.iotex.iopay.R
import io.iotex.iopay.base.BaseBindToolbarActivity
import io.iotex.iopay.databinding.ActivityExportMnemonicBinding
import io.iotex.iopay.ui.binder.MnemonicBinder
import io.iotex.iopay.ui.binder.MnemonicItemWrapper
import io.iotex.iopay.util.Constant
import io.iotex.iopay.util.EncryptUtil
import io.iotex.iopay.util.FireBaseEvent
import io.iotex.iopay.util.FireBaseUtil
import io.iotex.iopay.util.extension.toast
import io.iotex.iopay.wallet.dialog.CopyMnemonicDialog
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.jetbrains.anko.startActivity

class ExportMnemonicActivity :
    BaseBindToolbarActivity<BaseViewModel, ActivityExportMnemonicBinding>(R.layout.activity_export_mnemonic) {

    private val mAdapter = MultiTypeAdapter()

    private val mEncryptedMnemonic by lazy {
        intent.getStringExtra(KEY_MNEMONIC_FILE)
    }

    private val coinType by lazy {
        intent.getIntExtra(KEY_COIN_TYPE, Constant.COIN_TYPE_IOTEX)
    }

    private lateinit var mMnemonicPhrases: String

    private var mVisible = false

    override fun onCreate(savedInstanceState: Bundle?) {
        window.addFlags(WindowManager.LayoutParams.FLAG_SECURE)
        super.onCreate(savedInstanceState)
    }


    override fun initView() {
        setToolbarTitle(getString(R.string.back_up))
        val binder = MnemonicBinder(showIndex = true)
        mAdapter.register(MnemonicItemWrapper::class.java, binder)
        mBinding.mRvMnemonic.adapter = mAdapter
        loadMnemonic()

        var des = String.format(getString(R.string.phrase_hd_path_bip44_standard), coinType.toString())
        if(coinType == Constant.COIN_TYPE_IOTEX){
            des = des.replace("EVM","IOTEX")
        } else if(coinType == Constant.COIN_TYPE_SOLANA){
            des = des.replace("EVM","Solana")
        } else if(coinType == Constant.COIN_TYPE_BITCOIN){
            des = des.replace("EVM","Bitcoin")
        }
        mBinding.mTvPath.text = des

        mBinding.mIvCopy.setOnClickListener {
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_SETTINGS_MANAGE_WALLET_RECOVERY_REVEAL_COPY)
            CopyMnemonicDialog(mMnemonicPhrases).show(supportFragmentManager, "copy_mnemonic")
        }

        mBinding.mIvViewer.setOnClickListener {
            changeVisible()
        }

        mBinding.llEye.setOnClickListener {
            changeVisible()
        }
    }

    private fun changeVisible() {
        mVisible = !mVisible
        if (mVisible) {
            mBinding.mIvViewer.setImageResource(R.drawable.icon_eye_open_with_bg)
        } else {
            mBinding.mIvViewer.setImageResource(R.drawable.icon_eye_close_with_bg)
        }
        mBinding.llEye.isVisible = !mVisible
    }

    private fun loadMnemonic() {
        val errorHandler = CoroutineExceptionHandler { _, e ->
            e.message?.toast()
        }
        lifecycleScope.launch(Dispatchers.IO + errorHandler) {
            if (mEncryptedMnemonic.isNullOrBlank()) {
                getString(R.string.mnemonic_invalid).toast()
                onBackPressed()
                return@launch
            }
            mMnemonicPhrases = EncryptUtil.decrypt(mEncryptedMnemonic!!)
            withContext(Dispatchers.Main) {
                renderMnemonic()
            }
        }
    }

    private fun renderMnemonic() {
        val items = mMnemonicPhrases.split(" ").map { MnemonicItemWrapper(it) }
        mAdapter.items = items
        mAdapter.notifyDataSetChanged()
    }

    companion object {
        const val KEY_MNEMONIC_FILE = "key_mnemonic"
        const val KEY_COIN_TYPE = "key_coin_type"

        fun start(context: Activity, encryptedMnemonic: String, coinType: Int) {
            context.startActivity<ExportMnemonicActivity>(
                KEY_MNEMONIC_FILE to encryptedMnemonic,
                KEY_COIN_TYPE to coinType
            )
        }
    }
}