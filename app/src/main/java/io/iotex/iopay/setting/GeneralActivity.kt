package io.iotex.iopay.setting

import android.content.Context
import android.content.Intent
import android.graphics.Typeface
import android.view.View
import com.blankj.utilcode.util.SPUtils
import com.blankj.utilcode.util.ToastUtils
import io.iotex.base.language.MultiLanguage
import io.iotex.iopay.BuildConfig
import io.iotex.iopay.R
import io.iotex.iopay.base.BaseBindToolbarActivity
import io.iotex.iopay.data.UserStore
import io.iotex.iopay.databinding.ActivityGeneralBinding
import io.iotex.iopay.meta.SP_KEY_GEO_LOCATION_ERROR
import io.iotex.iopay.meta.SP_KEY_W3B_STREAM_MENU_LOCAL
import io.iotex.iopay.meta.ui.w3bstream.W3bStreamActivity
import io.iotex.iopay.support.enum.Web3AddressType
import io.iotex.iopay.support.eventbus.RefreshNetworkListEvent
import io.iotex.iopay.support.eventbus.SwitchWalletEvent
import io.iotex.iopay.support.eventbus.WalletAvatarChangeEvent
import io.iotex.iopay.util.*
import io.iotex.iopay.util.SPConstant.SP_WALLET_AVATAR
import io.iotex.iopay.util.extension.loadSvgOrImage
import io.iotex.iopay.util.extension.setGone
import io.iotex.iopay.util.extension.setVisible
import io.iotex.iopay.util.extension.toast
import io.iotex.iopay.viewmodel.setting.GeneralViewModel
import org.greenrobot.eventbus.EventBus
import org.jetbrains.anko.startActivity

class GeneralActivity :
    BaseBindToolbarActivity<GeneralViewModel, ActivityGeneralBinding>(R.layout.activity_general) {

    companion object {
        private const val KEY_EMPTY_WALLET = "key_empty_wallet"
        fun startActivity(context: Context?, empty: Boolean) {
            val intent = Intent(context, GeneralActivity::class.java)
            intent.putExtra(KEY_EMPTY_WALLET, empty)
            context?.startActivity(intent)
        }
    }

    private val emptyWallet by lazy {
        intent?.getBooleanExtra(KEY_EMPTY_WALLET, false) ?: false
    }

    override fun onResume() {
        super.onResume()
        val error = SPUtils.getInstance().getBoolean(SP_KEY_GEO_LOCATION_ERROR)
        if (error) {
            mBinding.viewGeoRad.visibility = View.VISIBLE
        } else {
            mBinding.viewGeoRad.visibility = View.GONE
        }
    }

    override fun initView() {
        setToolbarTitle(getString(R.string.general))

        mBinding.tvLanguage.text = MultiLanguage.getSetLanguageString(this)
        mBinding.llLanguage.setOnClickListener {
            LanguageSwitchActivity.startActivity(this@GeneralActivity)
            FireBaseUtil.logFireBase(FireBaseEvent.CLICK_SWITCH_LANGUAGE)
        }

        mBinding.llGeo.setOnClickListener {
            startActivity<W3bStreamActivity>()
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_SETTING_W3BSTREAM)
            PageEventUtil.logEvent(PageEventUtil.GEOLOCATION)
        }

        mBinding.llAppearance.setOnClickListener {
            startActivity(Intent(this, AppearanceActivity::class.java))
        }

        mBinding.SwitchPush.setOnCheckedChangeListener { _, isChecked ->
            Constant.currentWallet?.let {
                val deviceToken = SPUtils.getInstance().getString(IoPayConstant.DEVICE_TOKEN, "")
                if (deviceToken.isEmpty()) {
                    NotificationHelper.registerFirebaseMessaging(callback = { token ->
                        if (token != null) {
                            mViewModel.updateNotifyPushAll(
                                token,
                                isChecked
                            )
                        } else {
                            getString(R.string.failed).toast()
                        }
                    })
                } else {
                    mViewModel.updateNotifyPushAll(
                        deviceToken,
                        isChecked
                    )
                }
            }
        }
        mBinding.SwitchNewsLetter.setOnCheckedChangeListener { _, isChecked ->
            val deviceToken = SPUtils.getInstance().getString(IoPayConstant.DEVICE_TOKEN, "")
            if (deviceToken.isEmpty()) {
                NotificationHelper.registerFirebaseMessaging(callback = { token ->
                    if (token != null) {
                        mViewModel.updateNewsLetterPush(token, isChecked)
                    } else {
                        getString(R.string.failed).toast()
                    }
                })
            } else {
                mViewModel.updateNewsLetterPush(deviceToken, isChecked)
            }
        }
        val type = UserStore.getWeb3Type()
        mBinding.SwitchEthAddress.setSwitchTypeface(
            Typeface.createFromAsset(
                assets,
                "fonts/Roboto-Regular.ttf"
            )
        )
        mBinding.SwitchEthAddress.isChecked = type == Web3AddressType.WEB3.type
        mBinding.SwitchEthAddress.setOnCheckedChangeListener { _, isChecked ->
            if (!isChecked) {
                UserStore.setWeb3Type(Web3AddressType.IO)
            } else {
                UserStore.setWeb3Type(Web3AddressType.WEB3)
            }
            EventBus.getDefault().post(SwitchWalletEvent())
        }
        when {
            BuildConfig.AUTO_UPDATE_APK -> {
                mBinding.llPushArea.visibility = View.GONE
            }

            else -> {
                mBinding.llPushArea.visibility = View.VISIBLE
                queryPushNotification()
            }
        }

        val devMode = SPUtils.getInstance().getBoolean(SPConstant.SP_DEVELOPER_MODE, false)
        mBinding.switchDeveloperMode.setSwitchTypeface(
            Typeface.createFromAsset(
                assets,
                "fonts/Roboto-Regular.ttf"
            )
        )
        mBinding.switchDeveloperMode.isChecked = devMode
        mBinding.switchDeveloperMode.setOnCheckedChangeListener { _, isChecked ->
            SPUtils.getInstance().put(SPConstant.SP_DEVELOPER_MODE, isChecked)
            EventBus.getDefault().post(RefreshNetworkListEvent())
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_NETWORK_DEVELOPER_MODE)
        }

        if (WalletHelper.getCurChainId() != Config.IOTEX_CHAIN_ID && WalletHelper.getCurChainId() != Config.IOTEX_TEST_CHAIN_ID) {
            mBinding.llWeb3.setGone()
        }
        initWalletAvatar()
        mBinding.llRobots.setOnClickListener {
            SPUtils.getInstance().put(SP_WALLET_AVATAR, Constant.AVATAR_THEME_ROBOTS)
            updateWallet()
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_SETTING_GENERAL_AVATAR)
        }
        mBinding.llBlock.setOnClickListener {
            SPUtils.getInstance().put(SP_WALLET_AVATAR, Constant.AVATAR_THEME_BLOCKIES)
            updateWallet()
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_SETTING_GENERAL_AVATAR)
        }
        mBinding.llJazz.setOnClickListener {
            SPUtils.getInstance().put(SP_WALLET_AVATAR, Constant.AVATAR_THEME_JAZZICONS)
            updateWallet()
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_SETTING_GENERAL_AVATAR)
        }

        if (emptyWallet) {
            mBinding.llTransferPush.setGone()
            mBinding.llWeb3.setGone()
            mBinding.llDevelop.setGone()
            mBinding.llAvatar.setGone()
            mBinding.llGeoLayout.setGone()
        }
    }

    override fun initData() {
        mViewModel.getPebbleMenu()
        mViewModel.menuOpenLiveData.observe(this) {
            if (emptyWallet) return@observe
            //menuService || menuLocal, well be show
            if (it) {
                mBinding.llGeoLayout.setVisible()
            } else {
                openWebStreamMenu()
            }
        }
    }

    private fun openWebStreamMenu() {
        val menu = SPUtils.getInstance().getBoolean(SP_KEY_W3B_STREAM_MENU_LOCAL, false)
        if (menu) {
            mBinding.llGeoLayout.setVisible()
        } else {
            mBinding.llGeoLayout.setGone()
        }
    }

    private fun updateWallet() {
        EventBus.getDefault().post(WalletAvatarChangeEvent())
        initWalletAvatar()
    }

    private fun initWalletAvatar() {
        Constant.currentWallet?.let { wallet ->
            val address = if (WalletHelper.isBitcoinNetwork()) {
                wallet.getBitcoinWallet()?.bitcoinAddress ?: ""
            } else {
                wallet.address
            }
            val url =
                WalletHelper.getWalletAvatarRobots(address)
            val ethUrl =
                WalletHelper.getWalletAvatarBlockies(address)
            val url3 =
                WalletHelper.getWalletAvatarJazzicons(address)
            mBinding.ivRobots.loadSvgOrImage(url, R.drawable.icon_wallet_default)
            mBinding.ivBlock.loadSvgOrImage(ethUrl, R.drawable.icon_wallet_default)
            mBinding.ivJazz.loadSvgOrImage(url3, R.drawable.icon_wallet_default)
            mBinding.llRobots.setBackgroundResource(R.drawable.shape_card_back)
            mBinding.llBlock.setBackgroundResource(R.drawable.shape_card_back)
            mBinding.llJazz.setBackgroundResource(R.drawable.shape_card_back)
            when (SPUtils.getInstance().getInt(SP_WALLET_AVATAR, Constant.AVATAR_THEME_ROBOTS)) {
                Constant.AVATAR_THEME_BLOCKIES -> {
                    mBinding.llBlock.setBackgroundResource(R.drawable.shape_card_back_stroke_gradient)
                }

                Constant.AVATAR_THEME_JAZZICONS -> {
                    mBinding.llJazz.setBackgroundResource(R.drawable.shape_card_back_stroke_gradient)
                }

                else -> {
                    mBinding.llRobots.setBackgroundResource(R.drawable.shape_card_back_stroke_gradient)
                }
            }
        }
    }

    private fun queryPushNotification() {
        val deviceToken = SPUtils.getInstance().getString(IoPayConstant.DEVICE_TOKEN, "")
        if (deviceToken.isEmpty()) {
            NotificationHelper.registerFirebaseMessaging(callback = { token ->
                if (token != null) {
                    mViewModel.queryNotifyPush(token)
                    mViewModel.queryNewsLetterPush(token)
                }
            })
        } else {
            mViewModel.queryNotifyPush(deviceToken)
            mViewModel.queryNewsLetterPush(deviceToken)
        }
        mViewModel.newsLetterPushLiveData.observe(this) {
            if (it != null) {
                mBinding.SwitchNewsLetter.isChecked = it
            } else {
                ToastUtils.showShort(R.string.failed)
            }
        }

        mViewModel.notifyPushLiveData.observe(this) {
            if (it != null) {
                mBinding.SwitchPush.isChecked = it
                UserStore.setPushRegister(it)
            } else {
                ToastUtils.showShort(R.string.failed)
            }
        }
    }
}