package io.iotex.iopay.setting

import com.blankj.utilcode.util.ClipboardUtils
import com.blankj.utilcode.util.ToastUtils
import io.iotex.base.BaseViewModel
import io.iotex.iopay.R
import io.iotex.iopay.base.BaseBindToolbarActivity
import io.iotex.iopay.databinding.ActivityInviteFriendBinding
import io.iotex.iopay.setting.dialog.InvitedShareDialog

class InviteFriendActivity :
    BaseBindToolbarActivity<InviteFriendViewModel, ActivityInviteFriendBinding>(R.layout.activity_invite_friend) {

    override fun initView() {
        setToolbarTitle(getString(R.string.invite_your_friends_to_iopay))
        mBinding.ivCopyCode.setOnClickListener {
            ClipboardUtils.copyText(mBinding.tvCode.text)
            ToastUtils.showShort(R.string.copy_success)
        }
        mBinding.ivCopyLink.setOnClickListener {
            ClipboardUtils.copyText(mBinding.tvLink.text)
            ToastUtils.showShort(R.string.copy_success)
        }
        mBinding.tvInvite.setOnClickListener {
            InvitedShareDialog(mBinding.tvCode.text.toString()).show(supportFragmentManager, System.currentTimeMillis().toString())
        }
    }

    override fun initData() {
        mViewModel.getUserInfo()
        mViewModel.userInfoLiveData.observe(this){
            mBinding.tvCode.text = it.userId
        }
    }
}