package io.iotex.iopay.setting

import android.app.Application
import androidx.lifecycle.MutableLiveData
import io.iotex.iopay.base.BaseLaunchVM
import io.iotex.iopay.data.UserStore
import io.iotex.iopay.data.bean.GiftUserInfo
import io.iotex.iopay.repo.GiftRepo

class InviteFriendViewModel(application: Application) : BaseLaunchVM(application) {

    val userInfoLiveData = MutableLiveData<GiftUserInfo>()
    fun getUserInfo(){
        addLaunch {
            val userInfo = GiftRepo().getGiftUserInfo(UserStore.getWalletAddress())
            userInfoLiveData.postValue(userInfo)
        }
    }
}