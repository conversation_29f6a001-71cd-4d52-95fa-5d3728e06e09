package io.iotex.iopay.setting

import android.content.Context
import android.content.Intent
import androidx.recyclerview.widget.LinearLayoutManager
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.SPUtils
import com.blankj.utilcode.util.Utils
import com.drakeet.multitype.MultiTypeAdapter
import io.iotex.base.BaseViewModel
import io.iotex.base.language.LanguageType
import io.iotex.base.language.MultiLanguage
import io.iotex.base.language.SP_LANGUAGE
import io.iotex.iopay.MainActivity
import io.iotex.iopay.R
import io.iotex.iopay.base.BaseBindToolbarActivity
import io.iotex.iopay.databinding.ActivityLanguageSwitchBinding
import io.iotex.iopay.setting.item.LanguageSwitchItemBinder

class LanguageSwitchActivity :
    BaseBindToolbarActivity<BaseViewModel, ActivityLanguageSwitchBinding>(R.layout.activity_language_switch) {

    private val mAdapter = MultiTypeAdapter()
    override fun initView() {
        setToolbarTitle(Utils.getApp().getString(R.string.language))
        mAdapter.register(LanguageType::class.java, LanguageSwitchItemBinder().apply {
            onItemClick = {
                SPUtils.getInstance().put(SP_LANGUAGE, it.getLanguage())
                MultiLanguage.onConfigurationChanged(this@LanguageSwitchActivity)
                MainActivity.getIsVisitor { isVisitor->
                    ActivityUtils.getActivityList().forEach { activity -> activity.finish() }
                    MainActivity.startActivity(this@LanguageSwitchActivity, isVisitor = isVisitor)
                }
            }
        })
        mAdapter.items = LanguageType.values().toMutableList()
        mBinding.recyclerView.layoutManager = LinearLayoutManager(this)
        mBinding.recyclerView.adapter = mAdapter
    }

    companion object {
        fun startActivity(context: Context) {
            context.startActivity(Intent(context, LanguageSwitchActivity::class.java))
        }
    }
}