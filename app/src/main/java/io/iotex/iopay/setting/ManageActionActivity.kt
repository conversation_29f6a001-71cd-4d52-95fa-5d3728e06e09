package io.iotex.iopay.setting

import io.iotex.base.BaseViewModel
import io.iotex.iopay.R
import io.iotex.iopay.base.BaseBindToolbarActivity
import io.iotex.iopay.databinding.ActivityManageActionBinding
import io.iotex.iopay.util.FireBaseEvent
import io.iotex.iopay.util.FireBaseUtil
import io.iotex.iopay.util.PageEventUtil
import io.iotex.iopay.wallet.list.SwitchWalletActivity
import org.jetbrains.anko.startActivity

class ManageActionActivity :
    BaseBindToolbarActivity<BaseViewModel, ActivityManageActionBinding>(R.layout.activity_manage_action) {

    override fun initView() {
        setToolbarTitle(getString(R.string.change_wallet))
        mBinding.mRlManagePrivateKey.setOnClickListener {
            SwitchWalletActivity.startActivity(this, manager = true)
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_SETTINGS_MANAGE_WALLETS_MANAGE_WALLET_TAB)
            PageEventUtil.logEvent(PageEventUtil.MANAGEWALLETS)
        }
        mBinding.mRlManageMnemonic.setOnClickListener {
            startActivity<MnemonicListActivity>()
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_SETTINGS_MANAGE_WALLET_RECOVERY)
            PageEventUtil.logEvent(PageEventUtil.MANAGERECOVERYPHRASE)
        }
    }


}