package io.iotex.iopay.setting

import android.view.MotionEvent
import android.view.View
import android.widget.EditText
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.lifecycleScope
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.KeyboardUtils
import com.blankj.utilcode.util.SPUtils
import com.blankj.utilcode.util.Utils
import com.drakeet.multitype.MultiTypeAdapter
import com.machinefi.lockscreen.LockAuthHelper
import io.iotex.base.BaseViewModel
import io.iotex.iopay.MainActivity
import io.iotex.iopay.R
import io.iotex.iopay.base.BaseBindToolbarActivity
import io.iotex.iopay.data.UserStore
import io.iotex.iopay.data.db.AppDatabase
import io.iotex.iopay.data.db.Mnemonic
import io.iotex.iopay.databinding.ActivityMnemonicListBinding
import io.iotex.iopay.support.guide.GuideBean
import io.iotex.iopay.support.guide.initGuideView
import io.iotex.iopay.ui.NoticePopupWindow
import io.iotex.iopay.ui.binder.MnemonicListBinder
import io.iotex.iopay.util.Config
import io.iotex.iopay.util.Constant
import io.iotex.iopay.util.FireBaseEvent
import io.iotex.iopay.util.FireBaseUtil
import io.iotex.iopay.util.SPConstant.SP_WALLET_REVEAL_GUIDE_STEP
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.util.extension.setGone
import io.iotex.iopay.util.extension.setVisible
import io.iotex.iopay.wallet.add.WalletAddMnemonicActivity
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext


class MnemonicListActivity :
    BaseBindToolbarActivity<BaseViewModel, ActivityMnemonicListBinding>(R.layout.activity_mnemonic_list) {

    private val mAdapter = MultiTypeAdapter()
    private var selectedMnemonic: Mnemonic? = null
    var guideBeanLD = MutableLiveData<ArrayList<GuideBean>>()

    override fun initView() {
        setToolbarTitle(getString(R.string.manage_mnemonic))
        setToolbarSubmitImage(R.drawable.icon_add_blue)
        setToolbarSubmitClick {
            WalletAddMnemonicActivity.startActivity(this)
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_SETTINGS_MANAGE_WALLET_RECOVERY_IMPORT)
        }
        val binder = MnemonicListBinder().apply {

            onFistItemLoad = {
                val guides = arrayListOf(
                    GuideBean(
                        it,
                        "",
                        getString(R.string.reveal_and_backup_your_recovery_phrase)
                    ),
                )
                guideBeanLD.postValue(guides)
            }

            setViewMnemonicListener {
                selectedMnemonic = it
                LockAuthHelper.showAuthLock(this@MnemonicListActivity) {
                    onAuthSuccess()
                }
            }
            setOnDeleteListener {
                NoticePopupWindow(
                    Utils.getApp().getString(R.string.warning),
                    Utils.getApp().getString(R.string.delete_mnemonic_warning_word),
                    null,
                    {
                        deleteMnemonic(it)
                    }).show()
            }
        }
        mAdapter.register(Mnemonic::class.java, binder)
        mBinding.mRvMnemonic.adapter = mAdapter

        guideBeanLD.observe(this) {
            if (!SPUtils.getInstance().getBoolean(SP_WALLET_REVEAL_GUIDE_STEP, false)) {
                initGuideView(this@MnemonicListActivity, it) {
                    SPUtils.getInstance().put(SP_WALLET_REVEAL_GUIDE_STEP, true)
                }
            }
        }
    }

    private fun deleteMnemonic(mnemonic: Mnemonic) {
        lifecycleScope.launch(Dispatchers.IO) {
            val walletList = AppDatabase.getInstance(Utils.getApp()).walletDao()
                .queryAllByMnemonic(mnemonic.id)
            walletList.forEach {
                AppDatabase.getInstance(Utils.getApp()).walletDao().deleteWallet(it)
            }
            AppDatabase.getInstance(Utils.getApp()).mnemonicDao()
                .delete(mnemonic)
            initData()
            val selectedWallet = walletList.firstOrNull {
                it.address == Constant.currentWallet?.address
            }
            if (selectedWallet != null) {
                val curWalletList = AppDatabase.getInstance(Utils.getApp()).walletDao()
                    .queryAllWallets()
                if (curWalletList.isNotEmpty()) {
                    WalletHelper.switchWallet(curWalletList[0])
                } else {
                    UserStore.setWalletAddress("")
                    UserStore.setChainId(Config.IOTEX_CHAIN_ID)
                    Constant.currentWallet = null
                    ActivityUtils.getActivityList().forEach { it?.finish() }
                    MainActivity.startActivity(this@MnemonicListActivity, isVisitor = true)
                }
            }
        }
    }

    override fun initData() {
        lifecycleScope.launch {
            val mnemonicList = withContext(Dispatchers.IO) {
                val list = AppDatabase.getInstance(Utils.getApp()).mnemonicDao().queryAll()
                list.forEach {
                    val walletList = AppDatabase.getInstance(Utils.getApp()).walletDao()
                        .queryByMnemonic(it.id)
                    if (walletList.isNotEmpty()) {
                        it.avatar = walletList[0].avatar
                    }
                }
                list
            }
            if (mnemonicList.isNotEmpty()) {
                mAdapter.items = mnemonicList
                mAdapter.notifyDataSetChanged()
                mBinding.mLlEmpty.setGone()
                mBinding.mRvMnemonic.setVisible()
            } else {
                mBinding.mLlEmpty.setVisible()
                mBinding.mRvMnemonic.setGone()
            }
        }

    }

    override fun dispatchTouchEvent(ev: MotionEvent): Boolean {
        if (ev.action == MotionEvent.ACTION_DOWN) {
            val v = currentFocus
            if (isShouldHideKeyboard(v, ev)) {
                KeyboardUtils.hideSoftInput(this)
            }
        }
        return super.dispatchTouchEvent(ev)
    }

    private fun isShouldHideKeyboard(v: View?, event: MotionEvent): Boolean {
        if (v is EditText) {
            val l = intArrayOf(0, 0)
            v.getLocationOnScreen(l)
            val left = l[0]
            val top = l[1]
            val bottom = top + v.getHeight()
            val right = left + v.getWidth()
            return !(event.rawX > left && event.rawX < right && event.rawY > top && event.rawY < bottom)
        }
        return false
    }

    private fun onAuthSuccess() {
        val payload = selectedMnemonic?.encryptedMnemonic ?: return
        val coinType = selectedMnemonic?.coinType ?: return
        ExportMnemonicActivity.start(this, payload, coinType)
    }
}