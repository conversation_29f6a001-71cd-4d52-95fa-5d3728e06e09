package io.iotex.iopay.setting

import android.content.Intent
import com.machinefi.lockscreen.LockScreenActivity
import io.iotex.base.BaseViewModel
import io.iotex.iopay.R
import io.iotex.iopay.base.BaseBindToolbarActivity
import io.iotex.iopay.databinding.ActivitySecuritPrivacyBinding
import io.iotex.iopay.util.FireBaseEvent
import io.iotex.iopay.util.FireBaseUtil

class SecurityPrivacyActivity :
    BaseBindToolbarActivity<BaseViewModel, ActivitySecuritPrivacyBinding>(R.layout.activity_securit_privacy) {
    override fun initView() {
        setToolbarTitle(getString(R.string.security_privacy))

        mBinding.viewChangePin.setOnClickListener {
            LockScreenActivity.startActivity(this, LockScreenActivity.PIN_RESET, true)
            FireBaseUtil.logFireBase(FireBaseEvent.NAVIGATETO_CHANGEPIN)
        }

        mBinding.viewDappAuth.setOnClickListener {
            val intent = Intent(this, DAppAuthActivity::class.java)
            startActivity(intent)
            FireBaseUtil.logFireBase(FireBaseEvent.NAVIGATETO_DAPPMANAGER)
        }

        mBinding.viewAuthentication.setOnClickListener {
            val intent = Intent(this, AuthenticationActivity::class.java)
            startActivity(intent)
        }
    }
}