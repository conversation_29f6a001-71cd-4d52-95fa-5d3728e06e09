package io.iotex.iopay.setting

import android.app.Application
import androidx.lifecycle.MutableLiveData
import com.blankj.utilcode.util.SPUtils
import com.blankj.utilcode.util.Utils
import io.iotex.iopay.base.BaseLaunchVM
import io.iotex.iopay.data.db.AppDatabase
import io.iotex.iopay.data.db.Mnemonic
import io.iotex.iopay.data.db.Wallet
import io.iotex.iopay.repo.GiftRepo
import io.iotex.iopay.util.SPConstant.SP_GIFT_INVITED_BEFORE
import io.iotex.iopay.util.WalletHelper

class SwapMenuViewModel(application: Application) : BaseLaunchVM(application) {

    val walletLiveData = MutableLiveData<Wallet?>()
    val recoveryLiveData = MutableLiveData<List<Mnemonic>?>()
    val deviceInviteLiveData = MutableLiveData<Boolean>()


    private val giftRepo by lazy{
        GiftRepo()
    }

    fun getWallet() {
        addLaunch {
            val wallet = WalletHelper.getCurWallet()
            wallet?.let {
                walletLiveData.postValue(it)
            }
        }
    }

    fun getRecoveryWallet() {
        addLaunch {
            val list = AppDatabase.getInstance(Utils.getApp()).mnemonicDao().queryAll()
            recoveryLiveData.postValue(list)
        }
    }



    fun getDeviceInvite(){
        addLaunch {
            var invited = SPUtils.getInstance().getBoolean(SP_GIFT_INVITED_BEFORE)
            deviceInviteLiveData.postValue(invited)
            invited = giftRepo.idDeviceInvited()
            SPUtils.getInstance().put(SP_GIFT_INVITED_BEFORE, invited)
            deviceInviteLiveData.postValue(invited)
        }
    }
}