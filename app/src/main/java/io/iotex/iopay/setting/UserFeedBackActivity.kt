package io.iotex.iopay.setting

import android.Manifest.permission
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.net.Uri
import android.os.Build
import android.util.Base64
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.widget.addTextChangedListener
import androidx.recyclerview.widget.GridLayoutManager
import com.blankj.utilcode.util.AppUtils
import com.blankj.utilcode.util.PermissionUtils
import com.blankj.utilcode.util.ToastUtils
import com.drakeet.multitype.MultiTypeAdapter
import io.iotex.iopay.R
import io.iotex.iopay.base.BaseBindToolbarActivity
import io.iotex.iopay.data.UserStore
import io.iotex.iopay.data.bean.UserFeedBack
import io.iotex.iopay.databinding.ActivityUserFeedBackBinding
import io.iotex.iopay.setting.item.AddImageBinder
import io.iotex.iopay.util.Config
import io.iotex.iopay.util.FireBaseEvent
import io.iotex.iopay.util.FireBaseUtil
import io.iotex.iopay.util.IoPayConstant
import io.iotex.iopay.xapp.XAppsActivity
import java.io.ByteArrayOutputStream
import java.io.FileNotFoundException
import java.io.IOException

class UserFeedBackActivity :
    BaseBindToolbarActivity<UserFeedBackViewModel, ActivityUserFeedBackBinding>(
        R.layout.activity_user_feed_back
    ) {

    private val mAdapter = MultiTypeAdapter()

    private val list = ArrayList<Bitmap>()
    private var addBitmap: Bitmap? = null
    private lateinit var pickImageLauncher: ActivityResultLauncher<String>

    override fun initView() {
        setToolbarTitle(getString(R.string.user_feedback))
        mBinding.ivTg.setOnClickListener {
            val intent = Intent(this, XAppsActivity::class.java)
            intent.putExtra(IoPayConstant.BROWSER_URL, Config.TELEGRAM_URL)
            startActivity(intent)
        }
        mBinding.ivDiscord.setOnClickListener {
            val intent = Intent(this, XAppsActivity::class.java)
            intent.putExtra(IoPayConstant.BROWSER_URL, Config.DISCORD_URL)
            startActivity(intent)
        }
        mBinding.etContent.addTextChangedListener {
            mBinding.tvTextSize.text = it?.length.toString() + "/300"
            mBinding.tvConfirm.isEnabled = !it.isNullOrEmpty()
            mBinding.tvConfirm.alpha = if (it.isNullOrEmpty()) 0.5f else 1f
        }
        addBitmap = BitmapFactory.decodeResource(resources, R.drawable.icon_feed_add_image)
        mBinding.recyclerView.layoutManager = GridLayoutManager(this, 3)
        mAdapter.register(Bitmap::class.java, AddImageBinder(addBitmap).apply {
            onItemClick = {
                if (it == addBitmap) {
                    requestImage()
                }
            }

            onItemDeleteClick = { delete ->
                list.remove(delete)
                mAdapter.items = list
                mAdapter.notifyDataSetChanged()
            }
        })
        addBitmap?.let {
            list.add(it)
        }
        mAdapter.items = list
        mBinding.recyclerView.adapter = mAdapter

        mBinding.tvConfirm.setOnClickListener {
            val content = mBinding.etContent.text.toString()
            val email = mBinding.etEmail.text.toString()
            val images = arrayListOf<String>()
            if(!email.contains("@")){
                ToastUtils.showShort(R.string.invalid_email)
                return@setOnClickListener
            }
            list.forEach {
                if (it != addBitmap) {
                    images.add(
                        "data:image/png;base64," + Base64.encodeToString(
                            compressBitmap(it),
                            Base64.NO_WRAP
                        )
                    )
                }
            }
            mViewModel.submit(
                UserFeedBack(
                    content,
                    email,
                    UserStore.getWalletAddress(),
                    images.toString(),
                    AppUtils.getAppVersionCode().toString(),
                    "android"
                )
            )
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_SETTING_USER_FEED_BACK_CONFIRM)
        }

        pickImageLauncher =
            registerForActivityResult(ActivityResultContracts.GetContent()) { uri: Uri? ->
                uri?.let {
                    handleSelectedImage(it)
                }
            }
    }

    override fun initData() {
        mViewModel.errorLiveData.observe(this) {
            if (it.isNullOrEmpty()) {
                ToastUtils.showShort(R.string.thanks_for_your_feedback)
                finish()
            } else {
                ToastUtils.showShort(it)
            }
        }
    }

    private fun requestImage() {
        val permission = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            permission.READ_MEDIA_IMAGES
        } else {
            permission.READ_EXTERNAL_STORAGE
        }
        PermissionUtils
            .permission(permission)
            .callback(object : PermissionUtils.SimpleCallback {
                override fun onGranted() {
                    pickImageLauncher.launch("image/*")
                }

                override fun onDenied() {
                    ToastUtils.showShort(R.string.need_permission)
                }
            })
            .request()
    }

    private fun handleSelectedImage(uri: Uri?) {
        if (uri != null) {
            try {
                contentResolver.openInputStream(uri)?.use { inputStream ->
                    val bitmap = BitmapFactory.decodeStream(inputStream)
                    compressBitmap(bitmap)
                    list.add(list.size - 1, bitmap)
                    mBinding.tvImageSize.text = (list.size - 1).toString() + "/9"
                    if (list.size == 10) {
                        list.remove(addBitmap)
                    }
                    mAdapter.items = list
                    mAdapter.notifyDataSetChanged()
                }
            } catch (e: FileNotFoundException) {
                e.printStackTrace()
            } catch (e: IOException) {
                e.printStackTrace()
            }
        }
    }

    private fun compressBitmap(bitmap: Bitmap): ByteArray {
        val outStream = ByteArrayOutputStream()
        var quality = 100
        bitmap.compress(Bitmap.CompressFormat.JPEG, quality, outStream)
        while (outStream.toByteArray().size / 1024 > 100) { //100kb
            outStream.reset()
            quality -= 10
            bitmap.compress(Bitmap.CompressFormat.JPEG, quality, outStream)
        }
        return outStream.toByteArray()
    }
}