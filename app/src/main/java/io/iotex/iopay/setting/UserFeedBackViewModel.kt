package io.iotex.iopay.setting

import android.app.Application
import androidx.lifecycle.MutableLiveData
import com.blankj.utilcode.util.Utils
import io.iotex.base.RetrofitClient
import io.iotex.iopay.R
import io.iotex.iopay.api.IoPayGatewayApi
import io.iotex.iopay.base.BaseLaunchVM
import io.iotex.iopay.data.bean.UserFeedBack
import io.iotex.iopay.repo.GiftRepo
import io.iotex.iopay.util.Config.IOPAY_GATEWAY_API

class UserFeedBackViewModel(application: Application) : BaseLaunchVM(application) {
    private val apiService by lazy {
        RetrofitClient.createApiService(IOPAY_GATEWAY_API, IoPayGatewayApi::class.java)
    }

    val errorLiveData = MutableLiveData<String?>()

    fun submit(userFeedBack: UserFeedBack) {
        addLaunch(true, onError = {
            errorLiveData.postValue(Utils.getApp().getString(R.string.network_error_please_try_again))
        }) {
            val resp = apiService.userFeedBack(
                "Bearer ${GiftRepo().generateJWT(userFeedBack.address)}",
                userFeedBack
            )
            errorLiveData.postValue(resp.error)
        }
    }
}