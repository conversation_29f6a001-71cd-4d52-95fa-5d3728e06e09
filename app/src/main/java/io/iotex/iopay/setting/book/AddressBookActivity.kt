package io.iotex.iopay.setting.book

import android.content.Context
import android.content.Intent
import androidx.core.view.isVisible
import androidx.core.widget.addTextChangedListener
import androidx.recyclerview.widget.LinearLayoutManager
import com.blankj.utilcode.util.ColorUtils
import com.drakeet.multitype.MultiTypeAdapter
import com.google.android.material.tabs.TabLayout
import io.iotex.iopay.R
import io.iotex.iopay.base.BaseBindToolbarActivity
import io.iotex.iopay.data.db.AddressBookEntry
import io.iotex.iopay.data.db.Wallet
import io.iotex.iopay.databinding.ActivityAddressBookBinding
import io.iotex.iopay.setting.book.item.BookListBinder
import io.iotex.iopay.setting.book.item.WalletListBinder
import io.iotex.iopay.ui.RemoveWarningDialog
import io.iotex.iopay.util.FireBaseEvent
import io.iotex.iopay.util.FireBaseUtil
import io.iotex.iopay.util.PageEventUtil
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.util.extension.removeItem
import io.iotex.iopay.util.extension.setGone
import io.iotex.iopay.util.extension.setVisible
import io.iotex.iopay.wallet.bitcoin.BitcoinBookBinder

class AddressBookActivity :
    BaseBindToolbarActivity<AddressBookViewModel, ActivityAddressBookBinding>(R.layout.activity_address_book) {

    private val mAdapterWallet = MultiTypeAdapter()
    private val mAdapterBook = MultiTypeAdapter()

    companion object {
        private const val KEY_MANAGER = "manager"
        private var selectAddress: ((address: String, name: String, isAA: Boolean) -> Unit)? = null

        fun startActivity(
            context: Context,
            manager: Boolean = false,
            back: ((address: String, name: String, isAA: Boolean) -> Unit)? = null
        ) {
            selectAddress = back
            val intent = Intent(context, AddressBookActivity::class.java)
            intent.putExtra(KEY_MANAGER, manager)
            context.startActivity(intent)
        }
    }

    private val manager by lazy {
        intent.getBooleanExtra(KEY_MANAGER, false)
    }

    override fun initView() {
        val title = if (manager) getString(R.string.manage_address_book) else getString(R.string.address_book)
        setToolbarTitle(title)
        setToolbarSubmitImage(R.drawable.icon_add_blue)
        setToolbarSubmitClick {
            EditAddressBookActivity.startActivity(this@AddressBookActivity) {
                mViewModel.getAddressBook(mBinding.etSearch.text.toString())
            }
            if (manager)
                FireBaseUtil.logFireBase(FireBaseEvent.ACTION_SETTING_ADDRESS_BOOK_ADD)
            else
                FireBaseUtil.logFireBase(FireBaseEvent.ACTION_SEND_ADDRESS_BOOK_ADD_NEW_ADDRESS)
        }

        mBinding.etSearch.addTextChangedListener {
            if (!manager) mViewModel.getWalletAddress(it?.toString() ?: "")
            mViewModel.getAddressBook(it?.toString() ?: "")
        }

        mBinding.llAdd.setOnClickListener {
            EditAddressBookActivity.startActivity(this@AddressBookActivity) {
                mViewModel.getAddressBook(mBinding.etSearch.text.toString())
            }
            if (manager)
                FireBaseUtil.logFireBase(FireBaseEvent.ACTION_SETTING_ADDRESS_BOOK_ADD)
            else
                FireBaseUtil.logFireBase(FireBaseEvent.ACTION_SEND_ADDRESS_BOOK_ADD_NEW_ADDRESS)
        }

        mBinding.tabs.isVisible = !manager
        mBinding.tabs.addTab(mBinding.tabs.newTab().apply {
            view.setBackgroundColor(ColorUtils.getColor(R.color.transparent))
        }.setText(R.string.address_book))
        mBinding.tabs.addTab(mBinding.tabs.newTab().apply {
            view.setBackgroundColor(ColorUtils.getColor(R.color.transparent))
        }.setText(R.string.my_wallet))

        mBinding.tabs.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
            override fun onTabSelected(tab: TabLayout.Tab?) {
                if (tab?.position == 0) {
                    mBinding.flBook.setVisible()
                    mBinding.recyclerViewWallet.setGone()
                } else {
                    mBinding.flBook.setGone()
                    mBinding.recyclerViewWallet.setVisible()
                    FireBaseUtil.logFireBase(FireBaseEvent.ACTION_SEND_ADDRESS_MY_WALLET_TAB)
                    PageEventUtil.logEvent(PageEventUtil.ADDADDRESS)
                }
            }

            override fun onTabUnselected(tab: TabLayout.Tab?) {
                //nothing.
            }

            override fun onTabReselected(tab: TabLayout.Tab?) {
                //nothing.
            }
        })

        if (WalletHelper.isBitcoinNetwork()) {
            mAdapterWallet.register(Wallet::class.java, BitcoinBookBinder().apply {
                onSelectWallet = { _, bitcoinWallet ->
                    if (!manager) {
                        selectAddress?.invoke(bitcoinWallet.bitcoinAddress, bitcoinWallet.typeName.split("(")[0], false)
                        finish()
                    }
                }
            })
        } else {
            mAdapterWallet.register(Wallet::class.java, WalletListBinder().apply {
                onSelectWallet = { address, alias, isAAWallet ->
                    if (!manager) {
                        selectAddress?.invoke(address, alias, isAAWallet)
                        finish()
                    }
                }
            })
        }

        mBinding.recyclerViewWallet.layoutManager = LinearLayoutManager(this)
        mBinding.recyclerViewWallet.adapter = mAdapterWallet

        mAdapterBook.register(AddressBookEntry::class.java, BookListBinder(manager).apply {
            onSelectAddressBook = {
                if (!manager) {
                    selectAddress?.invoke(it.address, it.name, false)
                    finish()
                }
            }
            onEditAddressBook = {
                EditAddressBookActivity.startActivity(this@AddressBookActivity, it.address) {
                    mViewModel.getAddressBook(mBinding.etSearch.text.toString())
                }
            }

            onDeleteAddressBook = {
                RemoveWarningDialog(getString(R.string.delete_wallet_confirm, it.name))
                    .apply {
                        onConfirmClick = {
                            dismiss()
                            mAdapterBook.removeItem<AddressBookEntry> { item -> item.address == it.address }
                            mViewModel.deleteAddressBook(it)
                        }
                    }.show(supportFragmentManager,System.currentTimeMillis().toString())
            }
        })
        mBinding.recyclerViewBook.layoutManager = LinearLayoutManager(this)
        mBinding.recyclerViewBook.adapter = mAdapterBook
    }

    override fun initData() {
        mViewModel.getWalletAddress("")
        mViewModel.walletListLiveData.observe(this) {
            mAdapterWallet.items = it
            mAdapterWallet.notifyDataSetChanged()
        }

        mViewModel.getAddressBook("")
        mViewModel.bookListLiveData.observe(this) {
            if (it.isEmpty()) {
                setToolbarSubmit(false)
                mBinding.llEmpty.setVisible()
            } else {
                setToolbarSubmit(true)
                mBinding.llEmpty.setGone()
                mAdapterBook.items = it
                mAdapterBook.notifyDataSetChanged()
            }
        }

        mViewModel.deleteLiveData.observe(this) {
            mViewModel.getAddressBook(mBinding.etSearch.text.toString())
        }
    }

}