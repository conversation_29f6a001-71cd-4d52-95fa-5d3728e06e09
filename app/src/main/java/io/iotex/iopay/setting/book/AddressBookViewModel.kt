package io.iotex.iopay.setting.book

import android.app.Application
import androidx.lifecycle.MutableLiveData
import com.blankj.utilcode.util.Utils
import io.iotex.iopay.base.BaseLaunchVM
import io.iotex.iopay.data.db.AddressBookEntry
import io.iotex.iopay.data.db.AppDatabase
import io.iotex.iopay.data.db.Wallet
import io.iotex.iopay.repo.NativeTokenRepo
import io.iotex.iopay.util.WalletHelper

class AddressBookViewModel(application: Application) : BaseLaunchVM(application) {
    val walletListLiveData = MutableLiveData<List<Wallet>>()
    val bookListLiveData = MutableLiveData<List<AddressBookEntry>>()
    val deleteLiveData = MutableLiveData<Boolean>()

    private val nativeTokenRepo by lazy {
        NativeTokenRepo()
    }

    fun getWalletAddress(search: String, update: Boolean = true) {
        addLaunch {
            val walletList = AppDatabase.getInstance(Utils.getApp()).walletDao()
                .queryWalletsLikeAlias(search).filter {
                    if (WalletHelper.isBitcoinNetwork()) {
                        it.isMnemonicWallet() || it.isEvmPrivateWallet() || it.isBitcoinWatchWallet()
                    } else if (WalletHelper.isSolanaNetwork()) {
                        it.isMnemonicWallet() || it.isSolanaPrivateWallet() || it.isSolanaWatchWallet()
                    } else {
                        !(it.isSolanaPrivateWallet() || it.isSolanaWatchWallet() || it.isBitcoinWatchWallet())
                    }
                }
            walletList.forEach {
                if (WalletHelper.isBitcoinNetwork()) {
                    it.bitcoinWallets = AppDatabase.getInstance(Utils.getApp()).bitcoinWalletDao()
                        .queryByEvmAddress(it.address, WalletHelper.getCurChainId())
                }
                if (WalletHelper.isSolanaNetwork()) {
                    it.solanaWallet = AppDatabase.getInstance(Utils.getApp()).solanaWalletDao()
                        .queryByAddressEvm(it.address)
                }
                val walletCache = AppDatabase.getInstance(Utils.getApp()).walletCacheDao()
                    .queryWalletCache(it.getCurNetworkAddress(), WalletHelper.getCurChainId())
                it.curBalance = walletCache?.balance ?: "0"
            }
            if (update) updateWalletBalance(search, walletList)
            walletListLiveData.postValue(walletList)
        }
    }

    private fun updateWalletBalance(search: String, wallets: List<Wallet>) {
        wallets.map { wallet ->
            val network = WalletHelper.getCurNetwork()
            addLaunch {
                val balance = nativeTokenRepo.getNetworkTokenBalance(network,wallet)
                if (wallet.curBalance != balance.toString()) {
                    getWalletAddress(search, false)
                }
            }
        }
    }

    fun getAddressBook(search: String) {
        addLaunch {
            var addressList = AppDatabase.getInstance(Utils.getApp()).addressBookDao()
                .queryLikeName(search).distinctBy {
                    WalletHelper.convertWeb3Address(it.address)
                }
            if (WalletHelper.isBitcoinNetwork()) {
                addressList = addressList.filter { WalletHelper.isBitcoinAddress(it.address) }
            }

            bookListLiveData.postValue(addressList)
        }
    }

    fun deleteAddressBook(addressBookEntry: AddressBookEntry) {
        addLaunch {
            AppDatabase.getInstance(Utils.getApp()).addressBookDao()
                .delete(addressBookEntry)
            deleteLiveData.postValue(true)
        }
    }
}