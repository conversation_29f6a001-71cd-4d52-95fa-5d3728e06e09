package io.iotex.iopay.setting.book

import android.content.Context
import android.content.Intent
import io.iotex.iopay.R
import io.iotex.iopay.base.BaseBindToolbarActivity
import io.iotex.iopay.data.db.AddressBookEntry
import io.iotex.iopay.databinding.ActivityEditAddressBookBinding
import io.iotex.iopay.util.RxUtil
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.util.extension.isAddress
import io.iotex.iopay.util.extension.toEvmAddress
import java.util.concurrent.TimeUnit

class EditAddressBookActivity :
    BaseBindToolbarActivity<EditAddressBookViewModel, ActivityEditAddressBookBinding>(R.layout.activity_edit_address_book) {

    companion object {
        private const val KEY_ADDRESS = "key_address"
        private var onEditSuccess: (() -> Unit)? = null
        fun startActivity(
            context: Context,
            address: String = "",
            onEdit: (() -> Unit)? = null
        ) {
            onEditSuccess = onEdit
            val intent = Intent(context, EditAddressBookActivity::class.java)
            intent.putExtra(KEY_ADDRESS, address)
            context.startActivity(intent)
        }
    }


    private val address by lazy {
        intent?.getStringExtra(KEY_ADDRESS)
    }

    override fun initView() {
        setToolbarTitle(if (address.isNullOrBlank()) {
            getString(R.string.new_address)
        } else {
            getString(R.string.edit_address)
        })
        setToolbarSubmitText(getString(R.string.save))
        setToolbarSubmitClick {
            save()
        }

        val sub = RxUtil.textChange(mBinding.etAddress)
            .debounce(800, TimeUnit.MILLISECONDS)
            .compose(RxUtil.applySchedulers())
            .subscribe {
                if (it.isNotEmpty() && it != address) {
                    mViewModel.checkAddress(it)
                } else {
                    mBinding.mTilAddress.error = null
                }
            }
        val sub2 = RxUtil.textChange(mBinding.etName)
            .debounce(800, TimeUnit.MILLISECONDS)
            .compose(RxUtil.applySchedulers())
            .subscribe {
                if (it.isNotEmpty()) {
                    mViewModel.checkAddressName(address, it)
                } else {
                    mBinding.mTilName.error = getString(R.string.please_enter)
                }
            }

    }

    override fun initData() {
        address?.let {
            mViewModel.getAddressBook(it)
            mViewModel.bookListLiveData.observe(this) { item ->
                mBinding.etAddress.setText(item?.address)
                mBinding.etName.setText(item?.name)
                mBinding.mEtDescribe.setText(item?.describe)
            }
        }

        mViewModel.addressErrorLiveData.observe(this) {
            mBinding.mTilAddress.error = it
        }

        mViewModel.nameErrorLiveData.observe(this) {
            mBinding.mTilName.error = it
        }

        mViewModel.saveLiveData.observe(this) {
            onEditSuccess?.invoke()
            finish()
        }
    }

    private fun save() {
        if (!mBinding.mTilAddress.error.isNullOrEmpty() || !mBinding.mTilName.error.isNullOrEmpty()) return
        val addressText = mBinding.etAddress.text.toString().trim()
        if(!addressText.isAddress()){
            mViewModel.checkAddress(addressText)
            return
        }
        val name = mBinding.etName.text.toString().trim()
        if(name.isEmpty()){
            mViewModel.checkAddressName(address, name)
        }
        val describe = mBinding.mEtDescribe.text.toString().trim()
        val addressBook =
            AddressBookEntry(WalletHelper.getCurChainId(), addressText.toEvmAddress(), name, describe)
        mViewModel.saveAddressBook(addressBook)
    }

}