package io.iotex.iopay.setting.book

import android.app.Application
import androidx.lifecycle.MutableLiveData
import com.blankj.utilcode.util.Utils
import io.iotex.iopay.R
import io.iotex.iopay.base.BaseLaunchVM
import io.iotex.iopay.data.db.AddressBookEntry
import io.iotex.iopay.data.db.AppDatabase
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.util.extension.isAddress
import io.iotex.iopay.util.extension.toEvmAddress
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

class EditAddressBookViewModel(application: Application) : BaseLaunchVM(application) {
    val bookListLiveData = MutableLiveData<AddressBookEntry>()
    val addressErrorLiveData = MutableLiveData<String>()
    val nameErrorLiveData = MutableLiveData<String>()
    val saveLiveData = MutableLiveData<Boolean>()

    fun getAddressBook(address: String) {
        addLaunch {
            val book = AppDatabase.getInstance(Utils.getApp()).addressBookDao()
                .queryByAddress(WalletHelper.convertWeb3Address(address))
            book?.let {
                bookListLiveData.postValue(it)
            }
        }
    }

    fun checkAddress(address: String) {
        addLaunch {
            if (!address.isAddress()) {
                addressErrorLiveData.postValue(Utils.getApp().getString(R.string.address_invalid))
                return@addLaunch
            }
            val item = withContext(Dispatchers.IO) {
                val formatAddress = if (WalletHelper.isBitcoinAddress(address)) {
                    address
                } else {
                    address.toEvmAddress()
                }
                AppDatabase.getInstance(Utils.getApp()).addressBookDao()
                    .queryByAddress(formatAddress)
            }
            if (item != null) {
                addressErrorLiveData.postValue(
                    Utils.getApp().getString(R.string.address_already_exists)
                )
                return@addLaunch
            }
            addressErrorLiveData.postValue("")
        }
    }

    fun checkAddressName(address: String?, name: String) {
        addLaunch {
            val item = AppDatabase.getInstance(Utils.getApp()).addressBookDao().queryByName(name)
            if (item?.address != null && item.address != address) {
                nameErrorLiveData.postValue(Utils.getApp().getString(R.string.validate_alias_exist))
                return@addLaunch
            }
            nameErrorLiveData.postValue("")
        }
    }

    fun saveAddressBook(addressBook: AddressBookEntry) {
        addLaunch {
            AppDatabase.getInstance(Utils.getApp()).addressBookDao()
                .insertOrReplace(addressBook)
            saveLiveData.postValue(true)
        }
    }
}