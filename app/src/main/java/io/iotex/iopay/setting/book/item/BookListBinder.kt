package io.iotex.iopay.setting.book.item

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isVisible
import com.blankj.utilcode.util.ClipboardUtils
import com.blankj.utilcode.util.ToastUtils
import com.drakeet.multitype.ItemViewBinder
import io.iotex.base.bindbase.BaseBindVH
import io.iotex.iopay.R
import io.iotex.iopay.data.db.AddressBookEntry
import io.iotex.iopay.databinding.ItemBookListBinding
import io.iotex.iopay.util.TokenUtil
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.util.extension.loadSvgOrImage

class BookListBinder(val manager: Boolean) :
    ItemViewBinder<AddressBookEntry, BaseBindVH<ItemBookListBinding>>() {
    var onSelectAddressBook: ((AddressBookEntry) -> Unit)? = null
    var onEditAddressBook: ((AddressBookEntry) -> Unit)? = null
    var onDeleteAddressBook: ((AddressBookEntry) -> Unit)? = null

    override fun onCreateViewHolder(
        inflater: LayoutInflater,
        parent: ViewGroup
    ): BaseBindVH<ItemBookListBinding> {
        val bind = ItemBookListBinding.inflate(inflater, parent, false)
        return BaseBindVH(bind)
    }

    override fun onBindViewHolder(holder: BaseBindVH<ItemBookListBinding>, item: AddressBookEntry) {
        val bind = holder.bind
        val avatar = WalletHelper.getAddressAvatar(item.address)
        bind.walletAvatar.loadSvgOrImage(avatar, R.drawable.icon_wallet_default)
        bind.walletName.text = item.name
        val address = WalletHelper.formatWalletAddress(item.address)
        bind.walletAddress.text = TokenUtil.textEllipsis(address, 6, 8)

        bind.swipeLayout.isSwipeEnabled = manager
        bind.ivEdit.isVisible = manager
        bind.ivEdit.setOnClickListener {
            onEditAddressBook?.invoke(item)
        }

        bind.llDelete.setOnClickListener {
            onDeleteAddressBook?.invoke(item)
        }

        bind.ivCopy.setOnClickListener {
            ClipboardUtils.copyText(address)
            ToastUtils.showShort(R.string.copy_success)
        }

        bind.root.setOnClickListener {
            onSelectAddressBook?.invoke(item)
        }

    }
}