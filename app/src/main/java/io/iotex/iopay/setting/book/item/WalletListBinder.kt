package io.iotex.iopay.setting.book.item

import android.view.LayoutInflater
import android.view.ViewGroup
import com.blankj.utilcode.util.ClipboardUtils
import com.blankj.utilcode.util.SPUtils
import com.blankj.utilcode.util.ToastUtils
import com.drakeet.multitype.ItemViewBinder
import io.iotex.base.bindbase.BaseBindVH
import io.iotex.iopay.R
import io.iotex.iopay.data.UserStore
import io.iotex.iopay.data.db.Wallet
import io.iotex.iopay.databinding.ItemWalletListBinding
import io.iotex.iopay.util.IoPayConstant
import io.iotex.iopay.util.SPConstant
import io.iotex.iopay.util.TokenUtil
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.util.extension.loadSvgOrImage
import io.iotex.iopay.util.extension.setGone
import io.iotex.iopay.util.extension.setVisible
import io.iotex.iopay.wallet.aawallet.dialog.AAReceiveDialog.showAAReceiveCopy

class WalletListBinder :
    ItemViewBinder<Wallet, BaseBindVH<ItemWalletListBinding>>() {
    var onSelectWallet: ((address: String, alias: String, isAAWallet: Boolean) -> Unit)? = null

    override fun onCreateViewHolder(
        inflater: LayoutInflater,
        parent: ViewGroup
    ): BaseBindVH<ItemWalletListBinding> {
        val bind = ItemWalletListBinding.inflate(inflater, parent, false)
        return BaseBindVH(bind)
    }

    override fun onBindViewHolder(holder: BaseBindVH<ItemWalletListBinding>, item: Wallet) {
        val bind = holder.bind
        bind.walletAvatar.loadSvgOrImage(item.avatar, R.drawable.icon_wallet_default)
        bind.walletName.text = item.alias
        val address = WalletHelper.formatWalletAddress(item.getCurNetworkAddress())
        bind.walletAddress.text = TokenUtil.textEllipsis(address, 6, 8)
        if (item.isAAWallet()) {
            bind.ivAA.setVisible()
        } else {
            bind.ivAA.setGone()
        }

        val nativeCurrency = SPUtils.getInstance()
            .getString(SPConstant.SP_RPC_NETWORK_NATIVE_CURRENCY, IoPayConstant.IOTX)
        val value = TokenUtil.weiToTokenBN(item.curBalance, UserStore.getNetworkDecimals().toLong())
        val balance = TokenUtil.displayBalance(value)
        bind.walletAmount.text = "$balance $nativeCurrency"

        bind.ivCopy.setOnClickListener {
            ClipboardUtils.copyText(address)
            if (item.isAAWallet()) {
                showAAReceiveCopy()
            } else {
                ToastUtils.showShort(R.string.copy_success)
            }
        }

        bind.root.setOnClickListener {
            onSelectWallet?.invoke(address, item.alias, item.isAAWallet())
        }

    }
}