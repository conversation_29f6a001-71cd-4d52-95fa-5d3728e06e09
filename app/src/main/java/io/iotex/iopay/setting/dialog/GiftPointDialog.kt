package io.iotex.iopay.setting.dialog

import android.view.Gravity
import com.blankj.utilcode.util.ConvertUtils
import com.blankj.utilcode.util.ScreenUtils
import io.iotex.base.BaseViewModel
import io.iotex.base.bindbase.BaseBindDialog
import io.iotex.iopay.R
import io.iotex.iopay.databinding.DialogGiftPointBinding
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class GiftPointDialog(val point:String) :
    BaseBindDialog<BaseViewModel, DialogGiftPointBinding>(R.layout.dialog_gift_point) {

    override fun initView() {
        mBinding.tvPoint.text = getString(R.string.you_have_completed_task_and_earn, point)
        MainScope().launch {
            withContext(Dispatchers.IO){
                delay(3000)
            }
            dismiss()
        }
    }

    override fun onStart() {
        super.onStart()
        val attributes = dialog?.window?.attributes
        attributes?.width = ScreenUtils.getScreenWidth()- ConvertUtils.dp2px(30f)
        attributes?.dimAmount = 0f
        attributes?.gravity = Gravity.TOP
        dialog?.window?.attributes = attributes
    }
}