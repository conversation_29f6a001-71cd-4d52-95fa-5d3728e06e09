package io.iotex.iopay.setting.dialog

import io.iotex.base.BaseViewModel
import io.iotex.base.bindbase.BaseBindDialog
import io.iotex.iopay.R
import io.iotex.iopay.databinding.DialogInvitedCodeBinding

class InvitedCodeDialog :
    BaseBindDialog<BaseViewModel, DialogInvitedCodeBinding>(R.layout.dialog_invited_code) {

    override fun initView() {
        mBinding.ivClose.setOnClickListener {
            dismiss()
        }
    }
}