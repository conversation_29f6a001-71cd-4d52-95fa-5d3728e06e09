package io.iotex.iopay.setting.dialog

import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Color
import android.net.Uri
import android.view.View
import com.blankj.utilcode.util.IntentUtils
import com.blankj.utilcode.util.Utils
import io.iotex.base.BaseViewModel
import io.iotex.base.bindbase.BaseBindDialog
import io.iotex.iopay.R
import io.iotex.iopay.databinding.DialogInvitedShareBinding
import io.iotex.iopay.util.FireBaseEvent
import io.iotex.iopay.util.FireBaseUtil
import io.iotex.iopay.util.QRCodeUtil
import io.iotex.iopay.util.extension.toast

class InvitedShareDialog(val code: String) :
    BaseBindDialog<BaseViewModel, DialogInvitedShareBinding>(R.layout.dialog_invited_share) {

    override fun initView() {
        mBinding.tvCode.text = code
        mBinding.mIvDownload.setOnClickListener {
            savePoster {
                Utils.getApp().getString(R.string.saved_successfully).toast()
            }
        }
        mBinding.mIvShare.setOnClickListener {
            savePoster { fileUri ->
                if (fileUri != null) {
                    val i = IntentUtils.getShareImageIntent(fileUri)
                    Utils.getApp().startActivity(i)
                    FireBaseUtil.logFireBase(FireBaseEvent.ACTION_ACTION_TXN_SHARE)
                }
            }
        }
    }

    private fun savePoster(cb: ((Uri?) -> Unit)? = null) {
        val bmp = captureView(mBinding.llPoster)
        val filename = "invite_${System.currentTimeMillis()}.jpg"
        val uri = QRCodeUtil.savePhoto(bmp, filename)
        if (uri != null) {
            cb?.invoke(uri)
        } else {
            Utils.getApp().getString(R.string.saved_fail).toast()
            cb?.invoke(null)
        }
    }
    private fun captureView(view: View): Bitmap {
        val bitmap: Bitmap = Bitmap.createBitmap(
            view.width,
            view.height,
            Bitmap.Config.RGB_565
        )
        val canvas = Canvas(bitmap)
        canvas.drawColor(Color.WHITE)
        view.draw(canvas)
        return bitmap
    }
}