package io.iotex.iopay.setting.item

import android.graphics.Bitmap
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isVisible
import com.drakeet.multitype.ItemViewBinder
import io.iotex.base.bindbase.BaseBindVH
import io.iotex.iopay.databinding.ItemAddImageBinding
import io.iotex.iopay.util.extension.loadImage
import io.iotex.iopay.util.extension.setGone
import io.iotex.iopay.util.extension.setVisible

class AddImageBinder(val addBitmap: Bitmap?) :
    ItemViewBinder<Bitmap, BaseBindVH<ItemAddImageBinding>>() {

    var onItemClick: ((Bitmap) -> Unit)? = null
    var onItemDeleteClick: ((Bitmap) -> Unit)? = null
    override fun onBindViewHolder(holder: BaseBindVH<ItemAddImageBinding>, item: Bitmap) {
        if (addBitmap == item) {
            holder.bind.flAdd.setVisible()
        } else {
            holder.bind.flAdd.setGone()
            holder.bind.ivImage.loadImage(item)
        }
        holder.bind.ivImage.setOnClickListener {
            onItemClick?.invoke(item)
        }

        holder.bind.ivDelete.isVisible = item != addBitmap
        holder.bind.ivDelete.setOnClickListener {
            onItemDeleteClick?.invoke(item)
        }
    }

    override fun onCreateViewHolder(
        inflater: LayoutInflater,
        parent: ViewGroup
    ): BaseBindVH<ItemAddImageBinding> {
        return BaseBindVH(
            ItemAddImageBinding.inflate(
                LayoutInflater.from(parent.context),
                parent,
                false
            )
        )
    }
}