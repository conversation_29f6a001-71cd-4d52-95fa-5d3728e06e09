package io.iotex.iopay.setting.item

import android.view.LayoutInflater
import android.view.ViewGroup
import com.blankj.utilcode.util.Utils
import com.drakeet.multitype.ItemViewBinder
import io.iotex.base.bindbase.BaseBindVH
import io.iotex.base.language.LanguageType
import io.iotex.base.language.MultiLanguage
import io.iotex.iopay.R
import io.iotex.iopay.databinding.ItemLanguageSwitchBinding

class LanguageSwitchItemBinder :
    ItemViewBinder<LanguageType, BaseBindVH<ItemLanguageSwitchBinding>>() {

    var onItemClick: ((LanguageType) -> Unit)? = null

    override fun onCreateViewHolder(
        inflater: LayoutInflater,
        parent: ViewGroup
    ): BaseBindVH<ItemLanguageSwitchBinding> {
        return BaseBindVH(ItemLanguageSwitchBinding.inflate(LayoutInflater.from(parent.context),parent,false))
    }

    override fun onBindViewHolder(
        holder: BaseBindVH<ItemLanguageSwitchBinding>,
        item: LanguageType
    ) {
        holder.bind.tvName.text = item.getName()
        holder.bind.llRoot.setOnClickListener {
            onItemClick?.invoke(item)
        }
        if (MultiLanguage.getSetLanguageType(Utils.getApp()) == item) {
            holder.bind.llRoot.setBackgroundResource(R.drawable.shape_card_back_stroke_gradient)
        } else {
            holder.bind.llRoot.setBackgroundResource(R.drawable.shape_card_back)
        }
    }

}