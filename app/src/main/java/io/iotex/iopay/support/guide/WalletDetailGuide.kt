package io.iotex.iopay.support.guide

import android.app.Activity
import android.view.LayoutInflater
import android.view.View
import android.view.animation.DecelerateInterpolator
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.RelativeLayout
import androidx.fragment.app.Fragment
import com.blankj.utilcode.util.ConvertUtils
import com.blankj.utilcode.util.LogUtils
import com.blankj.utilcode.util.ScreenUtils
import com.takusemba.spotlight.OnSpotlightListener
import com.takusemba.spotlight.OnTargetListener
import com.takusemba.spotlight.Spotlight
import com.takusemba.spotlight.Target
import io.iotex.iopay.R
import io.iotex.iopay.databinding.GuideViewStepBinding
import io.iotex.iopay.ui.widget.StrokeRectangleShape
import io.iotex.iopay.util.extension.dp2px
import io.iotex.iopay.util.extension.locateOnScreen
import io.iotex.iopay.util.extension.setGone
import io.iotex.iopay.util.extension.setVisible


fun initGuideView(view: View,fragment: Fragment,onEndCallBack:(()->Unit)? = null){
    if (fragment.isDetached) return
    fragment.context ?: return
    val rectanglePadding = ConvertUtils.dp2px(4f)
    val dialogMargin = ConvertUtils.dp2px(7f)
    val targets = ArrayList<Target>()

    //step1
    val root1 = FrameLayout(fragment.requireContext())
    val dialogView1 = fragment.layoutInflater.inflate(R.layout.guide_view_step_1, root1)
    val anchorView1 = view.findViewById<View>(R.id.tvWalletValue)
    val contentView1 = dialogView1.findViewById<View>(R.id.view_guide_content)
    val arrowView1 = dialogView1.findViewById<ImageView>(R.id.iv_arrow_top)
    anchorView1.locateOnScreen()?.let { rect ->
        contentView1.apply {
            val layoutParams = this.layoutParams as RelativeLayout.LayoutParams
            layoutParams.setMargins(rect.left, rect.bottom + dialogMargin, 0, 0)
            contentView1.layoutParams = layoutParams
        }
        arrowView1.apply {
            val layoutParams = this.layoutParams as LinearLayout.LayoutParams
            layoutParams.setMargins(ConvertUtils.dp2px(30f), 0, 0, 0)
            arrowView1.layoutParams = layoutParams
        }
        val targetStep1 = Target.Builder()
                .setAnchor((rect.left + ((rect.right - rect.left) / 2)).toFloat(), (rect.top + ((rect.bottom - rect.top) / 2).toFloat()))
                .setShape(StrokeRectangleShape((rect.bottom - rect.top).toFloat() + rectanglePadding, (rect.right - rect.left).toFloat() + rectanglePadding, 8f))
                .setOverlay(dialogView1)
                .setOnTargetListener(object : OnTargetListener {
                    override fun onStarted() {
                        LogUtils.d("WalletDetailGuide", "firstTarget-onStarted")
                    }

                    override fun onEnded() {
                        LogUtils.d("WalletDetailGuide", "firstTarget-onEnded")
                    }
                })
                .build()
        targets.add(targetStep1)
    }
    //step2
    val root2 = FrameLayout(fragment.requireContext())
    val dialogView2 = fragment.layoutInflater.inflate(R.layout.guide_view_step_2, root2)
    val anchorView2 = view.findViewById<View>(R.id.llSend)
    val contentView2 = dialogView2.findViewById<View>(R.id.view_guide_content)
    val arrowView2 = dialogView2.findViewById<ImageView>(R.id.iv_arrow_top)
    anchorView2.locateOnScreen()?.let { rect ->
        contentView2.apply {
            val layoutParams = this.layoutParams as RelativeLayout.LayoutParams
            layoutParams.setMargins(rect.left, rect.bottom + dialogMargin, 0, 0)
            contentView2.layoutParams = layoutParams
        }
        arrowView2.apply {
            val layoutParams = this.layoutParams as LinearLayout.LayoutParams
            layoutParams.setMargins(ConvertUtils.dp2px(24f), 0, 0, 0)
            arrowView2.layoutParams = layoutParams
        }
        val targetStep2 = Target.Builder()
                .setAnchor((rect.left + ((rect.right - rect.left) / 2)).toFloat(), (rect.top + ((rect.bottom - rect.top) / 2).toFloat()))
                .setShape(StrokeRectangleShape((rect.bottom - rect.top).toFloat() + rectanglePadding, (rect.right - rect.left).toFloat() + rectanglePadding, 8f))
                .setOverlay(dialogView2)
                .setOnTargetListener(object : OnTargetListener {
                    override fun onStarted() {
                        LogUtils.d("WalletDetailGuide", "firstTarget-onStarted")
                    }

                    override fun onEnded() {
                        LogUtils.d("WalletDetailGuide", "firstTarget-onEnded")
                    }
                })
                .build()
        targets.add(targetStep2)
    }
    //step3
    val root3 = FrameLayout(fragment.requireContext())
    val dialogView3 = fragment.layoutInflater.inflate(R.layout.guide_view_step_3, root3)
    val anchorView3 = view.findViewById<View>(R.id.llReceive)
    val contentView3 = dialogView3.findViewById<View>(R.id.view_guide_content)
    val arrowView3 = dialogView3.findViewById<ImageView>(R.id.iv_arrow_top)
    anchorView3.locateOnScreen()?.let { rect ->
        contentView3.apply {
            val layoutParams = this.layoutParams as RelativeLayout.LayoutParams
            layoutParams.setMargins(ConvertUtils.dp2px(43f), rect.bottom + dialogMargin, 0, 0)
            contentView3.layoutParams = layoutParams
        }
        arrowView3.apply {
            val layoutParams = this.layoutParams as LinearLayout.LayoutParams
            layoutParams.setMargins(ConvertUtils.dp2px(60f), 0, 0, 0)
            this.layoutParams = layoutParams
        }
        val targetStep3 = Target.Builder()
                .setAnchor((rect.left + ((rect.right - rect.left) / 2)).toFloat(), (rect.top + ((rect.bottom - rect.top) / 2).toFloat()))
                .setShape(StrokeRectangleShape((rect.bottom - rect.top).toFloat() + rectanglePadding, (rect.right - rect.left).toFloat() + rectanglePadding, 8f))
                .setOverlay(dialogView3)
                .setOnTargetListener(object : OnTargetListener {
                    override fun onStarted() {
                        LogUtils.d("WalletDetailGuide", "firstTarget-onStarted")
                    }

                    override fun onEnded() {
                        LogUtils.d("WalletDetailGuide", "firstTarget-onEnded")
                    }
                })
                .build()
        targets.add(targetStep3)
    }

    //step4
    val root4 = FrameLayout(fragment.requireContext())
    val dialogView4 = fragment.layoutInflater.inflate(R.layout.guide_view_step_4, root4)
    val anchorView4 = view.findViewById<View>(R.id.llEarn)
    val contentView4 = dialogView4.findViewById<View>(R.id.view_guide_content)
    val arrowView4 = dialogView4.findViewById<ImageView>(R.id.iv_arrow_top)
    anchorView4.locateOnScreen()?.let { rect ->
        contentView4.apply {
            val layoutParams = this.layoutParams as RelativeLayout.LayoutParams
            layoutParams.setMargins(ConvertUtils.dp2px(36f), rect.bottom + dialogMargin, 0, 0)
            contentView4.layoutParams = layoutParams
        }
        arrowView4.apply {
            val layoutParams = this.layoutParams as LinearLayout.LayoutParams
            layoutParams.setMargins(0, 0, ConvertUtils.dp2px(64f), 0)
            arrowView4.layoutParams = layoutParams
        }
        val targetStep4 = Target.Builder()
                .setAnchor((rect.left + ((rect.right - rect.left) / 2)).toFloat(), (rect.top + ((rect.bottom - rect.top) / 2).toFloat()))
                .setShape(StrokeRectangleShape((rect.bottom - rect.top).toFloat() + rectanglePadding, (rect.right - rect.left).toFloat() + rectanglePadding, 8f))
                .setOverlay(dialogView4)
                .setOnTargetListener(object : OnTargetListener {
                    override fun onStarted() {
                    }

                    override fun onEnded() {
                    }
                })
                .build()
        targets.add(targetStep4)

    }

    //step5
    val root5 = FrameLayout(fragment.requireContext())
    val dialogView5 = fragment.layoutInflater.inflate(R.layout.guide_view_step_5, root5)
    val anchorView5 = view.findViewWithTag<View>("token")
    val contentView5 = dialogView5.findViewById<View>(R.id.view_guide_content)
    val arrowView5 = dialogView5.findViewById<ImageView>(R.id.iv_arrow_top)
    anchorView5?.locateOnScreen()?.let { rect ->
        contentView5.apply {
            val layoutParams = this.layoutParams as RelativeLayout.LayoutParams
            layoutParams.setMargins(rect.left , rect.top - ConvertUtils.dp2px(182f)-dialogMargin, 0, 0)
            contentView5.layoutParams = layoutParams
        }
        arrowView5.apply {
            val layoutParams = this.layoutParams as LinearLayout.LayoutParams
            layoutParams.setMargins(ConvertUtils.dp2px(27f), 0, 0, 0)
            arrowView5.layoutParams = layoutParams
        }
        val targetStep5 = Target.Builder()
                .setAnchor((rect.left + ((rect.right - rect.left) / 2)).toFloat(), (rect.top + ((rect.bottom - rect.top) / 2).toFloat()))
                .setShape(StrokeRectangleShape((rect.bottom - rect.top).toFloat() + rectanglePadding, (rect.right - rect.left).toFloat() + rectanglePadding, 8f))
                .setOverlay(dialogView5)
                .setOnTargetListener(object : OnTargetListener {
                    override fun onStarted() {
                    }

                    override fun onEnded() {
                    }
                })
                .build()
        targets.add(targetStep5)

    }

 //step6
    val root6 = FrameLayout(fragment.requireContext())
    val dialogView6 = fragment.layoutInflater.inflate(R.layout.guide_view_step_6, root6)
    val anchorView6 = view.findViewById<View>(R.id.ivSetting)
    val contentView6 = dialogView6.findViewById<View>(R.id.view_guide_content)
    val arrowView6 = dialogView6.findViewById<ImageView>(R.id.iv_arrow_top)
    anchorView6.locateOnScreen()?.let { rect ->
        contentView6.apply {
            val layoutParams = this.layoutParams as RelativeLayout.LayoutParams
            layoutParams.setMargins(0 , rect.bottom + dialogMargin, 0, 0)
            contentView6.layoutParams = layoutParams
        }
        arrowView6.apply {
            val layoutParams = this.layoutParams as LinearLayout.LayoutParams
            layoutParams.setMargins(0, 0, ConvertUtils.dp2px(20f), 0)
            arrowView6.layoutParams = layoutParams
        }
        val targetStep6 = Target.Builder()
                .setAnchor((rect.left + ((rect.right - rect.left) / 2)).toFloat(), (rect.top + ((rect.bottom - rect.top) / 2).toFloat()))
                .setShape(StrokeRectangleShape((rect.bottom - rect.top).toFloat() + rectanglePadding, (rect.right - rect.left).toFloat() + rectanglePadding, 8f))
                .setOverlay(dialogView6)
                .setOnTargetListener(object : OnTargetListener {
                    override fun onStarted() {
                    }

                    override fun onEnded() {
                    }
                })
                .build()
        targets.add(targetStep6)

    }
 //step7
    val root7 = FrameLayout(fragment.requireContext())
    val dialogView7 = fragment.layoutInflater.inflate(R.layout.guide_view_step_7, root7)
    val anchorView7 = view.findViewById<View>(R.id.llNetwork)
    val contentView7 = dialogView7.findViewById<View>(R.id.view_guide_content)
    val arrowView7 = dialogView7.findViewById<ImageView>(R.id.iv_arrow_top)
    anchorView7.locateOnScreen()?.let { rect ->
        contentView7.apply {
            val layoutParams = this.layoutParams as RelativeLayout.LayoutParams
            layoutParams.setMargins(0, rect.bottom + dialogMargin, 0, 0)
            contentView7.layoutParams = layoutParams
        }
        arrowView7.apply {
            val layoutParams = this.layoutParams as LinearLayout.LayoutParams
            layoutParams.setMargins(ConvertUtils.dp2px(20f), 0, 0, 0)
            arrowView7.layoutParams = layoutParams
        }
        val targetStep7 = Target.Builder()
                .setAnchor((rect.left + ((rect.right - rect.left) / 2)).toFloat(), (rect.top + ((rect.bottom - rect.top) / 2).toFloat()))
                .setShape(StrokeRectangleShape((rect.bottom - rect.top).toFloat() + rectanglePadding, (rect.right - rect.left).toFloat() + rectanglePadding, 8f))
                .setOverlay(dialogView7)
                .setOnTargetListener(object : OnTargetListener {
                    override fun onStarted() {
                    }

                    override fun onEnded() {
                    }
                })
                .build()
        targets.add(targetStep7)

    }
    // create spotlight
    val spotlight = Spotlight.Builder(fragment.requireActivity())
            .setTargets(targets)
            .setBackgroundColorRes(R.color.spotlightBackground)
            .setDuration(1000L)
            .setAnimation(DecelerateInterpolator(2f))
            .setOnSpotlightListener(object : OnSpotlightListener {
                override fun onStarted() {
                    LogUtils.d("WalletDetailGuide", "Spotlight-onStarted")
                }

                override fun onEnded() {
                    onEndCallBack?.invoke()
                    LogUtils.d("WalletDetailGuide", "Spotlight-onEnded")
                }
            })
            .build()
    spotlight.start()
    dialogView1.findViewById<View>(R.id.tv_skip).setOnClickListener {
        spotlight.finish()
    }
    dialogView1.findViewById<View>(R.id.iv_step_next).setOnClickListener {
        spotlight.next()
    }
    dialogView2.findViewById<View>(R.id.tv_skip).setOnClickListener {
        spotlight.finish()
    }
    dialogView2.findViewById<View>(R.id.iv_step_next).setOnClickListener {
        spotlight.next()
    }
    dialogView2.findViewById<View>(R.id.iv_step_forward).setOnClickListener {
        spotlight.previous()
    }
    dialogView3.findViewById<View>(R.id.tv_skip).setOnClickListener {
        spotlight.finish()
    }
    dialogView3.findViewById<View>(R.id.iv_step_next).setOnClickListener {
        spotlight.next()
    }
    dialogView3.findViewById<View>(R.id.iv_step_forward).setOnClickListener {
        spotlight.previous()
    }
    dialogView3.findViewById<View>(R.id.tv_skip).setOnClickListener {
        spotlight.finish()
    }
    dialogView3.findViewById<View>(R.id.iv_step_next).setOnClickListener {
        spotlight.next()
    }
    dialogView3.findViewById<View>(R.id.iv_step_forward).setOnClickListener {
        spotlight.previous()
    }
    dialogView4.findViewById<View>(R.id.tv_skip).setOnClickListener {
        spotlight.finish()
    }
    dialogView4.findViewById<View>(R.id.iv_step_next).setOnClickListener {
        spotlight.next()
    }
    dialogView4.findViewById<View>(R.id.iv_step_forward).setOnClickListener {
        spotlight.previous()
    }
    dialogView5.findViewById<View>(R.id.tv_skip).setOnClickListener {
        spotlight.finish()
    }
    dialogView5.findViewById<View>(R.id.iv_step_next).setOnClickListener {
        spotlight.next()
    }
    dialogView5.findViewById<View>(R.id.iv_step_forward).setOnClickListener {
        spotlight.previous()
    }
    dialogView6.findViewById<View>(R.id.tv_skip).setOnClickListener {
        spotlight.finish()
    }
    dialogView6.findViewById<View>(R.id.iv_step_next).setOnClickListener {
        spotlight.next()
    }
    dialogView6.findViewById<View>(R.id.iv_step_forward).setOnClickListener {
        spotlight.previous()
    }
    dialogView7.findViewById<View>(R.id.iv_step_next).setOnClickListener {
        spotlight.next()
    }
    dialogView7.findViewById<View>(R.id.iv_step_forward).setOnClickListener {
        spotlight.previous()
    }
}

data class GuideBean(
    val anchorView: View,
    val title: String,
    val content: String,
)

fun initGuideView(
    context: Activity,
    GuideBeans: ArrayList<GuideBean>,
    onEndCallBack: (() -> Unit)? = null
) {
    val targets = ArrayList<Target>()
    var spotlight: Spotlight? = null
    GuideBeans.forEachIndexed { index, guideBean ->
        val bind = GuideViewStepBinding.inflate(LayoutInflater.from(context))
        bind.tvTitle.text = guideBean.title
        bind.tvContent.text = guideBean.content
        bind.tvSteps.text = "${index + 1}/${GuideBeans.size}"
        bind.root.setOnClickListener {
            //nothing.
        }
        bind.ivForward.setOnClickListener {
            spotlight?.previous()
        }
        bind.ivNext.setOnClickListener {
            spotlight?.next()
        }
        bind.tvGet.setOnClickListener {
            spotlight?.finish()
        }
        bind.tvSkip.setOnClickListener {
            spotlight?.finish()
        }

        if (guideBean.title.isEmpty()) {
            bind.llTitle.setGone()
        } else {
            bind.llTitle.setVisible()
        }

        if (index == 0) {
            bind.ivForward.alpha = 0.4f
            bind.ivForward.isEnabled = false
        } else {
            bind.ivForward.alpha = 1f
        }
        if (index == GuideBeans.size - 1) {
            bind.ivNext.setGone()
            bind.tvGet.setVisible()
        } else {
            bind.ivNext.setVisible()
            bind.tvGet.setGone()
        }

        if (GuideBeans.size == 1) {
            bind.ivForward.setGone()
            bind.ivNext.setGone()
            bind.tvSteps.setGone()
        }

        guideBean.anchorView.locateOnScreen()?.let { rect ->
            var arrowLeft: Int
            bind.llContent.apply {
                val layoutParams = this.layoutParams as LinearLayout.LayoutParams
                val right = ScreenUtils.getScreenWidth() - rect.right
                if (rect.left - right < -10) {
                    arrowLeft = (rect.right + rect.left) / 2 - 15.dp2px()
                    layoutParams.setMargins(15.dp2px(), rect.bottom, 60.dp2px(), 0)
                } else if (rect.left - right > 10) {
                    arrowLeft = (rect.right + rect.left) / 2 - 60.dp2px()
                    layoutParams.setMargins(60.dp2px(), rect.bottom, 15.dp2px(), 0)
                } else {
                    layoutParams.setMargins(40.dp2px(), rect.bottom, 40.dp2px(), 0)
                    arrowLeft = (rect.right + rect.left) / 2 - 40.dp2px()
                }
                bind.llContent.layoutParams = layoutParams
            }
            bind.ivArrow.apply {
                val layoutParams = this.layoutParams as LinearLayout.LayoutParams
                layoutParams.setMargins(arrowLeft, 0, 0, 0)
                bind.ivArrow.layoutParams = layoutParams
            }
            val targetStep = Target.Builder()
                .setAnchor(
                    (rect.left + ((rect.right - rect.left) / 2)).toFloat(),
                    (rect.top + ((rect.bottom - rect.top) / 2).toFloat())
                )
                .setShape(
                    StrokeRectangleShape(
                        (rect.bottom - rect.top).toFloat(),
                        (rect.right - rect.left).toFloat(),
                        8f
                    )
                )
                .setOverlay(bind.root)
                .setOnTargetListener(object : OnTargetListener {
                    override fun onStarted() {
                    }

                    override fun onEnded() {
                    }
                })
                .build()
            targets.add(targetStep)
        }
    }
    spotlight = Spotlight.Builder(context)
        .setTargets(targets)
        .setBackgroundColorRes(R.color.spotlightBackground)
        .setDuration(1000L)
        .setAnimation(DecelerateInterpolator(2f))
        .setOnSpotlightListener(object : OnSpotlightListener {
            override fun onStarted() {
                LogUtils.d("Spotlight-onStarted")
            }

            override fun onEnded() {
                onEndCallBack?.invoke()
                LogUtils.d("Spotlight-onEnded")
            }
        })
        .build()
    spotlight.start()

}