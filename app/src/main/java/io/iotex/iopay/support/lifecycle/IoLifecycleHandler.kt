package io.iotex.iopay.support.lifecycle

import android.app.Activity
import android.app.Application
import android.os.Bundle
import com.blankj.utilcode.util.SnackbarUtils
import com.blankj.utilcode.util.ToastUtils
import io.iotex.iopay.R
import io.iotex.iopay.util.DialogUtil

class IoLifecycleHandler : Application.ActivityLifecycleCallbacks {
    var store = mutableListOf<Activity>()

    override fun onActivityCreated(activity: Activity, savedInstanceState: Bundle?) {
        store.add(activity)
    }

    override fun onActivityStarted(activity: Activity) {
        ++started
        if(timestamp.isNotEmpty()){
            SnackbarUtils.dismiss()
            DialogUtil.showTransferLoading(timestamp)
        }
    }

    override fun onActivityResumed(activity: Activity) {
        ++resumed
    }

    override fun onActivityPaused(activity: Activity) {
        ++paused
    }

    override fun onActivityStopped(activity: Activity) {
        ++stopped
        if (!isApplicationInForeground) {
            ToastUtils.showShort(R.string.You_have_left_iopay)
        }
    }

    override fun onActivityDestroyed(activity: Activity) {
        store.remove(activity)
    }

    override fun onActivitySaveInstanceState(activity: Activity, outState: Bundle) {}

    companion object {
        private var resumed: Int = 0
        private var paused: Int = 0
        private var started: Int = 0
        private var stopped: Int = 0

        var timestamp = ""

        val isApplicationInForeground: Boolean
            get() = resumed > paused
    }
}
