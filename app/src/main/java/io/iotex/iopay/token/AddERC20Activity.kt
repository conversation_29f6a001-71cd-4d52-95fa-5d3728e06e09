package io.iotex.iopay.token

import android.annotation.SuppressLint
import androidx.lifecycle.lifecycleScope
import com.blankj.utilcode.util.KeyboardUtils
import com.blankj.utilcode.util.TimeUtils
import com.blankj.utilcode.util.Utils
import io.iotex.base.BaseViewModel
import io.iotex.iopay.R
import io.iotex.iopay.base.BaseBindToolbarActivity
import io.iotex.iopay.data.UserStore
import io.iotex.iopay.data.db.AppDatabase
import io.iotex.iopay.databinding.ActivityAddErc20Binding
import io.iotex.iopay.data.db.LIKE_STATUS_LIKE
import io.iotex.iopay.data.db.LIKE_STATUS_NORMAL
import io.iotex.iopay.data.db.TokenCacheEntry
import io.iotex.iopay.data.db.TokenEntry
import io.iotex.iopay.support.eventbus.AddERC20Event
import io.iotex.iopay.support.eventbus.FavoriteOrDislikeERC20Event
import io.iotex.iopay.util.FireBaseEvent
import io.iotex.iopay.util.FireBaseUtil
import io.iotex.iopay.util.RxUtil
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.util.extension.asBigDecimal
import io.iotex.iopay.util.extension.isNumeric
import io.iotex.iopay.util.extension.toEvmAddress
import io.iotex.iopay.util.extension.toast
import io.iotex.iopay.wallet.solana.SolanaWeb3
import io.iotex.iopay.wallet.web3.Web3Delegate
import io.reactivex.Observable
import io.reactivex.android.schedulers.AndroidSchedulers
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.greenrobot.eventbus.EventBus
import java.math.BigDecimal
import java.util.concurrent.TimeUnit

class AddERC20Activity : BaseBindToolbarActivity<BaseViewModel, ActivityAddErc20Binding>(R.layout.activity_add_erc20) {

    private var mContractValid = false

    override fun initView() {
        setToolbarTitle("${UserStore.getNetWorkTokensAlias()} ${getString(R.string.token_lower_case)}")
        setToolbarSubmitText(getString(R.string.save))
        setToolbarSubmitClick {
            addERC20Token(mBinding.mEtContract.text().trim())
        }
        setToolbarSubmit(false)
        verifyForm()

        mBinding.mEtName.isEnabled = WalletHelper.isSolanaNetwork()
        mBinding.mEtSymbol.isEnabled = WalletHelper.isSolanaNetwork()
        mBinding.mEtDecimals.isEnabled = false

        mBinding.mTvContractAddress.text =
            "${UserStore.getNetWorkTokensAlias()} ${getString(R.string.token_address)}"

        val sub = RxUtil.textChange(mBinding.mEtContract.editText())
            .debounce(1, TimeUnit.SECONDS)
            .compose(RxUtil.applySchedulers())
            .subscribe {
                mContractValid = validContract(it.trim())
                if (!mContractValid) {
                    mBinding.mEtContract.setErrorInfo(getString(R.string.address_invalid))
                } else {
                    KeyboardUtils.hideSoftInput(this)
                    mBinding.mEtContract.setRight()
                    resolveToken(it)
                }
            }
    }

    private fun validContract(contract: String): Boolean {
        if (WalletHelper.isBitcoinNetwork()) {
            return false
        }
        if (WalletHelper.isSolanaNetwork() && WalletHelper.isSolanaAddress(contract)) {
            return true
        }
        return WalletHelper.isEvmAddress(contract)
    }

    @SuppressLint("CheckResult")
    private fun verifyForm() {
        val symbolOb = RxUtil.textChange(mBinding.mEtSymbol.editText()).debounce(800, TimeUnit.MILLISECONDS)
        val nameOb = RxUtil.textChange(mBinding.mEtName.editText()).debounce(800, TimeUnit.MILLISECONDS)
        val decimalsOb =
            RxUtil.textChange(mBinding.mEtDecimals.editText()).debounce(800, TimeUnit.MILLISECONDS)
        Observable.combineLatest(symbolOb, nameOb, decimalsOb) { symbol, name, decimals ->
            val symbolValid = symbol.isNotBlank()
            runOnUiThread {
                if (!symbolValid) {
                    mBinding.mEtSymbol.setErrorInfo(getString(R.string.input_symbol))
                } else {
                    mBinding.mEtSymbol.setRight()
                }
            }

            val nameValid = name.isNotBlank()
            runOnUiThread {
                if (!nameValid) {
                    mBinding.mEtName.setErrorInfo(getString(R.string.input_name))
                } else {
                    mBinding.mEtName.setRight()
                }
            }
            val decimalsValid = decimals.isNotBlank() && decimals.isNumeric()
            runOnUiThread {
                if (decimals.isBlank()) {
                    mBinding.mEtDecimals.setErrorInfo(getString(R.string.input_decimals))
                } else if (!decimals.isNumeric()) {
                    mBinding.mEtDecimals.setErrorInfo(getString(R.string.decimals_error))
                } else if (decimals.asBigDecimal() < BigDecimal.ZERO) {
                    mBinding.mEtContract.setErrorInfo(getString(R.string.address_invalid))
                } else {
                    mBinding.mEtDecimals.setRight()
                }
            }

            return@combineLatest symbolValid && nameValid && decimalsValid
        }.observeOn(AndroidSchedulers.mainThread())
            .subscribe {
                setToolbarSubmit(it && mContractValid)
            }
    }

    private fun resolveToken(contract: String) {
        if (WalletHelper.isBitcoinNetwork()) return
        showLoading()
        val errorHandler = CoroutineExceptionHandler { _, e ->
            hideLoading()
            mBinding.mEtContract.setErrorInfo(getString(R.string.network_anomaly))
        }
        lifecycleScope.launch(errorHandler) {
            if (WalletHelper.isSolanaNetwork()) {
                val decimals = withContext(Dispatchers.IO) {
                    SolanaWeb3.getTokenAccountDecimals(contract)
                }
                mBinding.mEtDecimals.setText(decimals.toString())
            } else {
                withContext(Dispatchers.IO) {
                    val decimals = Web3Delegate.erc20Decimals(contract.toEvmAddress())
                    val symbol = Web3Delegate.erc20Symbol(contract.toEvmAddress())
                    val name = Web3Delegate.erc20Name(contract.toEvmAddress())
                    withContext(Dispatchers.Main) {
                        mBinding.mEtName.setText(name)
                        mBinding.mEtSymbol.setText(symbol)
                        mBinding.mEtDecimals.setText(decimals.toString())
                    }
                }
            }
            hideLoading()
        }
    }

    private fun addERC20Token(contract: String) {
        val name = mBinding.mEtName.text().trim()
        val symbol = mBinding.mEtSymbol.text().trim()
        val decimals = mBinding.mEtDecimals.text().trim()
        insertCustomToken(contract.lowercase(), "", symbol, decimals, name, "0") {
            Utils.getApp().getString(R.string.success).toast()
            finish()
        }
    }

}

fun insertCustomToken(
    contract: String,
    pubkey: String,
    symbol: String,
    decimals: String,
    name: String,
    amount: String,
    callback: (() -> Unit)? = null
) {
    if (contract.isBlank() || name.isBlank() || symbol.isBlank() || decimals.isBlank()) return
    val errorHandler = CoroutineExceptionHandler { _, e ->
    }
    CoroutineScope(Dispatchers.IO + errorHandler).launch {
        val wallet = WalletHelper.getCurWallet() ?: return@launch
        val ethAddress = WalletHelper.convertWeb3Address(contract)

        var token = AppDatabase.getInstance(Utils.getApp()).tokenDao()
            .queryByAddress(WalletHelper.getCurChainId(), ethAddress)

        if (token == null) {
            token = TokenEntry(
                System.currentTimeMillis().toString(),
                WalletHelper.getCurChainId(),
                ethAddress,
                name,
                symbol,
                decimals.toIntOrNull() ?: 0,
                "",
                "0",
                false,
                "",
                true,
                isDepinToken = false,
                isOfficial = false,
                updateTime = TimeUtils.getNowMills().toString(),
            )
        } else {
            token.symbol = token.symbol.ifEmpty { symbol }
            token.decimals = decimals.toIntOrNull() ?: 18
        }
        AppDatabase.getInstance(Utils.getApp()).tokenDao().insertOrReplace(token)

        var erc20Entry = AppDatabase.getInstance(Utils.getApp()).tokenCacheDao().queryByAddress(
            wallet.address, WalletHelper.getCurChainId(), contract
        )

        if (erc20Entry == null) {
            erc20Entry = TokenCacheEntry(
                wallet.address,
                WalletHelper.getCurChainId(),
                contract,
                amount,
                LIKE_STATUS_LIKE,
                pubkey,
            )
            erc20Entry.let {
                AppDatabase.getInstance(Utils.getApp()).tokenCacheDao().insertOrReplace(it)
            }
        } else {
            if (erc20Entry.likeStatus == LIKE_STATUS_NORMAL) {
                erc20Entry.likeStatus = LIKE_STATUS_LIKE
            }
            erc20Entry.pubkey = pubkey
            erc20Entry.balance = amount
            AppDatabase.getInstance(Utils.getApp()).tokenCacheDao().insertOrReplace(erc20Entry)
        }
        EventBus.getDefault().post(FavoriteOrDislikeERC20Event())
        EventBus.getDefault().post(AddERC20Event())
        FireBaseUtil.logFireBase(FireBaseEvent.ACTION_CUSTOM_TOKEN_CLICK_SAVE)
        callback?.invoke()
    }
}

