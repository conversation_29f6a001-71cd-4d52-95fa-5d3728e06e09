package io.iotex.iopay.token

import io.iotex.base.BaseViewModel
import io.iotex.iopay.R
import io.iotex.iopay.SchemeUtil
import io.iotex.iopay.base.BaseBindToolbarActivity
import io.iotex.iopay.databinding.ActivityStakeLearnMoreBinding

class StakeLearnMoreActivity :
    BaseBindToolbarActivity<BaseViewModel, ActivityStakeLearnMoreBinding>(R.layout.activity_stake_learn_more) {
    override fun initView() {
        setToolbarTitle(getString(R.string.learn_more_cap))
        mBinding.tvStakeStart.setOnClickListener {
            SchemeUtil.goto(this, SchemeUtil.SCHEME_STAKE_PAGE)
        }
    }
}