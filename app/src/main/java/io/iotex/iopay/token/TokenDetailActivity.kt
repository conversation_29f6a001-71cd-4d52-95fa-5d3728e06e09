package io.iotex.iopay.token

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.util.DisplayMetrics
import androidx.core.view.isVisible
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.LinearSmoothScroller
import com.blankj.utilcode.util.ClipboardUtils
import com.blankj.utilcode.util.ColorUtils
import com.blankj.utilcode.util.KeyboardUtils
import com.blankj.utilcode.util.ToastUtils
import com.blankj.utilcode.util.Utils
import com.drakeet.multitype.MultiTypeAdapter
import com.machinefi.w3bstream.utils.extension.setForeTextColor
import com.robinhood.ticker.TickerUtils
import io.iotex.base.bindbase.BaseBindActivity
import io.iotex.iopay.R
import io.iotex.iopay.SchemeUtil
import io.iotex.iopay.data.UserStore
import io.iotex.iopay.databinding.ActivityTokenDetailBinding
import io.iotex.iopay.support.eventbus.ActionRefreshEvent
import io.iotex.iopay.data.db.AddressType_Legacy
import io.iotex.iopay.data.db.DiscoverDApps
import io.iotex.iopay.home.SwapActivity
import io.iotex.iopay.token.item.BinoQuestionBinder
import io.iotex.iopay.token.item.StockTimeBinder
import io.iotex.iopay.token.item.TokenMarketBinder
import io.iotex.iopay.transaction.bean.OptionEntry
import io.iotex.iopay.util.Config
import io.iotex.iopay.util.Config.BSC_CHAIN_ID
import io.iotex.iopay.util.Config.ETH_CHAIN_ID
import io.iotex.iopay.util.Config.IOTEX_CHAIN_ID
import io.iotex.iopay.util.Config.POLYGON_CHAIN_ID
import io.iotex.iopay.util.Constant
import io.iotex.iopay.util.FireBaseEvent
import io.iotex.iopay.util.FireBaseUtil
import io.iotex.iopay.util.IoPayConstant
import io.iotex.iopay.util.PageEventUtil
import io.iotex.iopay.util.SignUtils
import io.iotex.iopay.util.TokenUtil
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.util.extension.loadSvgOrImage
import io.iotex.iopay.util.extension.setGone
import io.iotex.iopay.util.extension.setVisible
import io.iotex.iopay.util.extension.toTokenBitmap
import io.iotex.iopay.util.extension.toast
import io.iotex.iopay.wallet.receive.ReceiveActivity
import io.iotex.iopay.wallet.TransferActivity
import io.iotex.iopay.wallet.add.WalletAddActivity
import io.iotex.iopay.xapp.XAppsActivity
import io.iotex.iopay.wallet.home.bean.TokenDetailBean
import io.iotex.iopay.wallet.receive.ReceiveDetailActivity
import io.iotex.iopay.xapp.XAppFragment
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.jetbrains.anko.startActivity

class TokenDetailActivity :
    BaseBindActivity<TokenDetailViewModel, ActivityTokenDetailBinding>(R.layout.activity_token_detail) {

    private val mAdapter = MultiTypeAdapter()
    private val mAdapterBino = MultiTypeAdapter()
    private val mAdapterStoke = MultiTypeAdapter()

    private var webViewFrag:XAppFragment?=null

    override fun initView() {
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this)
        }

        mBinding.ivBack.setOnClickListener {
            finish()
        }
        mBinding.tvName.text = tokenDetailBean?.name

        mBinding.ivDex.isVisible = WalletHelper.isIoTexNetWork() && !UserStore.getAllNetwork()
        mBinding.ivDex.setOnClickListener {
            startActivity<XAppsActivity>(
                IoPayConstant.BROWSER_URL to Config.DEX_SCREENER_URL + tokenDetailBean?.address
            )
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_TOKEN_DETAIL_DEX_SCAN)
        }

        mBinding.mSrfRefresh.setOnRefreshListener {
            mViewModel.getBalance(tokenDetailBean)
        }

        mBinding.recyclerView.layoutManager = LinearLayoutManager(this)
        mBinding.recyclerView.adapter = mAdapter
        mAdapter.register(OptionEntry::class.java, TokenMarketBinder())

        mBinding.recyclerViewStock.layoutManager =  GridLayoutManager(this,4)
        mBinding.recyclerViewStock.adapter = mAdapterStoke
        mAdapterStoke.items = arrayListOf(
            Utils.getApp().getString(R.string.one_day),
            Utils.getApp().getString(R.string.one_week),
            Utils.getApp().getString(R.string.one_month),
            Utils.getApp().getString(R.string.one_year),)
        mAdapterStoke.register(String::class.java,StockTimeBinder().apply {
            onItemClick = {time ->
                mAdapterStoke.notifyDataSetChanged()
                mViewModel.getTokenStock(tokenDetailBean,time.start, time.end)
            }
        })

        val layoutManager = LinearLayoutManager(this,LinearLayoutManager.HORIZONTAL,false)
        mBinding.recyclerViewBino.layoutManager = layoutManager
        mBinding.recyclerViewBino.adapter = mAdapterBino
        mAdapterBino.register(String::class.java, BinoQuestionBinder().apply {
            onItemClick = {
                webViewFrag?.evaluateMessage(it)
                mBinding.flBino.setVisible()
                mBinding.flBino.post {
                    webViewFrag?.presetAddress(UserStore.getWalletAddress())
                }
                mBinding.mDragBtnBino.setGone()
                val bundle = Bundle()
                bundle.putString("quest", it)
                FireBaseUtil.logFireBase(FireBaseEvent.ACTION_TOKEN_DETAIL_BINO_QUEST,bundle)
            }
        })
        mBinding.ivLeft.setOnClickListener {
            val first = layoutManager.findFirstVisibleItemPosition()
            if (first > 1) {
                smoothScrollTo(first -1)
            }
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_TOKEN_DETAIL_BINO_QUEST_LEFT)
        }
        mBinding.ivRight.setOnClickListener {
            val first = layoutManager.findFirstVisibleItemPosition()
            if (first < mAdapterBino.items.size) {
                smoothScrollTo(first + 1)
            }
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_TOKEN_DETAIL_BINO_QUEST_RIGHT)
        }
        initBino()
        mBinding.ivClose.setOnClickListener {
            mBinding.flBino.setGone()
            mBinding.mDragBtnBino.setVisible()
        }
        mBinding.vBack.setOnClickListener {
            mBinding.flBino.setGone()
            mBinding.mDragBtnBino.setVisible()
        }
        mBinding.mDragBtnBino.setOnClickListener {
            mBinding.flBino.setVisible()
            mBinding.flBino.post {
                webViewFrag?.presetAddress(UserStore.getWalletAddress())
            }
            mBinding.mDragBtnBino.setGone()
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_TOKEN_DETAIL_BINO_FLOAT_BUTTON)
        }

        val chain = tokenDetailBean?.chainId
        mBinding.llSwapLayout.isVisible = !UserStore.getAllNetwork()
                && (Config.SWAP_SUPPORT_CHAIN.contains(chain) || UserStore.getNetWorkSwapUrl().isNotEmpty())
    }

    private fun smoothScrollTo(position: Int) {
        val scroller = object : LinearSmoothScroller(this) {
            override fun getHorizontalSnapPreference(): Int = SNAP_TO_START

            override fun calculateSpeedPerPixel(displayMetrics: DisplayMetrics): Float {
                return 25f / displayMetrics.densityDpi
            }
        }
        scroller.targetPosition = position
        mBinding.recyclerViewBino.layoutManager?.startSmoothScroll(scroller)
    }

    @SuppressLint("CommitTransaction")
    private fun initBino(){
        val baseUrl = UserStore.getBinoAiUrl().ifEmpty {
            Config.BINO_AI_WEB_URL
        }
        val url = if (UserStore.isDarkTheme()) {
            "${baseUrl}?theme=dark"
        } else {
            "${baseUrl}?theme=light"
        }
        webViewFrag = XAppFragment.newInstance(url, "", "", false).apply {
            supportFragmentManager.beginTransaction().add(R.id.flWebView, this).commit()
        }
        KeyboardUtils.registerSoftInputChangedListener(this) { height ->
            if (height > 100) {
                val layoutParams = mBinding.keyboardView.layoutParams
                layoutParams.height = height
                mBinding.keyboardView.layoutParams = layoutParams
            } else {
                val layoutParams = mBinding.keyboardView.layoutParams
                layoutParams.height = 0
                mBinding.keyboardView.layoutParams = layoutParams
            }
        }
    }

    override fun initData() {
        if (tokenDetailBean?.isVisitorToken == true) {
            mBinding.llNewWallet.setVisible()
            mBinding.llAction.setGone()
            mBinding.mTvBalance.setGone()
            mBinding.llAddress.setGone()
            mBinding.llStake.setGone()
            mBinding.tvAddWallet.setOnClickListener {
                WalletAddActivity.startActivity(this)
            }
        } else {
            mBinding.llNewWallet.setGone()
            mBinding.llAction.setVisible()
            mBinding.mTvBalance.setVisible()
            mBinding.llAddress.setVisible()
            if (tokenDetailBean?.chainId == IOTEX_CHAIN_ID && tokenDetailBean?.address.isNullOrEmpty()) {
                mBinding.llStake.setVisible()
                mBinding.tvStakeLearn.setOnClickListener {
                    startActivity(Intent(this, StakeLearnMoreActivity::class.java))
                    FireBaseUtil.logFireBase(FireBaseEvent.ACTION_TOKEN_DETAIL_LEARN_MORE)
                }
                mBinding.tvStakeStart.setOnClickListener {
                    SchemeUtil.goto(this, SchemeUtil.SCHEME_STAKE_PAGE)
                    FireBaseUtil.logFireBase(FireBaseEvent.ACTION_TOKEN_DETAIL_STARK_STAKE)
                }
                mBinding.llStakeInfo.setOnClickListener {
                    SchemeUtil.goto(this, SchemeUtil.SCHEME_STAKE_PAGE)
                    FireBaseUtil.logFireBase(FireBaseEvent.ACTION_TOKEN_DETAIL_STAKE_VIEW)
                }
            } else {
                mBinding.llStake.setGone()
            }
        }


        if (tokenDetailBean?.address.isNullOrEmpty() || tokenDetailBean?.isVisitorToken == true) {
            if (!tokenDetailBean?.website.isNullOrEmpty() && tokenDetailBean?.website != "null") {
                mViewModel.getDAppTools(tokenDetailBean?.website?:"")
                mBinding.ivWebsite.setOnClickListener {
                    startActivity<XAppsActivity>(
                        IoPayConstant.BROWSER_URL to tokenDetailBean?.website
                    )
                    FireBaseUtil.logFireBase(FireBaseEvent.ACTION_TOKEN_DETAIL_TOKEN_SCAN)
                }
            }
        } else {
            mBinding.ivWebsite.setOnClickListener {
                WalletHelper.gotoExplorerTokenScan(
                    tokenDetailBean?.address ?: "",
                    tokenDetailBean?.chainId ?: WalletHelper.getCurChainId()
                )
                FireBaseUtil.logFireBase(FireBaseEvent.ACTION_TOKEN_DETAIL_TOKEN_SCAN)
            }
        }

        action()

        if (tokenDetailBean?.logo.isNullOrEmpty() && !tokenDetailBean?.symbol.isNullOrEmpty()) {
            mBinding.mIvLogo.setImageBitmap(tokenDetailBean?.symbol?.toTokenBitmap(this))
        } else {
            mBinding.mIvLogo.loadSvgOrImage(tokenDetailBean?.logo, R.drawable.icon_token_default)
        }
        mBinding.llAddress.isVisible = !tokenDetailBean?.address.isNullOrEmpty()
        mBinding.tvAddress.text = tokenDetailBean?.address
        mBinding.mTvSymbol.text = tokenDetailBean?.symbol

        val map = HashMap<String, String>()
        map["contract"] = tokenDetailBean?.address ?: ""
        map["symbol"] = tokenDetailBean?.symbol ?: ""
        PageEventUtil.logEvent(PageEventUtil.TOKEN, map)
        mViewModel.getBalance(tokenDetailBean)
        mViewModel.balanceLiveData.observe(this) {
            mBinding.mSrfRefresh.isRefreshing = false
            mBinding.mTvBalance.isVisible = tokenDetailBean?.isVisitorToken == false
            mBinding.mTvBalance.setCharacterLists(TickerUtils.provideNumberList())
            mBinding.mTvBalance.text = it
            if (tokenDetailBean?.price.isNullOrEmpty()) {
                mBinding.tvPrice.text = "$ --"
            } else {
                mBinding.tvPrice.text = "$ " + TokenUtil.displayPrice(tokenDetailBean?.price ?: "")
            }
        }

        mViewModel.getMarketValue(tokenDetailBean?.id?:"")
        mViewModel.marketLiveData.observe(this){
            if(it.isNullOrEmpty()){
                mBinding.tvMarket.setGone()
            }else{
                mBinding.tvMarket.setVisible()
            }
            mAdapter.items = it
            mAdapter.notifyDataSetChanged()
        }

        mViewModel.launchDAppLiveData.observe(this) { discoverDApp ->
            if (UserStore.getAllNetwork()) return@observe
            mBinding.llLaunchLayout.setVisible()
            mBinding.mLlLaunch.setOnClickListener {
                goDAppBrowser(discoverDApp)
                FireBaseUtil.logFireBase(FireBaseEvent.ACTION_TOKEN_DETAIL_CLICK_LAUNCH_APP)
            }
        }

        if (tokenDetailBean?.isVisitorToken == true) {
            mBinding.tags.setChains(tokenDetailBean?.chains ?: "")
        }

        val time = StockTimeBinder.getPositionTime(0)
        mViewModel.getTokenStock(tokenDetailBean,time.start,time.end)
        mViewModel.stockLiveData.observe(this){ list ->
            if (list == null) {
                mBinding.llError.setVisible()
                mBinding.llEmpty.setGone()
            } else if (list.isEmpty()) {
                mBinding.llError.setGone()
                mBinding.llEmpty.setVisible()
            } else {
                mBinding.llError.setGone()
                mBinding.llEmpty.setGone()
                mBinding.stockShadeView.setStockList(list)
            }
        }

        mViewModel.getQuestion(tokenDetailBean?.symbol)
        mViewModel.questionLiveData.observe(this){
            mBinding.llBino.isVisible = it.isNotEmpty()
            mAdapterBino.items = it
            mAdapterBino.notifyDataSetChanged()
        }

        mViewModel.getStakeToken()
        mViewModel.stakeTokenLiveData.observe(this){
            if(it == null){
                mBinding.llStartStake.setVisible()
                mBinding.llStakeInfo.setGone()
            } else {
                mBinding.llStartStake.setGone()
                mBinding.llStakeInfo.setVisible()
                mBinding.tvStakeAmount.text = it.displayAmount + " " + UserStore.getNetworkSymbol()
                mBinding.tvStakeValue.text = "$"+it.displayValue
                mBinding.tvStakeChange.text = it.displayValueChange
            }
        }
    }

    private fun goDAppBrowser(item: DiscoverDApps) {
        val intent = Intent(this, XAppsActivity::class.java)
        intent.putExtra(IoPayConstant.BROWSER_URL, item.url)
        intent.putExtra(IoPayConstant.BROWSER_DAPP_NAME, item.title)
        intent.putExtra(IoPayConstant.BROWSER_DAPP_LOGO, item.img_url)
        startActivity(intent)
        SignUtils.recordDAppRecord(this, item)
    }

    private fun action() {
        mBinding.ivCopy.setOnClickListener {
            ClipboardUtils.copyText(tokenDetailBean?.address)
            getString(R.string.copy_success).toast()
        }
        mBinding.mLlSend.setOnClickListener {
            if (!isWatchWallet()) {
                TransferActivity.start(this, contract = tokenDetailBean?.address)
                FireBaseUtil.logFireBase(FireBaseEvent.NAVIGATION_TO_TRANSFER)
            } else {
                ToastUtils.showShort(R.string.watch_wallet_warning)
            }
        }
        mBinding.mLlReceive.setOnClickListener {
            if (tokenDetailBean?.address.isNullOrEmpty()) {
                if (WalletHelper.isBitcoinNetwork(tokenDetailBean?.chainId?:UserStore.getChainId())) {
                    ReceiveActivity.startActivity(this, true)
                } else {
                    ReceiveDetailActivity.startActivity(this, tokenDetailBean?.chainId ?: 1)
                }
            } else {
                val addressType = Constant.currentWallet?.addressType ?: AddressType_Legacy
                ReceiveDetailActivity.startActivity(this, WalletHelper.getCurChainId(), addressType)
            }
            FireBaseUtil.logFireBase(FireBaseEvent.NAVIGATE_TO_RECEIVE)
        }

        mBinding.mLlSwap.setOnClickListener {
            if (isWatchWallet()) {
                ToastUtils.showShort(R.string.watch_wallet_warning)
                return@setOnClickListener
            }
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_TOKEN_DETAIL_SWAP)
            val chain = tokenDetailBean?.chainId

            if (Config.SWAP_SUPPORT_CHAIN.contains(chain)) {
                SwapActivity.startActivity(
                    this@TokenDetailActivity, UserStore.getChainId(),
                    tokenDetailBean?.address
                )
            } else {
                val intent = Intent(this@TokenDetailActivity, XAppsActivity::class.java)
                intent.putExtra(IoPayConstant.BROWSER_URL, UserStore.getNetWorkSwapUrl())
                startActivity(intent)
            }
        }
    }

    private fun isWatchWallet(): Boolean {
        return Constant.currentWallet?.isWatch == true
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onRefreshActionListEvent(event: ActionRefreshEvent) {
        mViewModel.getBalance(tokenDetailBean)
    }

    override fun onDestroy() {
        super.onDestroy()
        EventBus.getDefault().unregister(this)
    }

    override fun onBackPressed() {
        if (mBinding.flBino.isVisible) {
            mBinding.flBino.setGone()
            mBinding.mDragBtnBino.setVisible()
        } else {
            super.onBackPressed()
        }
    }

    companion object {
        private var tokenDetailBean: TokenDetailBean? = null
        fun startActivity(context: Context, tokenDetailBean: TokenDetailBean) {
            this.tokenDetailBean = tokenDetailBean
            context.startActivity(Intent(context, TokenDetailActivity::class.java))
        }
    }

}