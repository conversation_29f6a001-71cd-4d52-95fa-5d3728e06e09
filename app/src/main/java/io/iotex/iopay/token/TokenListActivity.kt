package io.iotex.iopay.token

import android.view.View
import androidx.core.widget.addTextChangedListener
import com.drakeet.multitype.MultiTypeAdapter
import io.iotex.iopay.R
import io.iotex.iopay.base.BaseBindToolbarActivity
import io.iotex.iopay.data.UserStore
import io.iotex.iopay.databinding.ActivityErc20ListBinding
import io.iotex.iopay.data.db.TokenEntry
import io.iotex.iopay.support.eventbus.AddERC20Event
import io.iotex.iopay.support.eventbus.TokenLoadFinishEvent
import io.iotex.iopay.token.item.TokenListItemBinder
import io.iotex.iopay.util.FireBaseEvent
import io.iotex.iopay.util.FireBaseUtil
import io.iotex.iopay.util.PageEventUtil
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.util.extension.removeItem
import io.iotex.iopay.util.extension.setGone
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.jetbrains.anko.startActivity

class TokenListActivity :
    BaseBindToolbarActivity<TokenListViewModel, ActivityErc20ListBinding>(R.layout.activity_erc20_list) {

    private val mAdapter = MultiTypeAdapter()

    override fun initView() {
        EventBus.getDefault().register(this)
        setToolbarTitle("${getString(R.string.add)} ${UserStore.getNetWorkTokensAlias()}")
        setToolbarSubmit(!WalletHelper.isBitcoinNetwork())
        setToolbarSubmitImage(R.drawable.icon_add_blue)
        setToolbarSubmitClick {
            startActivity<AddERC20Activity>()
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_TOKEN_LIST_CLICK_CUSTOM)
            PageEventUtil.logEvent(PageEventUtil.CUSTOMTOKEN)
        }

        mAdapter.register(TokenEntry::class.java, TokenListItemBinder().apply {
            onLikeOrUnlikeClick = {
                mViewModel.likeOrUnlikeToken(it)
            }
            onDeleteClick = { token ->
                mAdapter.removeItem<TokenEntry> { it == token }
                mViewModel.deleteToken(token)
            }
        })
        mBinding.mRvContent.adapter = mAdapter

        mBinding.refreshLayout.setOnRefreshListener {
            val search = mBinding.mEtSearch.text.toString()
            mViewModel.getAllToken(search)
            mViewModel.checkChainTokenList(true)
        }
        mBinding.mEtSearch.addTextChangedListener {
            if (it?.isNotEmpty() == true) {
                mBinding.mIvDelete.visibility = View.VISIBLE
            } else {
                mBinding.mIvDelete.visibility = View.GONE
            }
            mViewModel.getAllToken(it.toString())
        }

        mBinding.mIvDelete.setOnClickListener {
            mBinding.mEtSearch.setText("")
            mBinding.mIvDelete.visibility = View.GONE
        }

        PageEventUtil.logEvent(PageEventUtil.ADDXRC20)
    }

    override fun initData() {
        mViewModel.getAllToken("")
        mViewModel.allTokenListLiveData.observe(this) {
            mBinding.shimmer.shimmerLayout.setGone()
            mBinding.refreshLayout.isRefreshing = false
            mAdapter.items = it
            mAdapter.notifyDataSetChanged()
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event: AddERC20Event) {
        val search = mBinding.mEtSearch.text.toString()
        mViewModel.getAllToken(search)
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event: TokenLoadFinishEvent) {
        val search = mBinding.mEtSearch.text.toString()
        mViewModel.getAllToken(search)
    }

    override fun onDestroy() {
        super.onDestroy()
        EventBus.getDefault().unregister(this)
    }
}
