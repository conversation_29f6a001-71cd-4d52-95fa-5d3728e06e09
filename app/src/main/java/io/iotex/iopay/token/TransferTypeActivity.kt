package io.iotex.iopay.token

import android.app.Activity
import android.content.Intent
import android.text.Editable
import android.text.TextWatcher
import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import com.drakeet.multitype.MultiTypeAdapter
import io.iotex.iopay.R
import io.iotex.iopay.base.BaseBindToolbarActivity
import io.iotex.iopay.databinding.ActivityTransferTypeBinding
import io.iotex.iopay.data.db.TokenEntry
import io.iotex.iopay.token.item.TransferTypeBinder
import io.iotex.iopay.util.extension.setGone
import io.iotex.iopay.util.extension.setVisible
import io.iotex.iopay.wallet.TransferActivity.Companion.KEY_SELECTED_ADDRESS

class TransferTypeActivity :
    BaseBindToolbarActivity<TransferTypeViewModel, ActivityTransferTypeBinding>(R.layout.activity_transfer_type) {

    private val mAdapter = MultiTypeAdapter()

    override fun initView() {
        setToolbarTitle(getString(R.string.choose_token))
        mBinding.etSearch.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {

            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {

            }

            override fun afterTextChanged(s: Editable?) {
                s?.let {
                    val content = it.toString().trim()
                    if (content.isNotBlank()) {
                        mBinding.ivDelete.setVisible()
                        mViewModel.searchToken(content)
                    } else {
                        mBinding.ivDelete.setGone()
                        mViewModel.searchToken("")
                    }
                }
            }

        })
        mBinding.recyclerView.layoutManager = LinearLayoutManager(this)
        mBinding.recyclerView.adapter = mAdapter
        mAdapter.register(TokenEntry::class.java, TransferTypeBinder().apply {
            onItemClick = {
                Intent().apply {
                    putExtra(KEY_SELECTED_ADDRESS, it.address)
                }.also {
                    setResult(Activity.RESULT_OK, it)
                }
                finish()
            }
        })
    }

    override fun initData() {
        mViewModel.searchToken("")
        mViewModel.tokenListLiveData.observe(this) {
            if (it.isNotEmpty()) {
                mBinding.recyclerView.visibility = View.VISIBLE
                mBinding.noHistory.visibility = View.GONE
                mAdapter.items = it
                mAdapter.notifyDataSetChanged()
            } else {
                mBinding.recyclerView.visibility = View.GONE
                mBinding.noHistory.visibility = View.VISIBLE
            }
        }
    }
}