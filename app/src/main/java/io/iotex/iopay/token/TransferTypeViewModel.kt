package io.iotex.iopay.token

import android.app.Application
import androidx.lifecycle.MutableLiveData
import com.blankj.utilcode.util.Utils
import io.iotex.iopay.base.BaseLaunchVM
import io.iotex.iopay.data.UserStore
import io.iotex.iopay.data.db.AppDatabase
import io.iotex.iopay.data.db.LIKE_STATUS_LIKE
import io.iotex.iopay.data.db.TokenEntry
import io.iotex.iopay.repo.NativeTokenRepo
import io.iotex.iopay.util.Constant
import io.iotex.iopay.util.WalletHelper

class TransferTypeViewModel(application: Application) : BaseLaunchVM(application) {

    val tokenListLiveData = MutableLiveData<List<TokenEntry>>()

    private val tokenRepo by lazy { NativeTokenRepo() }

    fun searchToken(search: String) {
        addLaunch {
            val address = UserStore.getWalletAddress()
            val chainId = WalletHelper.getCurChainId()
            val list = AppDatabase.getInstance(Utils.getApp()).tokenCacheDao().queryByStatus(
                address, chainId,
                LIKE_STATUS_LIKE
            )
            val tokens = list.mapNotNull {
                AppDatabase.getInstance(Utils.getApp()).tokenDao().queryByAddress(it.chainId, it.address)
                    ?.apply {
                        balance = it.balance
                    }
            }.toMutableList()
            val networkToken = tokenRepo.getNetworkToken(Constant.currentWallet?.getCurNetworkAddress(WalletHelper.getCurChainId())?:"", WalletHelper.getCurChainId())
            networkToken?.let { tokens.add(0, it) }
            if (search.isEmpty()) {
                tokenListLiveData.postValue(tokens)
            } else {
                val searchData = mutableListOf<TokenEntry>()
                tokens.forEach {
                    if (it.name.lowercase().contains(search.lowercase())) {
                        searchData.add(it)
                    } else if (it.symbol.lowercase().contains(search.lowercase())) {
                        searchData.add(it)
                    } else if (it.address.lowercase().contains(search.lowercase())) {
                        searchData.add(it)
                    }
                }
                tokenListLiveData.postValue(searchData)
            }
        }
    }
}