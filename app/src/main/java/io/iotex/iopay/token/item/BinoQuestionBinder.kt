package io.iotex.iopay.token.item

import android.view.LayoutInflater
import android.view.ViewGroup
import com.drakeet.multitype.ItemViewBinder
import io.iotex.base.bindbase.BaseBindVH
import io.iotex.iopay.databinding.ItemBinoQuestionBinding

class BinoQuestionBinder:
    ItemViewBinder<String, BaseBindVH<ItemBinoQuestionBinding>>() {

    var onItemClick: ((item: String) -> Unit)? = null

    override fun onBindViewHolder(holder: BaseBindVH<ItemBinoQuestionBinding>, item: String) {
        holder.bind.tvItem.text = item
        holder.bind.root.setOnClickListener {
            onItemClick?.invoke(item)
        }
    }

    override fun onCreateViewHolder(
        inflater: LayoutInflater,
        parent: ViewGroup
    ): BaseBindVH<ItemBinoQuestionBinding> {
        return BaseBindVH(ItemBinoQuestionBinding.inflate(inflater, parent, false))
    }
}