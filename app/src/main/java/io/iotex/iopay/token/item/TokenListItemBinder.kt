package io.iotex.iopay.token.item

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isVisible
import com.drakeet.multitype.ItemViewBinder
import io.iotex.base.bindbase.BaseBindVH
import io.iotex.iopay.R
import io.iotex.iopay.databinding.ItemTokenListBinding
import io.iotex.iopay.data.db.LIKE_STATUS_LIKE
import io.iotex.iopay.data.db.LIKE_STATUS_UNLIKE
import io.iotex.iopay.data.db.TokenEntry
import io.iotex.iopay.token.TokenDetailActivity
import io.iotex.iopay.util.FireBaseEvent
import io.iotex.iopay.util.FireBaseUtil
import io.iotex.iopay.util.TokenUtil
import io.iotex.iopay.util.extension.loadSvgOrImage
import io.iotex.iopay.util.extension.toTokenBitmap
import io.iotex.iopay.wallet.home.bean.fromToken

class TokenListItemBinder :
    ItemViewBinder<TokenEntry, BaseBindVH<ItemTokenListBinding>>() {

    var onLikeOrUnlikeClick: ((item: TokenEntry) -> Unit)? = null
    var onDeleteClick: ((item: TokenEntry) -> Unit)? = null

    override fun onBindViewHolder(holder: BaseBindVH<ItemTokenListBinding>, item: TokenEntry) {
        val bind = holder.bind
        bind.swipeLayout.isSwipeEnabled = item.isCustomToken
        if (item.logo.isEmpty() && item.symbol.isNotEmpty()) {
            bind.mIvTokenLogo.setImageBitmap(item.symbol.toTokenBitmap(bind.mIvTokenLogo.context))
        } else {
            bind.mIvTokenLogo.loadSvgOrImage(item.logo, R.drawable.icon_token_default)
        }
        bind.mTvTokenName.text = item.name
        bind.mTvContractAddress.text = TokenUtil.textEllipsis(item.address, 6, 8)
        bind.mTvBalance.text = TokenUtil.displayBalance(
            TokenUtil.weiToTokenBN(
                item.balance,
                item.decimals.toLong()
            )
        )
        bind.mTvBalanceUnit.text = item.symbol
        likeOrUnlike(bind, item)

        bind.ivDepin.isVisible = item.isDepinToken
        bind.ivOfficial.isVisible = item.isOfficial
        bind.mIvStatus.setOnClickListener {
            item.status = if (item.status == LIKE_STATUS_LIKE) {
                LIKE_STATUS_UNLIKE
            } else {
                LIKE_STATUS_LIKE
            }
            likeOrUnlike(bind, item)
            onLikeOrUnlikeClick?.invoke(item)
            if (item.isDepinToken) {
                FireBaseUtil.logFireBase(FireBaseEvent.ACTION_ADD_TOKEN_DEPIN_TOKEN)
            }
        }

        bind.llDelete.setOnClickListener {
            onDeleteClick?.invoke(item)
        }

        bind.mRlContent.setOnClickListener {
            TokenDetailActivity.startActivity(
                bind.mRlContent.context,
                fromToken(item)
            )
            if (item.isDepinToken) {
                FireBaseUtil.logFireBase(FireBaseEvent.ACTION_HOME_TOKEN_LIST_DEPIN_TOKEN)
            }
        }
    }

    private fun likeOrUnlike(bind: ItemTokenListBinding, item: TokenEntry) {
        if (item.status == LIKE_STATUS_LIKE) {
            bind.mIvStatus.setImageResource(R.drawable.ic_token_iten_selected)
        } else {
            bind.mIvStatus.setImageResource(R.drawable.ic_token_item_add)
        }
    }

    override fun onCreateViewHolder(
        inflater: LayoutInflater,
        parent: ViewGroup
    ): BaseBindVH<ItemTokenListBinding> {
        return BaseBindVH(ItemTokenListBinding.inflate(inflater, parent, false))
    }

}