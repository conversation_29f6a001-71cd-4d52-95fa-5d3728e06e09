package io.iotex.iopay.token.item

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isVisible
import com.drakeet.multitype.ItemViewBinder
import io.iotex.base.bindbase.BaseBindVH
import io.iotex.iopay.databinding.ItemTokenMarketBinding
import io.iotex.iopay.transaction.bean.OptionEntry

class TokenMarketBinder : ItemViewBinder<OptionEntry, BaseBindVH<ItemTokenMarketBinding>>() {

    override fun onCreateViewHolder(inflater: LayoutInflater, parent: ViewGroup): BaseBindVH<ItemTokenMarketBinding> {
        val bind = ItemTokenMarketBinding.inflate(inflater,parent,false)
        return BaseBindVH(bind)
    }

    override fun onBindViewHolder(holder: BaseBindVH<ItemTokenMarketBinding>, item: OptionEntry) {
        holder.bind.mTvLabel.text = item.key
        holder.bind.mTvValue.text = item.value
        holder.bind.vLine.isVisible = holder.adapterPosition != adapterItems.size - 1
    }
}