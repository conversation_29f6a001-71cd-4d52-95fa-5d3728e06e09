package io.iotex.iopay.token.item

import android.content.Context
import android.content.Intent
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.fragment.app.FragmentManager
import com.blankj.utilcode.util.SPUtils
import com.drakeet.multitype.ItemViewBinder
import io.iotex.base.bindbase.BaseBindVH
import io.iotex.iopay.R
import io.iotex.iopay.dapp.dialog.DAppEnterTipsDialog
import io.iotex.iopay.data.db.DiscoverDApps
import io.iotex.iopay.databinding.ItemTokenToolsBinding
import io.iotex.iopay.util.IoPayConstant
import io.iotex.iopay.util.SPConstant
import io.iotex.iopay.util.SignUtils
import io.iotex.iopay.util.extension.loadSvgOrImage
import io.iotex.iopay.xapp.XAppsActivity

class TokenToolsItemBinder(val manager: FragmentManager) :
    ItemViewBinder<DiscoverDApps, BaseBindVH<ItemTokenToolsBinding>>() {

    override fun onBindViewHolder(holder: BaseBindVH<ItemTokenToolsBinding>, item: DiscoverDApps) {
        val bind = holder.bind
        bind.tvName.text = item.title
        bind.tvDesc.text = item.content
        bind.ivIcon.loadSvgOrImage(item.img_url, R.drawable.ic_dapp_placeholder)
        bind.root.setOnClickListener {
            onItemClick(bind.root.context, item)
        }
        bind.executePendingBindings()
    }

    override fun onCreateViewHolder(
        inflater: LayoutInflater,
        parent: ViewGroup
    ): BaseBindVH<ItemTokenToolsBinding> {
        return BaseBindVH(ItemTokenToolsBinding.inflate(inflater, parent, false))
    }

    private var dAppEnterTipsDialog: DAppEnterTipsDialog? = null

    private fun onItemClick(context: Context, item: DiscoverDApps) {
        if (SPUtils.getInstance()
                .getBoolean(SPConstant.SP_DAPP_ENTER_TIPS_UNDERSTOOD + item.title)
        ) {
            goDAppBrowser(context, item)
        } else {
            if (dAppEnterTipsDialog == null) {
                dAppEnterTipsDialog = DAppEnterTipsDialog(item.title).apply {
                    onConfirmClick = {
                        if (it) SPUtils.getInstance()
                            .put(SPConstant.SP_DAPP_ENTER_TIPS_UNDERSTOOD + item.title, true)
                        goDAppBrowser(context, item)
                    }
                    onCancelClick = {
                        dAppEnterTipsDialog = null
                    }
                }
                dAppEnterTipsDialog?.show(manager, System.currentTimeMillis().toString())
            }
        }
    }

    private fun goDAppBrowser(context: Context, item: DiscoverDApps) {
        val intent = Intent(context, XAppsActivity::class.java)
        intent.putExtra(IoPayConstant.BROWSER_URL, item.url)
        intent.putExtra(IoPayConstant.BROWSER_DAPP_NAME, item.title)
        intent.putExtra(IoPayConstant.BROWSER_DAPP_LOGO, item.img_url)
        context.startActivity(intent)
        SignUtils.recordDAppRecord(context, item)
    }
}