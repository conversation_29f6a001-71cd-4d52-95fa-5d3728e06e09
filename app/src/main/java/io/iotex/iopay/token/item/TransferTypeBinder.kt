package io.iotex.iopay.token.item

import android.view.LayoutInflater
import android.view.ViewGroup
import com.drakeet.multitype.ItemViewBinder
import io.iotex.base.bindbase.BaseBindVH
import io.iotex.iopay.R
import io.iotex.iopay.databinding.ItemTransferTypeBinding
import io.iotex.iopay.data.db.TokenEntry
import io.iotex.iopay.util.TokenUtil
import io.iotex.iopay.util.extension.loadSvgOrImage

class TransferTypeBinder : ItemViewBinder<TokenEntry, BaseBindVH<ItemTransferTypeBinding>>() {

    var onItemClick: ((TokenEntry) -> Unit)? = null

    override fun onBindViewHolder(holder: BaseBindVH<ItemTransferTypeBinding>, item: TokenEntry) {
        holder.bind.tvContent.text = item.name
        holder.bind.tvValue.text =
            "${TokenUtil.weiToTokenBN(item.balance, item.decimals.toLong())} ${item.symbol}"
        holder.bind.ivIcon.loadSvgOrImage(item.logo, R.drawable.icon_token_default)
        holder.bind.root.setOnClickListener {
            onItemClick?.invoke(item)
        }
    }

    override fun onCreateViewHolder(
        inflater: LayoutInflater,
        parent: ViewGroup
    ): BaseBindVH<ItemTransferTypeBinding> {
        val bind = ItemTransferTypeBinding.inflate(inflater, parent, false)
        return BaseBindVH(bind)
    }
}