package io.iotex.iopay.token.view

import android.content.Context
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.Path
import android.util.AttributeSet
import android.view.View
import com.blankj.utilcode.util.ColorUtils
import io.iotex.iopay.R
import io.iotex.iopay.util.extension.dp2px


class StockShadeView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    style: Int = 0
) : View(context, attrs, style) {
    private val linePaint = Paint()
    private val pathLine = Path()
    private var pointList: ArrayList<Float> = ArrayList()
    private var colorLine = ColorUtils.getColor(R.color.color_855eff)
    private var min = 0f
    private var max = 0f
    private var padding = 5f

    init {
        linePaint.isAntiAlias = true
        linePaint.color = colorLine
        linePaint.strokeWidth = 2.5f.dp2px().toFloat()
        linePaint.style = Paint.Style.STROKE

        setLayerType(LAYER_TYPE_SOFTWARE, null)
    }

    override fun onDraw(canvas: Canvas) {
        linePaint.color = colorLine
        if (pointList.size == 0) {
            canvas.drawLine(
                width.toFloat()/2 - 4.dp2px(),
                height.toFloat() / 2,
                width.toFloat()/2 + 4.dp2px(),
                height.toFloat() / 2,
                linePaint
            )
            return
        }

        if (max == min) {
            canvas.drawLine(
                0f,
                height.toFloat() / 2,
                width.toFloat(),
                height.toFloat() / 2,
                linePaint
            )
            return
        }

        val spaceX = width.toFloat() / (pointList.size - 1)
        val spaceY = (height - padding * 2.dp2px()) / (max - min)
        var lastX = 0f
        var lastY = height - (pointList[0] - min) * spaceY - padding.dp2px()
        pathLine.reset()
        pathLine.moveTo(lastX, lastY)
        for (i in 1 until pointList.size) {
            val x = spaceX * i
            val y = height - (pointList[i] - min) * spaceY - padding.dp2px()
            pathLine.quadTo(lastX, lastY, (x + lastX) / 2, (y + lastY) / 2)
            lastX = x
            lastY = y
        }
        pathLine.lineTo(lastX, lastY)
        canvas.drawPath(pathLine, linePaint)

    }

    fun setStockList(pointList: ArrayList<Float>?) {
        this.pointList = pointList?: ArrayList()
        this.padding = 50f
        if (pointList.isNullOrEmpty()) {
            colorLine = ColorUtils.getColor(R.color.gray_B4B8CB)
            invalidate()
            return
        }
        min = pointList[0]
        max = pointList[0]
        pointList.forEach {
            if (it < min) {
                min = it
            }
            if (it > max) {
                max = it
            }
        }
        colorLine = ColorUtils.getColor(R.color.color_855eff)

        invalidate()
    }
}