package io.iotex.iopay.transaction

import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import android.widget.PopupWindow
import com.blankj.utilcode.util.SPUtils
import com.blankj.utilcode.util.Utils
import io.iotex.base.bindbase.BaseBindDialog
import io.iotex.iopay.R
import io.iotex.iopay.data.UserStore
import io.iotex.iopay.databinding.DialogAdvanceGasFeeBinding
import io.iotex.iopay.databinding.ViewPopAdvanceBinding
import io.iotex.iopay.util.SPConstant.SP_ADVANCED_BASE_FEE
import io.iotex.iopay.util.SPConstant.SP_ADVANCED_PRIORITY_FEE
import io.iotex.iopay.util.TokenUtil
import io.iotex.iopay.util.extension.asBigDecimal
import io.iotex.iopay.util.extension.dp2px
import io.iotex.iopay.util.extension.setGone
import io.iotex.iopay.util.extension.setVisible
import org.web3j.utils.Convert
import java.math.BigDecimal

class AdvancedGasFeeDialog(
    private val defGasLimit: String,
    private val curBaseFee: String,
    private val curPriorityFee: String
) :
    BaseBindDialog<Nothing, DialogAdvanceGasFeeBinding>(R.layout.dialog_advance_gas_fee) {

    var onConfirmCallback: ((String, String, String) -> Unit)? = null

    override fun onStart() {
        super.onStart()
        val attributes = dialog?.window?.attributes
        attributes?.width = ViewGroup.LayoutParams.MATCH_PARENT
        attributes?.height = ViewGroup.LayoutParams.WRAP_CONTENT
        attributes?.gravity = Gravity.BOTTOM
        dialog?.window?.attributes = attributes
    }

    override fun initView() {
        mBinding.run {
            ivBack.setOnClickListener { dismissAllowingStateLoss() }
            etGasLimit.setText(defGasLimit)
            tvCurBaseFee.text = "$curBaseFee GWEI"
            tvCurPriorityFee.text = "$curPriorityFee GWEI"
            etMaxBaseFee.setText(SPUtils.getInstance().getString(SP_ADVANCED_BASE_FEE, curBaseFee))
            etPriorityFee.setText(SPUtils.getInstance().getString(SP_ADVANCED_PRIORITY_FEE, curPriorityFee))

            tvBaseFeeSymbol.text = TokenUtil.getNativeCurrencySymbol()
            tvPriorityFeeSymbol.text = TokenUtil.getNativeCurrencySymbol()

            tvEditGasLimit.setOnClickListener {
                llDisplayGasLimit.setGone()
                llEditGasLimit.setVisible()
            }

            ivBaseFee.setOnClickListener {
                showPopupWindow(it,Utils.getApp().getString(R.string.when_your_transaction_is_included_in_the_block))
            }

            ivPriorityFee.setOnClickListener {
                showPopupWindow(it,Utils.getApp().getString(R.string.priority_fee_goes_directly))
            }

            if (SPUtils.getInstance().getString(SP_ADVANCED_BASE_FEE, curBaseFee) != "0") {
                checkbox.isChecked = true
            }
            btnConfirm.setOnClickListener {
                val gasLimit = etGasLimit.text().trim()
                val baseFee = etMaxBaseFee.text().trim()
                val priorityFee = etPriorityFee.text().trim()
                if (checkbox.isChecked) {
                    SPUtils.getInstance().put(SP_ADVANCED_BASE_FEE, baseFee)
                    SPUtils.getInstance().put(SP_ADVANCED_PRIORITY_FEE, priorityFee)
                } else {
                    SPUtils.getInstance().remove(SP_ADVANCED_BASE_FEE)
                    SPUtils.getInstance().remove(SP_ADVANCED_PRIORITY_FEE)
                }
                onConfirmCallback?.invoke(gasLimit, baseFee, priorityFee)
                dismissAllowingStateLoss()
            }

            tvAdvance.text = Utils.getApp().getString(R.string.save_these_values_as_my_default, UserStore.getNetWorkName())
        }
    }

    override fun initData() {
        calculateValue()
    }

    private fun calculateValue() {
        val baseFee = mBinding.etMaxBaseFee.text().trim()
        val priorityFee = mBinding.etPriorityFee.text().trim()
        val gasLimit = mBinding.etGasLimit.text().trim()
        val bigBaseFeeValue = Convert.toWei(baseFee, Convert.Unit.GWEI).multiply(gasLimit.asBigDecimal())
        mBinding.tvBaseFeeValue.text = "~ ${TokenUtil.weiToTokenBN(bigBaseFeeValue.toString())}"

        val bigPriorityFeeValue = Convert.toWei(priorityFee, Convert.Unit.GWEI).multiply(gasLimit.asBigDecimal())
        mBinding.tvPriorityFeeValue.text = "~ ${TokenUtil.weiToTokenBN(bigPriorityFeeValue.toString())}"
    }

    private fun showPopupWindow(view: View, content:String) {
        val bind = ViewPopAdvanceBinding.inflate(LayoutInflater.from(view.context))
        val layoutParams = bind.ivArrow.layoutParams as LinearLayout.LayoutParams
        layoutParams.marginStart = (view.left+view.right)/2 - 18.dp2px()
        PopupWindow(
            bind.root,
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.WRAP_CONTENT
        ).apply {
            bind.tvInfo.text = content
            isOutsideTouchable = true
            showAsDropDown(view)
        }
    }

}