package io.iotex.iopay.transaction

import android.annotation.SuppressLint
import android.view.Gravity
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.lifecycle.lifecycleScope
import com.blankj.utilcode.util.SPUtils
import io.iotex.base.bindbase.BaseBindDialog
import io.iotex.iopay.R
import io.iotex.iopay.databinding.DialogBitcoinEditGasBinding
import io.iotex.iopay.meta.bean.UTXOBundle
import io.iotex.iopay.transaction.bean.BitcoinFeeData
import io.iotex.iopay.util.RxUtil
import io.iotex.iopay.util.SPConstant.SP_BITCOIN_CUSTOMIZE_FEE
import io.iotex.iopay.util.TokenUtil
import io.iotex.iopay.util.extension.fromSatoshis
import io.iotex.iopay.viewmodel.wallet.BitcoinViewModel
import io.iotex.iopay.wallet.bitcoin.BitcoinHelper
import kotlinx.coroutines.launch
import org.bitcoinj.core.Transaction
import java.math.BigInteger
import java.util.concurrent.TimeUnit

class BitcoinEditGasDialog(
    private val transaction: Transaction?,
    private val utxoBundle: UTXOBundle?,
    defLevel: Int,
) :
    BaseBindDialog<BitcoinViewModel, DialogBitcoinEditGasBinding>(R.layout.dialog_bitcoin_edit_gas) {

    var onConfirmCallback: ((
        fee: BigInteger,
        level: Int
    ) -> Unit)? = null

    private var bitcoinFeeData: BitcoinFeeData? = null
    private var selectLevel: Int = defLevel

    override fun onStart() {
        super.onStart()
        val attributes = dialog?.window?.attributes
        attributes?.width = ViewGroup.LayoutParams.MATCH_PARENT
        attributes?.height = ViewGroup.LayoutParams.WRAP_CONTENT
        attributes?.gravity = Gravity.BOTTOM
        dialog?.window?.attributes = attributes
    }

    @SuppressLint("CheckResult")
    override fun initView() {
        mBinding.run {
            ivBack.setOnClickListener { dismissAllowingStateLoss() }
            performSelected()
            tvSlowTime.text = getString(R.string.transaction_time, "60")
            tvAverageTime.text = getString(R.string.transaction_time, "30")
            tvFastTime.text = getString(R.string.transaction_time, "10")
            llSlowGasFee.setOnClickListener {
                selectLevel = BITCOIN_GAS_LEVEL_SLOW
                performSelected()
            }
            llAverageGasFee.setOnClickListener {
                selectLevel = BITCOIN_GAS_LEVEL_AVERAGE
                performSelected()
            }
            llFastGasFee.setOnClickListener {
                selectLevel = BITCOIN_GAS_LEVEL_FAST
                performSelected()
            }
            llEdit.setOnClickListener {
                selectLevel = BITCOIN_GAS_LEVEL_CUSTOMIZE
                performSelected()
            }
            tvCustomizeConfirm.setOnClickListener {
                val customizeFee = etCustomizeFee.text().toBigIntegerOrNull() ?: BigInteger.ZERO
                SPUtils.getInstance().put(SP_BITCOIN_CUSTOMIZE_FEE, customizeFee.toString())
                onConfirmCallback?.invoke(customizeFee, BITCOIN_GAS_LEVEL_CUSTOMIZE)
                dismiss()
            }
            RxUtil.textChange(etCustomizeFee.editText())
                .debounce(500, TimeUnit.MILLISECONDS)
                .compose(RxUtil.applySchedulers())
                .subscribe {
                    checkCustomizeFee()
                }

            etCustomizeFee.post {
                val customizeFee = SPUtils.getInstance().getString(SP_BITCOIN_CUSTOMIZE_FEE)
                val customizeFeeText =
                    if (customizeFee.isNotBlank()) "$customizeFee Sat/vB" else "0 Sat/vB"
                tvCustomizeFee.text = customizeFeeText
                tvCurCustomizeFee.text = customizeFeeText
                if (customizeFee.isNotBlank()) {
                    etCustomizeFee.setText(customizeFee)
                }
            }
            tvConfirm.setOnClickListener {
                val fee = when (selectLevel) {
                    BITCOIN_GAS_LEVEL_SLOW -> {
                        bitcoinFeeData?.slowFee ?: BigInteger.ZERO
                    }

                    BITCOIN_GAS_LEVEL_AVERAGE -> {
                        bitcoinFeeData?.averageFee ?: BigInteger.ZERO
                    }

                    BITCOIN_GAS_LEVEL_FAST -> {
                        bitcoinFeeData?.fastFee ?: BigInteger.ZERO
                    }

                    else -> {
                        val customizeFee = etCustomizeFee.text().toBigIntegerOrNull() ?: BigInteger.ZERO
                        SPUtils.getInstance().put(SP_BITCOIN_CUSTOMIZE_FEE, customizeFee.toString())
                        customizeFee
                    }
                }
                onConfirmCallback?.invoke(fee, selectLevel)
                dismiss()
            }
        }
    }

    private fun performSelected() {
        mBinding.llSlowGasFee.setBackgroundResource(R.drawable.shape_card_back)
        mBinding.llAverageGasFee.setBackgroundResource(R.drawable.shape_card_back)
        mBinding.llFastGasFee.setBackgroundResource(R.drawable.shape_card_back)
        mBinding.llCustomizeGasFee.setBackgroundResource(R.drawable.shape_card_back)
        when (selectLevel) {
            BITCOIN_GAS_LEVEL_SLOW -> {
                mBinding.llSlowGasFee.setBackgroundResource(R.drawable.shape_card_back_stroke_gradient)
                mBinding.llCustomizePanel.isVisible = false
                mBinding.ivCustomEdit.isVisible = true
                mBinding.ivCustomArrow.rotation = 0f
                mBinding.tvConfirm.isVisible = true
                mBinding.tvConfirm.isEnabled = true
                mBinding.tvConfirm.alpha = 1F
            }

            BITCOIN_GAS_LEVEL_AVERAGE -> {
                mBinding.llAverageGasFee.setBackgroundResource(R.drawable.shape_card_back_stroke_gradient)
                mBinding.llCustomizePanel.isVisible = false
                mBinding.ivCustomEdit.isVisible = true
                mBinding.ivCustomArrow.rotation = 0f
                mBinding.tvConfirm.isVisible = true
                mBinding.tvConfirm.isEnabled = true
                mBinding.tvConfirm.alpha = 1F
            }

            BITCOIN_GAS_LEVEL_FAST -> {
                mBinding.llFastGasFee.setBackgroundResource(R.drawable.shape_card_back_stroke_gradient)
                mBinding.llCustomizePanel.isVisible = false
                mBinding.ivCustomEdit.isVisible = true
                mBinding.ivCustomArrow.rotation = 0f
                mBinding.tvConfirm.isVisible = true
                mBinding.tvConfirm.isEnabled = true
                mBinding.tvConfirm.alpha = 1F
            }

            BITCOIN_GAS_LEVEL_CUSTOMIZE -> {
                mBinding.llCustomizeGasFee.setBackgroundResource(R.drawable.shape_card_back_stroke_gradient)
                if (mBinding.llCustomizePanel.isVisible) {
                    mBinding.llCustomizePanel.isVisible = false
                    mBinding.ivCustomEdit.isVisible = true
                    mBinding.ivCustomArrow.rotation = 0f
                    mBinding.tvConfirm.isVisible = true
                } else {
                    mBinding.llCustomizePanel.isVisible = true
                    mBinding.ivCustomEdit.isVisible = false
                    mBinding.ivCustomArrow.rotation = 180f
                    mBinding.tvConfirm.isVisible = false
                }
                checkCustomizeFee()
            }
        }
    }

    private fun checkCustomizeFee(): Boolean {
        val customizeFee = mBinding.etCustomizeFee.text().toBigIntegerOrNull() ?: BigInteger.ZERO
        return if (customizeFee <= BigInteger.ZERO) {
            mBinding.etCustomizeFee.notifyError(
                getString(
                    R.string.lower_limit_2,
                    getString(R.string.gas_fee_only),
                    "0"
                )
            )
            mBinding.tvCustomizeConfirm.isEnabled = false
            mBinding.tvCustomizeConfirm.alpha = 0.5F
            mBinding.tvConfirm.isEnabled = false
            mBinding.tvConfirm.alpha = 0.5F
            false
        } else {
            mBinding.etCustomizeFee.notifyNormal()
            mBinding.tvCustomizeConfirm.isEnabled = true
            mBinding.tvCustomizeConfirm.alpha = 1F
            mBinding.tvConfirm.isEnabled = true
            mBinding.tvConfirm.alpha = 1F
            true
        }
    }

    override fun initData() {
        mViewModel.getBitcoinFeeData()
    }

    override fun initEvent() {
        mViewModel.bitcoinFeeDataLD.observe(viewLifecycleOwner) {
            bitcoinFeeData = it
            lifecycleScope.launch {
                mBinding.run {
                    tvSlowFee.text = "${it.slowFee} Sat/vB"
                    tvAverageFee.text = "${it.averageFee} Sat/vB"
                    tvFastFee.text = "${it.fastFee} Sat/vB"
                    tvSlowSpentFee.isVisible = true
                    tvAverageSpentFee.isVisible = true
                    tvFastSpentFee.isVisible = true
                    val slowGas = if(transaction != null)BitcoinHelper.estimateGasFee(transaction, it.slowFee)
                    else BigInteger.ZERO
                    val slowGasMoney = slowGas.fromSatoshis().toBigDecimal()
                        .multiply(it.currencyPrice.toBigDecimal())
                    val displaySlowMoneyText = getString(
                        R.string.value_money,
                        TokenUtil.displayBalance(slowGasMoney.toString())
                    )
                    tvSlowSpentFee.text =
                        "${slowGas.fromSatoshis()} ${TokenUtil.getNativeCurrencySymbol()} $displaySlowMoneyText"
                    val averageGas = if(transaction!=null)BitcoinHelper.estimateGasFee(transaction, it.averageFee)
                    else BigInteger.ZERO
                    val averageGasMoney = averageGas.fromSatoshis().toBigDecimal()
                        .multiply(it.currencyPrice.toBigDecimal())
                    val displayAverageMoneyText = getString(
                        R.string.value_money,
                        TokenUtil.displayBalance(averageGasMoney.toString())
                    )
                    tvAverageSpentFee.text =
                        "${averageGas.fromSatoshis()} ${TokenUtil.getNativeCurrencySymbol()} $displayAverageMoneyText"
                    val fastGas = if(transaction!=null)BitcoinHelper.estimateGasFee(transaction, it.fastFee)
                    else BigInteger.ZERO
                    val fastGasMoney = fastGas.fromSatoshis().toBigDecimal()
                        .multiply(it.currencyPrice.toBigDecimal())
                    val displayFastMoneyText = getString(
                        R.string.value_money,
                        TokenUtil.displayBalance(fastGasMoney.toString())
                    )
                    tvFastSpentFee.text =
                        "${fastGas.fromSatoshis()} ${TokenUtil.getNativeCurrencySymbol()} $displayFastMoneyText"
                }
            }
        }
    }

    companion object {
        const val BITCOIN_GAS_LEVEL_SLOW = 1
        const val BITCOIN_GAS_LEVEL_AVERAGE = 2
        const val BITCOIN_GAS_LEVEL_FAST = 3
        const val BITCOIN_GAS_LEVEL_CUSTOMIZE = 4
    }
}