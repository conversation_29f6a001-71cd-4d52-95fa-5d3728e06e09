package io.iotex.iopay.transaction

import android.annotation.SuppressLint
import android.content.DialogInterface
import android.view.Gravity
import android.view.ViewGroup
import androidx.recyclerview.widget.LinearLayoutManager
import com.drakeet.multitype.MultiTypeAdapter
import io.iotex.base.bindbase.BaseBindDialog
import io.iotex.iopay.R
import io.iotex.iopay.databinding.DialogTransferBinding
import io.iotex.iopay.transaction.bean.ExtraHead
import io.iotex.iopay.transaction.bean.OptionEntry
import io.iotex.iopay.transaction.gas.BitcoinGasFragment
import io.iotex.iopay.transaction.item.EntryItemBinder
import io.iotex.iopay.util.extension.loadSvgOrImage
import io.iotex.iopay.util.extension.setGone
import io.iotex.iopay.util.extension.setVisible
import org.bitcoinj.core.Transaction
import java.math.BigInteger

class BitcoinTransactionDialog @JvmOverloads constructor(
    private val to: String,
    private val value: BigInteger,
    private val option: List<OptionEntry>,
    private val extraHead: ExtraHead? = null,
) : BaseBindDialog<TransactionViewModel, DialogTransferBinding>(R.layout.dialog_transfer) {

    var onTransactionConfirm: ((
        to: String,
        value: BigInteger,
        transaction: Transaction
    ) -> Unit)? = null
    private var confirm = false
    var onCancel: (() -> Unit)? = null

    private val mAdapter = MultiTypeAdapter()

    private var transaction: Transaction? = null

    @SuppressLint("CommitTransaction")
    override fun initView() {
        childFragmentManager.beginTransaction()
            .add(R.id.flGas, BitcoinGasFragment(to, value).apply {
                onBuildTransaction = { transaction: Transaction?  ->
                    <EMAIL> = transaction
                }
                onGasOrBalanceError = {
                    mBinding.mBtnConfirm.isEnabled = !it
                    mBinding.mBtnConfirm.alpha = if (it) 0.5F else 1F
                }
            }).commit()
        mBinding.recyclerView.layoutManager = LinearLayoutManager(context)
        mAdapter.register(OptionEntry::class.java, EntryItemBinder())
        mAdapter.items = option
        mBinding.recyclerView.adapter = mAdapter

        mBinding.mBtnCancel.setOnClickListener {
            dismiss()
        }
        mBinding.mBtnConfirm.setOnClickListener {
            confirm = transaction != null
            transaction?.let {
                onTransactionConfirm?.invoke(to, value, it)
            }
        }

        //avatar
        if (extraHead != null) {
            mBinding.llHead.setVisible()
            mBinding.tvSymbol.text = extraHead.name
            if (extraHead.isNft) {
                mBinding.flNftLogo.setVisible()
                mBinding.flToken.setGone()
                mBinding.ivNftLogo.loadSvgOrImage(extraHead.avatar, R.drawable.icon_nft_default)
            } else {
                mBinding.flNftLogo.setGone()
                mBinding.flToken.setVisible()
                mBinding.ivLogo.loadSvgOrImage(extraHead.avatar, R.drawable.icon_token_default)
            }
        }
    }

    override fun initData() {
        mViewModel.getCurNetwork()
        mViewModel.curNetworkLiveData.observe(this) {
            mBinding.ivNetwork.loadSvgOrImage(it.logo, R.drawable.ic_network_default)
            mBinding.ivNftNetwork.loadSvgOrImage(it.logo, R.drawable.ic_network_default)
        }
    }

    override fun onStart() {
        super.onStart()
        val attributes = dialog?.window?.attributes
        attributes?.width = ViewGroup.LayoutParams.MATCH_PARENT
        attributes?.height = ViewGroup.LayoutParams.WRAP_CONTENT
        attributes?.gravity = Gravity.BOTTOM
        dialog?.window?.attributes = attributes
    }

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        if (!confirm) onCancel?.invoke()
    }
}