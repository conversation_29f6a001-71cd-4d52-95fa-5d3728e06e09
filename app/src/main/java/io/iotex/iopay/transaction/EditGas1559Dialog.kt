package io.iotex.iopay.transaction

import android.content.DialogInterface
import android.view.Gravity
import android.view.ViewGroup
import androidx.recyclerview.widget.LinearLayoutManager
import com.blankj.utilcode.util.SPUtils
import com.drakeet.multitype.MultiTypeAdapter
import io.iotex.base.bindbase.BaseBindDialog
import io.iotex.iopay.R
import io.iotex.iopay.databinding.DialogEditGas1559Binding
import io.iotex.iopay.transaction.bean.EditGasItem
import io.iotex.iopay.transaction.bean.GasLevel.GAS_LEVEL_ADVANCED
import io.iotex.iopay.transaction.bean.GasLevel.GAS_LEVEL_HIGH
import io.iotex.iopay.transaction.bean.GasLevel.GAS_LEVEL_LOW
import io.iotex.iopay.transaction.bean.GasLevel.GAS_LEVEL_MARKET
import io.iotex.iopay.transaction.bean.GasTime
import io.iotex.iopay.transaction.bean.GasTrackerWrapper
import io.iotex.iopay.transaction.gas.GasViewModel
import io.iotex.iopay.transaction.item.EditGas1559ItemBinder
import io.iotex.iopay.util.Config
import io.iotex.iopay.util.SPConstant
import io.iotex.iopay.util.TokenUtil
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.util.extension.asBigDecimal
import io.iotex.iopay.util.extension.toFixed
import org.web3j.utils.Convert
import java.math.BigDecimal
import java.math.BigInteger
import kotlin.math.floor

class EditGas1559Dialog(
    private val defGasLimit: String,
    defMaxPriorityFeePerGas: BigInteger,
    defMaxFeePerGas: BigInteger,
    private val defLevel: Int,
) :
    BaseBindDialog<GasViewModel, DialogEditGas1559Binding>(R.layout.dialog_edit_gas_1559) {

    private val mAdapter = MultiTypeAdapter()
    val binder = EditGas1559ItemBinder()

    var onConfirmCallback: ((
        maxPriorityFeePerGas: BigInteger,
        maxFeePerGas: BigInteger,
        level: Int
    ) -> Unit)? = null

    private var gasLimit = defGasLimit
    private var maxPriorityFeePerGas: BigInteger = defMaxPriorityFeePerGas
    private var maxFeePerGas: BigInteger = defMaxFeePerGas
    private var gasTrackerWrapper: GasTrackerWrapper? = null
    private var gasTimeWrapper: GasTime? = null
    private var advancedBaseFee =
        SPUtils.getInstance().getString(SPConstant.SP_ADVANCED_BASE_FEE, "0")
    private var advancedPriorityFee =
        SPUtils.getInstance().getString(SPConstant.SP_ADVANCED_PRIORITY_FEE, "0")

    override fun onStart() {
        super.onStart()
        val attributes = dialog?.window?.attributes
        attributes?.width = ViewGroup.LayoutParams.MATCH_PARENT
        attributes?.height = ViewGroup.LayoutParams.WRAP_CONTENT
        attributes?.gravity = Gravity.BOTTOM
        dialog?.window?.attributes = attributes
    }

    override fun initView() {
        mBinding.run {
            ivBack.setOnClickListener { dismissAllowingStateLoss() }

            binder.selectLevel = defLevel
            binder.apply {
                onEditAdvancedFee = {
                    editAdvancedFee()
                }
                onClickItem = {
                    binder.selectLevel = it.level
                    mAdapter.notifyDataSetChanged()
                    val baseFee = if (it.level == GAS_LEVEL_ADVANCED) {
                        Convert.toWei(advancedBaseFee, Convert.Unit.GWEI).toBigInteger()
                    } else {
                        Convert.toWei(gasTrackerWrapper?.baseFee ?: "0", Convert.Unit.GWEI)
                            .toBigInteger()
                    }
                    maxFeePerGas = Convert.toWei(
                        getMaxFeePerGasGei(binder.selectLevel).toString(),
                        Convert.Unit.GWEI
                    )
                        .toBigInteger()

                    maxPriorityFeePerGas = maxFeePerGas - baseFee
                }
            }

            val list = ArrayList<EditGasItem>()
            list.add(EditGasItem(GAS_LEVEL_LOW, ""))
            list.add(EditGasItem(GAS_LEVEL_MARKET, ""))
            list.add(EditGasItem(GAS_LEVEL_HIGH, ""))
            list.add(EditGasItem(GAS_LEVEL_ADVANCED, ""))
            mAdapter.register(EditGasItem::class.java, binder)
            mBinding.recyclerView.layoutManager = LinearLayoutManager(context)
            mAdapter.items = list
            mBinding.recyclerView.adapter = mAdapter

            btnConfirm.setOnClickListener {
                onConfirmCallback?.invoke(maxPriorityFeePerGas, maxFeePerGas, binder.selectLevel)
                dismiss()
            }
        }
    }

    private fun editAdvancedFee() {
        val baseFee = gasTrackerWrapper?.baseFee ?: "0"
        val flooredBaseFee = floor(baseFee.toDouble()).toString()
        val priorityFee =
            ((gasTrackerWrapper?.marketGasPriceGwei ?: "0").asBigDecimal() - flooredBaseFee.asBigDecimal()).toFixed(0)
        val advancedPriorityFee =
            SPUtils.getInstance().getString(SPConstant.SP_ADVANCED_PRIORITY_FEE, priorityFee)
        AdvancedGasFeeDialog(gasLimit, baseFee, advancedPriorityFee).apply {
            onConfirmCallback = { gasLimit, baseFee, priorityFee ->
                <EMAIL> = gasLimit
                <EMAIL> = baseFee
                <EMAIL> = priorityFee
                binder.selectLevel = GAS_LEVEL_ADVANCED
                maxFeePerGas = Convert.toWei(
                    getMaxFeePerGasGei(binder.selectLevel).toString(),
                    Convert.Unit.GWEI
                ).toBigInteger()
                maxPriorityFeePerGas =
                    maxFeePerGas - Convert.toWei(advancedBaseFee, Convert.Unit.GWEI).toBigInteger()
                resolveTracker(gasTrackerWrapper)
                mAdapter.notifyDataSetChanged()
            }
        }.show(requireActivity().supportFragmentManager, AdvancedGasFeeDialog::class.java.name)
    }

    private fun getMaxFeePerGasGei(level: Int): BigDecimal {
        return when (level) {
            GAS_LEVEL_LOW -> (gasTrackerWrapper?.lowGasPriceGwei ?: "0").asBigDecimal()

            GAS_LEVEL_MARKET -> (gasTrackerWrapper?.marketGasPriceGwei ?: "0").asBigDecimal()

            GAS_LEVEL_HIGH -> (gasTrackerWrapper?.highGasPriceGwei ?: "0").asBigDecimal()

            else -> advancedBaseFee.asBigDecimal() + advancedPriorityFee.asBigDecimal()
        }
    }

    override fun initData() {
        mViewModel.getGasTracker(defGasLimit)
    }

    override fun initEvent() {
        mViewModel.gasTracker.observe(this) {
            gasTrackerWrapper = it
            resolveTracker(it)
        }

        mViewModel.gasTime.observe(this){
            gasTimeWrapper = it
            resolveTracker(gasTrackerWrapper)
        }
    }

    private fun resolveTracker(gasTracker: GasTrackerWrapper?) {
        if (gasTracker == null) return
        mBinding.run {
            val list = ArrayList<EditGasItem>()
            list.add(EditGasItem(GAS_LEVEL_LOW, gasTracker.lowGasFee, gasTimeWrapper?.lowGasTime ?: ""))
            list.add(EditGasItem(GAS_LEVEL_MARKET, gasTracker.marketGasFee, gasTimeWrapper?.marketGasTime ?: ""))
            list.add(EditGasItem(GAS_LEVEL_HIGH, gasTracker.highGasFee, gasTimeWrapper?.highGasTime ?: ""))

            val advancedGwei = advancedBaseFee.asBigDecimal() + advancedPriorityFee.asBigDecimal()
            if (advancedGwei == BigDecimal.ZERO) {
                list.add(EditGasItem(GAS_LEVEL_ADVANCED, ""))
            } else {
                val advancedFee = Convert.toWei(advancedGwei.toString(), Convert.Unit.GWEI)
                val advancedGas =
                    TokenUtil.weiToTokenBN(advancedFee.multiply(gasLimit.asBigDecimal()).toString())
                list.add(EditGasItem(GAS_LEVEL_ADVANCED, advancedGas))
            }

            mAdapter.items = list
            mAdapter.notifyDataSetChanged()
        }
    }

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        onConfirmCallback?.invoke(maxPriorityFeePerGas, maxFeePerGas, binder.selectLevel)
    }
}