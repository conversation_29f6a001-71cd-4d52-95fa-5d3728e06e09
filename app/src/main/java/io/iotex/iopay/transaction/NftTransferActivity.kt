package io.iotex.iopay.transaction

import io.iotex.iopay.wallet.NftTokenDetailActivity
import android.annotation.SuppressLint
import android.view.View
import android.view.ViewConfiguration
import androidx.lifecycle.lifecycleScope
import com.blankj.utilcode.util.KeyboardUtils
import com.blankj.utilcode.util.TimeUtils
import io.iotex.iopay.R
import io.iotex.iopay.base.BaseBindToolbarActivity
import io.iotex.iopay.data.db.NFT_TYPE_1155
import io.iotex.iopay.data.db.NFT_TYPE_721
import io.iotex.iopay.data.db.NftTokenEntry
import io.iotex.iopay.databinding.ActivityNftTransferBinding
import io.iotex.iopay.util.*
import io.iotex.iopay.util.extension.*
import io.iotex.iopay.wallet.qrcode.IoScanQRCodeActivity
import io.iotex.iopay.setting.book.AddressBookActivity
import io.iotex.iopay.transaction.bean.ExtraHead
import io.iotex.iopay.transaction.bean.OptionEntry
import io.iotex.iopay.wallet.viewmodel.TransferViewModel
import io.iotex.iopay.wallet.web3.FunctionSignData
import io.iotex.iopay.wallet.web3.Web3Delegate
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.math.BigDecimal
import java.math.BigInteger
import java.util.concurrent.TimeUnit

class NftTransferActivity : BaseBindToolbarActivity<TransferViewModel,ActivityNftTransferBinding>(R.layout.activity_nft_transfer) {

    private var mNftToken: NftTokenEntry? = null
    private var transactionDialog: TransactionDialog? = null

    private var address: String? = null
    private var name: String? = null

    @SuppressLint("CheckResult")
    override fun initView() {
        mNftToken =
            intent.getParcelableExtra(NftTokenDetailActivity.KEY_NFT_TOKEN) as? NftTokenEntry
        mNftToken ?: finish()
        setToolbarTitle(getString(R.string.transfer_send))
        mBinding.tvNext.setOnClickListener {
            transfer()
        }

        if (mNftToken?.type == NFT_TYPE_1155) {
            mBinding.mLlAmount.setVisible()
            mBinding.mTvBalance.setVisible()
            mBinding.mTvBalance.text =
                "${getString(R.string.amount_remain)}: ${mNftToken?.amount} ${mNftToken?.name}"
        } else {
            mBinding.mLlAmount.setGone()
            mBinding.mTvBalance.setGone()
        }
        mBinding.tvNftName.text = mNftToken?.name
        mBinding.tvNftId.text = getString(R.string.nft_no, mNftToken?.tokenId.toString())
        mBinding.ivNft.loadImage(mNftToken?.tokenUrl, R.drawable.icon_nft_default)
        RxUtil.textChange(mBinding.mEtReceipt)
            .debounce(500, TimeUnit.MILLISECONDS)
            .compose(RxUtil.applySchedulers())
            .subscribe {
                formatAddress(it)
            }
        mBinding.mIvQrcode.setOnClickListener {
            IoScanQRCodeActivity.startActivity(this) {
                onAddressReceive(it)
            }
        }
        mBinding.mIvNotebook.setOnClickListener {
            AddressBookActivity.startActivity(this) { address, name, _ ->
                this.address = address
                this.name = name
                if (address.isNotEmpty()) {
                    mBinding.tvNameFinish.setVisible()
                    mBinding.tvNameFinish.text = name
                    mBinding.mEtReceipt.setText(WalletHelper.formatWalletAddress(address))
                    mBinding.mEtReceipt.setSelection(mBinding.mEtReceipt.text.toString().length)
                }
            }
        }

        mBinding.mIvClear.setOnClickListener {
            mBinding.mEtReceipt.setText("")
        }

        mBinding.mIvClear2.setOnClickListener {
            mBinding.mLlComplete.visibility = View.GONE
            address = ""
            name = ""
            mBinding.mEtReceipt.setText("")
            mBinding.mEtReceipt.requestFocus()
            KeyboardUtils.showSoftInput(mBinding.mEtReceipt)
        }

        mBinding.mLlComplete.setOnClickListener {
            mBinding.mLlComplete.visibility = View.GONE
            mBinding.mEtReceipt.requestFocus()
            KeyboardUtils.showSoftInput(mBinding.mEtReceipt)
        }

        mBinding.ivAmountDown.setOnClickListener {
            var amount = mBinding.mEtAmount.text.toString().toIntOrNull() ?: 1
            if (amount > 1) amount -= 1
            mBinding.mEtAmount.setText(amount.toString())
            amountAlpha()
        }
        mBinding.ivAmountUp.setOnClickListener {
            var amount = mBinding.mEtAmount.text.toString().toIntOrNull() ?: 1
            if (amount < (mNftToken?.amount?.toIntOrNull() ?: 0)) amount += 1
            mBinding.mEtAmount.setText(amount.toString())
            amountAlpha()
        }
        amountAlpha()

        KeyboardUtils.registerSoftInputChangedListener(this) {
            if (it < ViewConfiguration.get(this).scaledTouchSlop) {
                if (!address.isNullOrBlank()) {
                    mBinding.tvNameFinish.setVisible()
                    mBinding.tvNameFinish.text = name
                    mBinding.mEtReceipt.setText(WalletHelper.formatWalletAddress(address ?: ""))
                    mBinding.mEtReceipt.setSelection(mBinding.mEtReceipt.text.toString().length)
                } else {
                    formatAddress(mBinding.mEtReceipt.text.toString())
                }
            }
        }

        mViewModel.confirmLiveData.observe(this) { timestamp ->
            transactionDialog?.dismiss()
            finish()
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_TRANSFER_SUCCESS)
        }
    }

    private fun amountAlpha() {
        val amount = mBinding.mEtAmount.text.toString().toIntOrNull() ?: 1
        if (amount <= 1) {
            mBinding.ivAmountDown.alpha = 0.5f
        } else {
            mBinding.ivAmountDown.alpha = 1f
        }
        if (amount < (mNftToken?.amount?.toIntOrNull() ?: 0)) {
            mBinding.ivAmountUp.alpha = 1f
        } else {
            mBinding.ivAmountUp.alpha = 0.5f
        }
    }

    private fun formatAddress(s: String) {
        lifecycleScope.launch {
            if (s.isBlank()) {
                mBinding.tvNameFinish.setGone()
                mBinding.mLlComplete.setGone()
                return@launch
            }
            mBinding.mIvClear.setVisible()
            val receiptValid = WalletHelper.isValidAddress(s)
            if (!receiptValid) {
                mBinding.mLlComplete.setGone()
                return@launch
            }
            mBinding.mLlComplete.visibility = View.VISIBLE
            mBinding.mTvWalletAddress.text = TokenUtil.textEllipsis(s, 6, 8)
            mBinding.mIvWalletIcon.loadSvgOrImage(WalletHelper.getAddressAvatar(s), R.drawable.icon_wallet_default)

            KeyboardUtils.hideSoftInput(mBinding.mEtReceipt)
            val isContract = withContext(Dispatchers.IO) {
                Web3Delegate.isContract(s.toEvmAddress())
            }
            mBinding.mTvError.text = if (isContract) {
                getString(R.string.receipt_contract)
            } else ""
        }
    }

    private fun transfer() {
        mNftToken ?: return
        if (mNftToken?.type == NFT_TYPE_1155) {
            val amount = mBinding.mEtAmount.text.toString()
            if (!validateAmount(amount)) return
        }
        val recipient = mBinding.mEtReceipt.text.toString().toEvmAddress()
        val isValid = transferIsValidated(recipient)
        val tokenId = mNftToken?.tokenId ?: return
        val contract = mNftToken?.contract ?: return
        val amount = if (mNftToken?.type == NFT_TYPE_1155) {
            if (mBinding.mEtAmount.text.toString().isBlank()) 1 else mBinding.mEtAmount.text.toString()
                .toInt()
        } else {
            null
        }
        if (isValid) {
            val data = FunctionSignData.getNftTransferSignData(
                Constant.currentWallet?.address ?: "",
                recipient,
                tokenId,
                amount
            )
            if (transactionDialog != null && transactionDialog?.showsDialog == true) return
            val list = ArrayList<OptionEntry>()
            list.add(OptionEntry(getString(R.string.method), getString(R.string.transfer)))
            list.add(OptionEntry(getString(R.string.from), Constant.currentWallet?.address ?: ""))
            list.add(OptionEntry(getString(R.string.to), recipient))
            list.add(OptionEntry(getString(R.string.no_dot), tokenId))
            val extraHead = ExtraHead(mNftToken?.tokenUrl ?: "", mNftToken?.symbol ?: "", true)
            transactionDialog =
                TransactionDialog(contract, BigInteger.ZERO, data, list, "", "", extraHead).apply {
                    onCancel = {
                        transactionDialog = null
                    }
                    onTransactionConfirm =
                        { gasLimit: Long, gasPrice: String, maxPriorityFeePerGas, maxFeePerGas ->
                            doTransfer(
                                recipient,
                                gasLimit,
                                gasPrice,
                                amount.toString(),
                                maxPriorityFeePerGas,
                                maxFeePerGas
                            )
                        }
                }
            transactionDialog?.show(supportFragmentManager, System.currentTimeMillis().toString())
        }
    }

    private fun doTransfer(
        recipient: String,
        gasLimit: Long,
        gasPrice: String,
        amountValue: String,
        maxPriorityFeePerGas: BigInteger,
        maxFeePerGas: BigInteger
    ) {
        val timestamp = TimeUtils.getNowMills().toString()
        if (mNftToken?.type == NFT_TYPE_721) {
            mViewModel.transferERC721(
                timestamp,
                recipient,
                mNftToken?.contract ?: "",
                mNftToken?.name ?: "",
                mNftToken?.symbol ?: "",
                mNftToken?.tokenId?.asBigDecimal()?.toBigInteger() ?: BigInteger.ZERO,
                gasLimit.toBigInteger(),
                gasPrice.asBigDecimal().toBigInteger(),
                maxPriorityFeePerGas,
                maxFeePerGas
            )
        } else {
            val amount = if (amountValue == "0") null else amountValue
            val value = if (!amount.isNullOrBlank()) amount.toBigIntegerOrNull()
                ?: BigInteger.ZERO else BigInteger.ZERO
            mViewModel.transferERC1155(
                timestamp,
                recipient,
                mNftToken?.contract ?: "",
                mNftToken?.name ?: "",
                mNftToken?.symbol ?: "",
                mNftToken?.tokenId?.asBigDecimal()?.toBigInteger() ?: BigInteger.ZERO,
                value,
                gasLimit.toBigInteger(),
                gasPrice.asBigDecimal().toBigInteger(),
                maxPriorityFeePerGas,
                maxFeePerGas
            )
        }
    }

    private fun validateAmount(amount: String): Boolean {
        try {
            if (amount.asBigDecimal() <= BigDecimal.ZERO) {
                getString(R.string.amount_must_greater_zero).toast()
                return false
            }
            if (amount.asBigDecimal() > (mNftToken?.amount?.asBigDecimal() ?: BigDecimal.ZERO)) {
                getString(R.string.transfer_amount_invalid).toast()
                return false
            }
            if (amount.toInt() <= 0) {
                getString(R.string.amount_must_greater_zero).toast()
                return false
            }
            return true
        } catch (e: Exception) {
            getString(R.string.invalid_amount_number).toast()
            return false
        }
    }

    private fun transferIsValidated(recipient: String): Boolean {
        return if (recipient.isBlank()) {
            getString(R.string.receipt_address_required).toast()
            false
        } else if (!WalletHelper.isValidAddress(recipient)) {
            getString(R.string.invalid_receiver).toast()
            false
        } else {
            true
        }
    }

    private fun onAddressReceive(address: String) {
        mBinding.tvNameFinish.setGone()
        mBinding.mEtReceipt.setText(WalletHelper.formatWalletAddress(address))
    }

    companion object {
        const val KEY_NFT_TOKEN = "key_nft_token"
    }
}