package io.iotex.iopay.transaction

import android.annotation.SuppressLint
import android.content.DialogInterface
import android.content.res.Configuration
import android.text.method.ScrollingMovementMethod
import android.view.Gravity
import android.view.MotionEvent
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.recyclerview.widget.LinearLayoutManager
import com.blankj.utilcode.util.ScreenUtils
import com.drakeet.multitype.MultiTypeAdapter
import io.iotex.base.bindbase.BaseBindDialog
import io.iotex.iopay.R
import io.iotex.iopay.databinding.DialogTransferBinding
import io.iotex.iopay.transaction.bean.ExtraExpend
import io.iotex.iopay.transaction.bean.ExtraHead
import io.iotex.iopay.transaction.bean.OptionEntry
import io.iotex.iopay.transaction.gas.GasFragment
import io.iotex.iopay.transaction.item.EntryItemBinder
import io.iotex.iopay.util.RxUtil
import io.iotex.iopay.util.extension.loadSvgOrImage
import io.iotex.iopay.util.extension.setGone
import io.iotex.iopay.util.extension.setVisible
import java.math.BigInteger
import java.util.concurrent.TimeUnit

class TransactionDialog @JvmOverloads constructor(
    private val to: String = "",
    private var value: BigInteger = BigInteger.ZERO,
    private var data: String = "",
    private val option: List<OptionEntry>? = null,
    private var receiveGasLimit: String = "",
    private var receiveGasPrice: String = "",
    private val extraHead: ExtraHead? = null,
    private val extraExpend: ExtraExpend? = null,
    private val extraMessage: String? = null
) :
    BaseBindDialog<TransactionViewModel, DialogTransferBinding>(R.layout.dialog_transfer) {

    var onTransactionConfirm: ((
        gasLimit: Long,
        gasPrice: String,
        maxPriorityFeePerGas: BigInteger,
        maxFeePerGas: BigInteger,
    ) -> Unit)? = null
    private var confirm = false
    var onCancel: (() -> Unit)? = null

    private var gasLimit = 0L
    private var gasPrice = "0"
    private var maxPriorityFeePerGas: BigInteger = BigInteger.ZERO
    private var maxFeePerGas: BigInteger = BigInteger.ZERO

    private val mAdapter = MultiTypeAdapter()

    @SuppressLint("CommitTransaction", "ClickableViewAccessibility")
    override fun initView() {
        if(option == null){
            dismiss()
            return
        }
        childFragmentManager.beginTransaction()
            .add(R.id.flGas, GasFragment(to, value, data, receiveGasLimit, receiveGasPrice, extraExpend).apply {
                onGasFree = { limit: BigInteger, price: BigInteger, maxPriorityFeePerGas, maxFeePerGas  ->
                    gasLimit = limit.toLong()
                    gasPrice = price.toString()
                    <EMAIL> = maxPriorityFeePerGas
                    <EMAIL> = maxFeePerGas
                }
                onGasOrBalanceError = {
                    mBinding.mBtnConfirm.isEnabled = !it
                    mBinding.mBtnConfirm.alpha = if (it) 0.5F else 1F
                }
            }).commit()
        mBinding.recyclerView.layoutManager = LinearLayoutManager(context)
        mAdapter.register(OptionEntry::class.java, EntryItemBinder())
        mAdapter.items = option
        mBinding.recyclerView.adapter = mAdapter

        mBinding.mBtnCancel.setOnClickListener {
            dismiss()
        }
        val sub = RxUtil.clicks(mBinding.mBtnConfirm)
            .debounce(500, TimeUnit.MILLISECONDS)
            .compose(RxUtil.applySchedulers())
            .subscribe{
                confirm = true
                onTransactionConfirm?.invoke(gasLimit, gasPrice, maxPriorityFeePerGas, maxFeePerGas)
            }

        //avatar
        if (extraHead != null) {
            mBinding.llHead.setVisible()
            mBinding.tvSymbol.text = extraHead.name
            if (extraHead.isNft) {
                mBinding.flNftLogo.setVisible()
                mBinding.flToken.setGone()
                mBinding.ivNftLogo.loadSvgOrImage(extraHead.avatar, extraHead.placeholder ?: R.drawable.icon_nft_default)
            } else {
                mBinding.flNftLogo.setGone()
                mBinding.flToken.setVisible()
                mBinding.ivLogo.loadSvgOrImage(extraHead.avatar, extraHead.placeholder ?: R.drawable.icon_token_default)
            }
        }

        mBinding.tvData.movementMethod = ScrollingMovementMethod()
        if (!extraMessage.isNullOrBlank()) {
            mBinding.llMassage.isVisible = true
            mBinding.tvData.text = extraMessage
        }
        mBinding.tvData.setOnTouchListener { v, event ->
            when (event.action) {
                MotionEvent.ACTION_DOWN -> {
                    v.parent.requestDisallowInterceptTouchEvent(true)
                }
                MotionEvent.ACTION_MOVE -> {
                    v.parent.requestDisallowInterceptTouchEvent(true)
                }
                MotionEvent.ACTION_UP -> {
                    v.parent.requestDisallowInterceptTouchEvent(false)
                }
            }
            false
        }
    }

    override fun initData() {
        mViewModel.getCurNetwork()
        mViewModel.curNetworkLiveData.observe(this) {
            mBinding.ivNetwork.loadSvgOrImage(it.logo, R.drawable.ic_network_default)
            mBinding.ivNftNetwork.loadSvgOrImage(it.logo, R.drawable.ic_network_default)
        }
    }

    override fun onStart() {
        super.onStart()
        val attributes = dialog?.window?.attributes
        val orientation = resources.configuration.orientation
        if (orientation == Configuration.ORIENTATION_LANDSCAPE) {
            attributes?.width = (ScreenUtils.getScreenHeight() * 0.8).toInt()
            attributes?.height = ViewGroup.LayoutParams.WRAP_CONTENT
        } else {
            attributes?.width = ViewGroup.LayoutParams.MATCH_PARENT
            attributes?.height = ViewGroup.LayoutParams.WRAP_CONTENT
        }
        attributes?.gravity = Gravity.BOTTOM
        dialog?.window?.attributes = attributes
    }

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        if (!confirm) onCancel?.invoke()
    }
}