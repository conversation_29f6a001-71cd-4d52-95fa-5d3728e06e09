package io.iotex.iopay.transaction

import android.app.Application
import androidx.lifecycle.MutableLiveData
import io.iotex.iopay.base.BaseLaunchVM
import io.iotex.iopay.data.db.RPCNetwork
import io.iotex.iopay.util.WalletHelper

class TransactionViewModel(application: Application) : BaseLaunchVM(application) {
    val curNetworkLiveData = MutableLiveData<RPCNetwork>()
    fun getCurNetwork() {
        addLaunch {
            val network = WalletHelper.getCurNetwork()
            network?.let {
                curNetworkLiveData.postValue(it)
            }
        }
    }
}