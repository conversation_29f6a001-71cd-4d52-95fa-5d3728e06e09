package io.iotex.iopay.transaction.approve

import android.annotation.SuppressLint
import android.content.DialogInterface
import android.content.res.Configuration
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup.LayoutParams
import android.widget.PopupWindow
import androidx.recyclerview.widget.LinearLayoutManager
import com.blankj.utilcode.util.ScreenUtils
import com.drakeet.multitype.MultiTypeAdapter
import io.iotex.base.bindbase.BaseBindDialog
import io.iotex.iopay.R
import io.iotex.iopay.databinding.DialogApproveBinding
import io.iotex.iopay.databinding.ViewPopNftApproveTipsBinding
import io.iotex.iopay.transaction.bean.OptionEntry
import io.iotex.iopay.transaction.item.EntryItemBinder
import io.iotex.iopay.util.*
import io.iotex.iopay.util.Config.MAX_AMOUNT
import io.iotex.iopay.util.extension.setVisible
import io.iotex.iopay.transaction.approve.viewmodel.CertifiedContractViewModel
import io.iotex.iopay.transaction.gas.GasFragment
import io.iotex.iopay.util.extension.loadSvgOrImage
import io.iotex.iopay.util.extension.setGone
import io.iotex.iopay.wallet.web3.FunctionSignData
import java.math.BigInteger
import java.util.concurrent.TimeUnit

class ApproveDialog(
    val to: String,
    var data: String,
    var receiveGasLimit: String = "",
    var receiveGasPrice: String = "",
) :
    BaseBindDialog<CertifiedContractViewModel, DialogApproveBinding>(R.layout.dialog_approve) {

    var onApproveConfirm: ((
        to: String,
        gasLimit: Long,
        gasPrice: String,
        data: String,
    ) -> Unit)? = null

    private var confirm = false
    var onCancel: (() -> Unit)? = null

    private var gasLimit = 0L
    private var gasPrice = "0"

    private val list = ArrayList<OptionEntry>()

    private var editApproveAmountDialog: EditApproveAmountDialog? = null

    val contract by lazy {
        AbiDecoderUtil.resolveParam(data, "guy", "wiotx.json")
    }

    val amount by lazy {
        AbiDecoderUtil.resolveParam(data, "wad", "wiotx.json")
    }

    private var editAmount = amount

    private val mAdapter = MultiTypeAdapter()

    @SuppressLint("CommitTransaction")
    override fun initView() {
        childFragmentManager.beginTransaction()
            .add(R.id.flGas, GasFragment(to, BigInteger.ZERO, data, receiveGasLimit, receiveGasPrice).apply {
                onGasFree = { limit: BigInteger, price: BigInteger, _, _ ->
                    gasLimit = limit.toLong()
                    gasPrice = price.toString()
                }
                onGasOrBalanceError = {
                    mBinding.mBtnConfirm.isEnabled = !it
                    mBinding.mBtnConfirm.alpha = if (it) 0.5F else 1F
                }
            }).commit()
        mBinding.recyclerView.layoutManager = LinearLayoutManager(context)
        mAdapter.register(OptionEntry::class.java, EntryItemBinder().apply {
            editApproveAmount = {
                if (editApproveAmountDialog == null) {
                    val tokenLogo = mViewModel.tokenLogoLiveData.value
                    val tokenName = mViewModel.tokenName.value
                    val decimals = mViewModel.isNftToken.value?.decimals?:18
                    editApproveAmountDialog =
                        EditApproveAmountDialog(
                            to,
                            editAmount,
                            amount,
                            tokenLogo,
                            tokenName,
                            decimals
                        )
                            .apply {
                                onDismiss = {
                                    editApproveAmountDialog = null
                                }

                                onConfirm = {
                                    editAmount = it
                                    list[4] = OptionEntry(
                                        getString(R.string.approve_amount),
                                        checkUnlimited(it,decimals)
                                    )
                                    mAdapter.notifyItemChanged(4)
                                }
                            }
                    activity?.let {
                        editApproveAmountDialog?.show(
                            it.supportFragmentManager,
                            System.currentTimeMillis().toString()
                        )
                    }
                    FireBaseUtil.logFireBase(FireBaseEvent.ACTION_SWAP_APPROVE_AMOUNT_EDIT)
                }
                FireBaseUtil.logFireBase(FireBaseEvent.ACTION_NFT_APPROVE_CONTRACT_EDIT)
            }
        })
        list.add(OptionEntry(getString(R.string.method), getString(R.string.approve)))
        list.add(OptionEntry(getString(R.string.contract_addr), contract))
        list.add(OptionEntry(getString(R.string.to), to))
        list.add(OptionEntry(getString(R.string.approve_token), ""))
        list.add(OptionEntry("", ""))
        mAdapter.items = list
        mBinding.recyclerView.adapter = mAdapter

        mBinding.mBtnCancel.setOnClickListener {
            dismiss()
        }

        mBinding.vEmpty.setOnClickListener {
            dismiss()
        }

        mBinding.ivTips.setOnClickListener {
            showTipsPopupWindow(it)
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_NFT_APPROVE_TITLE_TOOLTIPS)
        }
        val sub = RxUtil.clicks(mBinding.mBtnConfirm)
            .debounce(500, TimeUnit.MILLISECONDS)
            .compose(RxUtil.applySchedulers())
            .subscribe {
                if (editAmount != amount) {
                    kotlin.runCatching {
                        data = FunctionSignData.getApproveSignData(
                            contract,
                            TokenUtil.toWei(editAmount)
                        )
                    }
                }
                confirm = true
                onApproveConfirm?.invoke(to, gasLimit, gasPrice, data)
            }
    }

    override fun initData() {
        mViewModel.tokenLogoLiveData.observe(this) {
            mBinding.ivLogo.loadSvgOrImage(it, R.drawable.icon_token_default)
        }
        mViewModel.nftLogoLiveData.observe(this) {
            mBinding.ivNftLogo.loadSvgOrImage(it, R.drawable.icon_nft_default)
        }
        mViewModel.getTokenName(to)
        mViewModel.tokenName.observe(this) {
            if (it.isNotEmpty()) {
                mBinding.tvSymbol.text = it
                list[3] = OptionEntry(getString(R.string.approve_token), it)
                mAdapter.notifyItemChanged(3)
            }
        }

        mViewModel.getTokenIsNft(to)
        mViewModel.isNftToken.observe(this) {
            if (it == null) {//is nft
                list[4] =
                    OptionEntry(getString(R.string.token_id_big), getString(R.string.nft_no, amount))
                mAdapter.notifyItemChanged(4)
                mBinding.ivTips.setVisible()
                mBinding.flToken.setGone()
                mBinding.flNftLogo.setVisible()
                mViewModel.getNftLogo(to, amount)
            } else { //erc20
                list[4] = OptionEntry(getString(R.string.approve_amount), checkUnlimited(amount,it.decimals))
                mAdapter.notifyItemChanged(4)
                mBinding.flToken.setVisible()
                mBinding.flNftLogo.setGone()
                mViewModel.getTokenLogo(to)
            }
        }
        mViewModel.getCurNetwork()
        mViewModel.curNetworkLiveData.observe(this){
            mBinding.ivNetwork.loadSvgOrImage(it.logo,R.drawable.ic_network_default)
            mBinding.ivNftNetwork.loadSvgOrImage(it.logo,R.drawable.ic_network_default)
        }
    }

    private fun checkUnlimited(amount: String,decimals:Int): String {
        kotlin.runCatching {
            if (BigInteger(amount).toString(16) == MAX_AMOUNT) {
                return getString(R.string.unlimited)
            }
        }
        return TokenUtil.weiToTokenBN(amount, decimals.toLong())
    }

    private fun showTipsPopupWindow(view: View) {
        val bind = ViewPopNftApproveTipsBinding.inflate(LayoutInflater.from(context))
        PopupWindow(
            bind.root,
            LayoutParams.MATCH_PARENT,
            LayoutParams.WRAP_CONTENT
        ).apply {
            isOutsideTouchable = true
            showAsDropDown(view)
        }
    }

    override fun onStart() {
        super.onStart()
        val attributes = dialog?.window?.attributes
        val orientation = resources.configuration.orientation
        if (orientation == Configuration.ORIENTATION_LANDSCAPE) {
            attributes?.width = (ScreenUtils.getScreenHeight() * 0.8).toInt()
            attributes?.height = LayoutParams.MATCH_PARENT
        } else {
            attributes?.width = LayoutParams.MATCH_PARENT
            attributes?.height = LayoutParams.MATCH_PARENT
        }
        attributes?.gravity = Gravity.BOTTOM
        dialog?.window?.attributes = attributes
    }

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        if (!confirm) onCancel?.invoke()
    }
}