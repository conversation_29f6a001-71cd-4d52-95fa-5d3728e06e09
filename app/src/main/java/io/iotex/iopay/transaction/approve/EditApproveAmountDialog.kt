package io.iotex.iopay.transaction.approve

import android.content.DialogInterface
import android.os.Bundle
import android.view.Gravity
import android.view.ViewGroup.LayoutParams
import io.iotex.base.bindbase.BaseBindDialog
import io.iotex.iopay.R
import io.iotex.iopay.databinding.DialogEditApproverAmountBinding
import io.iotex.iopay.util.TokenUtil
import io.iotex.iopay.transaction.approve.viewmodel.CertifiedContractViewModel
import io.iotex.iopay.util.extension.loadSvgOrImage

class EditApproveAmountDialog(
    val to: String, private val oldValue: String, private val default: String,
    private val tokenLogo: String?, private val symbol: String?,
    private val decimals: Int
) :
    BaseBindDialog<CertifiedContractViewModel, DialogEditApproverAmountBinding>(R.layout.dialog_edit_approver_amount) {

    var onDismiss: (() -> Unit)? = null
    var onConfirm: ((amount:String) -> Unit)? = null

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        onDismiss?.invoke()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NO_FRAME, R.style.Theme_dialog)
    }

    override fun initView() {
        mBinding.ivLogo.loadSvgOrImage(tokenLogo, R.drawable.icon_token_default)
        mBinding.tvSymbol.text = symbol
        val balance = TokenUtil.weiToTokenBN(oldValue, decimals.toLong())
        mBinding.etAmount.setText(balance)

        mBinding.mIvBack.setOnClickListener {
            dismiss()
        }
        mBinding.tvMax.setOnClickListener {
            mViewModel.getTokenBalance(to)
        }

        mBinding.mBtnSave.setOnClickListener {
            var value = mBinding.etAmount.text.toString()
            if (value.isEmpty()) value = "0"
            onConfirm?.invoke(TokenUtil.toWei(value,decimals).toString())
            dismiss()
        }

        mBinding.tvDefault.setOnClickListener {
            val balance = TokenUtil.weiToTokenBN(default, decimals.toLong())
            mBinding.etAmount.setText(balance)
        }
    }

    override fun initData() {
        mViewModel.tokenBalance.observe(this) {
            if (it != null) {
                val balance = TokenUtil.weiToTokenBN(it, decimals.toLong())
                mBinding.etAmount.setText(balance)
            }
        }
    }

    override fun onStart() {
        super.onStart()
        val attributes = dialog?.window?.attributes
        attributes?.width = LayoutParams.MATCH_PARENT
        attributes?.height = LayoutParams.WRAP_CONTENT
        attributes?.gravity = Gravity.BOTTOM
        dialog?.window?.attributes = attributes
    }
}