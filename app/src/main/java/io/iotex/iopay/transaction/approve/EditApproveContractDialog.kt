package io.iotex.iopay.transaction.approve

import android.content.DialogInterface
import android.os.Bundle
import android.view.Gravity
import android.view.ViewGroup
import com.blankj.utilcode.util.ClipboardUtils
import io.iotex.base.bindbase.BaseBindDialog
import io.iotex.iopay.R
import io.iotex.iopay.databinding.DialogEditApproverContractBinding
import io.iotex.iopay.util.TokenUtil
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.util.extension.setGone
import io.iotex.iopay.util.extension.setInvisible
import io.iotex.iopay.util.extension.setVisible
import io.iotex.iopay.util.extension.toast
import io.iotex.iopay.transaction.approve.viewmodel.CertifiedContractViewModel

class EditApproveContractDialog(val contract: String, private val oldName: String, private val certified:Boolean = false) :
    BaseBindDialog<CertifiedContractViewModel, DialogEditApproverContractBinding>(R.layout.dialog_edit_approver_contract) {

    var onDismiss: (() -> Unit)? = null
    var onConfirm: ((String) -> Unit)? = null

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        onDismiss?.invoke()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NO_FRAME, R.style.Theme_dialog)
    }

    override fun initView() {
        if (certified) {
            mBinding.tvTitle.text = getString(R.string.view_contract)
            mBinding.mTvContract.text = oldName
            mBinding.llEdit.setInvisible()
            mBinding.llCertified.setVisible()
        } else {
            mBinding.llEdit.setVisible()
            mBinding.llCertified.setGone()
        }
        mBinding.tvContract.text = contract

        if (TokenUtil.textEllipsis(contract,6,8) != oldName) {
            mBinding.etName.setText(oldName)
        }

        mBinding.mIvBack.setOnClickListener {
            dismiss()
        }

        mBinding.mBtnSave.setOnClickListener {
            val nikeName = mBinding.etName.text.toString()
            mViewModel.updateContractName(contract, nikeName)
        }

        mBinding.ivCopy.setOnClickListener {
            ClipboardUtils.copyText(contract)
            getString(R.string.copy_success).toast()
        }

        mBinding.tvWeb.setOnClickListener {
            WalletHelper.gotoExplorerTokenScan(contract)
        }
    }

    override fun initData() {
        mViewModel.contractLiveData.observe(this){
            val nikeName = mBinding.etName.text.toString()
            onConfirm?.invoke(nikeName)
            dismiss()
        }
    }

    override fun onStart() {
        super.onStart()
        val attributes = dialog?.window?.attributes
        attributes?.width = ViewGroup.LayoutParams.MATCH_PARENT
        attributes?.height = ViewGroup.LayoutParams.WRAP_CONTENT
        attributes?.gravity = Gravity.BOTTOM
        dialog?.window?.attributes = attributes
    }
}