package io.iotex.iopay.transaction.approve.viewmodel

import android.app.Application
import androidx.lifecycle.MutableLiveData
import com.apollographql.apollo.ApolloClient
import com.apollographql.apollo.coroutines.await
import com.blankj.utilcode.util.Utils
import io.iotex.api.CertifiedContractQuery
import io.iotex.base.RetrofitClient
import io.iotex.base.okHttpClient
import io.iotex.iopay.api.NftApi
import io.iotex.iopay.base.BaseLaunchVM
import io.iotex.iopay.data.UserStore
import io.iotex.iopay.data.db.AppDatabase
import io.iotex.iopay.data.db.CertifiedContractEntry
import io.iotex.iopay.data.db.RPCNetwork
import io.iotex.iopay.data.db.TokenEntry
import io.iotex.iopay.util.Config
import io.iotex.iopay.util.DateTimeUtils
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.wallet.web3.Web3Delegate
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.math.BigInteger

class CertifiedContractViewModel(application: Application) : BaseLaunchVM(application) {

    private val apolloClient by lazy {
        ApolloClient.builder()
            .serverUrl(Config.IoPayUrl)
            .okHttpClient(okHttpClient)
            .build()
    }

    private val apiService by lazy {
        RetrofitClient.createApiService(Config.NFT_QUERY_URL, NftApi::class.java)
    }

    val tokenLogoLiveData = MutableLiveData<String>()
    val nftLogoLiveData = MutableLiveData<String?>()
    val tokenBalance = MutableLiveData<String>()
    val tokenName = MutableLiveData<String>()
    val isNftToken = MutableLiveData<TokenEntry?>()
    val curNetworkLiveData = MutableLiveData<RPCNetwork>()
    val contractLiveData = MutableLiveData<CertifiedContractEntry?>()

    fun getCurNetwork() {
        addLaunch {
            val network = WalletHelper.getCurNetwork()
            network?.let {
                curNetworkLiveData.postValue(it)
            }
        }
    }

    fun updateContractName(contract: String, nikeName: String) {
        addLaunch {
            var entry = AppDatabase.getInstance(getApplication()).certifiedContractDao()
                .queryByContract(contract.lowercase())
            if (entry == null) {
                entry = CertifiedContractEntry(contract.lowercase(), "", nikeName, "")
                AppDatabase.getInstance(getApplication()).certifiedContractDao()
                    .insertIfNonExist(entry)
            } else {
                entry.nikeName = nikeName
                AppDatabase.getInstance(getApplication()).certifiedContractDao()
                    .insertIfNonExist(entry)
            }
            contractLiveData.postValue(entry)
        }
    }

    fun getCertifiedContractOneDay(){
        if (DateTimeUtils.formatYMD(UserStore.getCertifiedContractTime()) != DateTimeUtils.formatYMD(System.currentTimeMillis())) {
            UserStore.setCertifiedContractTime(System.currentTimeMillis())
            getCertifiedContract()
        }
    }

    private fun getCertifiedContract(
        contract: String = "",
        callBack: ((CertifiedContractEntry) -> Unit)? = null
    ) {
        val categoriesQuery = CertifiedContractQuery.builder()
            .build()

        addLaunchNoCancel("getCertifiedContract") {
            apolloClient.query(categoriesQuery).await().data
                ?.certified_contract()?.let { data ->
                    data.forEach {
                        val entry =
                            CertifiedContractEntry(
                                it.contract().lowercase(),
                                it.name(),
                                "",
                                it.option() ?: ""
                            )
                        AppDatabase.getInstance(getApplication()).certifiedContractDao()
                            .insertIfNonExist(entry)
                        CoroutineScope(Dispatchers.Main).launch {
                            if (entry.contract == contract) callBack?.invoke(entry)
                        }
                        if (entry.contract == contract) contractLiveData.postValue(entry)
                    }
                }
        }
    }

    fun getTokenIsNft(tokenAddress: String) {
        addLaunch(true) {
            val chainId = WalletHelper.getCurChainId()
            val erc20 = AppDatabase.getInstance(Utils.getApp()).tokenDao()
                .queryByAddress(chainId, tokenAddress) ?: AppDatabase.getInstance(Utils.getApp())
                .tokenDao().queryByAddress(chainId, tokenAddress.lowercase())
            isNftToken.postValue(erc20)
        }
    }

    fun getTokenName(tokenAddress: String) {
        addLaunch {
            val symbol = Web3Delegate.erc20Symbol(tokenAddress)
            val name = Web3Delegate.erc20Name(tokenAddress)
            if (symbol.isNotEmpty()) {
                tokenName.postValue(symbol)
            } else if (name.isNotEmpty()) {
                tokenName.postValue(name)
            } else {
                tokenName.postValue("")
            }
        }
    }

    fun getNftLogo(contract: String,tokenId:String) {
        addLaunch {
            val chainId = WalletHelper.getCurChainId()
            val url = kotlin.runCatching {
                apiService.getNftTokenLogo(chainId, contract, tokenId).string()
            }.getOrNull()
            nftLogoLiveData.postValue(url)
        }
    }
    fun getTokenLogo(tokenAddress: String){
        addLaunch {
            val chainId = WalletHelper.getCurChainId()
            val erC20Entry = AppDatabase.getInstance(Utils.getApp()).tokenDao()
                .queryByAddress(chainId, tokenAddress) ?: AppDatabase.getInstance(Utils.getApp())
                .tokenDao().queryByAddress(chainId, tokenAddress.lowercase())
            erC20Entry?.logo?.let {
                tokenLogoLiveData.postValue(it)
            }
        }
    }

    fun getTokenBalance(tokenAddress: String) {
        addLaunch {
            val balanceList = Web3Delegate.getErc20Balance(tokenAddress)
            val value = if (balanceList.isNotEmpty()) balanceList[0] else BigInteger.ZERO
            tokenBalance.postValue(value.toString())
        }
    }

}