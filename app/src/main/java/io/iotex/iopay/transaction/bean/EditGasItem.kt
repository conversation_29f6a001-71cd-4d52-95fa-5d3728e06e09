package io.iotex.iopay.transaction.bean

import android.content.res.ColorStateList
import androidx.core.content.ContentProviderCompat.requireContext
import androidx.core.content.ContextCompat
import com.blankj.utilcode.util.ColorUtils
import com.blankj.utilcode.util.Utils
import io.iotex.iopay.R

data class EditGasItem(
    val level: Int,
    val fee: String = "",
    var time: String = "",
)

object EditGasUtil {

    fun getLevelIcon(level: Int):Int{
        return when (level) {
            GasLevel.GAS_LEVEL_LOW ->R.drawable.icon_gas_slow
            GasLevel.GAS_LEVEL_MARKET -> R.drawable.icon_gas_average
            GasLevel.GAS_LEVEL_HIGH ->R.drawable.icon_gas_fast
            GasLevel.GAS_LEVEL_ADVANCED -> R.drawable.icon_gas_custom
            else -> R.drawable.icon_gas_custom
        }
    }
    fun getLevelName(level: Int): String {
        return when (level) {
            GasLevel.GAS_LEVEL_LOW -> Utils.getApp().getString(R.string.slow)
            GasLevel.GAS_LEVEL_MARKET -> Utils.getApp().getString(R.string.average)
            GasLevel.GAS_LEVEL_HIGH -> Utils.getApp().getString(R.string.fast)
            GasLevel.GAS_LEVEL_ADVANCED -> Utils.getApp().getString(R.string.customize)
            else -> ""
        }
    }

    fun getLevelColor(level: Int):Int{
        return when (level) {
            GasLevel.GAS_LEVEL_LOW -> ColorUtils.getColor(R.color.color_E53737)
            GasLevel.GAS_LEVEL_MARKET -> ColorUtils.getColor(R.color.color_ec7f11)
            GasLevel.GAS_LEVEL_HIGH -> ColorUtils.getColor(R.color.color_00B4A0)
            GasLevel.GAS_LEVEL_ADVANCED -> ColorUtils.getColor(R.color.color_00d3dc)
            else -> ColorUtils.getColor(R.color.color_797979)
        }
    }
    fun getLevelColorStateList(level: Int): ColorStateList? {
        return when (level) {
            GasLevel.GAS_LEVEL_LOW -> ContextCompat.getColorStateList(
                Utils.getApp(),
                R.color.color_E53737
            )
            GasLevel.GAS_LEVEL_MARKET -> ContextCompat.getColorStateList(
                Utils.getApp(),
                R.color.color_ec7f11
            )
            GasLevel.GAS_LEVEL_HIGH -> ContextCompat.getColorStateList(
                Utils.getApp(),
                R.color.color_00B4A0
            )
            GasLevel.GAS_LEVEL_ADVANCED -> ContextCompat.getColorStateList(
                Utils.getApp(),
                R.color.color_00d3dc
            )
            else -> ContextCompat.getColorStateList(
                Utils.getApp(),
                R.color.color_797979
            )
        }
    }
}

object GasLevel {
    const val GAS_LEVEL_LOW = 1
    const val GAS_LEVEL_MARKET = 2
    const val GAS_LEVEL_HIGH = 3
    const val GAS_LEVEL_ADVANCED = 4
}
