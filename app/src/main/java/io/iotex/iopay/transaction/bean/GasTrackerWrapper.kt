package io.iotex.iopay.transaction.bean

import java.io.Serializable

data class GasTrackerWrapper (
    val lowGasPriceGwei: String,
    val marketGasPriceGwei: String,
    val highGasPriceGwei: String,
    val lowGasFee: String,
    val marketGasFee: String,
    val highGasFee: String,
    val baseFee: String
) : Serializable

data class GasTime(
    val lowGasTime: String,
    val marketGasTime: String,
    val highGasTime: String,
)