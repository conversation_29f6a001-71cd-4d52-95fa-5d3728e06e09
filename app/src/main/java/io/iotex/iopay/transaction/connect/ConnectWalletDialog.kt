package io.iotex.iopay.transaction.connect

import android.content.DialogInterface
import android.content.res.Configuration
import android.view.Gravity
import android.view.ViewGroup
import com.blankj.utilcode.util.ScreenUtils
import io.iotex.base.bindbase.BaseBindDialog
import io.iotex.iopay.R
import io.iotex.iopay.data.UserStore
import io.iotex.iopay.databinding.DialogConnectWalletBinding
import io.iotex.iopay.network.viewmodel.AddOrEditNetworkViewModel
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.util.extension.loadSvgOrImage
import io.iotex.iopay.util.extension.setGone
import io.iotex.iopay.util.extension.setVisible

class ConnectWalletDialog(
    private val dAppLogo: String,
    private val dAppUrl: String,
    private val address: String,
) :
    BaseBindDialog<AddOrEditNetworkViewModel, DialogConnectWalletBinding>(R.layout.dialog_connect_wallet) {

    var onResult: ((confirm: Boolean) -> Unit)? = null
    private var confirm = false

    override fun initView() {
        mBinding.ivAppLogo.loadSvgOrImage(dAppLogo, R.drawable.icon_dapp_default)
        mBinding.tvAppUrl.text = dAppUrl
        mBinding.tvAddress.text = WalletHelper.formatWalletAddress(address)

        mBinding.mBtnCancel.setOnClickListener {
            dismiss()
        }

        mBinding.mBtnConfirm.setOnClickListener {
            confirm = true
            dismiss()
        }
    }

    override fun initData() {
        mViewModel.getCurNetwork()
        mViewModel.curNetworkLiveData.observe(this){
            if (UserStore.getAllNetwork()) {
                mBinding.ivNetwork.setGone()
            } else {
                mBinding.ivNetwork.setVisible()
                mBinding.ivNetwork.loadSvgOrImage(it.logo, R.drawable.ic_network_default)
            }
        }
    }

    override fun onStart() {
        super.onStart()
        val attributes = dialog?.window?.attributes
        val orientation = resources.configuration.orientation
        if (orientation == Configuration.ORIENTATION_LANDSCAPE) {
            attributes?.width = (ScreenUtils.getScreenHeight() * 0.8).toInt()
            attributes?.height = ViewGroup.LayoutParams.WRAP_CONTENT
        } else {
            attributes?.width = ScreenUtils.getScreenWidth()
            attributes?.height = ViewGroup.LayoutParams.WRAP_CONTENT
        }
        attributes?.gravity = Gravity.BOTTOM
        dialog?.window?.attributes = attributes
    }

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        onResult?.invoke(confirm)
    }
}