package io.iotex.iopay.transaction.gas

import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import com.blankj.utilcode.util.ColorUtils
import io.iotex.base.bindbase.BaseBindFragment
import io.iotex.iopay.R
import io.iotex.iopay.databinding.FragmentBitcoinGasBinding
import io.iotex.iopay.transaction.BitcoinEditGasDialog
import io.iotex.iopay.transaction.BitcoinEditGasDialog.Companion.BITCOIN_GAS_LEVEL_AVERAGE
import io.iotex.iopay.transaction.BitcoinEditGasDialog.Companion.BITCOIN_GAS_LEVEL_CUSTOMIZE
import io.iotex.iopay.transaction.BitcoinEditGasDialog.Companion.BITCOIN_GAS_LEVEL_FAST
import io.iotex.iopay.transaction.BitcoinEditGasDialog.Companion.BITCOIN_GAS_LEVEL_SLOW
import io.iotex.iopay.transaction.bean.BitcoinTransactionData
import io.iotex.iopay.util.TokenUtil
import io.iotex.iopay.util.extension.fromSatoshis
import io.iotex.iopay.util.extension.setGone
import io.iotex.iopay.util.extension.setVisible
import io.iotex.iopay.viewmodel.wallet.BitcoinViewModel
import org.bitcoinj.core.Transaction
import java.math.BigDecimal
import java.math.BigInteger

class BitcoinGasFragment(
    private val to: String,
    private val value: BigInteger,
) : BaseBindFragment<BitcoinViewModel, FragmentBitcoinGasBinding>(R.layout.fragment_bitcoin_gas) {

    var onBuildTransaction: ((transaction: Transaction?) -> Unit)? = null
    var onGasOrBalanceError: ((error: Boolean) -> Unit)? = null
    private var gasError = false

    private var level = BITCOIN_GAS_LEVEL_AVERAGE
    private var bitcoinTransactionData: BitcoinTransactionData? = null

    override fun initView() {
        mBinding.llGasLevel.isVisible = true
        mBinding.llGasLevel.setOnClickListener {
            editGas()
        }
    }

    private fun editGas() {
        val transaction = bitcoinTransactionData?.transaction
        BitcoinEditGasDialog(transaction, bitcoinTransactionData?.uTXOBundle, level).apply {
            onConfirmCallback = { fee: BigInteger, level: Int ->
                <EMAIL> = level
                mViewModel.getBitcoinTransactionData(to, value, fee)
                when (level) {
                    BITCOIN_GAS_LEVEL_SLOW -> {
                        mBinding.tvGasLevel.text = getString(R.string.slow)
                        mBinding.tvGasLevel.setTextColor(ColorUtils.getColor(R.color.color_E53737))
                        mBinding.ivChoseGas.imageTintList = ContextCompat.getColorStateList(
                            requireContext(),
                            R.color.color_E53737
                        )
                    }
                    BITCOIN_GAS_LEVEL_AVERAGE -> {
                        mBinding.tvGasLevel.text = getString(R.string.average)
                        mBinding.tvGasLevel.setTextColor(ColorUtils.getColor(R.color.color_ec7f11))
                        mBinding.ivChoseGas.imageTintList = ContextCompat.getColorStateList(
                            requireContext(),
                            R.color.color_ec7f11
                        )
                    }
                    BITCOIN_GAS_LEVEL_FAST -> {
                        mBinding.tvGasLevel.text = getString(R.string.fast)
                        mBinding.tvGasLevel.setTextColor(ColorUtils.getColor(R.color.color_00B4A0))
                        mBinding.ivChoseGas.imageTintList = ContextCompat.getColorStateList(
                            requireContext(),
                            R.color.color_00B4A0
                        )
                    }
                    BITCOIN_GAS_LEVEL_CUSTOMIZE -> {
                        mBinding.tvGasLevel.text = getString(R.string.customize)
                        mBinding.tvGasLevel.setTextColor(ColorUtils.getColor(R.color.barcode_field_value))
                        mBinding.ivChoseGas.imageTintList = ContextCompat.getColorStateList(
                            requireContext(),
                            R.color.barcode_field_value
                        )
                    }
                }
            }
        }.show(requireActivity().supportFragmentManager, BitcoinEditGasDialog::class.java.name)
    }

    override fun initData() {
        mViewModel.getBitcoinTransactionData(to, value)
    }

    override fun initEvent() {
        mViewModel.transactionDataLD.observe(viewLifecycleOwner) {
            bitcoinTransactionData = it
            onBuildTransaction?.invoke(it.transaction)
            mBinding.progressBar.setGone()
            if (it.gasFee == BigInteger.ZERO) mBinding.llGasError.setVisible()
            displayGas(it)
            displayBalance(it)
        }
    }

    private fun displayBalance(transactionData: BitcoinTransactionData) {
        mBinding.tvAAValue.text = getString(
            R.string.two_string_add,
            transactionData.balance.fromSatoshis(),
            TokenUtil.getNativeCurrencySymbol()
        )
        mBinding.tvAmountName.text = getString(R.string.amount_gas_fee)
        val total = transactionData.gasFee + value
        mBinding.tvTotalAmount.text = getString(
            R.string.two_string_add,
            total.fromSatoshis(),
            TokenUtil.getNativeCurrencySymbol()
        )
        val money = (total.fromSatoshis().toBigDecimalOrNull() ?: BigDecimal.ZERO).multiply(
            transactionData.currencyPrice.toBigDecimalOrNull() ?: BigDecimal.ZERO
        )
        mBinding.tvAmountValue.text =
            getString(R.string.value_money, TokenUtil.displayBalance(money.toString()))
        displayError(transactionData.balance, total)
    }

    private fun displayError(balance: BigInteger, total: BigInteger) {
        val error = balance < total
        mBinding.llBalanceError.isVisible = error
        onGasOrBalanceError?.invoke(error || gasError)
    }

    private fun displayGas(transactionData: BitcoinTransactionData){
        if (transactionData.gasFee == BigInteger.ZERO) {
            mBinding.tvGasFree.text = "--"
            gasError = true
        } else {
            mBinding.tvGasFree.text = getString(
                R.string.two_string_add,
                transactionData.gasFee.fromSatoshis(),
                TokenUtil.getNativeCurrencySymbol()
            )
            gasError = false
        }
    }
}