package io.iotex.iopay.transaction.gas

import androidx.core.view.isVisible
import androidx.lifecycle.ViewModelProvider
import com.blankj.utilcode.util.Utils
import io.iotex.base.bindbase.BaseBindFragment
import io.iotex.iopay.R
import io.iotex.iopay.data.UserStore
import io.iotex.iopay.databinding.FragmentGasBinding
import io.iotex.iopay.transaction.EditGas1559Dialog
import io.iotex.iopay.transaction.bean.EditGasUtil
import io.iotex.iopay.transaction.bean.ExtraExpend
import io.iotex.iopay.transaction.bean.GasLevel.GAS_LEVEL_HIGH
import io.iotex.iopay.transaction.bean.GasLevel.GAS_LEVEL_LOW
import io.iotex.iopay.transaction.bean.GasLevel.GAS_LEVEL_MARKET
import io.iotex.iopay.transaction.bean.GasUIData
import io.iotex.iopay.util.FireBaseEvent
import io.iotex.iopay.util.FireBaseUtil
import io.iotex.iopay.util.TokenUtil
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.util.extension.asBigDecimal
import io.iotex.iopay.util.extension.setGone
import io.iotex.iopay.util.extension.setVisible
import io.iotex.iopay.wallet.dialog.EditGasDialog
import io.iotex.iopay.wallet.viewmodel.AAWalletViewModel
import org.web3j.tx.gas.DefaultGasProvider
import java.math.BigDecimal
import java.math.BigInteger

class GasFragment @JvmOverloads constructor(
    private val to: String = "",
    private val value: BigInteger = BigInteger.ZERO,
    private val data: String = "",
    private var receiveGasLimit: String = "",
    private var receiveGasPrice: String = "",
    private val extraExpend: ExtraExpend? = null
) : BaseBindFragment<GasViewModel, FragmentGasBinding>(R.layout.fragment_gas) {

    var onGasFree: ((
        gasLimit: BigInteger,
        gasPrice: BigInteger,
        maxPriorityFeePerGas: BigInteger,
        maxFeePerGas: BigInteger
    ) -> Unit)? = null
    var onGasOrBalanceError: ((error: Boolean) -> Unit)? = null
    private var gasError = false

    private var defGasLimit = DefaultGasProvider.GAS_LIMIT
    private var defGasPrice = DefaultGasProvider.GAS_PRICE
    private var gasLimit = DefaultGasProvider.GAS_LIMIT
    private var gasPrice = DefaultGasProvider.GAS_PRICE
    private var maxPriorityFeePerGas = BigInteger.ZERO
    private var maxFeePerGas = BigInteger.ZERO
    private var level = GAS_LEVEL_MARKET
    private var available1559 = false

    private val aaWalletViewModel by lazy {
        ViewModelProvider(this)[AAWalletViewModel::class.java]
            .apply {
                lifecycle.addObserver(this)
            }
    }

    override fun initView() {
        mBinding.ivEditGas.setOnClickListener {
            editGas()
        }
        mBinding.llGasLevel.setOnClickListener {
            editGas()
        }
    }

    private fun editGas() {
        if (available1559) {
            EditGas1559Dialog(gasLimit.toString(), maxPriorityFeePerGas, maxFeePerGas, level)
                .apply {
                    onConfirmCallback = { maxPriorityFeePerGas, maxFeePerGas, level ->
                        <EMAIL> = level
                        mBinding.tvGasLevel.text = EditGasUtil.getLevelName(level)
                        mBinding.tvGasLevel.setTextColor(EditGasUtil.getLevelColor(level))
                        mBinding.ivChoseGas.imageTintList= EditGasUtil.getLevelColorStateList(level)
                        <EMAIL> = maxPriorityFeePerGas
                        <EMAIL> = maxFeePerGas

                        mViewModel.gasUIDataLiveData.value?.let { gasUIData->
                            gasUIData.gasBean.gasPrice = <EMAIL>
                            gasUIData.gasBean.gasLimit = <EMAIL>
                            gasUIData.gasBean.maxPriorityFeePerGas = <EMAIL>
                            gasUIData.gasBean.maxFeePerGas = <EMAIL>
                            displayGas(gasUIData)
                            displayBalance(gasUIData)
                        }

                        onGasFree?.invoke(
                            gasLimit,
                            gasPrice,
                            maxPriorityFeePerGas,
                            maxFeePerGas
                        )
                        val event = when (level) {
                            GAS_LEVEL_LOW -> FireBaseEvent.ACTION_ETH_NETWORK_TXN_GAS_FEE_SLOW
                            GAS_LEVEL_MARKET -> FireBaseEvent.ACTION_ETH_NETWORK_TXN_GAS_FEE_AVERAGE
                            GAS_LEVEL_HIGH -> FireBaseEvent.ACTION_ETH_NETWORK_TXN_GAS_FEE_FAST
                            else -> FireBaseEvent.ACTION_ETH_NETWORK_TXN_GAS_FEE_CUSTMOIZED
                        }
                        FireBaseUtil.logFireBase(event)
                    }
                }
                .show(requireActivity().supportFragmentManager, EditGas1559Dialog::class.java.name)
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_TXN_EDIT_EIP1559_GAS_FEE)
        } else {
            val dialog = EditGasDialog()
            dialog.addValues("defGasLimit", defGasLimit.toLong())
            dialog.addValues("defGasPrice", defGasPrice.toLong())
            dialog.addValues("gasLimit", gasLimit.toLong())
            dialog.addValues("gasPrice", gasPrice.toString())
            dialog.setOnTextChangeCallback { gasLimit, gasPrice ->
                this.gasLimit = gasLimit.toBigInteger()
                this.gasPrice = gasPrice.asBigDecimal().toBigInteger()

                mViewModel.gasUIDataLiveData.value?.let { gasUIData->
                    gasUIData.gasBean.gasPrice = this.gasPrice
                    gasUIData.gasBean.gasLimit = this.gasLimit
                    displayGas(gasUIData)
                    displayBalance(gasUIData)
                }
                onGasFree?.invoke(
                    gasLimit.toBigInteger(),
                    gasPrice.asBigDecimal().toBigInteger(),
                    maxPriorityFeePerGas,
                    maxFeePerGas
                )
            }
            dialog.commitAddValues()
            dialog.showAllowingStateLoss(activity)
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_TXN_EDIT_NORMAL_GAS_FEE)
        }
    }

    override fun initData() {
        mViewModel.estimateGas(to, value, data, receiveGasLimit, receiveGasPrice)
        mViewModel.gasUIDataLiveData.observe(this) {
            mBinding.progressBar.setGone()
            available1559 = it.available1559
            defGasLimit = it.gasBean.gasLimit
            defGasPrice =
                if (defGasPrice > it.gasBean.gasPrice) it.gasBean.gasPrice else defGasPrice
            gasLimit = defGasLimit
            gasPrice = it.gasBean.gasPrice
            onGasFree?.invoke(
                it.gasBean.gasLimit,
                it.gasBean.gasPrice,
                it.gasBean.maxPriorityFeePerGas,
                it.gasBean.maxFeePerGas
            )
            this.maxPriorityFeePerGas = it.gasBean.maxPriorityFeePerGas
            this.maxFeePerGas = it.gasBean.maxFeePerGas

            if (it.isAAWallet) {
                mBinding.llAAInfo.setVisible()
                if (it.gasBean.freeGas) {
                    mBinding.tvAAKey.text = getString(R.string.remaining_gas_subsidy)
                    getFreeGas()
                } else {
                    mBinding.tvAAKey.text = getString(R.string.balance_cap)
                    mBinding.tvAAValue.text = getWalletBalanceText(it.walletBalance ?: "")
                }
            } else if (WalletHelper.isSolanaNetwork()) {
                mBinding.ivEditGas.setGone()
                mBinding.llGasLevel.setGone()
            } else {
                if (it.available1559) {
                    mBinding.ivEditGas.setGone()
                    mBinding.llGasLevel.setVisible()
                } else {
                    mBinding.ivEditGas.setVisible()
                    mBinding.llGasLevel.setGone()
                }
            }
            if (!it.gasBean.success) mBinding.llGasError.setVisible()
            displayGas(it)
            displayBalance(it)
        }
    }

    private fun displayBalance(gasUIData: GasUIData) {
        val gasFee = getGasFree(gasUIData)
        if (extraExpend == null) {
            mBinding.tvAmountName.text = getString(R.string.gas_fee_only)
            mBinding.tvTotalAmount.text = gasFee + " " +
                TokenUtil.getNativeCurrencySymbol()
            val money = (gasFee.toBigDecimalOrNull() ?: BigDecimal.ZERO).multiply(
                gasUIData.walletPrice?.toBigDecimalOrNull() ?: BigDecimal.ZERO
            )
            mBinding.tvAmountValue.text =
                getString(R.string.value_money, TokenUtil.displayBalance(money.toString()))
            val totalExpend = if (gasUIData.gasBean.freeGas) "0" else gasFee
            displayError(gasUIData.walletBalance, totalExpend)
        } else {
            mBinding.tvAmountName.text = getString(R.string.amount_gas_fee)
            if (extraExpend.symbol == TokenUtil.getNativeCurrencySymbol()) {
                val total =
                    (gasFee.toBigDecimalOrNull() ?: BigDecimal.ZERO) + extraExpend.amount
                mBinding.tvTotalAmount.text = total.toString() + " " +
                    TokenUtil.getNativeCurrencySymbol()
                val money = total.multiply(extraExpend.price)
                mBinding.tvAmountValue.text =
                    getString(R.string.value_money, TokenUtil.displayBalance(money.toString()))
                val totalExpend =
                    (if (gasUIData.gasBean.freeGas) "0" else gasFee) + extraExpend.amount
                displayError(gasUIData.walletBalance, totalExpend)
            } else {
                val amountToken = extraExpend.amount.toString() + " " + extraExpend.symbol
                val amount = gasFee + " " + TokenUtil.getNativeCurrencySymbol()
                mBinding.tvTotalAmount.text = "$amountToken\n+$amount"
                val moneyToken = extraExpend.amount.multiply(extraExpend.price)
                val money = (gasFee.toBigDecimalOrNull() ?: BigDecimal.ZERO).multiply(
                    gasUIData.walletPrice?.toBigDecimalOrNull() ?: BigDecimal.ZERO
                )
                mBinding.tvAmountValue.text = getString(
                    R.string.value_money,
                    TokenUtil.displayBalance((moneyToken + money).toString())
                )
                val totalExpend = if (gasUIData.gasBean.freeGas) "0" else gasFee
                displayError(gasUIData.walletBalance, totalExpend)
            }
        }
    }

    private fun displayError(walletBalance: String?, total: String) {
        val error = (walletBalance?.toBigIntegerOrNull()
            ?: BigInteger.ZERO) < TokenUtil.toWei(total, extraExpend?.decimal ?: UserStore.getNetworkDecimals())
        mBinding.llBalanceError.isVisible = error
        onGasOrBalanceError?.invoke(error || gasError)
    }

    private fun getGasFree(gasUIData: GasUIData):String{
        return if (gasUIData.available1559) {
            val gas =gasUIData.gasBean.maxFeePerGas.multiply(gasUIData.gasBean.gasLimit)
            TokenUtil.weiToTokenBN(gas.toString())
        } else {
            TokenUtil.weiToTokenBN(
                gasUIData.gasBean.gasPrice.multiply(gasUIData.gasBean.gasLimit).toString(), UserStore.getNetworkDecimals().toLong()
            )
        }
    }

    private fun displayGas(gasUIData: GasUIData){
        val gasFee = getGasFree(gasUIData)

        if (gasFee == "0") {
            mBinding.tvGasFree.text = "--"
            gasError = true
        } else {
            mBinding.tvGasFree.text = gasFee + " " +
                TokenUtil.getNativeCurrencySymbol()
            gasError = false
        }
    }

    private fun getFreeGas() {
        aaWalletViewModel.getRemainFeeGas()
        aaWalletViewModel.freeGasLD.observe(this) {
            mBinding.tvAAValue.text = "$it ${TokenUtil.getNativeCurrencySymbol()}\n(${Utils.getApp().getString(R.string.gas_subsidy)})"
        }
    }

    private fun getWalletBalanceText(balance: String): String {
        val value =
            TokenUtil.weiToTokenBN(
                balance,
                18
            )
        val str = TokenUtil.displayBalance(
            value
        )

        return str + " " + TokenUtil.getNativeCurrencySymbol()
    }

}