package io.iotex.iopay.transaction.gas

import android.app.Application
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.MutableLiveData
import io.iotex.base.RetrofitClient
import io.iotex.iopay.api.UrlApi
import io.iotex.iopay.base.BaseLaunchVM
import io.iotex.iopay.meta.bean.GasTracker
import io.iotex.iopay.transaction.bean.GasTrackerWrapper
import com.blankj.utilcode.util.Utils
import io.iotex.iopay.data.UserStore
import io.iotex.iopay.data.db.AppDatabase
import io.iotex.iopay.transaction.bean.GasTime
import io.iotex.iopay.transaction.bean.GasUIData
import io.iotex.iopay.util.Config
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.util.extension.asBigDecimal
import io.iotex.iopay.wallet.solana.SolanaWeb3
import io.iotex.iopay.wallet.web3.Web3Delegate
import io.iotex.iopay.wallet.web3.aawallet.GasBean
import io.iotex.iopay.wallet.web3.aawallet.P256AccountManager
import kotlinx.coroutines.delay
import org.web3j.tx.gas.DefaultGasProvider
import org.web3j.utils.Convert
import java.math.BigDecimal
import java.math.BigInteger

class GasViewModel(application: Application) : BaseLaunchVM(application) {

    private val apiService by lazy {
        RetrofitClient.createApiService(Config.IOPAY_URL, UrlApi::class.java)
    }

    private var waitTime = 8000L

    val gasUIDataLiveData = MutableLiveData<GasUIData>()
    val gasTracker = MutableLiveData<GasTrackerWrapper>()
    val gasTime = MutableLiveData<GasTime>()

    fun estimateGas(
        to: String,
        value: BigInteger,
        data: String,
        receiveGasLimit: String = "",
        receiveGasPrice: String = "",
    ) {
        addLaunch {
            val wallet = WalletHelper.getCurWallet() ?: return@addLaunch
            val network = WalletHelper.getCurNetwork() ?: return@addLaunch
            val gasLimit: BigInteger
            var gasPrice: BigInteger = DefaultGasProvider.GAS_PRICE
            var maxPriorityFeePerGas: BigInteger = BigInteger.ZERO
            var maxFeePerGas: BigInteger = BigInteger.ZERO
            val gasBean: GasBean
            var available1559 = false
            if (wallet.isAAWallet()) {
                gasBean = P256AccountManager.estimateGas(to, value, data)
            } else if (WalletHelper.isSolanaNetwork()) {
                val gas = SolanaWeb3.getFree(data)
                gasBean = GasBean(
                    gas?.toBigInteger()?: BigInteger.ZERO,
                    BigInteger.ONE,
                    maxPriorityFeePerGas,
                    maxFeePerGas,
                    freeGas = false,
                    success = gas != null
                )
            } else {
                if (receiveGasLimit.isNotEmpty()) {
                    gasLimit = BigInteger(receiveGasLimit.replace("0x", ""), 16)
                    val receiveGasPriceBig =
                        receiveGasPrice.replace("0x", "").toBigIntegerOrNull(16) ?: BigInteger.ZERO
                    gasPrice = if(receiveGasPriceBig == BigInteger.ZERO) Web3Delegate.gasPrice() else receiveGasPriceBig
                    gasBean = GasBean(
                        gasLimit,
                        gasPrice,
                        maxPriorityFeePerGas,
                        maxFeePerGas,
                        freeGas = false,
                        success = true
                    )
                } else {
                    gasLimit = Web3Delegate.estimate(to, data, value)
                    if (WalletHelper.isSupport1559Gas()) {
                        val gas1559List = get1559Gas()
                        if (gas1559List.isNotEmpty()) {
                            available1559 = true
                            maxPriorityFeePerGas = gas1559List[0]
                            maxFeePerGas = gas1559List[1]
                        } else {
                            gasPrice = Web3Delegate.gasPrice()
                        }
                    } else {
                        gasPrice = Web3Delegate.gasPrice()
                    }
                    gasBean = GasBean(
                        gasLimit,
                        gasPrice,
                        maxPriorityFeePerGas,
                        maxFeePerGas,
                        freeGas = false,
                        success = gasLimit != DefaultGasProvider.GAS_LIMIT
                    )
                }
            }
            val walletCache = AppDatabase.getInstance(Utils.getApp()).walletCacheDao()
                .queryWalletCache(wallet.getCurNetworkAddress(), WalletHelper.getCurChainId())

            gasUIDataLiveData.postValue(
                GasUIData(
                    gasBean,
                    wallet.isAAWallet(),
                    walletCache?.balance ?: "0",
                    network.currencyPrice,
                    available1559
                )
            )
        }
    }

    private suspend fun get1559Gas(): List<BigInteger> {
        val response = runCatching {
            apiService.getGasTracker(UserStore.getNetWorkGasStation())
        }.getOrNull()
        if (response?.status == "1") {
            val priorityFee = response.result.ProposeGasPrice.asBigDecimal() - response.result.suggestBaseFee.asBigDecimal()
            val maxPriorityFeePerGas = Convert.toWei(priorityFee, Convert.Unit.GWEI).toBigInteger()
            val maxFeePerGas = Convert.toWei(response.result.ProposeGasPrice, Convert.Unit.GWEI).toBigInteger()
            return listOf(maxPriorityFeePerGas, maxFeePerGas)
        }
        return emptyList()
    }

    fun getGasTracker(gasLimit: String) {
        addLaunchSingle("getGasTracker") {
            while (true) {
                val response = apiService.getGasTracker(UserStore.getNetWorkGasStation())
                if (response.status == "1") {
                    val data = resolveGasTracker(gasLimit, response.result)
                    gasTracker.postValue(data)
                    requestGasTime(data)
                }
                delay(waitTime)
            }
        }
    }

    private fun resolveGasTracker(gasLimit: String, gasTracker: GasTracker): GasTrackerWrapper {
        val lowGasPrice = Convert.toWei(gasTracker.SafeGasPrice, Convert.Unit.GWEI)
        val lowGasFee = Convert.fromWei(gasLimit.asBigDecimal().multiply(lowGasPrice), Convert.Unit.ETHER)
        val marketGasPrice = Convert.toWei(gasTracker.ProposeGasPrice, Convert.Unit.GWEI)
        val marketGasFee = Convert.fromWei(gasLimit.asBigDecimal().multiply(marketGasPrice), Convert.Unit.ETHER)
        val highGasPrice = Convert.toWei(gasTracker.FastGasPrice, Convert.Unit.GWEI)
        val highGasFee = Convert.fromWei(gasLimit.asBigDecimal().multiply(highGasPrice), Convert.Unit.ETHER)
        return GasTrackerWrapper(
            gasTracker.SafeGasPrice,
            gasTracker.ProposeGasPrice,
            gasTracker.FastGasPrice,
            lowGasFee.toString(),
            marketGasFee.toString(),
            highGasFee.toString(),
            gasTracker.suggestBaseFee
        )
    }

    private fun requestGasTime(gasTracker: GasTrackerWrapper) {
        addLaunchSingle("requestGasTime") {
            val url = if (WalletHelper.isIoTexNetWork()) {
                Config.GAS_TIME_TRACKER_IOTEX
            } else {
                Config.GAS_TIME_TRACKER_EVM
            }
            val lowTime = apiService.getGasTime(url + gasTracker.lowGasPriceGwei)
            val marketTime = apiService.getGasTime(url + gasTracker.marketGasFee)
            val highTime = apiService.getGasTime(url + gasTracker.highGasPriceGwei)
            gasTime.postValue(GasTime(lowTime.result, marketTime.result, highTime.result))
        }
    }

    override fun onStop(owner: LifecycleOwner) {
        super.onStop(owner)
        cancelAllJob()
    }
}