package io.iotex.iopay.transaction.item

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isVisible
import com.blankj.utilcode.util.Utils
import com.drakeet.multitype.ItemViewBinder
import io.iotex.base.bindbase.BaseBindVH
import io.iotex.iopay.R
import io.iotex.iopay.data.UserStore
import io.iotex.iopay.databinding.ItemEditGas1559Binding
import io.iotex.iopay.transaction.bean.EditGasItem
import io.iotex.iopay.transaction.bean.EditGasUtil
import io.iotex.iopay.transaction.bean.GasLevel.GAS_LEVEL_ADVANCED
import io.iotex.iopay.transaction.bean.GasLevel.GAS_LEVEL_HIGH
import io.iotex.iopay.transaction.bean.GasLevel.GAS_LEVEL_LOW
import io.iotex.iopay.transaction.bean.GasLevel.GAS_LEVEL_MARKET
import io.iotex.iopay.util.TokenUtil
import io.iotex.iopay.util.extension.asBigDecimal

class EditGas1559ItemBinder : ItemViewBinder<EditGasItem, BaseBindVH<ItemEditGas1559Binding>>() {

    var selectLevel = GAS_LEVEL_MARKET
    var onEditAdvancedFee: (() -> Unit)? = null
    var onClickItem: ((EditGasItem) -> Unit)? = null

    override fun onCreateViewHolder(
        inflater: LayoutInflater,
        parent: ViewGroup
    ): BaseBindVH<ItemEditGas1559Binding> {
        val bind = ItemEditGas1559Binding.inflate(inflater, parent, false)
        return BaseBindVH(bind)
    }

    override fun onBindViewHolder(holder: BaseBindVH<ItemEditGas1559Binding>, item: EditGasItem) {
        holder.bind.ivEdit.isVisible = item.level == GAS_LEVEL_ADVANCED
        holder.bind.tvTime.isVisible = item.level != GAS_LEVEL_ADVANCED
        val money = item.fee.asBigDecimal() * UserStore.getNetWorkPrice().asBigDecimal()
        holder.bind.tvFee.text = if (item.fee.isBlank()) "—— " + TokenUtil.getNativeCurrencySymbol()
        else TokenUtil.formatDecimal(item.fee,3) + " " + TokenUtil.getNativeCurrencySymbol() + " " +
            Utils.getApp()
                .getString(R.string.value_money, TokenUtil.formatDecimal(money.toString(),3))
        holder.bind.tvLevelName.text = EditGasUtil.getLevelName(item.level)
        holder.bind.ivLevelIcon.setImageResource(EditGasUtil.getLevelIcon(item.level))

        if (selectLevel == item.level) {
            holder.bind.llItem.setBackgroundResource(R.drawable.shape_card_back_stroke_gradient)
        } else {
            holder.bind.llItem.setBackgroundResource(R.drawable.shape_card_back)
        }

        holder.bind.tvTime.text = if (item.time.isNotEmpty()) {
            item.time + Utils.getApp().getString(R.string.sec_or_longer)
        } else {
            ""
        }

        holder.bind.root.setOnClickListener {
            onClickItem?.invoke(item)
        }
        holder.bind.ivEdit.setOnClickListener {
            onEditAdvancedFee?.invoke()
        }
    }

}