package io.iotex.iopay.transaction.item

import android.text.method.LinkMovementMethod
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.PopupWindow
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.ColorUtils
import com.blankj.utilcode.util.Utils
import com.drakeet.multitype.ItemViewBinder
import com.machinefi.w3bstream.utils.extension.setClickSpan
import io.iotex.base.bindbase.BaseBindVH
import io.iotex.iopay.R
import io.iotex.iopay.databinding.ItemEntryTransferBinding
import io.iotex.iopay.databinding.ViewPopCertifiedBinding
import io.iotex.iopay.repo.TransferEntryMapRepo
import io.iotex.iopay.transaction.approve.EditApproveContractDialog
import io.iotex.iopay.transaction.bean.OptionEntry
import io.iotex.iopay.util.FireBaseEvent
import io.iotex.iopay.util.FireBaseUtil
import io.iotex.iopay.util.TokenUtil
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.util.extension.loadSvgOrImage
import io.iotex.iopay.util.extension.setGone
import io.iotex.iopay.util.extension.setVisible

class EntryItemBinder : ItemViewBinder<OptionEntry, BaseBindVH<ItemEntryTransferBinding>>() {

    private val transferEntryMapRepo by lazy {
        TransferEntryMapRepo()
    }

    private var editApproveContractDialog: EditApproveContractDialog? = null
    var editApproveAmount: (() -> Unit)? = null
    override fun onCreateViewHolder(
        inflater: LayoutInflater,
        parent: ViewGroup
    ): BaseBindVH<ItemEntryTransferBinding> {
        val bind = ItemEntryTransferBinding.inflate(inflater, parent, false)
        return BaseBindVH(bind)
    }

    override fun onBindViewHolder(holder: BaseBindVH<ItemEntryTransferBinding>, item: OptionEntry) {
        holder.bind.tvKey.text = item.key
        holder.bind.tvValue.text = item.value
        holder.bind.ivCertified.setGone()
        holder.bind.ivEdit.setGone()

        if (item.key == Utils.getApp().getString(R.string.contract_addr)) {
            showContract(holder.bind, item)
        } else if (item.key == Utils.getApp().getString(R.string.approve_amount)) {
            showApproveAmount(holder.bind)
        } else if (item.key == Utils.getApp().getString(R.string.candidate_name)) {
            showCandidate(holder.bind, item)
        } else if (item.key == Utils.getApp().getString(R.string.status)) {
            showStatus(holder.bind, item)
        } else if (item.key == Utils.getApp().getString(R.string.you_send_only)
            || item.key == Utils.getApp().getString(R.string.swap_from)
            || item.key == Utils.getApp().getString(R.string.swap_to)
            || item.key == Utils.getApp().getString(R.string.minimum_receive)
            || item.key == Utils.getApp().getString(R.string.maximum_pay)
            || item.key == Utils.getApp().getString(R.string.amount)
        ) {
            showValue(holder.bind, item)
        } else if (WalletHelper.isValidAddress(item.value)) {
            showAddress(holder.bind, item)
        }
    }

    private fun showCandidate(bind: ItemEntryTransferBinding, item: OptionEntry) {
        bind.tvValue.text = ""
        transferEntryMapRepo.getStakeName(item.value) {
            if (!it.isNullOrEmpty()) {
                bind.tvValue.text = it
            } else {
                if (WalletHelper.isValidAddress(item.value)) {
                    bind.tvValue.text = TokenUtil.textEllipsis(item.value, 6, 8)
                } else {
                    bind.tvValue.text = item.value
                }
            }
        }
    }

    private fun showStatus(bind: ItemEntryTransferBinding, item: OptionEntry) {
        when (item.value) {
            Utils.getApp().getString(R.string.waiting) -> {
                bind.tvValue.setTextColor(ColorUtils.getColor(R.color.color_ec7f11))
            }

            Utils.getApp().getString(R.string.failed) -> {
                bind.tvValue.setTextColor(ColorUtils.getColor(R.color.color_E53737))
            }

            Utils.getApp().getString(R.string.success) -> {
                bind.tvValue.setTextColor(ColorUtils.getColor(R.color.color_00dc9c))
            }
        }
    }

    private fun showContract(bind: ItemEntryTransferBinding, item: OptionEntry) {
        transferEntryMapRepo.getContractName(item.value) { entry ->
            if (entry.name.isNotEmpty()) {
                bind.tvValue.text = entry.name
            } else if (entry.nikeName.isNotEmpty()) {
                bind.tvValue.text = entry.nikeName
            } else if (WalletHelper.isValidAddress(item.value)) {
                showAddress(bind, item)
            } else {
                bind.tvValue.text = item.value
            }
            bind.ivCertified.isVisible = entry.name.isNotEmpty()
            bind.ivEdit.isVisible = entry.name.isEmpty()
            bind.ivCertified.setOnClickListener {
                showPopupWindow(bind.ivCertified)
            }
            if (entry.name.isNotEmpty()) {
                bind.tvValue.setOnClickListener {
                    val activity = ActivityUtils.getTopActivity() as? AppCompatActivity
                    activity?.let { context ->
                        if (editApproveContractDialog == null) {
                            editApproveContractDialog =
                                EditApproveContractDialog(entry.contract, entry.name, true)
                                    .apply {
                                        onDismiss = {
                                            editApproveContractDialog = null
                                        }
                                    }
                            editApproveContractDialog?.show(
                                context.supportFragmentManager,
                                System.currentTimeMillis().toString()
                            )
                        }
                    }
                }
            } else {
                bind.ivEdit.setOnClickListener {
                    val activity = ActivityUtils.getTopActivity() as? AppCompatActivity
                    activity?.let {
                        if (editApproveContractDialog == null) {
                            editApproveContractDialog =
                                EditApproveContractDialog(item.value, entry.nikeName)
                                    .apply {
                                        onDismiss = {
                                            editApproveContractDialog = null
                                        }

                                        onConfirm = {
                                            if (it.isNotEmpty()) {
                                                entry.nikeName = it
                                                bind.tvValue.text = it
                                            } else if (WalletHelper.isValidAddress(item.value)) {
                                                showAddress(bind, item)
                                            } else {
                                                bind.tvValue.text = item.value
                                            }
                                        }
                                    }
                            editApproveContractDialog?.show(
                                activity.supportFragmentManager,
                                System.currentTimeMillis().toString()
                            )
                        }
                    }
                }
            }
        }
    }

    private fun showApproveAmount(bind: ItemEntryTransferBinding) {
        bind.ivEdit.setVisible()
        bind.ivEdit.setOnClickListener {
            editApproveAmount?.invoke()
        }
    }

    private fun showAddress(bind: ItemEntryTransferBinding, item: OptionEntry) {
        transferEntryMapRepo.getWalletOrBookName(item.value) {
            val address = WalletHelper.formatWalletAddress(item.value)
            if (it.isNullOrEmpty()) {
                val short = TokenUtil.textEllipsis(address, 6, 8)
                bind.tvValue.text = short.setClickSpan(short, ContextCompat.getColor(bind.tvValue.context, R.color.color_title)) {
                    WalletHelper.gotoExplorerTokenScan(item.value)
                    FireBaseUtil.logFireBase(FireBaseEvent.ACTION_HOME_NFT_CONTRACT_CLICK)
                }
                bind.tvValue.movementMethod = LinkMovementMethod()
            } else {
                val short = TokenUtil.textEllipsis(address, 6, 8)
                bind.tvValue.text = "$short\n($it)"
            }
        }
    }

    private fun showPopupWindow(view: View) {
        val bind = ViewPopCertifiedBinding.inflate(LayoutInflater.from(view.context))
        PopupWindow(
            bind.root,
            ViewGroup.LayoutParams.WRAP_CONTENT,
            ViewGroup.LayoutParams.WRAP_CONTENT
        ).apply {
            isOutsideTouchable = true
            showAsDropDown(view)
        }
    }

    private fun showValue(bind: ItemEntryTransferBinding, item: OptionEntry){
        bind.tvValue.setVisible()
        bind.llValue.setGone()
        if(item.value.contains(" ")){
            val valueList = item.value.split(" ")
            if(valueList.size==2){
                val amount = valueList[0]
                val symbol = valueList[1]
                transferEntryMapRepo.getTokenBySymbol(amount,symbol){logo,money->
                    bind.tvValue.setGone()
                    bind.llValue.setVisible()
                    bind.tvValueOnly.text = amount
                    if (amount.contains("-")) {
                        bind.tvValueOnly.setTextColor(ColorUtils.getColor(R.color.color_E53737))
                        bind.tvValueOnly.setBackgroundResource(R.drawable.shape_1ae53737_r11)
                    }
                    if (amount.contains("+")) {
                        bind.tvValueOnly.setTextColor(ColorUtils.getColor(R.color.color_00dc9c))
                        bind.tvValueOnly.setBackgroundResource(R.drawable.shape_1a00dc9c_r11)
                    }
                    bind.tvSymbol.text = symbol
                    bind.ivLogo.loadSvgOrImage(logo, R.drawable.ic_network_default)
                    bind.tvMoney.text = money
                }

            }
        }
    }
}