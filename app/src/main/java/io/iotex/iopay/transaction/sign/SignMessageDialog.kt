package io.iotex.iopay.transaction.sign

import android.annotation.SuppressLint
import android.content.DialogInterface
import android.content.res.Configuration
import android.text.method.ScrollingMovementMethod
import android.view.Gravity
import android.view.MotionEvent
import android.view.ViewGroup
import androidx.appcompat.widget.AppCompatTextView
import androidx.recyclerview.widget.LinearLayoutManager
import com.blankj.utilcode.util.ScreenUtils
import com.drakeet.multitype.MultiTypeAdapter
import com.google.gson.Gson
import io.iotex.base.bindbase.BaseBindDialog
import io.iotex.iopay.R
import io.iotex.iopay.databinding.DialogSignMessageBinding
import io.iotex.iopay.network.viewmodel.AddOrEditNetworkViewModel
import io.iotex.iopay.transaction.bean.OptionEntry
import io.iotex.iopay.transaction.bean.SignDataBean
import io.iotex.iopay.transaction.item.EntryItemBinder
import io.iotex.iopay.util.extension.loadSvgOrImage
import io.iotex.iopay.util.extension.setGone
import io.iotex.iopay.util.extension.setVisible
import io.iotex.iopay.util.extension.toHexStringNoPrefix
import org.web3j.utils.HexToUtf8

class SignMessageDialog @JvmOverloads constructor(
    private val dAppLogo: String = "",
    private val dAppUrl: String = "",
    private val data: ByteArray? = null,
    private val raw: String? = null
) :
    BaseBindDialog<AddOrEditNetworkViewModel, DialogSignMessageBinding>(R.layout.dialog_sign_message) {

    var onResult: ((confirm: Boolean) -> Unit)? = null
    private var confirm = false
    private val mAdapter = MultiTypeAdapter()

    @SuppressLint("ClickableViewAccessibility")
    override fun initView() {
        mBinding.ivAppLogo.loadSvgOrImage(dAppLogo, R.drawable.icon_dapp_default)
        mBinding.tvAppUrl.text = dAppUrl

        mAdapter.register(OptionEntry::class.java, EntryItemBinder())
        mBinding.recyclerView.layoutManager = LinearLayoutManager(context)
        mBinding.recyclerView.adapter = mAdapter

        mBinding.ivDown.setOnClickListener {
            mBinding.recyclerView.scrollToPosition(mAdapter.items.size - 1)
        }

        mBinding.mBtnCancel.setOnClickListener {
            dismiss()
        }

        mBinding.mBtnConfirm.setOnClickListener {
            confirm = true
            dismiss()
        }

        mBinding.tvData.movementMethod = ScrollingMovementMethod()
        mBinding.tvData.setOnTouchListener { v, event ->
            mBinding.ivDownData.setGone()
            when (event.action) {
                MotionEvent.ACTION_DOWN -> {
                    v.parent.requestDisallowInterceptTouchEvent(true)
                }
                MotionEvent.ACTION_MOVE -> {
                    v.parent.requestDisallowInterceptTouchEvent(true)
                }
                MotionEvent.ACTION_UP -> {
                    v.parent.requestDisallowInterceptTouchEvent(false)
                }
            }
            false
        }
    }

    override fun initData() {
        var signDataBean: SignDataBean? = null
        try {
            signDataBean = Gson().fromJson(raw, SignDataBean::class.java)
        } catch (e: Exception) {
            e.printStackTrace()
        }
        val list = ArrayList<OptionEntry>()
        signDataBean?.message?.forEach { (key, value) ->
            list.add(OptionEntry(key, value))
        }

        if (list.isEmpty()) {
            mBinding.llMassage.setVisible()
            mBinding.llRawMassage.setGone()
            if(raw == null)
                mBinding.tvData.text = HexToUtf8.hexStringToBytes(data?.toHexStringNoPrefix())
            else
                mBinding.tvData.text = raw
            mBinding.tvData.movementMethod = ScrollingMovementMethod()
        } else {
            if (list.size > 5) {
                mBinding.ivDown.setVisible()
            } else {
                mBinding.ivDown.setGone()
            }
            mBinding.llMassage.setGone()
            mBinding.llRawMassage.setVisible()
            mAdapter.items = list
            mAdapter.notifyDataSetChanged()
        }

        mViewModel.getCurNetwork()
        mViewModel.curNetworkLiveData.observe(this) {
            mBinding.ivNetwork.loadSvgOrImage(it.logo, R.drawable.ic_network_default)
        }

        mBinding.tvData.post {
            if (needsScrolling(mBinding.tvData)) {
                mBinding.ivDownData.setVisible()
            } else {
                mBinding.ivDownData.setGone()
            }
        }
    }

    private fun needsScrolling(textView: AppCompatTextView): Boolean {
        val height = textView.height
        val contentHeight = textView.lineCount * textView.lineHeight
        return contentHeight > height
    }

    override fun onStart() {
        super.onStart()
        val attributes = dialog?.window?.attributes
        val orientation = resources.configuration.orientation
        if (orientation == Configuration.ORIENTATION_LANDSCAPE) {
            attributes?.width = (ScreenUtils.getScreenHeight() * 0.8).toInt()
            attributes?.height = ViewGroup.LayoutParams.WRAP_CONTENT
        } else {
            attributes?.width = ScreenUtils.getScreenWidth()
            attributes?.height = ViewGroup.LayoutParams.WRAP_CONTENT
        }
        attributes?.gravity = Gravity.BOTTOM
        dialog?.window?.attributes = attributes
    }

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        onResult?.invoke(confirm)
    }
}