package io.iotex.iopay.ui

import android.animation.ValueAnimator
import android.content.Context
import android.graphics.*
import android.graphics.drawable.Drawable
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.animation.Animation
import android.view.animation.CycleInterpolator
import android.view.animation.TranslateAnimation
import androidx.appcompat.widget.AppCompatEditText
import androidx.core.content.ContextCompat
import androidx.core.graphics.drawable.DrawableCompat
import io.iotex.iopay.R

class ClearEditText : AppCompatEditText {

    private val CLEAR = R.drawable.btn_delete
    private val ANIMATOR_TIME = 200
    private val INTERVAL = 5
    private val WIDTH_OF_CLEAR = 23
    var interval: Int = 0
        private set
    private var mWidth_clear: Int = 0
    private var mPaddingRight: Int = 0
    private var mBitmap_clear: Bitmap? = null
    private var mAnimator_visible: ValueAnimator? = null
    private var mAnimator_gone: ValueAnimator? = null
    private var isVisible = false
    private var mRight = 0
    private var heightExtra: Int = 0

    constructor(context: Context) : super(context) {
        init(context)
    }

    constructor(context: Context, attrs: AttributeSet) : super(context, attrs) {
        init(context)
    }

    constructor(context: Context, attrs: AttributeSet, defStyleAttr: Int) : super(context, attrs, defStyleAttr) {
        init(context)
    }

    private fun init(context: Context) {

        mBitmap_clear = createBitmap(CLEAR, context)
        interval = dp2px(INTERVAL.toFloat())
        mWidth_clear = dp2px(WIDTH_OF_CLEAR.toFloat())
        mPaddingRight = interval + mWidth_clear + interval
        mAnimator_gone = ValueAnimator.ofFloat(1f, 0f).setDuration(ANIMATOR_TIME.toLong())
        mAnimator_visible = ValueAnimator.ofInt(mWidth_clear + interval, 0).setDuration(ANIMATOR_TIME.toLong())
        heightExtra = resources.getDimensionPixelOffset(com.google.android.material.R.dimen.mtrl_textinput_outline_box_expanded_padding)
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)
        setPadding(paddingLeft, paddingTop, mPaddingRight + mRight, paddingBottom)
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        canvas.drawFilter = PaintFlagsDrawFilter(0, Paint.ANTI_ALIAS_FLAG or Paint.FILTER_BITMAP_FLAG)
        if (mAnimator_visible!!.isRunning) {
            val x = mAnimator_visible!!.animatedValue as Int
            drawClear(x, canvas)
            invalidate()
        } else if (isVisible) {
            drawClear(0, canvas)
        }

        if (mAnimator_gone!!.isRunning) {
            val scale = mAnimator_gone!!.animatedValue as Float
            drawClearGone(scale, canvas)
            invalidate()
        }
    }

    protected fun drawClear(translationX: Int, canvas: Canvas) {
        val right = width + scrollX - interval - mRight + translationX
        val left = right - mWidth_clear
        val top = (height + heightExtra - mWidth_clear) / 2
        val bottom = top + mWidth_clear
        val rect = Rect(left, top, right, bottom)
        canvas.drawBitmap(mBitmap_clear!!, null, rect, null)
    }

    protected fun drawClearGone(scale: Float, canvas: Canvas) {
        val right =
            ((width + scrollX).toFloat() - interval.toFloat() - mRight.toFloat() - mWidth_clear * (1f - scale) / 2f).toInt()
        val left =
            ((width + scrollX).toFloat() - interval.toFloat() - mRight.toFloat() - mWidth_clear * (scale + (1f - scale) / 2f)).toInt()
        val top = ((height + heightExtra - mWidth_clear * scale) / 2).toInt()
        val bottom = (top + mWidth_clear * scale).toInt()
        val rect = Rect(left, top, right, bottom)
        canvas.drawBitmap(mBitmap_clear!!, null, rect, null)
    }

    private fun startVisibleAnimator() {
        endAnaimator()
        mAnimator_visible!!.start()
        invalidate()
    }

    private fun startGoneAnimator() {
        endAnaimator()
        mAnimator_gone!!.start()
        invalidate()
    }

    private fun endAnaimator() {
        mAnimator_gone!!.end()
        mAnimator_visible!!.end()
    }

    fun setVisibleClear(visible: Boolean) {
        isVisible = visible
        startVisibleAnimator()
    }
    override fun onTextChanged(text: CharSequence, start: Int, lengthBefore: Int, lengthAfter: Int) {
        super.onTextChanged(text, start, lengthBefore, lengthAfter)

        if (text.isNotEmpty()) {
            if (!isVisible) {
                isVisible = true
                startVisibleAnimator()
            }
        } else {
            if (isVisible) {
                isVisible = false
                startGoneAnimator()
            }
        }
    }

    override fun onTouchEvent(event: MotionEvent): Boolean {
        if (event.action == MotionEvent.ACTION_UP) {

            val touchable = width - interval - mRight - mWidth_clear < event.x && event.x < width - interval - mRight
            if (touchable && isVisible) {
                error = null
                this.setText("")
            }
        }
        return super.onTouchEvent(event)
    }

    fun startShakeAnimation() {
        if (animation == null) {
            this.animation = shakeAnimation(4)
        }
        this.startAnimation(animation)
    }

    private fun shakeAnimation(counts: Int): Animation {
        val translateAnimation = TranslateAnimation(0f, 10f, 0f, 0f)
        translateAnimation.interpolator = CycleInterpolator(counts.toFloat())
        translateAnimation.duration = 500
        return translateAnimation
    }

    fun createBitmap(resources: Int, context: Context): Bitmap {
        val drawable = ContextCompat.getDrawable(context, resources)
        val wrappedDrawable = DrawableCompat.wrap(drawable!!)
        DrawableCompat.setTint(wrappedDrawable, currentHintTextColor)
        return drawableToBitamp(wrappedDrawable)
    }

    private fun drawableToBitamp(drawable: Drawable): Bitmap {
        val w = drawable.intrinsicWidth
        val h = drawable.intrinsicHeight
        val config = if (drawable.opacity != PixelFormat.OPAQUE) Bitmap.Config.ARGB_8888 else Bitmap.Config.RGB_565
        val bitmap = Bitmap.createBitmap(w, h, config)
        val canvas = Canvas(bitmap)
        drawable.setBounds(0, 0, w, h)
        drawable.draw(canvas)
        return bitmap
    }

    fun dp2px(dipValue: Float): Int {
        val scale = resources.displayMetrics.density
        return (dipValue * scale + 0.5f).toInt()
    }
}