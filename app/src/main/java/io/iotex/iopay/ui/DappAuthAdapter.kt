package io.iotex.iopay.ui

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.daimajia.swipe.adapters.RecyclerSwipeAdapter
import io.iotex.iopay.R
import io.iotex.iopay.data.db.TrustDapp
import io.iotex.iopay.util.extension.loadSvgOrImage

internal class DappAuthAdapter(
    private val dataSource: Array<TrustDapp>
) : RecyclerSwipeAdapter<DappAuthAdapter.DappAuthAdapterHolder>() {

    private var itemDelClickListener: IKotlinItemDelClickListener? = null

    override fun getItemCount(): Int = dataSource.size

    override fun getSwipeLayoutResourceId(position: Int): Int {
        return R.id.swipeLayout
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): DappAuthAdapterHolder =
        DappAuthAdapterHolder.create(parent)

    override fun getItemId(position: Int): Long {
        return position.toLong()
    }

    override fun onBindViewHolder(holder: DappAuthAdapterHolder, position: Int) {
        val data = dataSource[position]
        holder.name.text = data.dappName
        holder.url.text = "https://"+data.dapp
        holder.icon.loadSvgOrImage(data.logo, R.drawable.ic_dapp_placeholder)
        holder.delete.setOnClickListener {
            itemDelClickListener?.onItemDelClickListener(dataSource[position])
        }
    }

    fun setOnKotlinItemDelClickListener(itemDelClickListener: IKotlinItemDelClickListener) {
        this.itemDelClickListener = itemDelClickListener
    }

    interface IKotlinItemDelClickListener {
        fun onItemDelClickListener(trustDapp: TrustDapp)
    }

    internal class DappAuthAdapterHolder private constructor(view: View) :
        RecyclerView.ViewHolder(view) {

        var item = view.findViewById(R.id.view_item) as View
        var delete = view.findViewById(R.id.rel_delete) as View
        var icon = view.findViewById(R.id.iv_icon) as ImageView
        var name = view.findViewById(R.id.tv_name) as TextView
        var url = view.findViewById(R.id.tv_url) as TextView

        companion object {
            fun create(parent: ViewGroup): DappAuthAdapterHolder {
                val view = LayoutInflater.from(parent.context)
                    .inflate(R.layout.item_dapp_auth, parent, false)
                return DappAuthAdapterHolder(view)
            }
        }
    }
}