package io.iotex.iopay.ui

import android.app.Activity
import android.view.Gravity
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.PopupWindow
import android.widget.RelativeLayout
import android.widget.TextView
import com.google.firebase.crashlytics.FirebaseCrashlytics
import io.iotex.iopay.R

class ErrorPopupWindow(
    private val activity: Activity?,
    private val title: String?,
    private val message: String,
    private val confirmAction: () -> Unit
) {

    private var dialog: PopupWindow? = null

    fun show() {
        val content = LayoutInflater.from(activity).inflate(R.layout.error_popup_dialog, null)

        if (title != null) {
            content.findViewById<TextView>(R.id.title).text = title
        }
        content.findViewById<TextView>(R.id.message).text = message
        dialog = PopupWindow(
            content,
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.MATCH_PARENT
        ).apply {
            activity?.let {
                if (!it.isFinishing && !activity.isDestroyed && it.window != null &&
                    it.window.isActive
                ) {
                    try {
                        this.showAtLocation(it.window.decorView, Gravity.CENTER, 0, 0)
                    } catch (e: Exception) {
                        FirebaseCrashlytics.getInstance().recordException(e)
                    }
                }
            }
        }

        content.findViewById<RelativeLayout>(R.id.dialog_background).setOnClickListener {
            dialog?.dismiss()
        }

        content.findViewById<TextView>(R.id.close).setOnClickListener {
            dialog?.dismiss()
            confirmAction()
        }
    }

    fun dismiss() {
        dialog?.dismiss()
        dialog = null
    }
}