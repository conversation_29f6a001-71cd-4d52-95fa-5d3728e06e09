package io.iotex.iopay.ui

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.FragmentActivity
import androidx.recyclerview.widget.RecyclerView
import com.daimajia.swipe.adapters.RecyclerSwipeAdapter
import io.iotex.iopay.data.db.Wallet

abstract class GenericRecyclerViewAdapter<T>(internal var dataSource: List<T>) :
    RecyclerSwipeAdapter<RecyclerView.ViewHolder>() {

    var fragmentActivity: FragmentActivity? = null
    open lateinit var wallet: Wallet

    override fun getItemCount(): Int = dataSource.size

    override fun getItemId(position: Int): Long {
        return position.toLong()
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        (holder as Binder<T>).bind(dataSource[position], position)
    }

    fun setItems(listItems: List<T>) {
        this.dataSource = listItems
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return getViewHolder(LayoutInflater.from(parent.context)
            .inflate(viewType, parent, false), viewType)
    }

    override fun getItemViewType(position: Int): Int {
        return getLayoutId(position, dataSource[position])
    }

    protected abstract fun getLayoutId(position: Int, obj: T): Int

    abstract fun getViewHolder(view: View, viewType: Int): RecyclerView.ViewHolder

    interface Binder<T> {
        fun bind(data: T, position: Int)
    }
}
