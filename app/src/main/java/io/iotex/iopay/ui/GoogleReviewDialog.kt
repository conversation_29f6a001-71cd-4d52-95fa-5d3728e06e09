package io.iotex.iopay.ui

import android.app.Activity
import android.content.Intent
import com.google.android.play.core.review.ReviewInfo
import com.google.android.play.core.review.ReviewManager
import io.iotex.base.BaseViewModel
import io.iotex.base.bindbase.BaseBindDialog
import io.iotex.iopay.R
import io.iotex.iopay.data.UserStore
import io.iotex.iopay.databinding.DialogGoogleReviewBinding
import io.iotex.iopay.setting.UserFeedBackActivity

class GoogleReviewDialog(
    private val activity: Activity,
    private val reviewInfo: ReviewInfo,
    private val manager: ReviewManager,
) : BaseBindDialog<BaseViewModel, DialogGoogleReviewBinding>(R.layout.dialog_google_review) {

    override fun initView() {
        mBinding.btnYes.setOnClickListener {
            gotoReview(activity)
            dismiss()
        }

        mBinding.btnNo.setOnClickListener {
            activity.startActivity(Intent(activity, UserFeedBackActivity::class.java))
            dismiss()
        }

        mBinding.ivClose.setOnClickListener {
            dismiss()
        }
    }

    private fun gotoReview(activity: Activity) {
        val flow = manager.launchReviewFlow(activity, reviewInfo)
        flow.addOnCompleteListener { v ->
            if (v.isComplete) {
                UserStore.setGoogleReviewDone(true)
            }
        }
    }
}