package io.iotex.iopay.ui

import androidx.annotation.DrawableRes
import androidx.appcompat.app.AppCompatActivity
import com.blankj.utilcode.util.ActivityUtils
import io.iotex.base.BaseViewModel
import io.iotex.base.bindbase.BaseBindDialog
import io.iotex.iopay.R
import io.iotex.iopay.databinding.NoticePopupDialogBinding

class NoticePopupWindow(
    private val title: String?,
    private val message: String,
    @DrawableRes private val imageId: Int?,
    private val confirmAction: () -> Unit,
    private val cancelAction: (() -> Unit)? = null,
    private val confirmText: String? = null
) :BaseBindDialog<BaseViewModel,NoticePopupDialogBinding>(R.layout.notice_popup_dialog){

    override fun initView() {
        if (imageId != null) {
            mBinding.image.setImageResource(imageId)
        }

        if (title != null) {
            mBinding.title.text = title
        }
        if (confirmText != null) {
            mBinding.confirm.text = confirmText
        }
        mBinding.message.text = message

        mBinding.confirm.setOnClickListener {
            dismiss()
            confirmAction()
        }

        mBinding.cancel.setOnClickListener {
            dismiss()
            cancelAction?.let { it1 -> it1() }
        }

        mBinding.ivCancel.setOnClickListener {
            dismiss()
        }
    }

    fun show() {
        val activity = ActivityUtils.getTopActivity() as? AppCompatActivity ?: return
        if (ActivityUtils.isActivityAlive(activity)) {
            show(
                activity.supportFragmentManager,
                System.currentTimeMillis().toString()
            )
        } else{
            confirmAction.invoke()
        }
    }
}