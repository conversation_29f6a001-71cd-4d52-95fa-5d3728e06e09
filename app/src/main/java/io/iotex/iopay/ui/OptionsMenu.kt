package io.iotex.iopay.ui

import android.content.Intent
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Picture
import android.graphics.drawable.ColorDrawable
import android.net.Uri
import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.webkit.WebView
import android.widget.LinearLayout
import android.widget.PopupWindow
import android.widget.TextView
import androidx.fragment.app.FragmentActivity
import com.blankj.utilcode.util.ClipboardUtils
import com.blankj.utilcode.util.SPUtils
import com.blankj.utilcode.util.ToastUtils
import com.blankj.utilcode.util.Utils
import io.iotex.iopay.R
import io.iotex.iopay.network.dialog.NetworkSwitchDialog
import io.iotex.iopay.util.Config.IOTEX_NAME
import io.iotex.iopay.util.Constant
import io.iotex.iopay.util.FireBaseEvent
import io.iotex.iopay.util.FireBaseUtil
import io.iotex.iopay.util.QRCodeUtil
import io.iotex.iopay.util.SPConstant.SP_RPC_NETWORK_NAME
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.util.extension.setGone
import io.iotex.iopay.util.extension.setVisible
import io.iotex.iopay.util.extension.toast
import io.iotex.iopay.wallet.aawallet.dialog.AAReceiveDialog.showAAReceiveCopy
import io.iotex.iopay.wallet.list.SwitchWalletDialog
import org.jetbrains.anko.toast


class OptionsMenu(
        private val context: FragmentActivity,
        private val url: String,
        private val webView: WebView,
        private val refreshCallback: (() -> Unit)? = null,
        private val scanResult: ((String) -> Unit)? = null,
        private val showRefresh:Boolean = true,
        private val visitor:Boolean = false
) {

    private var dialog: PopupWindow? = null

    fun show(view: View) {
        val content =
            LayoutInflater.from(context).inflate(R.layout.dapp_options_menu, null)

        dialog = PopupWindow(
                content,
                ViewGroup.LayoutParams.WRAP_CONTENT,
                ViewGroup.LayoutParams.WRAP_CONTENT,false
        ).apply {
            this.setBackgroundDrawable(ColorDrawable(0))
            this.isOutsideTouchable = true
            if(showRefresh){
                this.showAtLocation(view, Gravity.BOTTOM or Gravity.END,0, 0)
            }else{
                this.showAsDropDown(view)
            }
        }

        if(showRefresh) {
            content.findViewById<LinearLayout>(R.id.btn_refresh).setVisible()
        } else {
            content.findViewById<LinearLayout>(R.id.btn_refresh).setGone()
        }

        if(visitor) {
            content.findViewById<LinearLayout>(R.id.btn_switch_wallet).setGone()
            content.findViewById<LinearLayout>(R.id.btn_switch_network).setGone()
            content.findViewById<LinearLayout>(R.id.scan_qr_code).setGone()
            content.findViewById<LinearLayout>(R.id.btn_copy_wallet_address).setGone()
        } else {
            content.findViewById<LinearLayout>(R.id.btn_switch_wallet).setVisible()
            content.findViewById<LinearLayout>(R.id.btn_switch_network).setVisible()
            content.findViewById<LinearLayout>(R.id.scan_qr_code).setVisible()
            content.findViewById<LinearLayout>(R.id.btn_copy_wallet_address).setVisible()
        }

        content.findViewById<TextView>(R.id.tv_network).text = SPUtils.getInstance().getString(SP_RPC_NETWORK_NAME, IOTEX_NAME)
        content.findViewById<LinearLayout>(R.id.btn_copy_wallet_address).setOnClickListener {
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_DAPP_WEB_COPY_WALLET_ADDRESS)
            dialog?.dismiss()
            val address = WalletHelper.formatWalletAddress(Constant.currentWallet?.address ?: "")
            ClipboardUtils.copyText(address)
            if (Constant.currentWallet?.isAAWallet() == true) {
                showAAReceiveCopy()
            } else {
                ToastUtils.showShort(R.string.copy_success)
            }
        }
        content.findViewById<LinearLayout>(R.id.btn_switch_wallet).setOnClickListener {
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_DAPP_WEB_CLICK_SWITCH_WALLET)
            dialog?.dismiss()
            SwitchWalletDialog().apply {
                onSwitchWallet = { oldAddress, address ->
                    if (url.contains(oldAddress, true)) {
                        val newUrl = url.replace(oldAddress, address, true)
                        webView.loadUrl(newUrl)
                    }
                }
            }.show(context.supportFragmentManager, System.currentTimeMillis().toString())
        }
        content.findViewById<LinearLayout>(R.id.btn_switch_network).setOnClickListener {
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_DAPP_WEB_CLICK_SWITCH_NETWORK)
            dialog?.dismiss()
            NetworkSwitchDialog().apply {
                onItemClick = {
                    val bundle = Bundle()
                    bundle.putString(FireBaseUtil.NAME, it.shortName.lowercase())
                    FireBaseUtil.logFireBase(FireBaseEvent.ACTION_BROWSER_NETWORK, bundle)
                }
            }.show(context.supportFragmentManager, System.currentTimeMillis().toString())
        }
        content.findViewById<LinearLayout>(R.id.scan_qr_code).setOnClickListener {
            webView.isDrawingCacheEnabled = true
            webView.buildDrawingCache()
            val snapShot: Picture = webView.capturePicture()
            if (snapShot.width <= 0 || snapShot.height <= 0) {
                Utils.getApp().getString(R.string.failed_qr_code).toast()
                return@setOnClickListener
            }
            val bitmap = Bitmap.createBitmap(snapShot.width, snapShot.height, Bitmap.Config.ARGB_8888)
            val canvas = Canvas(bitmap)
            snapShot.draw(canvas)
            canvas.save()
            canvas.restore()
            webView.destroyDrawingCache()
            val text = QRCodeUtil.syncDecodeQRCode(bitmap)
            if (text != null) {
                scanResult?.invoke(text)
                dialog?.dismiss()
            } else {
                Utils.getApp().getString(R.string.failed_qr_code).toast()
            }
        }
        content.findViewById<LinearLayout>(R.id.btn_copy_url).setOnClickListener {
            dialog?.dismiss()
            ClipboardUtils.copyText(url)
            context.toast(R.string.copy_success)
        }
        content.findViewById<LinearLayout>(R.id.btn_refresh).setOnClickListener {
            dialog?.dismiss()
            refreshCallback?.invoke()
        }
        content.findViewById<LinearLayout>(R.id.btn_open_browser).setOnClickListener {
            try {
                FireBaseUtil.logFireBase(FireBaseEvent.ACTION_DAPP_WEB_CLICK_OPEN_IN_BROWSER)
                dialog?.dismiss()
                val intent = Intent()
                intent.action = Intent.ACTION_VIEW
                intent.data = Uri.parse(url)
                context.startActivity(Intent.createChooser(intent, ""))
            } catch (e: Exception) {
                e.printStackTrace()
            }

        }
    }
    fun dismiss(){
        dialog?.dismiss()
    }
}