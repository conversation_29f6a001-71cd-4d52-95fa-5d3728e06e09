package io.iotex.iopay.ui

import android.content.Context
import android.view.Gravity
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.PopupWindow
import androidx.appcompat.widget.AppCompatButton
import io.iotex.iopay.R

class RemoveTokenDialog(
    private val context: Context,
    private val cancelAction: () -> Unit,
    private val confirmAction: () -> Unit
) {

    private var dialog: PopupWindow? = null

    fun show() {
        val content = LayoutInflater.from(context).inflate(R.layout.dialog_remove_token_confirm, null)
        dialog = PopupWindow(content, ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT).apply {
            this.showAtLocation(content, Gravity.CENTER, 0, 0)
        }
        content.findViewById<AppCompatButton>(R.id.btn_cancel).setOnClickListener {
            cancelAction()
            dialog?.dismiss()
        }
        content.findViewById<ImageView>(R.id.iv_close).setOnClickListener {
            cancelAction()
            dialog?.dismiss()
        }

        content.findViewById<AppCompatButton>(R.id.btn_confirm).setOnClickListener {
            confirmAction()
            dialog?.dismiss()
        }
    }
}