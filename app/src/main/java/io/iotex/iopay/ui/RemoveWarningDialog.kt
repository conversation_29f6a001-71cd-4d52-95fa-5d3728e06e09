package io.iotex.iopay.ui

import io.iotex.base.bindbase.BaseBindDialog
import io.iotex.iopay.R
import io.iotex.iopay.base.BaseLaunchVM
import io.iotex.iopay.databinding.DialogRemoveWarningBinding

class RemoveWarningDialog(val content:String): BaseBindDialog<BaseLaunchVM, DialogRemoveWarningBinding>(R.layout.dialog_remove_warning) {

    var onConfirmClick:(()->Unit)? = null
    override fun initView() {
        mBinding.tvContent.text = content
        mBinding.ivClose.setOnClickListener {
            dismiss()
        }
        mBinding.mBtnCancel.setOnClickListener {
            dismiss()
        }
        mBinding.mBtnConfirm.setOnClickListener {
            onConfirmClick?.invoke()
        }
    }
}