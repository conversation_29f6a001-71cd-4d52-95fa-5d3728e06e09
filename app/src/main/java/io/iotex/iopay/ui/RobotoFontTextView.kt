package io.iotex.iopay.ui

import android.content.Context
import android.graphics.Typeface
import android.util.AttributeSet
import androidx.appcompat.widget.AppCompatTextView
import io.iotex.iopay.R

class RobotoFontTextView : AppCompatTextView {
    constructor(context: Context) : this(context,null)

    constructor(context: Context, attrs: AttributeSet?) : this(context, attrs,0)

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
            context,
            attrs,
            defStyleAttr
    ){
        val a = context.obtainStyledAttributes(attrs, R.styleable.RobotoFontTextView, defStyleAttr, 0)
        val textStyle = a.getInt(R.styleable.RobotoFontTextView_customTextStyle,0)
        a?.recycle()
        if(textStyle==0){
            typeface = Typeface.createFromAsset(context.assets, "fonts/Roboto-Regular.ttf")
        }
        if(textStyle==1){
            typeface = Typeface.createFromAsset(context.assets, "fonts/Roboto-Medium.ttf")
        }
        if(textStyle==2){
            typeface = Typeface.createFromAsset(context.assets, "fonts/Roboto-Bold.ttf")
        }
    }

}