package io.iotex.iopay.ui

import com.blankj.utilcode.util.SPUtils
import io.iotex.base.BaseViewModel
import io.iotex.base.bindbase.BaseBindDialog
import io.iotex.iopay.R
import io.iotex.iopay.databinding.DialogGuideStepBinding
import io.iotex.iopay.util.FireBaseEvent
import io.iotex.iopay.util.FireBaseUtil
import io.iotex.iopay.util.SPConstant

class WalletGuideStepDialog @JvmOverloads constructor(
    private val cancelAction: (() -> Unit)? = null,
    private val confirmAction: (() -> Unit)? = null
):BaseBindDialog<BaseViewModel,DialogGuideStepBinding>(R.layout.dialog_guide_step) {

    override fun initView() {
        mBinding.ivClose.setOnClickListener {
            SPUtils.getInstance().put(SPConstant.SP_GUIDE_STEP, true)
            cancelAction?.invoke()
            dismiss()
        }
        mBinding.btnCancel.setOnClickListener {
            SPUtils.getInstance().put(SPConstant.SP_GUIDE_STEP, true)
            cancelAction?.invoke()
            dismiss()
        }

        mBinding.btnConfirm.setOnClickListener {
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_NEWER_GUIDE_CLICK_START)
            confirmAction?.invoke()
            dismiss()
        }
    }
}