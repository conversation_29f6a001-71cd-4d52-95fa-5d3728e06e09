package io.iotex.iopay.ui.binder

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.blankj.utilcode.util.ClipboardUtils
import com.blankj.utilcode.util.Utils
import com.drakeet.multitype.ItemViewBinder
import com.machinefi.w3bstream.utils.extension.ellipsis
import io.iotex.iopay.R
import io.iotex.iopay.api.EmailAccount
import io.iotex.iopay.util.TokenUtil
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.util.extension.loadSvgOrImage
import io.iotex.iopay.util.extension.toast

class AAWalletItemBinder : ItemViewBinder<EmailAccount, AAWalletItemBinder.VH>() {

    private var mItemClickCallback: ((EmailAccount) -> Unit)? = null

    override fun onCreateViewHolder(inflater: LayoutInflater, parent: ViewGroup): VH {
        val view = inflater.inflate(R.layout.item_recover_aa_wallet, parent, false)
        return VH(view)
    }

    override fun onBindViewHolder(holder: VH, item: EmailAccount) {
        holder.tvAddress.text = item.account.ellipsis(6, 8)
        holder.itemView.setOnClickListener {
            mItemClickCallback?.invoke(item)
        }
        holder.tvExists.isVisible = item.exists
        if (item.exists) {
            holder.llContent.alpha = 0.5f
        } else {
            holder.llContent.alpha = 1f
        }
        holder.tvBalance.text = "${item.balance} ${TokenUtil.getNativeCurrencySymbol()}"
        val avatar = WalletHelper.getAddressAvatar(item.account)
        holder.walletAvatar.loadSvgOrImage(avatar, R.drawable.icon_wallet_default)
        holder.ivCopy.setOnClickListener {
            ClipboardUtils.copyText(item.account)
            Utils.getApp().getString(R.string.success).toast()
        }
    }

    fun setItemClickCallback(l: (EmailAccount) -> Unit) {
        this.mItemClickCallback = l
    }

    inner class VH(view: View) : RecyclerView.ViewHolder(view) {
        val tvAddress = view.findViewById<TextView>(R.id.tvAddress)
        val tvBalance = view.findViewById<TextView>(R.id.tvBalance)
        val walletAvatar = view.findViewById<ImageView>(R.id.walletAvatar)
        val ivCopy = view.findViewById<ImageView>(R.id.ivCopy)
        val tvExists = view.findViewById<TextView>(R.id.tvExists)
        val llContent = view.findViewById<LinearLayout>(R.id.llContent)
    }
}