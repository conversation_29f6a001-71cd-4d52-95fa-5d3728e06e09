package io.iotex.iopay.ui.binder

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.annotation.IntDef
import androidx.databinding.DataBindingUtil
import com.blankj.utilcode.util.ColorUtils
import com.blankj.utilcode.util.Utils
import com.drakeet.multitype.ItemViewBinder
import io.iotex.base.bindbase.BaseBindVH
import io.iotex.iopay.R
import io.iotex.iopay.databinding.ItemActivateAaProcessBinding
import io.iotex.iopay.util.extension.loadResources
import io.iotex.iopay.util.extension.setGone
import io.iotex.iopay.util.extension.setVisible

class ActivateAAProcessBinder :
    ItemViewBinder<ActivateAAProcessWrapper, BaseBindVH<ItemActivateAaProcessBinding>>() {

    var onTryAgainListener: ((Int) -> Unit)? = null
    override fun onCreateViewHolder(
        inflater: LayoutInflater,
        parent: View<PERSON>roup
    ): BaseBindVH<ItemActivateAaProcessBinding> {
        val bind = DataBindingUtil.inflate<ItemActivateAaProcessBinding>(
            inflater,
            R.layout.item_activate_aa_process,
            parent,
            false
        )
        return BaseBindVH(bind)
    }

    override fun onBindViewHolder(
        holder: BaseBindVH<ItemActivateAaProcessBinding>,
        item: ActivateAAProcessWrapper
    ) {
        holder.bind.tvTitle.text = item.title
        when (item.step) {
            ActivateAAStep_VERIFY_EMAIL -> {
                holder.bind.tvStatus.text = Utils.getApp().getString(R.string.verified_failed)
            }
            ActivateAAStep_APPLY_GAS -> {
                holder.bind.tvStatus.text = Utils.getApp().getString(R.string.applied_failed)
            }
            ActivateAAStep_CREATE_ACCOUNT -> {
                holder.bind.tvStatus.text = Utils.getApp().getString(R.string.create_failed)
            }
            ActivateAAStep_BIND_EMAIL -> {
                holder.bind.tvStatus.text = Utils.getApp().getString(R.string.bind_failed)
            }
        }

        when (item.status) {
            ActivateAAStatus_SUCCESS -> {
                holder.bind.progress.setGone()
                holder.bind.ivStatus.setVisible()
                holder.bind.ivStatus.loadResources(R.drawable.icon_aa_ready)
                holder.bind.tvTitle.setTextColor(ColorUtils.getColor(R.color.color_617AFF))
                holder.bind.llTryAgain.setGone()
            }
            ActivateAAStatus_FAILED -> {
                holder.bind.progress.setGone()
                holder.bind.ivStatus.setVisible()
                holder.bind.ivStatus.loadResources(R.drawable.icon_aa_error)
                holder.bind.tvTitle.setTextColor(ColorUtils.getColor(R.color.gray_B4B8CB))
                holder.bind.llTryAgain.setVisible()
            }
            ActivateAAStatus_LOADING -> {
                holder.bind.ivStatus.setGone()
                holder.bind.progress.setVisible()
                holder.bind.tvTitle.setTextColor(ColorUtils.getColor(R.color.gray_B4B8CB))
                holder.bind.llTryAgain.setGone()
            }
        }

        holder.bind.tvTryAgain.setOnClickListener {
            onTryAgainListener?.invoke(item.step)
        }
    }
}

data class ActivateAAProcessWrapper(
    val title: String,
    @ActivateAAStep val step: Int,
    @ActivateAAStatus var status: Int,
)

const val ActivateAAStep_VERIFY_EMAIL = 0
const val ActivateAAStep_APPLY_GAS = 1
const val ActivateAAStep_CREATE_ACCOUNT = 2
const val ActivateAAStep_BIND_EMAIL = 3
@Target(AnnotationTarget.VALUE_PARAMETER, AnnotationTarget.FIELD, AnnotationTarget.FUNCTION)
@MustBeDocumented
@IntDef(
    ActivateAAStep_VERIFY_EMAIL,
    ActivateAAStep_APPLY_GAS,
    ActivateAAStep_CREATE_ACCOUNT,
    ActivateAAStep_BIND_EMAIL,
)
@Retention(AnnotationRetention.SOURCE)
annotation
class ActivateAAStep


const val ActivateAAStatus_SUCCESS = 0
const val ActivateAAStatus_FAILED = 1
const val ActivateAAStatus_LOADING = 2
@Target(AnnotationTarget.VALUE_PARAMETER, AnnotationTarget.FIELD, AnnotationTarget.FUNCTION)
@MustBeDocumented
@IntDef(
    ActivateAAStatus_SUCCESS,
    ActivateAAStatus_FAILED,
    ActivateAAStatus_LOADING
)
@Retention(AnnotationRetention.SOURCE)
annotation
class ActivateAAStatus