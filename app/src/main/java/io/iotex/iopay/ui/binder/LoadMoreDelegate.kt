package io.iotex.iopay.ui.binder

import android.view.ViewConfiguration
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.drakeet.multitype.MultiTypeAdapter
import io.iotex.iopay.data.db.ActionRecordEntry
import io.iotex.iopay.util.extension.removeItem
import io.iotex.iopay.util.extension.updateItem

class LoadMoreDelegate(val mAdapter: MultiTypeAdapter, val mListener: OnLoadMoreListener) {

    private lateinit var mScrollListener: ScrollListener
    var loading = false
    private var scrolling = false
    var noMore = false

    fun attach(recyclerView: RecyclerView) {
        val linearLayoutManager = recyclerView.layoutManager as LinearLayoutManager
        mScrollListener = ScrollListener(linearLayoutManager)
        recyclerView.addOnScrollListener(mScrollListener)
    }

    fun loadMoreComplete() {
        loading = false
    }

    fun setNoMoreData(noMore: Boolean) {
        this.noMore = noMore
    }

    fun refresh(items: List<Any>) {
        mAdapter.items = items
        mAdapter.notifyDataSetChanged()
    }

    fun addData(items: List<Any>) {
        val list = mAdapter.items.toMutableList()
        if (list.isNotEmpty() && list.last()::class.java.name == LoadMoreEntry::class.java.name) {
            list.removeLast()
            list.addAll(items)
            mAdapter.items = list
            mAdapter.notifyItemRemoved(list.size)
        } else {
            list.addAll(items)
        }
        mAdapter.items = list
        mAdapter.notifyItemRangeInserted(list.size - items.size, items.size)
        mAdapter.notifyItemRangeChanged(list.size - items.size, items.size)
    }

    fun addDataInHeader(item: Any) {
        val list = mAdapter.items.toMutableList()
        if (list.isNotEmpty()) {
            list.add(0, item)
        } else {
            list.add(item)
        }
        mAdapter.items = list
        mAdapter.notifyDataSetChanged()
    }

    fun updateItem(item: ActionRecordEntry) {
        mAdapter.updateItem(item) {
            it.hash == item.hash
        }
    }

    fun deleteItem(item: ActionRecordEntry) {
        mAdapter.removeItem<ActionRecordEntry> {
            it.hash == item.hash
        }
    }

    inner class ScrollListener(val linearLayoutManager: LinearLayoutManager) : RecyclerView.OnScrollListener() {

        override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
            super.onScrollStateChanged(recyclerView, newState)
            if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                scrolling = false
            }
        }

        override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
            if (dy < ViewConfiguration.get(recyclerView.context).scaledTouchSlop) {
                return
            }
            val totalNum: Int = linearLayoutManager.itemCount
            val lastVisiblePosition = linearLayoutManager.findLastVisibleItemPosition()
            if (lastVisiblePosition >= totalNum - 2) {
                loadMore()
            }
        }
    }

    fun loadMore(){
        if (!loading && !scrolling && !noMore) {
            loading = true
            scrolling = true
            val list = mAdapter.items.toMutableList()
            list.add(LoadMoreEntry())
            mAdapter.items = list
            mAdapter.notifyItemInserted(list.size - 1)
            mListener?.onLoadMore()
        }
    }
}

interface OnLoadMoreListener {
    fun onLoadMore()
}
