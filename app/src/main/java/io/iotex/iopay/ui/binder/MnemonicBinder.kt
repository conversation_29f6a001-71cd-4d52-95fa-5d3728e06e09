package io.iotex.iopay.ui.binder

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.blankj.utilcode.util.ColorUtils
import com.drakeet.multitype.ItemViewBinder
import io.iotex.iopay.R
import io.iotex.iopay.util.extension.setGone
import io.iotex.iopay.util.extension.setVisible
import java.io.Serializable

class MnemonicBinder(
    private val mMnemonicList: List<String>? = null,
    private val showIndex: Boolean = false,
    private val indexAlpha: Float = 0.6f
) : ItemViewBinder<MnemonicItemWrapper, MnemonicBinder.VH>() {

    private var mOnDeleteListener: ((String) -> Unit)? = null
    private var mOnSelectedListener: ((String) -> Unit)? = null
    private var mOnSelectStatusListener: ((Boolean) -> Unit)? = null
    private var lastSelectedPhrase: String? = null

    override fun onCreateViewHolder(
        inflater: LayoutInflater,
        parent: ViewGroup
    ): MnemonicBinder.VH {
        val view = inflater.inflate(R.layout.item_mnemonic, parent, false)
        return VH(view)
    }

    override fun onBindViewHolder(holder: MnemonicBinder.VH, item: MnemonicItemWrapper) {
        holder.mTvMnemonic.text = item.phrase
        holder.tvPosition.alpha = indexAlpha
        holder.tvPosition.isVisible = showIndex
        if (showIndex) {
            holder.llContent.setBackgroundResource(0)
        } else {
            holder.llContent.setBackgroundResource(R.drawable.shape_card_back)
            holder.mTvMnemonic.layoutParams.width = ViewGroup.LayoutParams.MATCH_PARENT
        }
        holder.tvPosition.text = (holder.adapterPosition + 1).toString() + "."

        if (mOnDeleteListener != null) {
            val mnemonic = mMnemonicList?.get(holder.layoutPosition)
            val index = adapterItems.indexOfLast {
                !(it as MnemonicItemWrapper).phrase.isNullOrBlank()
            }
            if (mnemonic != item.phrase) {
                holder.mTvMnemonic.setTextColor(ColorUtils.getColor(R.color.color_E53737))
            } else {
                holder.mTvMnemonic.setTextColor(ContextCompat.getColor(holder.mTvMnemonic.context, R.color.color_title))
            }
            if (holder.layoutPosition == index && mnemonic != item.phrase) {
                holder.mIvDelete.setVisible()
            } else {
                holder.mIvDelete.setGone()
            }
        }

        if (mOnSelectedListener != null) {
            if (item.isSelected) {
                holder.llContent.alpha = 0.5f
                holder.itemView.isEnabled = false
            } else {
                holder.llContent.alpha = 1f
                holder.itemView.isEnabled = true
                if (lastSelectedPhrase != null) {
                    val selectedCount = adapterItems.count { mnemonicWrapper ->
                        (mnemonicWrapper as MnemonicItemWrapper).isSelected
                    }
                    mOnSelectStatusListener?.invoke(true)
                    lastSelectedPhrase = if (selectedCount > 0) {
                        mMnemonicList?.get(selectedCount - 1)
                    } else {
                        null
                    }
                }
            }
        }

        holder.mIvDelete.setOnClickListener {
            item.phrase?.let { phrase ->
                item.phrase = null
                adapter.notifyItemChanged(holder.layoutPosition)
                if (holder.layoutPosition - 1 > -1) {
                    adapter.notifyItemChanged(holder.layoutPosition - 1)
                }
                mOnDeleteListener?.invoke(phrase)
            }
        }

        holder.itemView.setOnClickListener {
            if (mOnSelectedListener == null) return@setOnClickListener
            val selectedCount = adapterItems.count { mnemonicWrapper ->
                (mnemonicWrapper as MnemonicItemWrapper).isSelected
            }
            var lastStandardPhrase: String? = null
            if (selectedCount - 1 > -1) {
                lastStandardPhrase = mMnemonicList?.get(selectedCount - 1)
            }
            if (lastSelectedPhrase != lastStandardPhrase) return@setOnClickListener
            if (mMnemonicList != null && selectedCount < mMnemonicList.size && item.phrase != mMnemonicList[selectedCount]) {
                mOnSelectStatusListener?.invoke(false)
            }
            lastSelectedPhrase = item.phrase
            item.phrase?.let { phrase ->
                item.isSelected = true
                adapter.notifyItemChanged(holder.layoutPosition)
                mOnSelectedListener?.invoke(phrase)
            }
        }
    }

    fun setOnDeleteListener(l: (String) -> Unit) {
        this.mOnDeleteListener = l
    }

    fun setOnSelectedListener(l: (String) -> Unit) {
        this.mOnSelectedListener = l
    }

    fun setOnSelectStatusListener(l: (Boolean) -> Unit) {
        this.mOnSelectStatusListener = l
    }

    inner class VH(view: View) : RecyclerView.ViewHolder(view) {
        val mTvMnemonic = view.findViewById<TextView>(R.id.mTvMnemonic)
        val tvPosition = view.findViewById<TextView>(R.id.tvPosition)
        val mIvDelete = view.findViewById<ImageView>(R.id.mIvDelete)
        val llContent = view.findViewById<LinearLayout>(R.id.llContent)
    }

}

data class MnemonicItemWrapper(
    var phrase: String? = null,
    var isSelected: Boolean = false
): Serializable