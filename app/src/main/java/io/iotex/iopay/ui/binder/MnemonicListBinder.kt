package io.iotex.iopay.ui.binder

import android.app.Activity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.EditorInfo
import android.widget.EditText
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.widget.addTextChangedListener
import androidx.recyclerview.widget.RecyclerView
import com.blankj.utilcode.util.KeyboardUtils
import com.blankj.utilcode.util.Utils
import com.drakeet.multitype.ItemViewBinder
import io.iotex.iopay.R
import io.iotex.iopay.data.db.AppDatabase
import io.iotex.iopay.data.db.Mnemonic
import io.iotex.iopay.util.FireBaseUtil
import io.iotex.iopay.util.extension.*
import org.jetbrains.anko.doAsync
import org.jetbrains.anko.uiThread

class MnemonicListBinder : ItemViewBinder<Mnemonic, MnemonicListBinder.VH>() {

    private var mViewMnemonicCb: ((Mnemonic) -> Unit)? = null
    private var mOnDeleteCb: ((Mnemonic) -> Unit)? = null
    var onFistItemLoad: ((View) -> Unit)? = null

    private var mLastPos = -1

    override fun onCreateViewHolder(inflater: LayoutInflater, parent: ViewGroup): VH {
        val view = inflater.inflate(R.layout.item_mnemonic_list, parent, false)
        return VH(view)
    }

    override fun onBindViewHolder(holder: VH, item: Mnemonic) {
        if(holder.adapterPosition == 0){
            onFistItemLoad?.invoke(holder.mTvView)
        }
    }

    override fun onBindViewHolder(holder: VH, item: Mnemonic, payloads: List<Any>) {
        super.onBindViewHolder(holder, item, payloads)
        holder.mTvMnemonicName.text = item.name
        holder.llEditContainer.setGone()
        holder.mLlNameContainer.setVisible()
        if (payloads.isEmpty()) {
            if (item.avatar.isSVG()) {
                holder.mIvMnemonic?.loadSvg(item.avatar, R.drawable.icon_wallet_default)
            } else {
                holder.mIvMnemonic?.loadImage(item.avatar, R.drawable.icon_wallet_default)
            }
            holder.mTvView.setOnClickListener {
                mViewMnemonicCb?.invoke(item)
                FireBaseUtil.logFireBase("action_settings_manage_wallet_recovery_reveal")
            }
            holder.mLlDelete.setOnClickListener {
                mOnDeleteCb?.invoke(item)
            }
            holder.mIvEdit.setOnClickListener {
                holder.llEditContainer.setVisible()
                holder.mLlNameContainer.setGone()
                holder.mEtMnemonicName.setText(item.name)
                KeyboardUtils.showSoftInput(holder.mEtMnemonicName)
                if (mLastPos != holder.layoutPosition && mLastPos != -1) {
                    adapter.notifyItemChanged(mLastPos)
                }
                mLastPos = holder.layoutPosition
                FireBaseUtil.logFireBase("action_settings_manage_wallet_recovery_edit")
            }
            holder.mEtMnemonicName.setOnFocusChangeListener { v, hasFocus ->
                if (!hasFocus) {
                    renameMnemonic(item, holder)
                }
            }
            holder.mEtMnemonicName.setOnEditorActionListener { v, actionId, event ->
                if (actionId == EditorInfo.IME_ACTION_DONE) {
                    renameMnemonic(item, holder)
                }
                return@setOnEditorActionListener true
            }
            holder.mEtMnemonicName.addTextChangedListener {text->
                holder.tvCount.text = "${text?.length}/20"
            }
            KeyboardUtils.registerSoftInputChangedListener(holder.itemView.context as Activity) {
                if (it <= 0) {
                    holder.mEtMnemonicName.clearFocus()
                }
            }
        }
    }

    override fun onViewRecycled(holder: VH) {
        super.onViewRecycled(holder)
        KeyboardUtils.unregisterSoftInputChangedListener((holder.itemView.context as Activity).window)
    }

    private fun renameMnemonic(mnemonic: Mnemonic, holder: VH) {
        val name = holder.mEtMnemonicName.text.toString().trim()
        if (name.isBlank()) {
            Utils.getApp().getString(R.string.validate_not_null).toast()
            return
        }
        mnemonic.name = name
        doAsync {
            AppDatabase.getInstance(Utils.getApp()).mnemonicDao().update(mnemonic)
            uiThread {
                holder.llEditContainer.setGone()
                holder.mLlNameContainer.setVisible()
                adapter.notifyItemChanged(holder.layoutPosition, "rename")
                KeyboardUtils.hideSoftInput(holder.mEtMnemonicName)
            }
        }
    }

    fun setViewMnemonicListener(l: (Mnemonic) -> Unit) {
        this.mViewMnemonicCb = l
    }

    fun setOnDeleteListener(l: (Mnemonic) -> Unit) {
        this.mOnDeleteCb = l
    }

    inner class VH(view: View) : RecyclerView.ViewHolder(view) {
        val mLlDelete = view.findViewById<View>(R.id.mLlDelete)
        val mTvMnemonicName = view.findViewById<TextView>(R.id.mTvMnemonicName)
        val mTvView = view.findViewById<TextView>(R.id.mTvView)
        val mIvMnemonic = view.findViewById<ImageView>(R.id.mIvMnemonic)
        val mLlNameContainer = view.findViewById<LinearLayout>(R.id.mLlNameContainer)
        val llEditContainer = view.findViewById<LinearLayout>(R.id.llEditContainer)
        val mIvEdit = view.findViewById<ImageView>(R.id.mIvEdit)
        val mEtMnemonicName = view.findViewById<EditText>(R.id.mEtMnemonicName)
        val tvCount = view.findViewById<TextView>(R.id.tvCount)
    }
}