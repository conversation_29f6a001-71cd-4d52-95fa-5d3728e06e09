package io.iotex.iopay.ui.binder

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.blankj.utilcode.util.Utils
import com.daimajia.swipe.SwipeLayout
import com.drakeet.multitype.ItemViewBinder
import io.iotex.iopay.R
import io.iotex.iopay.data.UserStore
import io.iotex.iopay.data.db.AppDatabase
import io.iotex.iopay.data.db.RPCNetwork
import io.iotex.iopay.network.AddOrEditNetworkActivity
import io.iotex.iopay.util.FireBaseEvent
import io.iotex.iopay.util.FireBaseUtil
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.util.extension.loadSvgOrImage
import io.iotex.iopay.util.extension.setGone
import io.iotex.iopay.util.extension.setVisible
import io.iotex.iopay.util.extension.viewScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class NetworkListBinder: ItemViewBinder<RPCNetwork, NetworkListBinder.VH>() {

    var mItemClickCallback: ((RPCNetwork) -> Unit)? = null
    var mItemRemoveListener: ((RPCNetwork) -> Unit)? = null

    override fun onCreateViewHolder(inflater: LayoutInflater, parent: ViewGroup): VH {
        val view = inflater.inflate(R.layout.item_network_list, parent, false)
        return VH(view)
    }

    override fun onBindViewHolder(holder: VH, item: RPCNetwork) {
        holder.slItem.isSwipeEnabled = !item.immutable
        holder.tvName.text = item.name
        holder.tvChain.text =
            Utils.getApp().getString(R.string.chain_id_for, item.chainId.toString())
        holder.itemView.viewScope.launch {
            val rpcNode = withContext(Dispatchers.IO) {
                AppDatabase.getInstance(Utils.getApp()).rpcNetworkNode()
                    .queryRPCNodeActivated(item.chainId)
            }
            holder.tvRpc.text = Utils.getApp().getString(R.string.rpc_url_for, rpcNode?.rpcUrl)
        }
        holder.tvChain.visibility = if (!WalletHelper.isBitcoinNetwork(item.chainId) && !WalletHelper.isSolanaNetwork(item.chainId)) View.VISIBLE else View.GONE

        showAction(holder, item)

        if (item.chainId == WalletHelper.getCurChainId() && !UserStore.getAllNetwork()) {
            holder.llItem.setBackgroundResource(R.drawable.shape_card_back_stroke_gradient)
            if (item.immutable) {
                holder.ivEdit.setGone()
            } else {
                holder.ivEdit.setVisible()
            }
        } else {
            holder.llItem.setBackgroundResource(R.drawable.shape_card_back)
            holder.ivEdit.setGone()
        }

        holder.ivLogo.loadSvgOrImage(item.logo, R.drawable.ic_network_default)
        holder.llItem.setOnClickListener {
            WalletHelper.switchNetwork(item)
            mItemClickCallback?.invoke(item)
        }
        holder.ivEdit.setOnClickListener {
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_EDIT_NETWORK)
            AddOrEditNetworkActivity.startActivity(holder.ivEdit.context, AddOrEditNetworkActivity.ACTION_EDIT_NETWORK)
        }
        holder.llDelete.setOnClickListener {
            mItemRemoveListener?.invoke(item)
        }
    }

    private fun showAction(holder: VH, item: RPCNetwork){
        if (holder.layoutPosition == 0 && !UserStore.getAllNetwork()) {
            holder.tvNetType.setVisible()
            holder.tvNetType.setText(R.string.current_network)
        } else if (holder.layoutPosition == 1 && !UserStore.getAllNetwork()
            || (holder.layoutPosition == 0 && UserStore.getAllNetwork())) {
            holder.tvNetType.setVisible()
            holder.tvNetType.setText(R.string.default_network)
        } else if (!item.immutable && (adapterItems[holder.layoutPosition - 1] as RPCNetwork).immutable) {
            holder.tvNetType.setVisible()
            holder.tvNetType.setText(R.string.customized_network)
        } else {
            holder.tvNetType.setGone()
        }
        if (holder.layoutPosition == 0 && !item.immutable) {
            holder.llWarn.setVisible()
        } else {
            holder.llWarn.setGone()
        }
    }

    inner class VH(view: View) : RecyclerView.ViewHolder(view) {
        val tvNetType = view.findViewById<TextView>(R.id.tvNetType)
        val slItem = view.findViewById<SwipeLayout>(R.id.slItem)
        val llDelete = view.findViewById<LinearLayout>(R.id.llDelete)
        val llItem = view.findViewById<LinearLayout>(R.id.llItem)
        val ivLogo = view.findViewById<ImageView>(R.id.ivLogo)
        val tvName = view.findViewById<TextView>(R.id.tvName)
        val tvChain = view.findViewById<TextView>(R.id.tvChain)
        val tvRpc = view.findViewById<TextView>(R.id.tvRpc)
        val ivEdit = view.findViewById<ImageView>(R.id.ivEdit)
        val llWarn = view.findViewById<LinearLayout>(R.id.llWarn)
    }
}