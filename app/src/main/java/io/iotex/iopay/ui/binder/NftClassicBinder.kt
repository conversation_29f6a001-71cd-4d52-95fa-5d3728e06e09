package io.iotex.iopay.ui.binder

import android.graphics.drawable.Drawable
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.annotation.Keep
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.blankj.utilcode.util.ImageUtils
import com.blankj.utilcode.util.Utils
import com.bumptech.glide.Glide
import com.bumptech.glide.request.target.CustomTarget
import com.bumptech.glide.request.transition.Transition
import com.drakeet.multitype.ItemViewBinder
import io.iotex.iopay.R
import io.iotex.iopay.data.db.NftTokenEntry
import io.iotex.iopay.util.extension.*
import java.io.File

class NftClassicBinder : ItemViewBinder<NftClassicItem, NftClassicBinder.VH>() {

    private var mItemClickCallback: ((NftClassicItem) -> Unit)? = null

    override fun onCreateViewHolder(inflater: LayoutInflater, parent: ViewGroup): VH {
        val view = inflater.inflate(R.layout.item_nft_token_list, parent, false)
        return VH(view)
    }

    override fun onBindViewHolder(holder: VH, item: NftClassicItem) {
        holder.mTvTokenName.text = item.nft.name
        loadImage(holder.mIvTokenLogo, item.nft.tokenUrl)

        if (item.nft.sbt) {
            holder.ivSbt.setVisible()
        } else {
            holder.ivSbt.setGone()
        }

        holder.mTvTokenAmount.isVisible = item.amount != 0
        holder.mTvTokenAmount.text = Utils.getApp().getString(R.string.nft, item.amount.toString())
        holder.mTvTokenId.setGone()
        holder.itemView.setOnClickListener {
            mItemClickCallback?.invoke(item)
        }
    }

    private fun loadImage(imageView: ImageView, url: String) {
        if (url.isBlank()) return
        val target = object : CustomTarget<File>() {

            override fun onResourceReady(resource: File, transition: Transition<in File>?) {
                val type = ImageUtils.getImageType(resource)
                val sizeArray = ImageUtils.getSize(resource)
                val with = sizeArray[0]
                val height = sizeArray[1]
                if (with * 0.75 > height) {
                    imageView.scaleType = ImageView.ScaleType.FIT_CENTER
                } else {
                    imageView.scaleType = ImageView.ScaleType.CENTER_CROP
                }
                if (type == ImageUtils.ImageType.TYPE_GIF) {
                    Glide.with(imageView).asGif().load(resource)
                        .placeholder(R.drawable.icon_nft_default)
                        .error(R.drawable.icon_nft_error_default)
                        .into(imageView)
                } else {
                    Glide.with(imageView)
                        .load(resource)
                        .placeholder(R.drawable.icon_nft_default)
                        .error(R.drawable.icon_nft_error_default)
                        .into(imageView)
                }
            }

            override fun onLoadFailed(errorDrawable: Drawable?) {
                super.onLoadFailed(errorDrawable)
                imageView.setImageResource(R.drawable.icon_nft_default)
            }

            override fun onLoadCleared(placeholder: Drawable?) {
                imageView.setImageResource(R.drawable.icon_nft_default)
            }
        }
        imageView.setTag(R.id.imageId, target)
        Glide.with(imageView.context)
            .downloadOnly()
            .load(url)
            .placeholder(R.drawable.icon_nft_default)
            .error(R.drawable.icon_nft_error_default)
            .into(target)
    }

    override fun onViewRecycled(holder: VH) {
        super.onViewRecycled(holder)
        val target = holder.mIvTokenLogo.getTag(R.id.imageId)
        if (target is CustomTarget<*>) {
            Glide.with(holder.itemView.context).clear(target)
        }
    }

    fun setItemClickCallback(l: (NftClassicItem) -> Unit) {
        this.mItemClickCallback = l
    }

    inner class VH(view: View) : RecyclerView.ViewHolder(view) {
        val mIvTokenLogo = view.findViewById<ImageView>(R.id.mIvTokenLogo)
        val mTvTokenName = view.findViewById<TextView>(R.id.mTvTokenName)
        val ivSbt = view.findViewById<ImageView>(R.id.ivSbt)
        val mTvTokenId = view.findViewById<TextView>(R.id.mTvTokenId)
        val mTvTokenAmount = view.findViewById<TextView>(R.id.mTvTokenAmount)
    }
}

@Keep
data class NftClassicItem(
    val nft: NftTokenEntry,
    val amount: Int
)

