package io.iotex.iopay.ui.binder

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.daimajia.swipe.SwipeLayout
import com.drakeet.multitype.ItemViewBinder
import io.iotex.iopay.R
import io.iotex.iopay.data.db.NETWORK_GENERAL
import io.iotex.iopay.data.db.NETWORK_POOR
import io.iotex.iopay.data.db.NETWORK_SMOOTH
import io.iotex.iopay.data.db.RPCNetworkNode
import io.iotex.iopay.util.WalletHelper

class RpcNetworkNodeBinder : ItemViewBinder<RPCNetworkNode, RpcNetworkNodeBinder.VH>() {

    var onItemClick: ((RPCNetworkNode) -> Unit)? = null
    var onItemRemoveClick: ((RPCNetworkNode) -> Unit)? = null
    var onItemEditClick: ((RPCNetworkNode) -> Unit)? = null

    override fun onCreateViewHolder(inflater: LayoutInflater, parent: ViewGroup): VH {
        val view = inflater.inflate(R.layout.item_rpc_network_node, parent, false)
        return VH(view)
    }

    override fun onBindViewHolder(holder: VH, item: RPCNetworkNode) {
        holder.mSlItem.isSwipeEnabled = !(item.immutable || item.rpcUrl == WalletHelper.getCurRpcUrl())
        holder.ivEdit.isVisible = !(item.immutable || item.rpcUrl == WalletHelper.getCurRpcUrl())
        holder.mTvRpcUrl.text = item.rpcUrl
        if (item.rpcUrl == WalletHelper.getCurRpcUrl()) {
            holder.mLlRpcNode.setBackgroundResource(R.drawable.shape_card_back_stroke_gradient)
        } else {
            holder.mLlRpcNode.setBackgroundResource(R.drawable.shape_card_back)
        }

        when (item.rpcStatus) {
            NETWORK_SMOOTH -> {
                holder.mRpcStatus.setBackgroundResource(R.drawable.shape_circle_39c0a8)
            }
            NETWORK_GENERAL -> {
                holder.mRpcStatus.setBackgroundResource(R.drawable.shape_circle_ffdc61)
            }
            NETWORK_POOR -> {
                holder.mRpcStatus.setBackgroundResource(R.drawable.shape_circle_ff6161)
            }
        }

        holder.mLlRpcNode.setOnClickListener {
            if (item.rpcUrl == WalletHelper.getCurRpcUrl()) return@setOnClickListener
            onItemClick?.invoke(item)
        }

        holder.mLlDelete.setOnClickListener {
            onItemRemoveClick?.invoke(item)
        }

        holder.ivEdit.setOnClickListener {
            onItemEditClick?.invoke(item)
        }
    }

    inner class VH(view: View) : RecyclerView.ViewHolder(view) {
        val mSlItem = view.findViewById<SwipeLayout>(R.id.mSlItem)
        val mLlRpcNode = view.findViewById<LinearLayout>(R.id.mLlRpcNode)
        val mRpcStatus = view.findViewById<View>(R.id.mRpcStatus)
        val mTvRpcUrl = view.findViewById<TextView>(R.id.mTvRpcUrl)
        val mLlDelete = view.findViewById<LinearLayout>(R.id.mLlDelete)
        val ivEdit = view.findViewById<ImageView>(R.id.ivEdit)
    }

}


