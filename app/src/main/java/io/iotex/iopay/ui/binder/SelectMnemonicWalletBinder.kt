package io.iotex.iopay.ui.binder

import android.graphics.drawable.ColorDrawable
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.blankj.utilcode.util.ColorUtils
import com.blankj.utilcode.util.ResourceUtils
import com.blankj.utilcode.util.Utils
import com.drakeet.multitype.ItemViewBinder
import com.machinefi.w3bstream.utils.extension.ellipsis
import io.iotex.iopay.R
import io.iotex.iopay.data.UserStore
import io.iotex.iopay.data.db.AppDatabase
import io.iotex.iopay.data.db.Wallet
import io.iotex.iopay.util.TokenUtil
import io.iotex.iopay.util.extension.toast
import io.iotex.iopay.util.extension.viewScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class SelectMnemonicWalletBinder :
    ItemViewBinder<SelectMnemonicWalletWrapper, SelectMnemonicWalletBinder.VH>() {

    private var mOnClickListener: (() -> Unit)? = null
    private val mMaxCount = 10

    override fun onCreateViewHolder(inflater: LayoutInflater, parent: ViewGroup): VH {
        val view = inflater.inflate(R.layout.item_select_mnemonic_wallet, parent, false)
        return VH(view)
    }

    override fun onBindViewHolder(holder: VH, item: SelectMnemonicWalletWrapper) {
        renderItem(holder, item)
        holder.mTvAddress.text = item.wallet.getCurNetworkAddress().ellipsis(6, 8)
        holder.mTvBalance.text = formatBalance(item.wallet.curBalance)
        holder.itemView.setOnClickListener {
            if (!item.selected && getOnSelectedWallets().size >= mMaxCount) {
                Utils.getApp().getString(R.string.select_wallet_warning).toast()
                return@setOnClickListener
            }
            item.selected = !item.selected
            adapter.notifyItemChanged(holder.layoutPosition)
            mOnClickListener?.invoke()
        }
    }

    private fun renderItem(holder: VH, item: SelectMnemonicWalletWrapper) {
        holder.itemView.viewScope.launch {
            val walletList = withContext(Dispatchers.IO) {
                AppDatabase.getInstance(Utils.getApp()).walletDao()
                    .queryWalletByAddress(item.wallet.address)
            }
            holder.itemView.isEnabled = walletList == null
            if (walletList != null) {
                holder.mIvStatus.foreground = ResourceUtils.getDrawable(R.drawable.shape_4dffffff_r6)
            } else {
                holder.mIvStatus.foreground = ColorDrawable(ColorUtils.getColor(R.color.transparent))
            }

            if (walletList != null) {
                holder.itemView.setBackgroundResource(R.drawable.shape_99617aff_r6)
                holder.mIvStatus.setImageResource(R.drawable.ic_selected_white)
            } else if (item.selected) {
                holder.itemView.setBackgroundResource(R.drawable.shape_617aff_r6)
                holder.mIvStatus.setImageResource(R.drawable.ic_selected_white)
            } else {
                holder.itemView.setBackgroundResource(R.drawable.shape_card_back)
                holder.mIvStatus.setImageResource(R.drawable.ic_unselected_gray)
            }
        }
    }

    private fun formatBalance(bigBalance: String): String {
        val currency = TokenUtil.getNativeCurrencySymbol()
        val formatBalance = TokenUtil.weiToTokenBN(bigBalance,UserStore.getNetworkDecimals().toLong())
        val balance = TokenUtil.displayBalance(formatBalance)
        return "$balance $currency"
    }

    fun switchNetwork() {
        adapter.notifyDataSetChanged()
    }

    fun getOnSelectedWallets(): List<Wallet> {
        val wallets = adapterItems.filter {
            it is SelectMnemonicWalletWrapper && it.selected
        }.map {
            (it as SelectMnemonicWalletWrapper).wallet
        }
        return wallets
    }

    fun setOnClickListener(l: () -> Unit) {
        this.mOnClickListener = l
    }

    inner class VH(view: View) : RecyclerView.ViewHolder(view) {
        val mIvStatus = view.findViewById<ImageView>(R.id.mIvStatus)
        val mTvAddress = view.findViewById<TextView>(R.id.mTvAddress)
        val mTvBalance = view.findViewById<TextView>(R.id.mTvBalance)
    }
}

data class SelectMnemonicWalletWrapper(
    val wallet: Wallet,
    var selected: Boolean
)