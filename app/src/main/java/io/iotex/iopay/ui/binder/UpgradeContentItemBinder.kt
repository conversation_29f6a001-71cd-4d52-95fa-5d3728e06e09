package io.iotex.iopay.ui.binder

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.drakeet.multitype.ItemViewBinder
import io.iotex.iopay.R

class UpgradeContentItemBinder : ItemViewBinder<String, UpgradeContentItemBinder.VH>() {

    override fun onCreateViewHolder(inflater: LayoutInflater, parent: ViewGroup): VH {
        val view = inflater.inflate(R.layout.item_upgrade_content, parent, false)
        return VH(view)
    }

    override fun onBindViewHolder(holder: VH, item: String) {
        holder.tvContent.text = item
    }

    inner class VH(view: View) : RecyclerView.ViewHolder(view) {
        val tvContent = view.findViewById<TextView>(R.id.tvContent)
    }
}