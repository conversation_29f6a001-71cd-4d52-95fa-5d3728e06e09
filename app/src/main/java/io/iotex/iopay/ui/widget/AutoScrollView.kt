package io.iotex.iopay.ui.widget

import android.content.Context
import android.util.AttributeSet
import android.view.View
import android.view.animation.AnimationUtils
import android.widget.ViewFlipper
import androidx.core.view.children
import androidx.core.view.contains
import io.iotex.iopay.R

class AutoScrollView(context: Context, attrs: AttributeSet) : ViewFlipper(context, attrs) {
    private val interval = 5000

    init {
        val animIn = AnimationUtils.loadAnimation(context, R.anim.anim_marquee_in)
        val animOut = AnimationUtils.loadAnimation(context, R.anim.anim_marquee_out)
        inAnimation = animIn
        outAnimation = animOut
        flipInterval = interval
    }

    fun setViews(views: List<View>?) {
        if (views == null || views.isEmpty()) return
        removeAllViews()
        for (i in views.indices) {
            views[i].setOnClickListener {
                if (onItemClickListener != null) {
                    onItemClickListener!!.onItemClick(i, views[i])
                }
            }
            addView(views[i])
        }
        if (views.size > 1) {
            startFlipping()
        }
    }

    fun removeItem(item: View) {
        if (contains(item)) {
            removeView(item)
            if (childCount < 2) {
                stopFlipping()
            }
        }
    }

    fun removeDAppItem() {
        children.filter { it.tag != "banner" }.forEach {
            removeItem(it)
        }
    }

    fun addDAppItem(item: View) {
        var added = false
        children.filter { it.tag != "banner" && it.tag != item.tag}.forEach {
            removeItem(it)
        }
        children.forEach {
            if (it.tag == item.tag) {
                added = true
            }
        }
        if (!added) {
            addItem(item)
        }
    }

    fun removeAllBannerItem() {
        children.filter { it.tag == "banner" }.forEach {
            removeItem(it)
        }
    }

    fun addBannerItem(item: View) {
        item.tag = "banner"
        addItem(item)
    }

    private fun addItem(item: View) {
        if (!contains(item)) {
            addView(item)
            if (childCount > 1) {
                startFlipping()
            }
        }
    }

    private var onItemClickListener: OnItemClickListener? =
        null

    fun setOnItemClickListener(onItemClickListener: OnItemClickListener?) {
        this.onItemClickListener = onItemClickListener
    }

    interface OnItemClickListener {
        fun onItemClick(position: Int, view: View?)
    }
}