package io.iotex.iopay.ui.widget

import android.content.Context
import android.gesture.GestureOverlayView.ORIENTATION_HORIZONTAL
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.DashPathEffect
import android.graphics.Paint
import android.util.AttributeSet
import android.view.View
import io.iotex.iopay.R
import io.iotex.iopay.util.extension.dp2px


class DashLineView @JvmOverloads constructor(context: Context, attrs: AttributeSet? = null) :
    View(context) {

    private val color: Int
    private val orientation: Int
    private val mPaint: Paint

    init {
        val a = context.theme.obtainStyledAttributes(attrs, R.styleable.DashLineView, 0, 0)

        try {
            color = a.getColor(R.styleable.DashLineView_lineColor, Color.WHITE)
            orientation =
                a.getInt(R.styleable.DashLineView_lineOrientation, ORIENTATION_HORIZONTAL)
        } finally {
            a.recycle()
        }

        mPaint = Paint()
        mPaint.isAntiAlias = true
        mPaint.color = color
        mPaint.style = Paint.Style.STROKE
        mPaint.strokeWidth = 1.dp2px().toFloat()
        mPaint.setPathEffect(
            DashPathEffect(
                floatArrayOf(8f, 8f),
                0f
            )
        )
    }

    override fun onDraw(canvas: Canvas) {
        if (orientation == ORIENTATION_HORIZONTAL) {
            val center = height * 0.5f
            canvas.drawLine(0f, center, width.toFloat(), center, mPaint)
        } else {
            val center = width * 0.5f
            canvas.drawLine(center, 0f, center, height.toFloat(), mPaint)
        }
    }

}