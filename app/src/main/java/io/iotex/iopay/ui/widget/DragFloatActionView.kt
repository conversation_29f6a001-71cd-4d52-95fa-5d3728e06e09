package io.iotex.iopay.ui.widget

import android.animation.ObjectAnimator
import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.ViewGroup
import android.view.animation.DecelerateInterpolator
import android.widget.RelativeLayout

class DragFloatActionView : RelativeLayout {
    private var parentHeight = 0
    private var parentWidth = 0
    var runnable = Runnable { alpha = 0.5f }
    private var lastX = 0
    private var lastY = 0
    private var isDrag = false
    private var parent: ViewGroup? = null

    constructor(context: Context?) : super(context) {}
    constructor(context: Context?, attrs: AttributeSet?) : super(
        context,
        attrs
    ) {
    }

    constructor(
        context: Context?,
        attrs: AttributeSet?,
        defStyleAttr: Int
    ) : super(context, attrs, defStyleAttr) {
    }

    override fun onTouchEvent(event: MotionEvent): Boolean {
        val rawX = event.rawX.toInt()
        val rawY = event.rawY.toInt()
        when (event.action and MotionEvent.ACTION_MASK) {
            MotionEvent.ACTION_DOWN -> {
                this.alpha = 0.9f
                isPressed = true
                isDrag = false
                getParent().requestDisallowInterceptTouchEvent(true)
                lastX = rawX
                lastY = rawY
                if (getParent() != null) {
                    parent = getParent() as ViewGroup
                    parentHeight = parent!!.height
                    parentWidth = parent!!.width
                }
            }
            MotionEvent.ACTION_MOVE -> {
                if (parentHeight <= 0.2 || parentWidth <= 0.2) {
                    isDrag = false
                    return false
                } else {
                    isDrag = true
                }
                this.alpha = 0.9f
                val dx = rawX - lastX
                val dy = rawY - lastY
                val distance = Math.sqrt(dx * dx + dy * dy.toDouble()).toInt()
                if (distance == 0) {
                    isDrag = false
                    return false
                }
                var x = x + dx
                var y = y + dy
                x =
                    if (x < 0) 0f else if (x > parentWidth - width) (parentWidth - width).toFloat() else x
                y =
                    if (getY() < 0) 0f else if (getY() + height > parentHeight) (parentHeight - height).toFloat() else y
                setX(x)
                setY(y)
                lastX = rawX
                lastY = rawY
            }
            MotionEvent.ACTION_UP -> if (!isNotDrag) {
                isPressed = false
                moveHide(rawX)
            } else {
                myRunable()
            }
        }
        return !isNotDrag || super.onTouchEvent(event)
    }

    private val isNotDrag: Boolean
        private get() = !isDrag && (x == 0f || x == parentWidth - width.toFloat())

    private fun moveHide(rawX: Int) {
        if (rawX >= parentWidth / 2) {
            animate().setInterpolator(DecelerateInterpolator())
                .setDuration(500)
                .xBy(parentWidth - width - x)
                .start()
            myRunable()
        } else {
            val oa =
                ObjectAnimator.ofFloat(this, "x", x, 0f)
            oa.interpolator = DecelerateInterpolator()
            oa.duration = 500
            oa.start()
            myRunable()
        }
    }

    private fun myRunable() {
        handler.removeCallbacks(runnable)
        handler.postDelayed(runnable, 2000)
    }
}