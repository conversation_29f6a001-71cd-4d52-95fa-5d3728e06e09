package io.iotex.iopay.ui.widget

import android.content.Context
import android.text.TextWatcher
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.EditText
import android.widget.LinearLayout
import android.widget.TextView
import io.iotex.iopay.R

class EditTextSelectView(context: Context?, attrs: AttributeSet?, defStyleAttr: Int, defStyleRes: Int) : LinearLayout(context, attrs, defStyleAttr, defStyleRes) {
    private var mContext: Context? = null
    private var mView: View? = null
    private var edit: EditText? = null
    private var tv_error: TextView? = null
    private var mEtHint: String? = null
    private var mEtInputType = 0x00000001

    @JvmOverloads
    constructor(context: Context?, attrs: AttributeSet? = null, defStyleAttr: Int = 0) : this(
        context,
        attrs,
        defStyleAttr,
        0
    ) {
        mContext = context
        val a =
            mContext?.obtainStyledAttributes(attrs, R.styleable.EditTextSelectView, defStyleAttr, 0)
        mEtHint = a?.getString(R.styleable.EditTextSelectView_etHint)
        mEtInputType = a?.getInt(R.styleable.EditTextSelectView_inputType, 0x00000001) ?: 0x00000001
        a?.recycle()
        initView()
    }
    private fun initView() {
        mContext?.let { context ->
            mView =
                LayoutInflater.from(context).inflate(R.layout.view_edittext_selector, this, false)
            edit = mView?.findViewById(R.id.edit)
            tv_error = mView?.findViewById(R.id.tv_error)
            mView?.run {
                mEtHint?.let {
                    edit?.hint = it
                }
                edit?.inputType = mEtInputType
            }
            addView(mView)
        }
    }
    fun addTextChangedListener(watcher: TextWatcher) {
        mView?.run {
            edit?.addTextChangedListener(watcher)
        }
    }
    fun setErrorInfo(error: String) {
        mView?.run {
            tv_error?.text = error
        }
    }

    fun setRight() {
        mView?.run {
            tv_error?.text = ""
        }
    }
    fun text(): String {
        mView?.run {
            return edit?.text.toString()
        }
        return ""
    }

    fun setText(text: String?) {
        mView?.run {
            edit?.setText(text)
        }
    }

    fun editText(): EditText? {
        return edit
    }
}