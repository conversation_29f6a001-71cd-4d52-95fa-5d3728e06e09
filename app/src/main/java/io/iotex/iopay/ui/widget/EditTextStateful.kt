package io.iotex.iopay.ui.widget

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.drawable.Drawable
import android.util.AttributeSet
import android.util.TypedValue
import android.view.LayoutInflater
import android.view.View
import android.widget.EditText
import android.widget.LinearLayout
import android.widget.TextView
import io.iotex.iopay.R
import io.iotex.iopay.util.RxUtil
import io.iotex.iopay.util.extension.dp2px
import java.util.concurrent.TimeUnit

class EditTextStateful(context: Context, attrs: AttributeSet) :
    LinearLayout(context, attrs) {

    private val errorBackground: Drawable?
    private val inputBackground: Drawable?

    private val mEtInput: EditText
    private val mTvError: TextView

    init {
        val a = context.obtainStyledAttributes(attrs, R.styleable.EditTextStateful)
        val height = a.getDimension(R.styleable.EditTextStateful_height, 56F)
        val paddingStart =
            a.getDimension(R.styleable.EditTextStateful_paddingStart, 8.dp2px().toFloat())
        val paddingEnd =
            a.getDimension(R.styleable.EditTextStateful_paddingEnd, 8.dp2px().toFloat())
        val hint = a.getString(R.styleable.EditTextStateful_hint)
        val hintColor = a.getColor(
            R.styleable.EditTextStateful_hintColor,
            context.getColor(R.color.transparent_80_white)
        )
        val background = a.getDrawable(R.styleable.EditTextStateful_background)
        inputBackground = a.getDrawable(R.styleable.EditTextStateful_inputBackground)
        errorBackground = a.getDrawable(R.styleable.EditTextStateful_errorBackground)
        val textSize = a.getDimension(R.styleable.EditTextStateful_textSize, 16F)
        val textColor =
            a.getColor(R.styleable.EditTextStateful_textColor, context.getColor(com.machinefi.lockscreen.R.color.color_title))
        val inputType = a.getInt(R.styleable.EditTextStateful_inputType, 0x00000001)
        a.recycle()

        LayoutInflater.from(context).inflate(R.layout.view_edit_stateful, this)
        mEtInput = findViewById(R.id.mEtInput)
        mTvError = findViewById(R.id.mTvError)

        mEtInput.hint = hint
        if (inputBackground != null) mEtInput.background = inputBackground
        val lp = mEtInput.layoutParams
        lp.height = height.toInt()
        mEtInput.layoutParams = lp
        mEtInput.setTextSize(TypedValue.COMPLEX_UNIT_PX, textSize)
        mEtInput.inputType = inputType
        mEtInput.setHintTextColor(hintColor)
        mEtInput.setTextColor(textColor)
        mEtInput.setPadding(paddingStart.toInt(), 0, paddingEnd.toInt(), 0)
        this.background = background
    }

    var isInputEnable: Boolean = mEtInput.isEnabled
        set(value) {
            field = value
            mEtInput.isEnabled = value
        }

    @SuppressLint("CheckResult")
    fun addTextChangedListener(debounce: Long = 800, l: (String) -> Unit) {
        RxUtil.textChange(mEtInput)
            .debounce(debounce, TimeUnit.MILLISECONDS)
            .compose(RxUtil.applySchedulers())
            .subscribe {
                l.invoke(it)
            }
    }

    fun notifyError(error: String) {
        mTvError.text = error
        mTvError.visibility = View.VISIBLE
        if (errorBackground != null) {
            mEtInput.background = errorBackground
        } else {
            if (inputBackground != null) {
                mEtInput.background = inputBackground
            } else {
                mEtInput.setBackgroundResource(R.drawable.shape_card_back)
            }
        }
    }

    fun notifyNormal() {
        mTvError.text = ""
        mTvError.visibility = View.GONE
        if (inputBackground != null) {
            mEtInput.background = inputBackground
        } else {
            mEtInput.setBackgroundResource(R.drawable.shape_card_back)
        }
    }

    fun text(): String {
        return mEtInput.text?.toString() ?: ""
    }

    fun setText(text: String?) {
        mEtInput.setText(text)
    }

    fun editText(): EditText {
        return mEtInput
    }
}