package io.iotex.iopay.ui.widget

import android.annotation.SuppressLint
import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.animation.Animation
import android.view.animation.AnimationUtils
import android.view.animation.LinearInterpolator
import android.widget.ImageView
import com.scwang.smartrefresh.layout.api.RefreshLayout
import com.scwang.smartrefresh.layout.constant.RefreshState
import com.scwang.smartrefresh.layout.internal.InternalAbstract
import io.iotex.iopay.R

class IoPayHeader protected constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) :
    InternalAbstract(context, attrs, defStyleAttr) {
    private lateinit var mIvRefresh: ImageView
    private lateinit var mAnimation: Animation

    @JvmOverloads
    constructor(context: Context, attrs: AttributeSet? = null) : this(context, attrs, 0) {
        init(context)
    }

    @SuppressLint("InflateParams")
    private fun init(context: Context) {
        val view = LayoutInflater.from(context).inflate(R.layout.common_refresh_header, null, false)
        mIvRefresh = view.findViewById(R.id.img_refresh)
        mAnimation = AnimationUtils.loadAnimation(context, R.anim.animation_refresh)
        val lin = LinearInterpolator()
        mAnimation.interpolator = lin
        background = resources.getDrawable(R.color.color_refresh_header, context.theme)
        addView(view)
    }

    override fun onFinish(refreshLayout: RefreshLayout, success: Boolean): Int {
        mIvRefresh.clearAnimation()
        return super.onFinish(refreshLayout, success)
    }

    override fun onStateChanged(refreshLayout: RefreshLayout, oldState: RefreshState, newState: RefreshState) {
        if (newState == RefreshState.Refreshing) {
            mIvRefresh.startAnimation(mAnimation)
        }
        super.onStateChanged(refreshLayout, oldState, newState)
    }
}
