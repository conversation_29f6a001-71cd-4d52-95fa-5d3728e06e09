package io.iotex.iopay.ui.widget

import android.content.Context
import android.content.res.Resources
import android.util.AttributeSet
import android.util.TypedValue
import android.view.Gravity
import android.widget.ImageView
import android.widget.LinearLayout
import androidx.annotation.AttrRes
import androidx.annotation.NonNull
import androidx.annotation.Nullable
import androidx.annotation.StyleRes
import androidx.core.view.children
import androidx.viewpager.widget.ViewPager
import androidx.viewpager2.widget.ViewPager2
import io.iotex.iopay.R

class IoViewPagerIndicator : LinearLayout {
    private var mIndicatorCount: Int = 0
    private var mIndicatorWidth: Float = 0f
    private var mIndicatorHeight: Float = 0f
    private var mIndicatorPadding: Float = dp2PxFloat(4f)
    private var mSelectedIndicatorDrawable: Int = 0
    private var mUnselectedIndicatorDrawable: Int = 0

    private var mViewPager: ViewPager? = null
    private val mOnPageChangedListener = object : ViewPager.OnPageChangeListener {
        override fun onPageScrollStateChanged(state: Int) {

        }

        override fun onPageScrolled(
            position: Int,
            positionOffset: Float,
            positionOffsetPixels: Int
        ) {

        }

        override fun onPageSelected(position: Int) {
            val realPosition = position % mIndicatorCount
            <EMAIL> { index, view ->
                view as ImageView
                if (index == realPosition) {
                    view.setImageResource(mSelectedIndicatorDrawable)
                } else {
                    view.setImageResource(mUnselectedIndicatorDrawable)
                }
            }
        }
    }

    private var mViewPager2: ViewPager2? = null
    private val mOnPageChangedCallback = object : ViewPager2.OnPageChangeCallback() {
        override fun onPageSelected(position: Int) {
            super.onPageSelected(position)
            val realPosition = position % mIndicatorCount
            <EMAIL> { index, view ->
                view as ImageView
                if (index == realPosition) {
                    view.setImageResource(mSelectedIndicatorDrawable)
                } else {
                    view.setImageResource(mUnselectedIndicatorDrawable)
                }
            }
        }
    }

    constructor(@NonNull context: Context) : this(context, null)

    constructor(@NonNull context: Context, @Nullable attrs: AttributeSet?) : this(context, attrs, 0)

    constructor(
        @NonNull context: Context,
        @Nullable attrs: AttributeSet?,
        @AttrRes defStyleAttr: Int
    ) : this(context, attrs, defStyleAttr, 0)

    constructor(
        @NonNull context: Context,
        @Nullable attrs: AttributeSet?,
        @AttrRes defStyleAttr: Int,
        @StyleRes defStyleRes: Int
    ) : super(context, attrs, defStyleAttr, defStyleRes) {
        val a = context.obtainStyledAttributes(
            attrs, R.styleable.IoViewPagerIndicator, defStyleAttr, defStyleRes
        )

        orientation = HORIZONTAL
        gravity = Gravity.CENTER

        mIndicatorCount = a.getInteger(R.styleable.IoViewPagerIndicator_ioIndicatorCount, 0)
        mSelectedIndicatorDrawable =
            a.getResourceId(R.styleable.IoViewPagerIndicator_ioSelectedIndicatorDrawable, 0)
        mUnselectedIndicatorDrawable =
            a.getResourceId(R.styleable.IoViewPagerIndicator_ioUnselectedIndicatorDrawable, 0)
        mIndicatorWidth = a.getDimension(R.styleable.IoViewPagerIndicator_ioIndicatorWidth, 0f)
        mIndicatorHeight = a.getDimension(R.styleable.IoViewPagerIndicator_ioIndicatorHeight, 0f)

        addIndicator()

        a.recycle()
    }

    private fun addIndicator() {
        removeAllViews()
        if (mIndicatorCount > 1) {
            for (i in 0 until mIndicatorCount) {
                val indicator = ImageView(context)
                val lp = LayoutParams(
                    if (mIndicatorWidth == 0f) LayoutParams.WRAP_CONTENT else mIndicatorWidth.toInt(),
                    if (mIndicatorHeight == 0f) LayoutParams.WRAP_CONTENT else mIndicatorHeight.toInt()
                )
                if (i > 0) {
                    lp.setMargins(
                        if (orientation == HORIZONTAL) mIndicatorPadding.toInt() else 0,
                        if (orientation == VERTICAL) mIndicatorPadding.toInt() else 0,
                        0,
                        0
                    )
                    indicator.setImageResource(mUnselectedIndicatorDrawable)
                } else {
                    indicator.setImageResource(mSelectedIndicatorDrawable)
                }
                addView(indicator, lp)
            }
        }
    }

    fun setIndicatorCount(count: Int) {
        this.mIndicatorCount = count
        addIndicator()
    }

    fun setupWithViewPager(viewPager: ViewPager?) {
        this.mViewPager = viewPager?.also {
            if (mIndicatorCount == 0) {
                mIndicatorCount = it.adapter?.count ?: 0
                addIndicator()
            }
            it.removeOnPageChangeListener(mOnPageChangedListener)
            it.addOnPageChangeListener(mOnPageChangedListener)
        }
    }

    fun setupWithViewPager(viewPager2: ViewPager2?) {
        this.mViewPager2 = viewPager2?.also {
            if (mIndicatorCount == 0) {
                mIndicatorCount = it.adapter?.itemCount ?: 0
                addIndicator()
            }
            it.unregisterOnPageChangeCallback(mOnPageChangedCallback)
            it.registerOnPageChangeCallback(mOnPageChangedCallback)
        }
    }

    override fun onDetachedFromWindow() {
        mViewPager?.removeOnPageChangeListener(mOnPageChangedListener)
        mViewPager2?.unregisterOnPageChangeCallback(mOnPageChangedCallback)
        super.onDetachedFromWindow()
    }

    fun dp2PxFloat(dp: Float): Float = if (dp == 0f) 0f else TypedValue.applyDimension(
            TypedValue.COMPLEX_UNIT_DIP,
            dp,
            Resources.getSystem().displayMetrics
    )
}