package io.iotex.iopay.ui.widget

import android.annotation.SuppressLint
import android.content.Context
import android.widget.ImageView
import android.widget.TextView
import androidx.core.graphics.TypefaceCompat
import com.bigkoo.pickerview.adapter.ArrayWheelAdapter
import com.blankj.utilcode.util.Utils
import com.contrarywind.view.WheelView
import io.iotex.iopay.R
import io.iotex.iopay.util.extension.dp2px
import io.iotex.iopay.wallet.dialog.BaseDialog

@SuppressLint("RestrictedApi")
class PickerDialog(context: Context, private val optionList: List<PickerItemData>) :
    BaseDialog(context, R.layout.dialog_picker) {

    private val mIvClose: ImageView = findView(R.id.mIvClose)
    private val mTvTitle: TextView = findView(R.id.mTvTitle)
    private val mTvConfirm: TextView = findView(R.id.mTvConfirm)
    private val mWheelView: WheelView = findView(R.id.mWheelView)

    init {
        mIvClose.setOnClickListener {
            dismiss()
        }

        mWheelView.setCyclic(false)
        mWheelView.setDividerColor(Utils.getApp().getColor(R.color.white_0fffffff))
        mWheelView.setDividerWidth(1.dp2px())
        mWheelView.setTextColorCenter(Utils.getApp().getColor(R.color.color_617AFF))
        mWheelView.setTextSize(16F)
        val typeface = TypefaceCompat.createFromResourcesFontFile(
            Utils.getApp(),
            Utils.getApp().resources,
            R.font.space_grotesk_regular,
            "",
            0
        )
        mWheelView.setTypeface(typeface)
        mWheelView.setLineSpacingMultiplier(15F)
        mWheelView.setItemsVisibleCount(3)

        mWheelView.adapter = ArrayWheelAdapter(optionList.map { it.label })
    }

    fun setTitle(title: String) = apply {
        mTvTitle.text = title
    }

    fun setCurrentItem(item: PickerItemData) = apply {
        val index = optionList.indexOfFirst {
            it.value == item.value
        }
        if (index != -1) {
            mWheelView.currentItem = index
        }
    }

    fun setPositiveButton(positive: String, callback: ((PickerItemData) -> Unit)? = null) = apply {
        mTvConfirm.text = positive
        mTvConfirm.setOnClickListener {
            val index = mWheelView.currentItem
            if (optionList.size > index) {
                callback?.invoke(optionList[index])
            }
            dismiss()
        }
    }
}

data class PickerItemData(val label: String, val value: Int)
