package io.iotex.iopay.ui.widget

import android.content.Context
import android.util.AttributeSet
import android.widget.FrameLayout


class SquareLayout @JvmOverloads constructor(context: Context, attr: AttributeSet? = null, defStyle: Int = 0) : FrameLayout(
        context,
        attr,
        defStyle
    ){


    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        setMeasuredDimension(
            getDefaultSize(0, widthMeasureSpec),
            getDefaultSize(0, heightMeasureSpec)
        )
        val childWidth = measuredWidth
        val widthSpec = MeasureSpec.makeMeasureSpec(childWidth, MeasureSpec.EXACTLY)
        super.onMeasure(widthSpec, widthSpec)
    }
}