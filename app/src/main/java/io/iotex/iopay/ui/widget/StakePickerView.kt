package io.iotex.iopay.ui.widget

import android.content.Context
import android.view.LayoutInflater
import android.widget.LinearLayout
import androidx.core.content.ContextCompat
import com.bigkoo.pickerview.adapter.ArrayWheelAdapter
import com.contrarywind.view.WheelView
import io.iotex.iopay.R
import io.iotex.iopay.util.TokenUtil
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.util.extension.asBigDecimal
import io.iotex.iopay.util.extension.dp2px
import io.iotex.iopay.wallet.web3.Web3Delegate
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class StakePickerView(context: Context): LinearLayout(context) {

    private var onSelectedCb: ((Int, Int) -> Unit)? = null

    private var wheelFirst: WheelView
    private var wheelSecond: WheelView

    private var balance: String = ""

    init {
        val root = LayoutInflater.from(context).inflate(R.layout.stake_picker_view, this, false)
        addView(root)

        wheelFirst = findViewById(R.id.wheelFirst)
        wheelSecond = findViewById(R.id.wheelSecond)

        wheelFirst.setCyclic(false)
        wheelFirst.setDividerColor(ContextCompat.getColor(wheelFirst.context, R.color.color_line_diver))
        wheelFirst.setDividerWidth(1.dp2px())
        wheelFirst.setTextColorCenter(ContextCompat.getColor(wheelFirst.context, R.color.color_title))
        wheelFirst.setTextSize(17F)
        wheelFirst.setItemsVisibleCount(5)
        wheelFirst.setLineSpacingMultiplier(5F)
        wheelSecond.setCyclic(false)
        wheelSecond.setDividerColor(ContextCompat.getColor(wheelFirst.context, R.color.color_line_diver))
        wheelSecond.setDividerWidth(1.dp2px())
        wheelSecond.setTextColorCenter(ContextCompat.getColor(wheelFirst.context, R.color.color_title))
        wheelSecond.setTextSize(17F)
        wheelSecond.setItemsVisibleCount(5)
        wheelSecond.setLineSpacingMultiplier(5F)

        getBalance()
    }

    fun setData(data: Map<String, List<String>>) {
        val firstData = data.keys.toList()
        wheelFirst.adapter = ArrayWheelAdapter(firstData)
        val firstIndex = wheelFirst.currentItem
        val secondData = data[firstData[firstIndex]]
        wheelSecond.adapter = ArrayWheelAdapter(secondData)

        wheelFirst.setOnItemSelectedListener { index ->
            val list = data[firstData[index]]
            wheelSecond.adapter = ArrayWheelAdapter(list)
            wheelSecond.currentItem = 0
            onSelectedCb?.invoke(wheelFirst.currentItem, wheelSecond.currentItem)
            if (firstData[index].asBigDecimal() > balance.asBigDecimal()) {
                val index = firstData.indexOfFirst {
                    it.asBigDecimal() <= balance.asBigDecimal()
                }
                if (index != -1) {
                    wheelFirst.currentItem = index
                    wheelSecond.adapter = ArrayWheelAdapter(data[firstData[index]])
                    wheelSecond.currentItem = 0
                    onSelectedCb?.invoke(wheelFirst.currentItem, wheelSecond.currentItem)
                }
            }
        }
        wheelSecond.setOnItemSelectedListener {
            onSelectedCb?.invoke(wheelFirst.currentItem, wheelSecond.currentItem)
        }
        onSelectedCb?.invoke(wheelFirst.currentItem, wheelSecond.currentItem)
    }

    fun setOnSelectedCallback(l: (Int, Int) -> Unit) = apply {
        onSelectedCb = l
    }

    private fun getBalance(){
        CoroutineScope(Dispatchers.IO).launch {
            val address = WalletHelper.getCurWallet()?.address?:""
            if (!WalletHelper.isValidAddress(address)) return@launch
            val balanceList = Web3Delegate.getCurrencyBalance(address)
            if (balanceList.isEmpty()) return@launch
            balance = TokenUtil.weiToTokenBN(balanceList[0].toString())
        }
    }

}