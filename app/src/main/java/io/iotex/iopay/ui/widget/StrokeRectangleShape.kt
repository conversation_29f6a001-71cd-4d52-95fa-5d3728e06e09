package io.iotex.iopay.ui.widget

import android.animation.TimeInterpolator
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.PointF
import android.graphics.RectF
import android.view.animation.DecelerateInterpolator
import com.blankj.utilcode.util.ColorUtils
import com.takusemba.spotlight.shape.Shape
import io.iotex.iopay.R
import io.iotex.iopay.util.extension.dp2px
import java.util.concurrent.TimeUnit

/**
 * [Shape] of StrokeRectangleShape with customizable height, width, and radius.
 */
class StrokeRectangleShape @JvmOverloads constructor(
    private val height: Float,
    private val width: Float,
    private val radius: Float,
    override val duration: Long = DEFAULT_DURATION,
    override val interpolator: TimeInterpolator = DEFAULT_INTERPOLATOR
) : Shape {
    private val linePaint = Paint()

    init {
        linePaint.isAntiAlias = true
        linePaint.color = ColorUtils.getColor(R.color.color_617AFF)
        linePaint.strokeWidth = 1f.dp2px().toFloat()
        linePaint.style = Paint.Style.STROKE
    }

    override fun draw(canvas: Canvas, point: PointF, value: Float, paint: Paint) {
        val halfWidth = width / 2 * value
        val halfHeight = height / 2 * value
        val left = point.x - halfWidth
        val top = point.y - halfHeight
        val right = point.x + halfWidth
        val bottom = point.y + halfHeight
        val rect = RectF(left, top, right, bottom)
        canvas.drawRoundRect(rect, radius, radius, paint)
        canvas.drawRoundRect(rect, radius, radius, linePaint)
    }

    companion object {

        val DEFAULT_DURATION = TimeUnit.MILLISECONDS.toMillis(500)

        val DEFAULT_INTERPOLATOR = DecelerateInterpolator(2f)
    }
}

