package io.iotex.iopay.ui.widget.selector

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import io.iotex.iopay.R
import io.iotex.iopay.databinding.ItemBitcoinGasOptionSelectorBinding

class BitcoinGasOptionSelector(context: Context, attrs: AttributeSet) : Selector(context, attrs) {

    private var binding: ItemBitcoinGasOptionSelectorBinding

    init {
        val params =
            LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT)

        binding = DataBindingUtil.inflate(
            LayoutInflater.from(context),
            R.layout.item_bitcoin_gas_option_selector,
            null,
            false
        )
        this.addView(binding.root, params)
    }

    fun setIndicator(res: Int) = apply {
        binding.ivIndicator.setImageResource(res)
    }

    fun setGasLevel(level: String) = apply {
        binding.tvLevel.text = level
    }

    fun setFee(fee: String) = apply {
        binding.tvFee.text = fee
    }

    fun setTransactionTime(text: String) = apply {
        binding.tvTransactionTime.text = text
    }

    fun setSpentFee(text: String) = apply {
        binding.tvSpentFee.text = text
    }

    override fun onSwitchSelected(isSelect: Boolean) {
        if (isSelect) {
            selectedUI()
        } else {
            unselectedUI()
        }
    }

    private fun selectedUI() {
        binding.llRoot.setBackgroundResource(R.drawable.shape_card_back_stroke_gradient)
    }

    private fun unselectedUI() {
        binding.llRoot.setBackgroundResource(R.drawable.shape_card_back)
    }

}