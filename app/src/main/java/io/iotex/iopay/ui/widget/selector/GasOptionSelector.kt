package io.iotex.iopay.ui.widget.selector

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import com.blankj.utilcode.util.ColorUtils
import io.iotex.iopay.R
import io.iotex.iopay.databinding.ItemGasOptionSelectorBinding
import io.iotex.iopay.util.extension.loadImage
import io.iotex.iopay.util.extension.setVisible

class GasOptionSelector(context: Context, attrs: AttributeSet) : Selector(context, attrs) {

    private var binding: ItemGasOptionSelectorBinding
    private var selectedIcon = R.drawable.ic_hook_blue

    init {
        val params =
            LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT)

        binding = DataBindingUtil.inflate(
            LayoutInflater.from(context),
            R.layout.item_gas_option_selector,
            null,
            false
        )
        this.addView(binding.root, params)
    }

    fun setGasLevel(level: String) = apply {
        binding.tvLevel.text = level
    }

    fun setFee(fee: String) = apply {
        binding.tvGas.text = fee
    }

    fun setEditAction(cb: () -> Unit) = apply {
        binding.ivEdit.setVisible()
        binding.ivEdit.setOnClickListener {
            cb.invoke()
        }
    }

    fun getGasLevel() = binding.tvLevel.text.toString()

    fun setSelectedIcon(icon: Int) = apply {
        selectedIcon = icon
    }

    override fun onSwitchSelected(isSelect: Boolean) {
        if (isSelect) {
            selectedUI()
        } else {
            unselectedUI()
        }
    }

    private fun selectedUI() {
        binding.ivIndicator.loadImage(selectedIcon, selectedIcon)
        binding.tvLevel.setTextColor(ColorUtils.getColor(R.color.color_617AFF))
        binding.tvGas.setTextColor(ColorUtils.getColor(R.color.color_617AFF))
    }

    private fun unselectedUI() {
        binding.ivIndicator.loadImage(R.drawable.ic_ring, R.drawable.ic_ring)
        binding.tvLevel.setTextColor(ColorUtils.getColor(R.color.gray_9a9fb1))
        binding.tvGas.setTextColor(ColorUtils.getColor(R.color.gray_9a9fb1))
    }

}