package io.iotex.iopay.ui.widget.selector

import android.content.Context
import android.util.AttributeSet
import android.view.View
import android.widget.FrameLayout
import io.iotex.iopay.ui.widget.selector.SelectorGroup

/**
 * it is a customized view acts like a checkbox.
 * it can be selected or unselected, the background will change accordingly. wrapping this business logic into a single view for clean code in Fragment
 */
abstract class Selector(context: Context, attrs: AttributeSet) :
    FrameLayout(context, attrs), View.OnClickListener {
    /**
     * the tag indicates which group this selector belongs to,
     * set the same group tag for selectors which want single choice mode
     */
    var groupTag: String = "default"
        private set

    /**
     * the group which this Selector belongs to
     */
    private var selectorGroup: SelectorGroup? = null

    init {
        this.setOnClickListener(this)
    }

    fun setGroup(groupTag: String, selectorGroup: SelectorGroup) = apply {
        this.selectorGroup = selectorGroup
        this.groupTag = groupTag
    }

    override fun setSelected(selected: <PERSON>olean) {
        val isPreSelected = isSelected
        super.setSelected(selected)
        if (isPreSelected != selected) {
            onSwitchSelected(selected)
        }
    }

    override fun onClick(v: View) {
        selectorGroup?.onSelectorClick(this)
    }

    protected abstract fun onSwitchSelected(isSelect: Boolean)
}