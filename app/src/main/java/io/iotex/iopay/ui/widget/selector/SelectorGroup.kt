package io.iotex.iopay.ui.widget.selector

import androidx.core.view.forEach

class SelectorGroup {

    private var choiceMode: ChoiceAction = SingleAction()
    private var onStateChangeListener: ((String, Int, Boolean) -> Unit)? = null

    private val selectorMap = HashMap<String, Selector>()

    fun setChoiceMode(mode: Int) {
        when (mode) {
            MODE_MULTIPLE_CHOICE -> choiceMode = MultipleAction()
            MODE_SINGLE_CHOICE -> choiceMode = SingleAction()
        }
    }

    fun setStateListener(onStateChangeListener: (String, Int, Boolean) -> Unit) {
        this.onStateChangeListener = onStateChangeListener
    }

    fun getPreSelector(groupTag: String): Selector? {
        return selectorMap[groupTag]
    }

    fun setSelected(selected: <PERSON><PERSON><PERSON>, selector: Selector) {
        if (choiceMode is SingleAction) {
            selectorMap.values.forEach {
                it.isSelected = false
            }
        }
        if (selected) {
            selectorMap[selector.groupTag] = selector
        }
        selector.isSelected = selected
        onStateChangeListener?.invoke(selector.groupTag, selector.id, selected)
    }

    private fun cancelPreSelector(selector: Selector) {
        val groupTag = selector.groupTag
        val preSelector = getPreSelector(groupTag)
        preSelector?.isSelected = false
    }

    fun onSelectorClick(selector: Selector) {
        choiceMode.onChoose(selector, this)
        selectorMap[selector.groupTag] = selector
    }

    fun clear() {
        selectorMap.clear()
    }

    interface ChoiceAction {
        fun onChoose(selector: Selector, selectorGroup: SelectorGroup)
    }

    private inner class SingleAction : ChoiceAction {
        override fun onChoose(selector: Selector, selectorGroup: SelectorGroup) {
            cancelPreSelector(selector)
            setSelected(true, selector)
        }
    }

    private inner class MultipleAction : ChoiceAction {
        override fun onChoose(selector: Selector, selectorGroup: SelectorGroup) {
            val isSelected = selector.isSelected
            setSelected(!isSelected, selector)
        }
    }

    companion object {
        val MODE_SINGLE_CHOICE = 1
        val MODE_MULTIPLE_CHOICE = 2
    }
}