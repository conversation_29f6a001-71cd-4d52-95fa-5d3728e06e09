package io.iotex.iopay.util

import com.blankj.utilcode.util.Utils
import net.osslabz.evm.abi.decoder.AbiDecoder

object AbiDecoderUtil {

    fun resolveParam(data: String, key: String, abi: String): String {
        kotlin.runCatching {
            val abiIs = Utils.getApp().assets.open(abi)
            val decoder = AbiDecoder(abiIs)
            val functionCall = decoder.decodeFunctionCall(data)
            val params = functionCall.paramList
            val param = params?.firstOrNull {
                it.name == key
            }
            return if (param?.value == null) {
                ""
            } else {
                return param.value.toString()
            }
        }
        return ""
    }
}