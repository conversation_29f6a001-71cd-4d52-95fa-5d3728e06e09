package io.iotex.iopay.util

import android.view.animation.Animation
import android.view.animation.TranslateAnimation

object AnimationUtil {
    fun moveToViewLeft(): TranslateAnimation {
        val mHiddenAction = TranslateAnimation(Animation.RELATIVE_TO_SELF, 0.0f,
                Animation.RELATIVE_TO_SELF, -1.0f, Animation.RELATIVE_TO_SELF,
                0.0f, Animation.RELATIVE_TO_SELF, 0.0f)
        mHiddenAction.duration = 500
        return mHiddenAction
    }

    fun moveToViewLocation(): TranslateAnimation {
        val mHiddenAction = TranslateAnimation(Animation.RELATIVE_TO_SELF, 1.0f,
                Animation.RELATIVE_TO_SELF, 0.0f, Animation.RELATIVE_TO_SELF,
                0.0f, Animation.RELATIVE_TO_SELF, 0.0f)
        mHiddenAction.duration = 500
        return mHiddenAction
    }
}