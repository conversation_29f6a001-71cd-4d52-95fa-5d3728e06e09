package io.iotex.iopay.util

import io.iotex.iopay.R
import io.iotex.iopay.data.db.Wallet
import io.iotex.iopay.viewmodel.data.InitialGuideModel
import io.iotex.stake.DelegateQuery

object Constant {

    var currentWallet: Wallet? = null

    var delegateList: List<DelegateQuery.Iopay_delegate>? = null

    const val COIN_TYPE_IOTEX = 304
    const val COIN_TYPE_IOTEX_DESC = "IoTeX Standard (IoTeX HD PATH)"
    const val COIN_TYPE_EVM = 60
    const val COIN_TYPE_SOLANA = 501
    const val COIN_TYPE_EVM_DESC = "ETH Standard (ETH HD PATH)"
    const val COIN_TYPE_BITCOIN = 0
    const val COIN_TYPE_BITCOIN_DESC = "Bitcoin Standard (Bitcoin HD PATH)"
    const val COIN_TYPE_SOLANA_DESC = "Solana Standard (Solana HD PATH)"


    const val MNEMONIC_PATH_IOTEX = "m/44'/304'/0'/0/0"
    const val MNEMONIC_PATH_ETH = "m/44'/60'/0'/0/0"
    const val MNEMONIC_PATH_SOLANA = "m/44'/501'/0'/0/0"
    const val MNEMONIC_PATH_LEGACY = "m/44'/0'/0'/0/0"
    const val MNEMONIC_PATH_NATIVE_SEGWIT = "m/84'/0'/0'/0/0"
    const val MNEMONIC_PATH_NESTED_SEGWIT = "m/49'/0'/0'/0/0"
    const val MNEMONIC_PATH_TAPROOT = "m/86'/0'/0'/0/0"

    const val PLATFORM_ANDROID = "android"
    const val TIME_EXIT_INTERVAL = 2000

    const val DEEPLINK_ACTION_WEB = "web"
    const val DEEPLINK_ACTION_STAKE = "stake"
    const val DEEPLINK_ACTION_WALLET_CONNECT = "walletConnect"

    const val IOPAY_W3BSTREAM_ENABLE = "iopay://w3bstream_enable"
    const val IOPAY_W3BSTREAM_DISABLE = "iopay://w3bstream_disable"

    const val IOPAY_OPEN_W3BSTREAM_PAGE = "iopay://open_w3bstream_page"
    const val IOPAY_HOST_FIOBACK = "iopay://io.iotex.iopay/fioback"
    const val IOPAY_IOTEX_2 = "https://iotex.io/2.0"


    const val URL_IIP_SBT = "https://iotex.io/blog/iip-voter-sbt"

    val balanceHide = "******"


    val mGuideSources = listOf(
        InitialGuideModel(
            imgSrc = R.drawable.ic_guide_image_1,
            title = R.string.app_initial_guide_tip_1,
            subTitle = R.string.app_initial_guide_sub_tip_1
        ),
        InitialGuideModel(
            imgSrc = R.drawable.ic_guide_image_2,
            title = R.string.app_initial_guide_tip_2,
            subTitle = R.string.app_initial_guide_sub_tip_2
        ),
        InitialGuideModel(
            imgSrc = R.drawable.ic_guide_image_3,
            title = R.string.app_initial_guide_tip_3,
            subTitle = R.string.app_initial_guide_sub_tip_3
        ),
        InitialGuideModel(
            imgSrc = R.drawable.ic_guide_image_4,
            title = R.string.app_initial_guide_tip_4,
            subTitle = R.string.choose_from_defi_nfts_gamefi_depin
        )
    )

    const val URL_GOOGLE_PLAY_APK = "https://play.google.com/store/apps/details?id=io.iotex.iopay.gp"

    const val AVATAR_THEME_ROBOTS  = 0
    const val AVATAR_THEME_BLOCKIES  = 1
    const val AVATAR_THEME_JAZZICONS  = 2

    const val ADDRESS_REVERSE  = ".addr.reverse"
}