package io.iotex.iopay.util

import com.blankj.utilcode.util.TimeUtils
import io.iotex.iopay.data.db.NETWORK_GENERAL
import io.iotex.iopay.data.db.NETWORK_POOR
import io.iotex.iopay.data.db.NETWORK_SMOOTH
import java.text.SimpleDateFormat
import java.util.*

object DateTimeUtils {

    //2022-04-09T07:28:10.279028+00:00
    fun isExPirationTime(expirationTime: String?): Boolean {
        if (expirationTime.isNullOrEmpty()) return false
        val times = expirationTime.split("T")
        if (times.size > 1) {
            val time = times[0]
            val exMills = TimeUtils.string2Millis(time, "yyyy-MM-dd")
            val nowMills = System.currentTimeMillis()
            return nowMills >= exMills
        } else {
            return false
        }
    }

    //2022-04-09T07:28:10.279028+00:00
    fun isActiveTime(startTime: String?, endTime: String?): Boolean {
        if (startTime.isNullOrEmpty()) return false
        val startTimes = startTime.split("T")
        if (startTimes.size > 1) {
            val start = startTimes[0]
            val startMills = TimeUtils.string2Millis(start, "yyyy-MM-dd")
            val nowMills = System.currentTimeMillis()
            if (endTime.isNullOrEmpty()) {
                return nowMills >= startMills
            } else {
                val endTimes = endTime.split("T")
                if (endTimes.size > 1) {
                    val end = endTimes[0]
                    val endMills = TimeUtils.string2Millis(end, "yyyy-MM-dd") + 24 * 3600 * 1000
                    return nowMills <= endMills
                } else {
                    return nowMills >= startMills
                }
            }
        } else {
            return false
        }
    }

    fun formatYMD(time: Long?): String {
        if (time == null) return ""
        val format = SimpleDateFormat("yyyy/MM/dd", Locale.getDefault())
        return format.format(time)
    }

    fun getRpcState(time: Long): Int {
        return when (time) {
            in (0..1000) -> NETWORK_SMOOTH
            in (1000..2000) -> NETWORK_GENERAL
            else -> NETWORK_POOR
        }
    }

    fun getIsoTime(): String {
        val calendar = Calendar.getInstance()
        val sdf = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSXXX", Locale.getDefault())
        sdf.timeZone = TimeZone.getTimeZone("UTC")
        return sdf.format(calendar.time)
    }

}
