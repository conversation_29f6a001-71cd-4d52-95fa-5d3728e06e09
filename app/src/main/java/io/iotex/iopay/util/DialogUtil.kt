package io.iotex.iopay.util

import android.animation.ValueAnimator
import android.graphics.Color
import android.view.Gravity
import android.view.MotionEvent
import android.view.View
import android.view.View.OnTouchListener
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.SnackbarUtils
import com.blankj.utilcode.util.Utils
import io.iotex.iopay.R
import io.iotex.iopay.SchemeUtil
import io.iotex.iopay.data.db.ActionRecordEntry
import io.iotex.iopay.data.db.AppDatabase
import io.iotex.iopay.data.db.STATUS_FAILED
import io.iotex.iopay.data.db.STATUS_SUCCESS
import io.iotex.iopay.repo.BitcoinRepo
import io.iotex.iopay.support.lifecycle.IoLifecycleHandler
import io.iotex.iopay.util.extension.dp2px
import io.iotex.iopay.util.extension.setGone
import io.iotex.iopay.util.extension.setVisible
import io.iotex.iopay.wallet.dialog.TransferEmptyHashDialog
import io.iotex.iopay.wallet.solana.SolanaWeb3
import io.iotex.iopay.wallet.web3.Web3Delegate
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.jetbrains.anko.contentView
import kotlin.math.abs


object DialogUtil {

    private var job: Job? = null
    fun showTransferLoading(timestamp: String) {
        MainScope(). launch {
            IoLifecycleHandler.timestamp = timestamp
            val actionRecordEntry = withContext(Dispatchers.IO) {
                AppDatabase.getInstance(Utils.getApp()).actionRecordDao()
                    .queryByTimestamp(WalletHelper.getCurChainId(), timestamp)
            }
            if (actionRecordEntry?.status == STATUS_FAILED && actionRecordEntry.hash.isEmpty()) {
                showTransferFailed(actionRecordEntry)
                IoLifecycleHandler.timestamp = ""
            } else {
                if (actionRecordEntry?.status == STATUS_SUCCESS) {
                    showTransferSucceed(actionRecordEntry)
                    return@launch
                }
                if (actionRecordEntry?.status == STATUS_FAILED) {
                    showTransferFailed(actionRecordEntry)
                    return@launch
                }
                if (actionRecordEntry?.hash.isNullOrBlank()) {
                    actionRecordEntry?.let {
                        showTransferSend(actionRecordEntry)
                    }
                } else if (actionRecordEntry?.cancel == true) {
                    showTransferSend(actionRecordEntry)
                } else {
                    actionRecordEntry?.let {
                        showTransferSubmitted(actionRecordEntry)
                        if (job?.isActive == true) job?.cancel()
                        job = CoroutineScope(Dispatchers.IO).launch {
                            val result = if (WalletHelper.isBitcoinNetwork()) {
                                val transactionDetail =
                                    BitcoinRepo().waitForTransactionDetail(actionRecordEntry.hash)
                                transactionDetail?.status?.confirmed
                            } else if (WalletHelper.isSolanaNetwork()) {
                                SolanaWeb3.getTransaction(actionRecordEntry.hash)
                            } else {
                                val receipt =
                                    Web3Delegate.queryTransactionReceipt(actionRecordEntry.hash)
                                receipt?.isStatusOK
                            }
                            val actionRecordEntryNew = withContext(Dispatchers.IO) {
                                AppDatabase.getInstance(Utils.getApp()).actionRecordDao()
                                    .queryByTimestamp(
                                        WalletHelper.getCurChainId(),
                                        timestamp
                                    )
                            }
                            MainScope().launch {
                                actionRecordEntryNew?.let {
                                    if (result == true) {
                                        showTransferSucceed(actionRecordEntryNew)
                                    } else if (!WalletHelper.isBitcoinNetwork() && result == false) {
                                        showTransferFailed(actionRecordEntryNew)
                                    } else {
                                        SnackbarUtils.dismiss()
                                    }
                                }
                                IoLifecycleHandler.timestamp = ""
                            }
                        }

                    }
                }
            }
        }
    }

    private fun showTransferSend(actionRecordEntry: ActionRecordEntry) {
        runCatching {
            var view = runCatching { SnackbarUtils.getView() }.getOrNull()
            if (view == null) {
                view = getSnackBarView()
                if (view == null) return
            }
            val llRoot = view.findViewById<LinearLayout>(R.id.llRoot)
            val llEmpty = view.findViewById<LinearLayout>(R.id.llEmpty)
            val tvNext = view.findViewById<TextView>(R.id.tvNext)
            val tag: Long? = runCatching {
                llRoot?.tag.toString().toLongOrNull()
            }.getOrNull()
            if ((tag ?: 0L) > (actionRecordEntry.timestamp.toLongOrNull() ?: 0L)) return
            if ((tag ?: 0L) != 0L && (tag ?: 0L) < (actionRecordEntry.timestamp.toLongOrNull()
                    ?: 0L) || actionRecordEntry.cancel
            ) {
                llEmpty.setVisible()
                val layoutParams = llRoot.layoutParams
                layoutParams.height = 100.dp2px()
                llRoot.layoutParams = layoutParams
                actionRecordEntry.getMethodName {
                    val next = it + " - " + Utils.getApp()
                        .getString(R.string.transaction_sending)
                    tvNext.text = next
                }
                val animator = ValueAnimator.ofInt(100, 54).setDuration(500L)
                animator.addUpdateListener {
                    val value = (it.animatedValue.toString().toIntOrNull() ?: 0)
                    layoutParams.height = value.dp2px()
                    llRoot.layoutParams = layoutParams
                    if (value == 54) {
                        llEmpty.setGone()
                        val ivTransferStatus = view.findViewById<ImageView>(R.id.ivTransferStatus)
                        ivTransferStatus.setImageResource(R.drawable.icon_transfer_status_loading)
                        val tvTransferStatus = view.findViewById<TextView>(R.id.tvTransferStatus)
                        actionRecordEntry.getMethodName {
                            val next = it + " - " + Utils.getApp()
                                .getString(R.string.transaction_sending)
                            tvTransferStatus.text = next
                        }

                        view.setOnClickListener {
                            //nothing
                        }
                    }
                }
                animator.start()
                llRoot.tag = actionRecordEntry.timestamp
            } else {
                val ivTransferStatus = view.findViewById<ImageView>(R.id.ivTransferStatus)
                ivTransferStatus.setImageResource(R.drawable.icon_transfer_status_loading)
                val tvTransferStatus = view.findViewById<TextView>(R.id.tvTransferStatus)
                actionRecordEntry.getMethodName {
                    val next = it + " - " + Utils.getApp()
                        .getString(R.string.transaction_sending)
                    tvTransferStatus.text = next
                }
            }

            llRoot.tag = actionRecordEntry.timestamp
        }
    }

    private fun getSnackBarView(): View? {
        val parent = ActivityUtils.getTopActivity()?.contentView ?: return null
        val params = ViewGroup.LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.MATCH_PARENT
        )
        SnackbarUtils.with(parent)
            .setBgColor(Color.TRANSPARENT)
            .setDuration(SnackbarUtils.LENGTH_INDEFINITE)
            .show()

        SnackbarUtils.addView(R.layout.dialog_transfer_status, params)
        val view = SnackbarUtils.getView()
        val llEmpty = view?.findViewById<LinearLayout>(R.id.llEmpty)
        llEmpty?.setGone()
        val rootParams = view?.layoutParams as? FrameLayout.LayoutParams
        rootParams?.gravity = Gravity.TOP
        view?.setPadding(16.dp2px(), 0, 16.dp2px(), 0)
        rootParams?.topMargin = 44.dp2px()
        view?.layoutParams = rootParams
        return view
    }

    private fun showTransferSubmitted(actionRecordEntry: ActionRecordEntry) {
        runCatching {
            var view = runCatching { SnackbarUtils.getView() }.getOrNull()
            if (view == null) {
                view = getSnackBarView()
                if (view == null) return
            }
            val ivTransferStatus = view.findViewById<ImageView>(R.id.ivTransferStatus)
            ivTransferStatus.setImageResource(R.drawable.icon_transfer_status_loading)
            val tvTransferStatus = view.findViewById<TextView>(R.id.tvTransferStatus)
            actionRecordEntry.getMethodName {
                tvTransferStatus.text = it + " - " + Utils.getApp()
                    .getString(R.string.transaction_submitted)
            }
            view.setOnClickListener {
                WalletHelper.gotoExplorer(actionRecordEntry.hash)
                SnackbarUtils.dismiss()
            }
            view.setOnTouchListener(object : OnTouchListener {
                private var initialX = 0f
                private var initialY = 0f
                private var finalX = 0f
                private var finalY = 0f
                override fun onTouch(v: View, event: MotionEvent): Boolean {
                    when (event.action) {
                        MotionEvent.ACTION_DOWN -> {
                            initialX = event.rawX
                            initialY = event.rawY
                        }

                        MotionEvent.ACTION_MOVE -> {
                            finalX = event.rawX
                            finalY = event.rawY
                            val dx = finalX - initialX
                            val dy = finalY - initialY
                            if(abs(dy) >5.dp2px() || abs(dx) >5.dp2px()){
                                IoLifecycleHandler.timestamp = ""
                                SnackbarUtils.dismiss()
                            }
                            initialX = finalX
                            initialY = finalY
                        }

                        MotionEvent.ACTION_UP -> {}
                    }
                    return true
                }
            })
            if (WalletHelper.isSupport1559Gas() || WalletHelper.isBitcoinNetwork() || WalletHelper.isSolanaNetwork()) {
                view.postDelayed({
                    SnackbarUtils.dismiss()
                    IoLifecycleHandler.timestamp = ""
                }, 3000)
            }
        }
    }

    private var lastSuccessTimestamp:String = ""

    private fun showTransferSucceed(actionRecordEntry: ActionRecordEntry) {
        if (lastSuccessTimestamp == actionRecordEntry.timestamp) return
        lastSuccessTimestamp = actionRecordEntry.timestamp
        runCatching {
            var view = runCatching { SnackbarUtils.getView() }.getOrNull()
            if (view == null) {
                view = getSnackBarView()
                if (view == null) return
            }
            val ivTransferStatus = view.findViewById<ImageView>(R.id.ivTransferStatus)
            if (actionRecordEntry.cancel)
                ivTransferStatus.setImageResource(R.drawable.icon_transfer_status_cancel)
            else
                ivTransferStatus.setImageResource(R.drawable.icon_transfer_status_confirm)
            val tvTransferStatus = view.findViewById<TextView>(R.id.tvTransferStatus)
            val tvHash = view.findViewById<TextView>(R.id.tvHash)
            tvHash.setVisible()
            actionRecordEntry.getMethodName {
                if (actionRecordEntry.cancel)
                    tvTransferStatus.text = it + " - " + Utils.getApp()
                        .getString(R.string.transaction_canceled)
                else
                    tvTransferStatus.text = it + " - " + Utils.getApp()
                        .getString(R.string.transaction_succeed)
            }
            view.setOnClickListener {
                WalletHelper.gotoExplorer(actionRecordEntry.hash)
                SnackbarUtils.dismiss()
                IoLifecycleHandler.timestamp = ""
            }
            view.postDelayed({
                SnackbarUtils.dismiss()
                IoLifecycleHandler.timestamp = ""
                val activity = ActivityUtils.getTopActivity() as? AppCompatActivity  ?: return@postDelayed
                if (ActivityUtils.isActivityAlive(activity)) {
                    SchemeUtil.goToReview(activity)
                }
            }, 2000)
        }
    }

    private var lastFailedTimestamp:String = ""

    private fun showTransferFailed(actionRecordEntry: ActionRecordEntry) {
        if (lastFailedTimestamp == actionRecordEntry.timestamp) return
        lastFailedTimestamp = actionRecordEntry.timestamp
        runCatching {
            var view = runCatching { SnackbarUtils.getView() }.getOrNull()
            if (view == null) {
                view = getSnackBarView()
                if (view == null) return
            }
            val ivTransferStatus = view.findViewById<ImageView>(R.id.ivTransferStatus)
            ivTransferStatus.setImageResource(R.drawable.icon_transfer_status_fail)
            val tvTransferStatus = view.findViewById<TextView>(R.id.tvTransferStatus)
            actionRecordEntry.getMethodName {
                tvTransferStatus.text = it + " - " + Utils.getApp()
                    .getString(R.string.transaction_fail)
            }
            view.setOnClickListener {
                if (actionRecordEntry.hash.isBlank()) {
                    (ActivityUtils.getTopActivity() as AppCompatActivity).let {
                        TransferEmptyHashDialog(actionRecordEntry).show(
                            it.supportFragmentManager,
                            System.currentTimeMillis().toString()
                        )
                    }
                } else {
                    val tvHash = view.findViewById<TextView>(R.id.tvHash)
                    tvHash.setVisible()
                    WalletHelper.gotoExplorer(actionRecordEntry.hash)
                }
                SnackbarUtils.dismiss()
                IoLifecycleHandler.timestamp = ""
            }
            view.postDelayed({
                SnackbarUtils.dismiss()
                IoLifecycleHandler.timestamp = ""
            }, 2000)
        }
    }

}