package io.iotex.iopay.util

import org.json.JSONArray
import org.json.JSONObject

object EIP712Converter {

    fun convertToStandardFormat(json: String): String {
        val original = JSONObject(json)
        val result = JSONObject()

        val types = original.getJSONObject("types")
        types.put(
            "EIP712Domain", JSONArray(
                arrayOf(
                    JSONObject()
                        .put("name", "name").put("type", "string"),
                    JSONObject().put("name", "chainId").put("type", "uint256"),
                    JSONObject().put("name", "verifyingContract")
                        .put("type", "address")
                )
            )
        )
        result.put("types", types)

        val domain = original.getJSONObject("domain")
        domain.put("chainId", domain.getDouble("chainId").toInt().toString())
        domain.put("verifyingContract", domain.getString("verifyingContract").lowercase())
        result.put("domain", domain)

        result.put("primaryType", "PermitSingle")

        result.put("message", original.getJSONObject("values"))

        val details = result.getJSONObject("message").getJSONObject("details")
        details.put("token", details.getString("token").lowercase())
        result.getJSONObject("message")
            .put("spender", result.getJSONObject("message").getString("spender").lowercase())

        return result.toString()
    }
}