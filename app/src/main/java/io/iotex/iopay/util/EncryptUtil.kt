package io.iotex.iopay.util

import android.util.Base64
import java.nio.charset.Charset
import java.security.KeyFactory
import java.security.KeyStore
import java.security.KeyStoreException
import java.security.cert.CertificateException
import java.security.spec.MGF1ParameterSpec
import java.security.spec.X509EncodedKeySpec
import javax.crypto.Cipher
import javax.crypto.spec.OAEPParameterSpec
import javax.crypto.spec.PSource

const val IOTX_KEYSTORE_ALIAS = "IOTX"
const val ANDROID_KEY_STORE = "AndroidKeyStore"
const val TRANSFORMATION_CIPHER = "RSA/ECB/OAEPWithSHA-256AndMGF1Padding"

object EncryptUtil {

    fun encrypt(source: String): String {
        val keyStore = KeyStore.getInstance(ANDROID_KEY_STORE)
        keyStore.load(null)
        if (!keyStore.containsAlias(IOTX_KEYSTORE_ALIAS)) {
            throw KeyStoreException("The application keystore has not been initialized")
        }

        val certificate = keyStore.getCertificate(IOTX_KEYSTORE_ALIAS)
            ?: throw KeyStoreException("Load certificate error")

        val key = certificate.publicKey
        val unrestrictedPublicKey = KeyFactory.getInstance(key.algorithm).generatePublic(
            X509EncodedKeySpec(key.encoded)
        )
        val spec = OAEPParameterSpec(
            "SHA-256", "MGF1",
            MGF1ParameterSpec.SHA1, PSource.PSpecified.DEFAULT
        )

        val cipher = Cipher.getInstance(TRANSFORMATION_CIPHER)
        cipher.init(Cipher.ENCRYPT_MODE, unrestrictedPublicKey, spec)
        return Base64.encodeToString(cipher.doFinal(source.toByteArray()), Base64.NO_WRAP)
    }

    fun decrypt(encryptedStr: String): String {
        val keyStore = KeyStore.getInstance(ANDROID_KEY_STORE)
        keyStore.load(null)
        if (!keyStore.containsAlias(IOTX_KEYSTORE_ALIAS)) {
            throw KeyStoreException("The application keystore has not been initialized")
        }

        val entry = keyStore.getEntry(IOTX_KEYSTORE_ALIAS, null)
        if (entry !is KeyStore.PrivateKeyEntry) {
            throw CertificateException("Not an instance of a PrivateKeyEntry")
        }

        val cipher = Cipher.getInstance(TRANSFORMATION_CIPHER)
        cipher.init(Cipher.DECRYPT_MODE, entry.privateKey)
        val str = Base64.decode(encryptedStr, Base64.NO_WRAP)
        return cipher.doFinal(str).toString(Charset.defaultCharset())
    }

}