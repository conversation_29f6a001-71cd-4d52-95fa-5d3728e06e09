package io.iotex.iopay.util

import android.os.Bundle
import com.google.firebase.analytics.FirebaseAnalytics
import io.iotex.iopay.IoPayApplication
import io.iotex.iopay.data.db.ACTION_TYPE_BID
import io.iotex.iopay.data.db.ACTION_TYPE_CLAIM
import io.iotex.iopay.data.db.ACTION_TYPE_DAPP
import io.iotex.iopay.data.db.ACTION_TYPE_DELEGATE
import io.iotex.iopay.data.db.ACTION_TYPE_DEPOSIT_STAKE
import io.iotex.iopay.data.db.ACTION_TYPE_NFT
import io.iotex.iopay.data.db.ACTION_TYPE_RESTAKE
import io.iotex.iopay.data.db.ACTION_TYPE_STAKE
import io.iotex.iopay.data.db.ACTION_TYPE_SWAP
import io.iotex.iopay.data.db.ACTION_TYPE_TRANSFER
import io.iotex.iopay.data.db.ACTION_TYPE_TRANSFER_BUCKET
import io.iotex.iopay.data.db.ACTION_TYPE_UNSTAKE
import io.iotex.iopay.data.db.ACTION_TYPE_WITHDRAW
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch

object FireBaseUtil {
    const val CLICK_TIME = "click_time"
    const val DAPP_URL = "dapp_url"
    const val DAPP_NAME = "dapp_name"
    const val START_TIME = "start_time"
    const val END_TIME = "end_time"
    const val NAME = "name"

    /**
     * add point base func
     *
     * @param tag String key
     */
    fun logFireBase(tag: String) {
        val firebaseAnalytics = FirebaseAnalytics.getInstance(IoPayApplication.getInstance())
        logFireBase(tag, firebaseAnalytics, null)
    }

    /**
     * add point base func
     *
     * @param tag String key
     * @param firebase FirebaseAnalytics object
     */
    fun logFireBase(tag: String, firebase: FirebaseAnalytics) {
        logFireBase(tag, firebase, null)
    }

    fun logFireBase(tag: String, bundle: Bundle?) {
        val firebaseAnalytics = FirebaseAnalytics.getInstance(IoPayApplication.getInstance())
        logFireBase(tag, firebaseAnalytics, bundle)
    }

    /**
     * add point with custom param
     *
     * @param tag String key
     * @param firebase FirebaseAnalytics object
     * @param bundle Bundle? param
     */
    private fun logFireBase(tag: String, firebase: FirebaseAnalytics, bundle: Bundle?) {
        GlobalScope.launch(Dispatchers.IO) {
            var keyString: String = tag
            if (tag.length > 40) {
                keyString = tag.substring(0, 40)
            }
            val firebaseBundle: Bundle = if (bundle != null) {
                Bundle(bundle)
            } else {
                Bundle()
            }
            firebaseBundle.putLong(CLICK_TIME, System.currentTimeMillis())
            firebaseBundle.putInt("chainID", WalletHelper.getCurChainId())
            firebase.logEvent(keyString.lowercase(), firebaseBundle)
        }
    }
}

fun transferTypeToEvent(type: Int): String {
    return when (type) {
        ACTION_TYPE_TRANSFER -> "TRANSFER"
        ACTION_TYPE_DAPP -> "DAPP"
        ACTION_TYPE_SWAP -> "SWAP"
        ACTION_TYPE_STAKE, ACTION_TYPE_RESTAKE, ACTION_TYPE_DEPOSIT_STAKE, ACTION_TYPE_UNSTAKE -> "STAKE"
        ACTION_TYPE_CLAIM -> "CLAIM"
        ACTION_TYPE_DELEGATE -> "DELEGATE"
        ACTION_TYPE_NFT -> "NFT"
        ACTION_TYPE_BID -> "BID"
        ACTION_TYPE_TRANSFER_BUCKET -> "BUCKET"
        ACTION_TYPE_WITHDRAW -> "WITHDRAW"
        else -> "UNKNOWN"
    }
}
