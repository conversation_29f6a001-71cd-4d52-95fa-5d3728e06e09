package io.iotex.iopay.util

import android.annotation.SuppressLint
import android.content.Context
import android.location.*
import android.os.Bundle
import com.blankj.utilcode.util.Utils
import com.machinefi.w3bstream.utils.extension.formatDecimal
import io.iotex.iopay.util.extension.asBigDecimal
import io.iotex.iopay.util.extension.e
import io.iotex.iopay.util.extension.i
import java.math.BigDecimal
import java.util.*

object GPSUtil {
    private val mLocationListener: LocationListener = object : LocationListener {
        override fun onStatusChanged(provider: String, status: Int, extras: Bundle) {
            "onStatusChanged".i()
        }

        override fun onProviderEnabled(provider: String) {
            "onProviderEnabled".i()
        }

        override fun onProviderDisabled(provider: String) {
            "onProviderDisabled".i()
        }

        override fun onLocationChanged(location: Location) {}
    }

    @SuppressLint("MissingPermission")
    fun getLocation(): Location? {
        var location: Location? = null
        try {
            val locationManager =
                Utils.getApp().getSystemService(Context.LOCATION_SERVICE) as LocationManager
            if (locationManager.isProviderEnabled(LocationManager.GPS_PROVIDER)) {
                location = locationManager.getLastKnownLocation(LocationManager.GPS_PROVIDER)
                if (location == null) {
                    location = getLocationByNetwork()
                }
            } else {
                location = getLocationByNetwork()
            }
        } catch (e: Exception) {
            e.message?.e()
        }
        return location?.let {
            if (isMockLocation(it)) null else it
        }
    }

    fun isLocationProviderEnabled(): Boolean {
        var result = false
        val locationManager =
            Utils.getApp().getSystemService(Context.LOCATION_SERVICE) as LocationManager
        if (locationManager.isProviderEnabled(LocationManager.GPS_PROVIDER) ||
            locationManager.isProviderEnabled(LocationManager.NETWORK_PROVIDER)
        ) {
            result = true
        }
        return result
    }

    private fun isMockLocation(location: Location): Boolean {
        return location.isFromMockProvider
    }

    @SuppressLint("MissingPermission")
    private fun getLocationByNetwork(): Location? {
        var location: Location? = null
        val locationManager =
            Utils.getApp().getSystemService(Context.LOCATION_SERVICE) as LocationManager
        try {
            if (locationManager.isProviderEnabled(LocationManager.NETWORK_PROVIDER)) {
                locationManager.requestLocationUpdates(
                    LocationManager.NETWORK_PROVIDER,
                    1000,
                    0f,
                    mLocationListener
                )
                location = locationManager.getLastKnownLocation(LocationManager.NETWORK_PROVIDER)
            }
        } catch (e: Exception) {
            e.message?.e()
        }
        return location
    }

    fun getAddress(location: Location): List<Address> {
        return runCatching {
            val gc = Geocoder(Utils.getApp(), Locale.getDefault())
            gc.getFromLocation(
                location.latitude,
                location.longitude, 1
            )
        }.getOrNull() ?: emptyList<Address>()
    }

    fun encodeLocation(value: Double, decimal: Int): Long {
        val v = value.formatDecimal(decimal)
        return v.asBigDecimal().multiply(BigDecimal.TEN.pow(7)).toLong()
    }

    fun decodeLocation(value: Long): String {
        return BigDecimal(value).divide(BigDecimal.TEN.pow(7)).toPlainString()
    }

}