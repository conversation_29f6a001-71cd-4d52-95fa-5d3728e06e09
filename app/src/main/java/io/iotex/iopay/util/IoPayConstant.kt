package io.iotex.iopay.util

class IoPayConstant {

    companion object {

        const val BROWSER_URL = "BROWSER_URL"
        const val BROWSER_DAPP_NAME = "BROWSER_DAPP_NAME"
        const val BROWSER_DAPP_LOGO = "BROWSER_DAPP_LOGO"
        const val BROWSER_SCREEN_NAME = "BROWSER_SCREEN_NAME"
        const val X_APP_TITLE = "X_APP_TITLE"
        const val DEVICE_TOKEN = "DEVICE_TOKEN"

        const val IOTX = "IOTX"

        const val AGREEMENT = "AGREEMENT"


        const val ACTION_TYPE = "action"
        const val WEB_URL = "url"

        const val BANNER_CLOSE = "home_banner_close"

        const val PUSH_HASH = "PUSH_HASH"
        const val PUSH_TYPE = "PUSH_TYPE"

    }
}