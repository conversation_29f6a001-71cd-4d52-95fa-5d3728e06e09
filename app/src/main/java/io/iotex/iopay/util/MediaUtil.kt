package io.iotex.iopay.util

import android.content.ContentUris
import android.content.Context
import android.database.Cursor
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.net.Uri
import android.provider.MediaStore
import com.blankj.utilcode.util.Utils
import com.machinefi.walletconnect2.WC2Helper
import io.iotex.iopay.R
import org.web3j.crypto.WalletUtils

object MediaUtil {
    fun getImageBitMap(context:Context,externalContentUri: Uri): Bitmap? {
        var bitmap: Bitmap? = null
        try {
            val cursor: Cursor? = context.contentResolver.query(externalContentUri,
                null, null, null, null)
            if (cursor != null) {
                if (cursor.moveToFirst()) {
                    val id = cursor.getLong(cursor.getColumnIndexOrThrow(MediaStore.MediaColumns._ID))
                    val uri = ContentUris.withAppendedId(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, id)
                    val openFileDescriptor = context.contentResolver.openFileDescriptor(uri, "r")
                    openFileDescriptor?.apply {
                        bitmap = BitmapFactory.decodeFileDescriptor(fileDescriptor)
                    }
                    openFileDescriptor?.close()
                }
                cursor.close()
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return bitmap
    }

    fun getScanType(content:String):String{
        return if (WalletHelper.isValidAddress(content)) {
            Utils.getApp().getString(R.string.found_address)
        } else if (WC2Helper.isValidWc2Bridge(content)) {
            Utils.getApp().getString(R.string.wallet_connect)
        } else if (WalletUtils.isValidPrivateKey(content)) {
            Utils.getApp().getString(R.string.private_key)
        } else {
            Utils.getApp().getString(R.string.content)
        }
    }
}