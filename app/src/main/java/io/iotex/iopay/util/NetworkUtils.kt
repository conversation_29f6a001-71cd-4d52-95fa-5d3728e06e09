package io.iotex.iopay.util

import android.content.Context
import android.net.ConnectivityManager
import android.net.NetworkInfo
import android.net.Uri
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.SPUtils
import com.blankj.utilcode.util.ShellUtils
import io.iotex.iopay.data.db.NETWORK_GENERAL
import io.iotex.iopay.data.db.NETWORK_POOR
import io.iotex.iopay.data.db.NETWORK_SMOOTH
import io.iotex.iopay.data.db.NetworkLevel
import io.iotex.iopay.util.extension.i
import io.iotex.iopay.util.extension.toJson
import io.iotex.iopay.wallet.dialog.NetworkAnomalyDialog
import kotlinx.coroutines.*

object NetworkUtils {

    private var anomalyShowing = false
    private var PING_TIMES = 2

    private fun getActiveNetworkInfo(context: Context): NetworkInfo? {
        val cm = context
            .getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        return cm.activeNetworkInfo
    }

    fun isConnected(context: Context): Boolean {
        val info = getActiveNetworkInfo(context)
        return info != null && info.isConnected
    }

    fun getDomain(url: String): String {
        return runCatching {
            Uri.parse(url).host
        }.getOrNull() ?: ""
    }

    suspend fun pingNetwork(host: String): NetworkStatus {
        var result: ShellUtils.CommandResult
        var times = 1
        do {
            times++
            result = withContext(Dispatchers.IO) {
                ShellUtils.execCmd(String.format("ping -c 1 %s", host), false)
            }
        } while (times <= PING_TIMES && result.result != 0)
        "pingNetwork available: ${result}".i()
        if (result.result != 0) {
            return NetworkStatus(false, -1, NETWORK_POOR)
        }
        return runCatching {
            val msg = result.successMsg
            if (msg.contains("avg")) {
                val i = msg.indexOf("/", 20)
                val j = msg.indexOf(".", i)
                val delay = msg.substring(i + 1, j).split("=")[1].trim().toLong()
                "pingNetwork delay: $delay".i()
                val level = if (delay < 1000) {
                    NETWORK_SMOOTH
                } else {
                    NETWORK_GENERAL
                }
                return NetworkStatus(true, delay, level)
            }
            NetworkStatus(true, -1, NETWORK_POOR)
        }.getOrNull() ?: NetworkStatus(true, -1, NETWORK_POOR)
    }

    fun checkNetwork(url: String) {
        val errorHandler = CoroutineExceptionHandler { _, e ->
            e.printStackTrace()
        }
        MainScope().launch(errorHandler) {
            val signHostStatus = withContext(Dispatchers.IO) {
                val signHost = getDomain(url)
                pingNetwork(signHost)
            }
            if (!signHostStatus.available || signHostStatus.status == NETWORK_POOR) {
                val context = ActivityUtils.getTopActivity() ?: return@launch
                if (!anomalyShowing) {
                    anomalyShowing = true
                    val time = SPUtils.getInstance().getLong(SPConstant.SP_NETWORK_TIPS_UNDERSTOOD_TIME)
                    if (System.currentTimeMillis() - time > 24 * 3600 * 1000) {
                        NetworkAnomalyDialog(context)
                            .setDismissCallback { anomalyShowing = false }
                            .show()
                    }
                }
            }
        }
    }
}

data class NetworkStatus(
    val available: Boolean,
    val delay: Long,
    @NetworkLevel val status: Int
)