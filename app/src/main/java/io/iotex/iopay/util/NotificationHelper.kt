package io.iotex.iopay.util

import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.graphics.BitmapFactory
import android.os.Build
import androidx.core.app.NotificationCompat
import androidx.core.app.NotificationManagerCompat
import com.blankj.utilcode.util.DeviceUtils
import com.blankj.utilcode.util.LogUtils
import com.blankj.utilcode.util.SPUtils
import com.google.firebase.messaging.FirebaseMessaging
import io.iotex.iopay.BuildConfig
import io.iotex.iopay.IoPayApplication
import io.iotex.iopay.MainActivity
import io.iotex.iopay.R
import io.iotex.iopay.reactnative.ReactNativeActivity
import io.iotex.iopay.reactnative.ReactScene
import io.iotex.iopay.xapp.XAppsActivity

object NotificationHelper {

    fun createNotificationChannel(context: Context) {
        // Create the NotificationChannel, but only on API 26+ because
        // the NotificationChannel class is new and not in the support library
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val name = context.getString(R.string.iopay_channel_name)
            val descriptionText = context.getString(R.string.channel_description)
            val importance = NotificationManager.IMPORTANCE_DEFAULT
            val channel = NotificationChannel(
                IoPayApplication.getAppChannelId(context),
                name,
                importance
            ).apply {
                description = descriptionText
            }
            val notificationManager: NotificationManager =
                context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
        }
    }

    fun showNotification(
        context: Context,
        title: String,
        content: String,
        data: Map<String, String>,
        isVisitor: Boolean
    ) {
        val mainIntent = Intent(context, MainActivity::class.java)
        var intent = Intent(context, MainActivity::class.java)
        intent.putExtra(MainActivity.KEY_SHOW_LOCK, true)
        intent.putExtra(MainActivity.KEY_IS_VISITOR, isVisitor)
        if (!data["link"].isNullOrEmpty()) {
            intent = Intent(context, XAppsActivity::class.java)
            intent.putExtra(IoPayConstant.BROWSER_URL, data["link"])
        } else if (!data["msg_id"].isNullOrEmpty()) {
            intent = Intent(context, ReactNativeActivity::class.java)
            intent.putExtra(
                ReactNativeActivity.REACT_COMPONENT_NAME,
                ReactScene.NotificationDetail.name
            )
            intent.putExtra("id", data["msg_id"])
        } else if (!data["actHash"].isNullOrEmpty()) {
            intent = Intent(context, XAppsActivity::class.java)
            intent.putExtra(IoPayConstant.BROWSER_URL, "https://iotexscan.io/tx/" + data["actHash"])
        }
        val pendingIntent = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            PendingIntent.getActivities(
                context,
                0,
                arrayOf(mainIntent, intent),
                PendingIntent.FLAG_MUTABLE
            )
        } else {
            PendingIntent.getActivities(
                context,
                0,
                arrayOf(mainIntent, intent),
                PendingIntent.FLAG_IMMUTABLE
            )
        }

        val builder = NotificationCompat.Builder(context, IoPayApplication.getAppChannelId(context))
            .setSmallIcon(R.mipmap.ic_app_logo)
            .setLargeIcon(
                BitmapFactory.decodeResource(
                    context.resources,
                    R.mipmap.ic_app_logo
                )
            )
            .setContentTitle(title)
            .setContentText(content)
            .setPriority(NotificationCompat.PRIORITY_DEFAULT)
            .setContentIntent(pendingIntent)
            .setAutoCancel(true)

        with(NotificationManagerCompat.from(context)) {
            // notificationId is a unique int for each notification that you must define
            notify(1001, builder.build())
        }
    }

    fun registerFirebaseMessaging(callback: ((String?) -> Unit)? = null) {
        if (!BuildConfig.AUTO_UPDATE_APK) {
            FirebaseMessaging.getInstance().token
                .addOnCompleteListener { task ->
                    LogUtils.i("token:${task.isSuccessful}")
                    if (!task.isSuccessful) {
                        callback?.invoke(null)
                        return@addOnCompleteListener
                    }
                    val token = task.result
                    callback?.invoke(token)
                    SPUtils.getInstance().put(IoPayConstant.DEVICE_TOKEN, token)
                }.addOnFailureListener {
                    LogUtils.i("token:${it.message}")
                    val uniqueId = DeviceUtils.getUniqueDeviceId()
                    callback?.invoke(uniqueId)
                    SPUtils.getInstance().put(IoPayConstant.DEVICE_TOKEN, uniqueId)
                }
        } else {
            val uniqueId = DeviceUtils.getUniqueDeviceId()
            callback?.invoke(uniqueId)
            SPUtils.getInstance().put(IoPayConstant.DEVICE_TOKEN, uniqueId)
        }
    }
}
