package io.iotex.iopay.util

import io.iotex.base.RetrofitClient
import io.iotex.iopay.api.UrlApi
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

object PageEventUtil {
    const val STARTHOME = "starthome"
    const val ADDWALLET = "addwallet"
    const val CREATEWALLET = "createwallet"
    const val IMPORTWALLET = "importwallet"
    const val IMPORTPRIVATEKEY = "importprivatekey"
    const val IMPORTRECOVERY = "importrecovery"
    const val IMPORTKEYSTORE = "importkeystore"
    const val WATCHMODEADDRESS = "watchmodeaddress"
    const val ADDAAWALLET = "addaawallet"
    const val CREATEAAWALLET = "createaawallet"
    const val RECOVERAAWALLET = "recoveraawallet"
    const val MENUWALLET = "menuwallet"
    const val MYWALLETS = "mywallets"
    const val SEND = "send"
    const val RECEIVE = "receive"
    const val TOKEN = "token"
    const val ADDXRC20 = "addXRC20"
    const val CUSTOMTOKEN = "customtoken"
    const val NFTS = "nfts"
    const val NFTCOLLECTION = "nftcollection"
    const val NFTDETAIL = "nftdetail"
    const val BACKUP = "backup"
    const val ACTIVITIES = "activities"
    const val MENUDEPINSCAN = "menudepinscan"
    const val MENUBROWSER = "menubrowser"
    const val MENUEXPLORER = "menuexplorer"
    const val MENUSETTINGS = "menusettings"
    const val NETWORK  = "network "
    const val ADDNETWORK  = "addnetwork "
    const val CUSTOMNETWORK  = "customnetwork "
    const val GENERAL  = "general "
    const val MANAGEWALLETS  = "managewallets "
    const val MANAGERECOVERYPHRASE  = "managerecoveryphrase "
    const val ADDRESSBOOK = "addressbook"
    const val ADDADDRESS = "addaddress"
    const val SECURITYPRIVACY = "securityprivacy"
    const val ABOUTIOPAY = "aboutiopay"
    const val GEOLOCATION = "geolocation"
    private val apiService by lazy {
        RetrofitClient.createApiService(Config.IOPAY_URL, UrlApi::class.java)
    }

    fun logEvent(page:String,param: HashMap<String,String>? = null){
        CoroutineScope(Dispatchers.IO).launch {
            runCatching {
                var paramString = ""
                param?.forEach {
                    paramString += "&${it.key}=${it.value}"
                }
                val map = mutableMapOf(
                    "name" to "pageview",
                    "url" to "http://iopay.android/$page?chainId=${WalletHelper.getCurChainId()}$paramString",
                    "domain" to "iopay.android"
                )
                apiService.postFirebaseEvent(Config.FIREBASE_EVENT_URL, map)
            }
        }

    }
}