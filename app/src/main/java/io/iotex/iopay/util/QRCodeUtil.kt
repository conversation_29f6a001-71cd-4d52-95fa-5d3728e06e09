package io.iotex.iopay.util

import android.content.ContentValues
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.Color
import android.net.Uri
import android.os.Build
import android.os.Environment
import android.provider.MediaStore
import android.text.TextUtils
import androidx.annotation.ColorInt
import androidx.annotation.Nullable
import com.google.zxing.*
import com.google.zxing.common.GlobalHistogramBinarizer
import com.google.zxing.common.HybridBinarizer
import com.blankj.utilcode.util.FileUtils
import com.blankj.utilcode.util.ImageUtils
import com.blankj.utilcode.util.Utils
import com.google.zxing.BarcodeFormat
import com.google.zxing.EncodeHintType
import com.google.zxing.WriterException
import com.google.zxing.qrcode.QRCodeWriter
import java.util.*
import java.io.File
import java.io.FileNotFoundException
import java.io.IOException
import java.io.OutputStream
import kotlin.collections.HashMap

object QRCodeUtil {
    @Nullable
    @JvmOverloads
    fun createQRCodeBitmap(
        content: String,
        width: Int,
        height: Int,
        @Nullable characterSet: String = "UTF-8",
        @Nullable errorCorrection: String = "H",
        @Nullable margin: String = "2",
        @ColorInt colorBlack: Int = Color.BLACK,
        @ColorInt colorWhite: Int = Color.WHITE
    ): Bitmap? {

        val TAG: String = QRCodeUtil::class.java.simpleName

        if (TextUtils.isEmpty(content)) {
            return null
        }

        if (width < 0 || height < 0) {
            return null
        }

        try {
            val hints = HashMap<EncodeHintType, String>()

            if (!TextUtils.isEmpty(characterSet)) {
                hints.put(EncodeHintType.CHARACTER_SET, characterSet)
            }

            if (!TextUtils.isEmpty(errorCorrection)) {
                hints.put(EncodeHintType.ERROR_CORRECTION, errorCorrection)
            }

            if (!TextUtils.isEmpty(margin)) {
                hints.put(EncodeHintType.MARGIN, margin)
            }
            val bitMatrix = QRCodeWriter().encode(content, BarcodeFormat.QR_CODE, width, height, hints)

            val pixels = IntArray(width * height)
            for (y in 0 until height) {
                for (x in 0 until width) {
                    if (bitMatrix.get(x, y)) {
                        pixels[y * width + x] = colorBlack
                    } else {
                        pixels[y * width + x] = colorWhite
                    }
                }
            }

            val bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
            bitmap.setPixels(pixels, 0, width, 0, 0, width, height)
            return bitmap
        } catch (e: WriterException) {
            e.printStackTrace()
        }

        return null
    }
    val HINTS: MutableMap<DecodeHintType, Any?> = EnumMap<DecodeHintType, Any>(DecodeHintType::class.java)
    fun syncDecodeQRCode(bitmap: Bitmap): String? {
        var result: Result? = null
        var source: RGBLuminanceSource? = null
        return try {
            val width = bitmap.width
            val height = bitmap.height
            val pixels = IntArray(width * height)
            bitmap.getPixels(pixels, 0, width, 0, 0, width, height)
            source = RGBLuminanceSource(width, height, pixels)
            result = MultiFormatReader().decode(BinaryBitmap(HybridBinarizer(source)), HINTS)
            result.text
        } catch (e: Exception) {
            e.printStackTrace()
            if (source != null) {
                try {
                    result = MultiFormatReader().decode(BinaryBitmap(GlobalHistogramBinarizer(source)), HINTS)
                    return result.text
                } catch (e2: Throwable) {
                    e2.printStackTrace()
                }
            }
            null
        }
    }
    init {
        val allFormats = mutableListOf<BarcodeFormat>()
        allFormats.add(BarcodeFormat.AZTEC)
        allFormats.add(BarcodeFormat.CODABAR)
        allFormats.add(BarcodeFormat.CODE_39)
        allFormats.add(BarcodeFormat.CODE_93)
        allFormats.add(BarcodeFormat.CODE_128)
        allFormats.add(BarcodeFormat.DATA_MATRIX)
        allFormats.add(BarcodeFormat.EAN_8)
        allFormats.add(BarcodeFormat.EAN_13)
        allFormats.add(BarcodeFormat.ITF)
        allFormats.add(BarcodeFormat.MAXICODE)
        allFormats.add(BarcodeFormat.PDF_417)
        allFormats.add(BarcodeFormat.QR_CODE)
        allFormats.add(BarcodeFormat.RSS_14)
        allFormats.add(BarcodeFormat.RSS_EXPANDED)
        allFormats.add(BarcodeFormat.UPC_A)
        allFormats.add(BarcodeFormat.UPC_E)
        allFormats.add(BarcodeFormat.UPC_EAN_EXTENSION)
        HINTS[DecodeHintType.TRY_HARDER] = BarcodeFormat.QR_CODE
        HINTS[DecodeHintType.POSSIBLE_FORMATS] = allFormats
        HINTS[DecodeHintType.CHARACTER_SET] = "utf-8"
    }
    fun savePhoto(bitmap: Bitmap, name: String): Uri? {
        val mimeType = "image/jpeg"
        val compressFormat =
                Bitmap.CompressFormat.JPEG
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            val path: String =
                    Environment.DIRECTORY_PICTURES + File.separator.toString() + "iopay/"
            val contentValues = ContentValues()
            contentValues.put(MediaStore.MediaColumns.DISPLAY_NAME, name)
            contentValues.put(MediaStore.MediaColumns.MIME_TYPE, mimeType)
            contentValues.put(MediaStore.MediaColumns.RELATIVE_PATH, path)
            val uri: Uri? =
                    Utils.getApp().contentResolver.insert(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, contentValues)
            if (uri != null) {
                try {
                    val outputStream: OutputStream? = Utils.getApp().contentResolver.openOutputStream(uri)
                    if (outputStream != null) {
                        bitmap.compress(compressFormat, 100, outputStream)
                    }
                    outputStream?.close()
                    return uri
                } catch (e: FileNotFoundException) {
                    e.printStackTrace()
                } catch (e: IOException) {
                    e.printStackTrace()
                } finally {
                    bitmap.recycle()
                }
            }
        } else {
            val file = File(Environment.getExternalStorageDirectory().absolutePath + File.separator + "iopay" + File.separator + "image" + File.separator + name)
            FileUtils.createOrExistsFile(file)
            return if (ImageUtils.save(bitmap, file, compressFormat)) {
                val uri = Uri.fromFile(file)
                Utils.getApp().sendBroadcast(Intent(Intent.ACTION_MEDIA_SCANNER_SCAN_FILE, uri))
                uri
            } else {
                null
            }
        }
        return null
    }
}
