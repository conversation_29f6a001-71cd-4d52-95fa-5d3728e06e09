package io.iotex.iopay.util

import android.text.TextUtils
import android.webkit.URLUtil
import java.util.regex.Pattern

object RegexUtils {
    private const val REGEX_LINK_URL = "((http[s]{0,1}|ftp)://[a-zA-Z0-9\\.\\-]+\\.([a-zA-Z]{2,4})(:\\d+)?(/[a-zA-Z0-9\\.\\-~!@#$%^&*+?:_/=<>]*)?)|((www.)?[a-zA-Z0-9\\.\\-]+\\.([a-zA-Z]{2,8})(:\\d+)?(/[a-zA-Z0-9\\.\\-~!@#$%^&*+?:_/=<>]*)?)"

    fun isXAppsURl(searchValues: String?): Boolean {
        return Regular(searchValues, REGEX_LINK_URL) || URLUtil.isValidUrl(searchValues)
    }
    fun isLinkURl(searchValues: String?): Bo<PERSON>an {
        return Regular(searchValues, REGEX_LINK_URL)
    }

    private fun Regular(str: String?, pattern: String): Boolean {
        if (TextUtils.isEmpty(str)) {
            return false
        }
        val p = Pattern.compile(pattern)
        val m = p.matcher(str)
        return m.matches()
    }
}
