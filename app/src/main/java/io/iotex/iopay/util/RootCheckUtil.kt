package io.iotex.iopay.util

import java.io.File

object RootCheckUtil {
    fun isRoot():Boolean{
        val strArr =
            arrayOf("/system/bin/", "/system/xbin/", "/system/sbin/", "/sbin/", "/vendor/bin/")
        var i = 0
        while (i < 5) {
            try {
                if (File(strArr[i] + "su").exists()) {
                    return true
                }
                i++
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
        return false
    }

}