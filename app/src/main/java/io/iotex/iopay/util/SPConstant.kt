package io.iotex.iopay.util

object SPConstant {
    const val SP_BALANCES_IS_HIDE = "sp_balances_is_hide"
    const val SP_DEVELOPER_MODE = "sp_developer_mode"

    const val SP_RPC_NETWORK_URL = "sp_rpc_network_url"

    const val SP_RPC_NETWORK_NAME = "sp_rpc_network_name"
    const val SP_RPC_NETWORK_NATIVE_CURRENCY = "sp_rpc_network_native_currency"
    const val SP_INITIAL_GUIDE = "sp_initial_guide"
    const val SP_GUIDE_STEP = "sp_guide_step"
    const val SP_INSTALL_SOURCE = "sp_install_source"
    const val SP_UPDATE_WALLET_TIME = "sp_update_wallet_record_time"
    const val SP_GOOGLE_REVIEW_VERSION = "sp_google_review_version"
    const val SP_FIRST_SWITCH_NET = "sp_first_switch_net"
    const val SP_WALLET_AVATAR = "sp_wallet_avatar"
    const val SP_FIRST_SELECT_CUSTOM_NETWORK = "sp_first_select_custom_network"

    const val SP_DAPP_CATEGORIES_QUERY_HOT = "sp_dapp_categories_query_hot_fix"
    const val SP_DAPP_BANNER_QUERY = "sp_dapp_banner_query_bean"

    const val SP_DAPP_ENTER_TIPS_UNDERSTOOD = "sp_dapp_enter_tips_understood"
    const val SP_FILDA_ENTER_TIPS_UNDERSTOOD = "sp_filda_enter_tips_understood"
    const val SP_BEDROCK_ENTER_TIPS_UNDERSTOOD = "sp_bedrock_enter_tips_understood"
    const val SP_BUY_ENTER_TIPS_UNDERSTOOD = "sp_buy_enter_tips_understood"
    const val SP_NETWORK_TIPS_UNDERSTOOD_TIME = "sp_network_tips_understood_time"

    const val SP_HOME_POST_EVENT_TIME = "sp_home_post_event_time"

    const val TRANSFER_TIPS_DIALOG = "transfer_tips_dialog"
    const val SP_INSTALL_AND_START_FOR_THE_FIRST_TIME = "sp_install_and_start_for_the_first_time"
    const val SP_ACTION_USER_REINSTALL = "sp_action_user_reinstall"
    const val SP_NOTIFICATION_OPTION_BEFORE = "sp_notification_option_before"

    const val SP_WALLET_REVEAL_GUIDE_STEP = "sp_wallet_reveal_guide_step"
    const val SP_INS_NAME_OF = "sp_ins_name_of"

    const val SP_ADVANCED_BASE_FEE = "sp_advanced_base_fee"
    const val SP_ADVANCED_PRIORITY_FEE = "sp_advanced_priority_fee"

    const val SP_BITCOIN_CUSTOMIZE_FEE = "sp_bitcoin_customize_fee"
    const val SP_MNEMONIC_CUSTOMIZE_PATH = "sp_mnemonic_customize_path"
    const val SP_GIFT_UPDATE_TASK = "sp_gift_update_task"
    const val SP_GIFT_INVITED_BEFORE = "sp_gift_invited_before"

}