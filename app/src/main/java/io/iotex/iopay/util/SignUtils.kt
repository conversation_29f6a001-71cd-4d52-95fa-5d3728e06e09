package io.iotex.iopay.util

import android.content.Context
import com.blankj.utilcode.util.Utils
import com.google.firebase.crashlytics.FirebaseCrashlytics
import io.iotex.iopay.data.db.AppDatabase
import io.iotex.iopay.data.db.DApps
import io.iotex.iopay.data.db.DiscoverDApps
import org.jetbrains.anko.doAsync

object SignUtils {
    const val TIME_LOGIN_TOKEN_EXPIRE = 20 * 24 * 3600 * 1000
    fun recordDAppRecord(context: Context?, dapp: DApps?) {
        try {
            doAsync {
                context?.let {
                    dapp?.let { dApp ->
                        val lastTime = System.currentTimeMillis()
                        var day = ((lastTime - dApp.lastTime) / (24 * 60 * 60 * 1000)).toInt()
                        if (day == 0) {
                            day = 1
                        }
                        val isExistsDapp =
                            AppDatabase.getInstance(Utils.getApp()).dAppSort().find(dApp.url)
                        if (isExistsDapp != null) {
                            AppDatabase.getInstance(it).dAppSort().updateDAppSort(
                                DApps(
                                    dApp.url,
                                    dApp.title,
                                    dApp.logo,
                                    dApp.named,
                                    dApp.category,
                                    dApp.content,
                                    dApp.contentCN,
                                    lastTime,
                                    day,
                                    isExistsDapp.count + 1,
                                    (5 / day + (isExistsDapp.count + 1) * 0.5),
                                    dApp.weight,
                                    dApp.chains
                                )
                            )
                        } else {
                            AppDatabase.getInstance(it).dAppSort().insertIfNonExist(
                                DApps(
                                    dApp.url,
                                    dApp.title,
                                    dApp.logo,
                                    dApp.named,
                                    dApp.category,
                                    dApp.content,
                                    dApp.contentCN,
                                    System.currentTimeMillis(),
                                    1,
                                    0,
                                    0.0,
                                    dApp.weight,
                                    dapp.chains
                                )
                            )
                        }

                    }
                }
            }
        } catch (e: Exception) {
            FirebaseCrashlytics.getInstance().recordException(e)
        }
    }

    fun recordDAppRecord(context: Context?, dappV3: DiscoverDApps) {
        val dapp = DApps(
            dappV3.url,
            dappV3.title,
            dappV3.img_url,
            false,
            dappV3.tags,
            dappV3.content,
            dappV3.content_cn,
            System.currentTimeMillis(),
            1,
            0,
            0.0,
            dappV3.weight as Int? ?: 0,
            dappV3.chains
        )
        recordDAppRecord(context, dapp)
    }

    fun transDAppsToDiscoverDApps(dApp:DApps): DiscoverDApps{
        return DiscoverDApps(dApp.url,dApp.logo,dApp.contentCN,dApp.content,dApp.chains,
            "",dApp.category,dApp.title,"",dApp.weight)
    }
}