package io.iotex.iopay.util

import android.content.Context
import android.content.Intent
import android.content.pm.ResolveInfo

object SystemUtil {

    fun findTargetApp(context: Context, appName: String): String? {
        val packageManager = context.packageManager
        val intent = Intent(Intent.ACTION_MAIN, null)
        intent.addCategory(Intent.CATEGORY_LAUNCHER)
        val resolveInfos = packageManager.queryIntentActivities(intent, 0)
        return resolveInfos.firstOrNull {
            appName.equals(it.loadLabel(packageManager).toString(), true)
        }?.activityInfo?.packageName
    }

    fun openTargetByPkg(context: Context, pkgName: String) {
        val intent = context.packageManager.getLaunchIntentForPackage(pkgName)
        context.startActivity(intent)
    }
}