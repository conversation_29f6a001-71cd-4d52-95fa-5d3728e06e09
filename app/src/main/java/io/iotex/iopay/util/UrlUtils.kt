package io.iotex.iopay.util

import android.net.Uri
import android.text.TextUtils

object UrlUtils {
    fun getDomain(url: String): String {
        var domain = ""
        if (!TextUtils.isEmpty(url) && url.startsWith("http")) {
            try {
                val host = Uri.parse(url).host
                if (!TextUtils.isEmpty(host)) {
                    if (host != null) {
                        domain = host
                    }
                }
            } catch (ex: Exception) {
            }
        }
        return domain
    }
}