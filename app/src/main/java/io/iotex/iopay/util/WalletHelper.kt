package io.iotex.iopay.util

import android.app.Activity
import android.content.Context
import android.content.Intent
import androidx.appcompat.app.AppCompatActivity
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.EncryptUtils
import com.blankj.utilcode.util.ResourceUtils
import com.blankj.utilcode.util.SPUtils
import com.blankj.utilcode.util.TimeUtils
import com.blankj.utilcode.util.ToastUtils
import com.blankj.utilcode.util.Utils
import com.github.iotexproject.antenna.account.Account
import com.github.iotexproject.antenna.account.IotexAccount
import com.github.iotexproject.antenna.crypto.Bech32
import com.github.iotexproject.antenna.crypto.SecureRandomUtils
import com.github.iotexproject.antenna.keystore.CipherException
import com.github.iotexproject.antenna.keystore.KeystoreFile
import com.github.iotexproject.antenna.keystore.KeystoreUtils
import com.github.iotexproject.antenna.utils.Numeric
import com.google.firebase.crashlytics.FirebaseCrashlytics
import com.machinefi.lockscreen.LockAuthHelper
import com.solana.core.DerivationPath
import com.solana.core.HotAccount
import io.iotex.iopay.R
import io.iotex.iopay.data.UserStore
import io.iotex.iopay.data.db.AddressType_NativeSegwit
import io.iotex.iopay.data.db.AppDatabase
import io.iotex.iopay.data.db.BitcoinWallet
import io.iotex.iopay.data.db.RPCNetwork
import io.iotex.iopay.data.db.SolanaWallet
import io.iotex.iopay.data.db.Wallet
import io.iotex.iopay.support.enum.Web3AddressType
import io.iotex.iopay.support.eventbus.NetworkSwitchEvent
import io.iotex.iopay.support.eventbus.SwitchAllNetworkEvent
import io.iotex.iopay.support.eventbus.SwitchWalletEvent
import io.iotex.iopay.ui.ErrorPopupWindow
import io.iotex.iopay.ui.NoticePopupWindow
import io.iotex.iopay.util.Config.BITCOIN_MAIN_CHAIN_ID
import io.iotex.iopay.util.Config.BITCOIN_TEST_CHAIN_ID
import io.iotex.iopay.util.Config.IOTEX_CHAIN_ID
import io.iotex.iopay.util.Config.IOTEX_TEST_CHAIN_ID
import io.iotex.iopay.util.Config.SOLANA_MAIN_CHAIN_ID
import io.iotex.iopay.util.Config.SOLANA_TEST_CHAIN_ID
import io.iotex.iopay.util.Constant.AVATAR_THEME_BLOCKIES
import io.iotex.iopay.util.Constant.AVATAR_THEME_JAZZICONS
import io.iotex.iopay.util.Constant.AVATAR_THEME_ROBOTS
import io.iotex.iopay.util.SPConstant.SP_WALLET_AVATAR
import io.iotex.iopay.util.extension.toEvmAddress
import io.iotex.iopay.util.extension.toHexByteArray
import io.iotex.iopay.util.extension.toHexString
import io.iotex.iopay.util.extension.toHexStringNoPrefix
import io.iotex.iopay.util.extension.toast
import io.iotex.iopay.xapp.WebActivity
import io.iotex.iopay.xapp.XAppsActivity
import io.iotex.iopay.wallet.aawallet.dialog.WalletExpiredDialog
import io.iotex.iopay.wallet.add.PATH_BTC
import io.iotex.iopay.wallet.add.PathType
import io.iotex.iopay.wallet.bitcoin.BitcoinHelper
import io.iotex.iopay.wallet.dialog.NoteDialog
import io.iotex.iopay.wallet.list.SwitchWalletActivity
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.bitcoinj.core.Base58
import org.greenrobot.eventbus.EventBus
import org.jetbrains.anko.doAsync
import org.jetbrains.anko.startActivity
import org.jetbrains.anko.uiThread
import org.web3j.crypto.Bip32ECKeyPair
import org.web3j.crypto.MnemonicUtils
import org.web3j.crypto.Wallet.createLight
import org.web3j.crypto.WalletUtils
import org.web3j.utils.Numeric.cleanHexPrefix
import org.web3j.utils.Numeric.prependHexPrefix
import org.web3j.utils.Numeric.toBigIntNoPrefix
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import java.io.IOException
import java.math.BigInteger
import kotlin.coroutines.Continuation
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine

object WalletHelper {
    private val accounts = mutableMapOf<String, Account>()

    fun createWallet(
        context: Context,
        alias: String,
        encodedPassword: String,
        password: String
    ): Wallet {
        var account: Account? = null
        try {
            account = IotexAccount.create()
        } catch (e: Exception) {
            e.printStackTrace()
        }
        val walletFile = KeystoreUtils.createWalletFileByAcount(password, account)
        saveWalletFile(context, walletFile)
        return Wallet(
            convertWeb3Address(account?.address() ?: ""),
            alias,
            encodedPassword,
            walletFile.id ?: "",
            false,
            timestamp = TimeUtils.getNowMills().toString()
        )
    }

    fun getAddressByPk(privateKey: String): String {
        return IotexAccount.create(Numeric.hexStringToByteArray(privateKey)).address()
    }

    fun importWalletByPK(
        context: Context,
        alias: String,
        privateKey: String,
        encodedPassword: String,
        password: String
    ): Wallet {
        var walletFile:KeystoreFile? = null
        val address = if (isSolanaPrivatakey(privateKey)) {
            val privateKeyByte = Base58.decode(privateKey)
            HotAccount(privateKeyByte).publicKey.toBase58()
        } else {
            walletFile = KeystoreUtils.createWalletFileByKey(password,
                Numeric.toBigInt(privateKey))
            saveWalletFile(context, walletFile)
            IotexAccount.create(privateKey.toHexByteArray()).address().toEvmAddress()
        }
        return Wallet(
            address,
            alias,
            encodedPassword,
            walletFile?.id ?: privateKey,
            false,
            timestamp = TimeUtils.getNowMills().toString()
        )
    }

    fun getWalletFileString(context: Context, fileId: String): String? {
        val file = File(context.filesDir, fileId)
        if (fileId == "") {
            return null
        }
        if (!file.exists()) {
            return null
        }

        val bArray = ByteArray(file.length().toInt())
        val fis = FileInputStream(file)
        return try {
            fis.read(bArray)
            fis.close()
            String(bArray)
        } catch (ioExp: IOException) {
            ioExp.printStackTrace()
            null
        } finally {
            try {
                fis.close()
            } catch (e: IOException) {
                e.printStackTrace()
            }
        }
    }

    fun saveWalletFile(context: Context, keystoretFile: KeystoreFile) {
        val file = File(context.filesDir, keystoretFile.id)
        if (file.exists()) {
            throw RuntimeException("wallet file exists")
        }
        var fos: FileOutputStream? = null
        try {
            fos = FileOutputStream(file)
            fos.write(keystoretFile.toJsonString().toByteArray())
        } catch (e: IOException) {
            throw RuntimeException("write wallet file error", e)
        } finally {
            if (fos != null) {
                try {
                    fos.close()
                } catch (e: IOException) {
                }
            }
        }
    }

    fun getAccountByWallet(wallet: Wallet): Account? {
        synchronized(accounts) {
            var account: Account? = null
            try {
                if (accounts.containsKey(wallet.address)) {
                    account = accounts[wallet.address]
                    if (account != null) return account
                }
                val pk = getPrivateKeyBy(
                    getWalletFileString(Utils.getApp(), wallet.file),
                    wallet.password
                )
                account = IotexAccount.create(pk)
                account?.let {
                    accounts[wallet.address] = it
                }
            } catch (e: Exception) {
                FirebaseCrashlytics.getInstance().recordException(e)
            }
            return account
        }
    }

    suspend fun isValidAccount(): Boolean {
        return withContext(Dispatchers.IO) {
            kotlin.runCatching {
                val wallet = getCurWallet() ?: return@withContext false
                if (wallet.isWatch) return@withContext true
                if (wallet.isAAWallet()) return@withContext true
                val privateKey = getAccountByWallet(wallet)?.privateKey()?.toHexString()
                    ?: return@withContext false
                WalletUtils.isValidPrivateKey(privateKey)
            }.getOrNull() ?: false
        }
    }

    suspend fun getWalletPrivateKey(wallet: Wallet? = null): String {
        return suspendCoroutine { continuation ->
            val w = wallet ?: getCurWallet()
            val context = ActivityUtils.getTopActivity()
            if (w == null || context == null) {
                Utils.getApp().getString(R.string.error_get_account).toast()
                continuation.resume(Utils.getApp().getString(R.string.error_get_account))
                return@suspendCoroutine
            }
            LockAuthHelper.showAuthLock(
                context,
                onSuccess = {
                    MainScope().launch {
                        resolvePrivateKey(w, continuation)
                    }
                },
                onCancel = {
                    continuation.resume(Utils.getApp().getString(R.string.error_get_account))
                }
            )
        }
    }

    private suspend fun resolvePrivateKey(wallet: Wallet, continuation: Continuation<String>) {
        withContext(Dispatchers.IO) {
            if (isBitcoinNetwork()) {
                val mnemonic = AppDatabase.getInstance(Utils.getApp()).mnemonicDao()
                    .queryByMnemonicId(wallet.mnemonicId)
                val path = wallet.getBitcoinWallet()?.path
                if (mnemonic != null && !path.isNullOrBlank()) {
                    val mnemonicPhrase = EncryptUtil.decrypt(mnemonic.encryptedMnemonic)
                    val privateKey = resolvePrivateKeyFromMnemonic(mnemonicPhrase, path)
                    continuation.resume(privateKey)
                    return@withContext
                }
            }

            if (isSolanaNetwork() || wallet.isSolanaPrivateWallet()) {
                if(wallet.isMnemonicWallet()){
                    val mnemonicWallet = AppDatabase.getInstance(Utils.getApp()).mnemonicDao()
                        .queryByMnemonicId(wallet.mnemonicId)
                    if (mnemonicWallet != null) {
                        val mnemonic = EncryptUtil.decrypt(mnemonicWallet.encryptedMnemonic)
                        val account = HotAccount.fromMnemonic(
                            mnemonic.split(" "),
                            "",
                            DerivationPath.BIP44_M_44H_501H_0H_OH,
                            wallet.addressIndex.toLong()
                        )
                        continuation.resume(Base58.encode(account.secretKey))
                    } else {
                        continuation.resume(Utils.getApp().getString(R.string.error_get_account))
                    }
                } else {
                    continuation.resume(wallet.file)
                }
                return@withContext
            }

            getAccountByWallet(wallet)?.let {
                continuation.resume(it.privateKey().toHexStringNoPrefix())
            } ?: kotlin.run {
                Utils.getApp().getString(R.string.error_get_account).toast()
                continuation.resume(Utils.getApp().getString(R.string.error_get_account))
            }
        }
    }

    fun getWalletPrivateKey(keystoreString: String, password: String): String {
        return Numeric.toHexString(getAccountByKeystore(keystoreString, password).privateKey())
    }

    private fun getAccountByKeystore(keystoreString: String, password: String): Account {
        val keyStoreJson = KeystoreUtils.loadKeyFromWalletFile(password, keystoreString)
        return IotexAccount.create(keyStoreJson)
    }

    private fun getPrivateKeyBy(keystore: String?, ePassword: String?): BigInteger {
        val password = ePassword?.let { it -> EncryptUtil.decrypt(it) }
        return try {
            KeystoreUtils.loadKeyFromWalletFile(password, keystore)
            if (!keystore.isNullOrEmpty()) {
                KeystoreUtils.loadKeyFromWalletFile(password, keystore)
            } else {
                BigInteger("0")
            }
        } catch (e: CipherException) {
            FirebaseCrashlytics.getInstance().recordException(e)
            KeystoreUtils.loadKeyFromWalletFile(ePassword, keystore)
            if (!keystore.isNullOrEmpty()) {
                KeystoreUtils.loadKeyFromWalletFile(password, keystore)
            } else {
                BigInteger("0")
            }
        }
    }

    fun showWatchAddress(activity: Activity) {
        ErrorPopupWindow(
            activity,
            activity.getString(R.string.error),
            activity.getString(R.string.watch_wallet_warning)
        ) {
        }.show()
    }

    private fun switchToAllNetwork(){
        CoroutineScope(Dispatchers.IO).launch {
            val network = AppDatabase.getInstance(Utils.getApp()).rpcNetworkDao()
                .queryRPCNetworkByChainId(IOTEX_CHAIN_ID)
            if (network != null && !isIoTexNetWork()) {
                switchNetwork(network)
            }
            UserStore.setAllNetwork(true)
            EventBus.getDefault().post(SwitchAllNetworkEvent())
        }
    }

    fun switchNetwork(network: RPCNetwork) {
        if (network.chainId == 0) {
            switchToAllNetwork()
            return
        }
        UserStore.setAllNetwork(false)
        CoroutineScope(Dispatchers.IO).launch {
            val from = getCurNetwork() ?: return@launch
            val rpcNode = AppDatabase.getInstance(Utils.getApp()).rpcNetworkNode()
                .queryRPCNodeActivated(network.chainId)

            rpcNode?.let {
                SPUtils.getInstance().put(SPConstant.SP_RPC_NETWORK_URL, it.rpcUrl)
            }

            UserStore.saveNetwork(network)

            if (isBitcoinNetwork(network.chainId)) {
                generateBitcoinWallet(getCurWallet())
            } else if (isSolanaNetwork()) {
                generateSolanaWallet(getCurWallet())
            }
            EventBus.getDefault().post(NetworkSwitchEvent(from, network))
        }
    }

    fun checkNetworkDialog(network:RPCNetwork,back: () -> Unit){
        val switch = checkWalletNetwork(Constant.currentWallet, network.chainId)
        if (switch) {
            NoteDialog().apply {
                title = Utils.getApp().getString(R.string.tips)
                message = Utils.getApp().getString(R.string.please_switch_wallets_or_create_a_new_wallet, network.shortName, network.shortName)
                onConfirm = {
                    val activity = ActivityUtils.getTopActivity()
                    activity.startActivity(Intent(activity, SwitchWalletActivity::class.java))
                }
            }.show()
        } else{
            back.invoke()
        }
    }

    fun generateBitcoinWallet(wallet: Wallet?) {
        if (wallet == null) return
        if (wallet.isAAWallet() || wallet.isSolanaPrivateWallet() || wallet.isWatch) return
        val networks = AppDatabase.getInstance(Utils.getApp()).rpcNetworkDao().queryBitcoinNetwork()
        networks.forEach { network ->
            val bitcoinWalletList = AppDatabase.getInstance(Utils.getApp()).bitcoinWalletDao()
                .queryByEvmAddress(wallet.address, network.chainId)
            val networkParams = BitcoinHelper.getBitcoinNetworkParams(network)
            if (bitcoinWalletList.size != 4) {
                val legacyWallet = BitcoinHelper.getLegacyWallet(wallet, networkParams)
                AppDatabase.getInstance(Utils.getApp()).bitcoinWalletDao()
                    .insertIfNotExists(legacyWallet)

                val segwithBech32Wallet = BitcoinHelper.getSegwitBech32Wallet(wallet, networkParams)
                AppDatabase.getInstance(Utils.getApp()).bitcoinWalletDao()
                    .insertIfNotExists(segwithBech32Wallet)

                val segwithBase58Wallet = BitcoinHelper.getSegwitBase58Wallet(wallet, networkParams)
                AppDatabase.getInstance(Utils.getApp()).bitcoinWalletDao()
                    .insertIfNotExists(segwithBase58Wallet)

                val taprootWallet = BitcoinHelper.getTaprootWallet(wallet, networkParams)
                AppDatabase.getInstance(Utils.getApp()).bitcoinWalletDao()
                    .insertIfNotExists(taprootWallet)
            }
        }
    }

    fun generateSolanaWallet(wallet: Wallet?){
        if (wallet == null) return
        if (!wallet.isMnemonicWallet()) return
        val solanaW = AppDatabase.getInstance(Utils.getApp()).solanaWalletDao().queryByAddressEvm(wallet.address)
        if (solanaW != null) return
        val mnemonicWallet = AppDatabase.getInstance(Utils.getApp()).mnemonicDao().queryByMnemonicId(wallet.mnemonicId)
            ?: return
        val mnemonic = EncryptUtil.decrypt(mnemonicWallet.encryptedMnemonic)
        generateSolanaWallet(mnemonic, wallet)
    }

    private fun generateSolanaWallet(mnemonic: String, wallet: Wallet){
        val account =  HotAccount.fromMnemonic(
            mnemonic.split(" "),
            "",
            DerivationPath.BIP44_M_44H_501H_0H_OH,
            wallet.addressIndex.toLong()
        )
        val publicKeyBase58 = account.publicKey.toBase58()
        val solanaWallet = SolanaWallet(wallet.address, publicKeyBase58)
        AppDatabase.getInstance(Utils.getApp()).solanaWalletDao().insertorReplace(solanaWallet)
    }

    fun checkWalletDialog(wallet: Wallet, back: () -> Unit) {
        val switch = checkWalletNetwork(wallet)
        if (switch) {
            NoticePopupWindow(
                Utils.getApp().getString(R.string.tips),
                Utils.getApp().getString(R.string.wallet_not_support_current_network),
                R.drawable.icon_tips_notice,
                confirmText = Utils.getApp().getString(R.string.switch_now),
                confirmAction = {
                    back.invoke()
                }
            ).show()
        } else {
            back.invoke()
        }
    }

    fun checkWalletNetwork(
        wallet: Wallet?,
        chainId: Int = UserStore.getChainId(),
    ): Boolean {
        if (wallet == null) {
            return true
        }
        return if (UserStore.getAllNetwork() && chainId == UserStore.getChainId()) {
            false
        } else if (wallet.isAAWallet()) {
            !isIoTexNetWork(chainId)
        } else if (wallet.isEvmPrivateWallet() || wallet.isEvmWatchWallet()) {
            isSolanaNetwork(chainId)
        } else if (wallet.isBitcoinWatchWallet()) {
            !isBitcoinNetwork(chainId)
        } else if (wallet.isSolanaPrivateWallet() || wallet.isSolanaWatchWallet()) {
            !isSolanaNetwork(chainId)
        } else {
            false
        }
    }

    fun switchWallet(wallet: Wallet) {
        val switchNetwork = checkWalletNetwork(wallet)
        if (switchNetwork) {
            changeNetworkForWallet(wallet)
        }
        changeWallet(wallet)
    }

    private fun changeNetworkForWallet(wallet: Wallet) {
        CoroutineScope(Dispatchers.IO).launch {
            val network =
                AppDatabase.getInstance(Utils.getApp()).rpcNetworkDao()
                    .queryAllRPCNetwork()
                    .findLast {
                        if (wallet.isAAWallet() || wallet.isEvmPrivateWallet() || wallet.isEvmWatchWallet()) {
                            it.chainId == IOTEX_CHAIN_ID
                        } else if (wallet.isBitcoinWatchWallet()) {
                            it.chainId == BITCOIN_MAIN_CHAIN_ID
                        } else if (wallet.isSolanaPrivateWallet() || wallet.isSolanaWatchWallet()) {
                            it.chainId == SOLANA_MAIN_CHAIN_ID
                        } else {
                            true
                        }
                    }
            network?.let {
                switchNetwork(it)
            }
        }
    }

    private fun changeWallet(wallet: Wallet) {
        CoroutineScope(Dispatchers.IO).launch {
            val bitcoinWallets = AppDatabase.getInstance(Utils.getApp()).bitcoinWalletDao()
                .queryByEvmAddress(wallet.address, getCurChainId())
            wallet.bitcoinWallets = bitcoinWallets
            Constant.currentWallet = wallet
            UserStore.setWalletAddress(wallet.address)
            EventBus.getDefault().post(SwitchWalletEvent())
        }
    }

    fun getCurNetwork(): RPCNetwork? {
        var network = AppDatabase.getInstance(Utils.getApp()).rpcNetworkDao()
            .queryRPCNetworkByChainId(UserStore.getChainId())
        if (network == null) {
            network = AppDatabase.getInstance(Utils.getApp()).rpcNetworkDao()
                .queryRPCNetworkByChainId(Config.IOTEX_CHAIN_ID)
        }
        return network
    }

    fun getCurChainId(): Int {
        return UserStore.getChainId()
    }

    fun getCurRpcUrl(): String {
        return SPUtils.getInstance().getString(SPConstant.SP_RPC_NETWORK_URL, Config.IOTEX_RPC_URL)
    }

    fun isIoTexNetWork(chainId:Int = getCurChainId()): Boolean {
        return chainId == IOTEX_CHAIN_ID || chainId == IOTEX_TEST_CHAIN_ID
    }

    fun isBitcoinNetwork(chainId:Int = getCurChainId()): Boolean {
        return chainId == BITCOIN_TEST_CHAIN_ID || chainId == BITCOIN_MAIN_CHAIN_ID
    }

    fun isSolanaNetwork(chainId:Int = getCurChainId()): Boolean {
        return chainId == SOLANA_TEST_CHAIN_ID || chainId == SOLANA_MAIN_CHAIN_ID
    }

    fun isSupport1559Gas(): Boolean {
        return UserStore.getNetWorkGasStation().isNotEmpty()
    }

    fun formatWalletAddress(input: String): String {
        runCatching {
            return if (getCurChainId() == Config.IOTEX_CHAIN_ID || getCurChainId() == Config.IOTEX_TEST_CHAIN_ID) {
                return getWalletAddress(input)
            } else {
                convertWeb3Address(input)
            }
        }.getOrElse {
            return input
        }
    }

    private fun getWalletAddress(address: String): String {
        if (address.isBlank()) return ""
        runCatching {
            return if (UserStore.getWeb3Type() == Web3AddressType.WEB3.type) {
                convertWeb3Address(address)
            } else {
                convertIoAddress(address)
            }
        }.getOrElse {
            return address
        }
    }

    fun getWeb3Address(): String {
        val wallet = Constant.currentWallet ?: return ""
        return if (isBitcoinNetwork()) {
            wallet.getBitcoinWallet()?.bitcoinAddress ?: ""
        } else {
            convertWeb3Address(wallet.address)
        }
    }

    fun getCurWallet(): Wallet? {
        val address = UserStore.getWalletAddress()
        var wallet = AppDatabase.getInstance(Utils.getApp()).walletDao()
            .queryWalletByAddress(address)
        if (wallet == null) {
            wallet = AppDatabase.getInstance(Utils.getApp()).walletDao()
                .queryFirstWallet()
            UserStore.setWalletAddress(wallet?.address ?: "")
        }
        if (wallet != null) {
            val chainId =
                if (getCurChainId() == BITCOIN_TEST_CHAIN_ID) BITCOIN_TEST_CHAIN_ID else BITCOIN_MAIN_CHAIN_ID
            val bitcoinWallets = AppDatabase.getInstance(Utils.getApp()).bitcoinWalletDao()
                .queryByEvmAddress(wallet.address, chainId)
            wallet.bitcoinWallets = bitcoinWallets
        }
        if(wallet?.isMnemonicWallet() == true){
            val solanaWallet = AppDatabase.getInstance(Utils.getApp()).solanaWalletDao().queryByAddressEvm(wallet.address)
            wallet.solanaWallet = solanaWallet
        }
        Constant.currentWallet = wallet
        return wallet
    }

    fun convertWeb3Address(address: String): String {
        if (address.startsWith("0x")) return address.lowercase()
        runCatching {
            val dec = Bech32.decode(address).data
            return "0x" + Numeric.toHexString(Bech32.convertBits(dec, 0, dec.size, 5, 8, false))
        }.getOrElse { return address }
    }

    fun convertIoAddress(address: String): String {
        if (!address.startsWith("0x")) return address
        val byteData = Numeric.hexStringToByteArray(address.substring(2, address.length))

        val grouped = Bech32.convertBits(
            byteData,
            0,
            byteData.size,
            8,
            5,
            true
        )
        return Bech32.encode(IotexAccount.AddressPrefix, grouped)
    }

    fun gotoExplorer(hash: String, chainId: Int = getCurChainId()) {
        doAsync {
            val networkEntry = AppDatabase.getInstance(Utils.getApp()).rpcNetworkDao()
                .queryRPCNetworkByChainId(chainId)
            networkEntry ?: return@doAsync

            uiThread {
                val explorerUrl = if (getCurChainId() == Config.IOTEX_CHAIN_ID) {
                    if (UserStore.getWeb3Type() == Web3AddressType.IO.type) {
                        "${networkEntry.explorer}tx/$hash?format=io"
                    } else {
                        "${networkEntry.explorer}tx/$hash?format=0x"
                    }
                } else if (isSolanaNetwork()) {
                    if (getCurChainId() == SOLANA_TEST_CHAIN_ID) {
                        "${networkEntry.explorer}tx/$hash?cluster=devnet"
                    } else {
                        "${networkEntry.explorer}tx/$hash"
                    }
                } else {
                    "${networkEntry.explorer}tx/$hash"
                }
                ActivityUtils.getTopActivity()?.startActivity<XAppsActivity>(
                    IoPayConstant.BROWSER_URL to explorerUrl
                )
            }
        }
    }

    fun isValidAddress(address: String): Boolean {
        try {
            if (address.isBlank()) {
                return false
            }
            if (isBitcoinAddress(address)) {
                return true
            }

            if (isSolanaAddress(address)) {
                return true
            }

            if(isEvmAddress(address)){
                return true
            }
        } catch (e: Exception) {
            return false
        }
        return false
    }

    fun isEvmAddress(address: String): Boolean {
        try {
            if (address.isBlank()) {
                return false
            }

            val ethContract = convertWeb3Address(address)

            if (ethContract.isBlank() || !ethContract.startsWith("0x"))
                return false

            val cleanInput = cleanHexPrefix(ethContract)

            val value = toBigIntNoPrefix(cleanInput)
            if (value == BigInteger.ZERO) return false
            return cleanInput.length == 40
        } catch (e: Exception) {
            return false
        }
    }

    fun isValidAddressOnCurNetwork(address: String): Boolean {
        if (isBitcoinNetwork() && isBitcoinAddress(address)) return true
        if (isSolanaNetwork() && isSolanaAddress(address)) return true
        if (!isBitcoinNetwork() && !isSolanaNetwork() && isEvmAddress(address)) return true
        return false
    }

    fun isBitcoinAddress(address: String): Boolean {
        return try {
            BitcoinHelper.resolveBitcoinAddress(
                address,
                BitcoinHelper.getBitcoinNetworkParams()
            )
            true
        } catch (e: Exception) {
            false
        }
    }

    fun isSolanaAddress(address: String): Boolean {
        val decode = kotlin.runCatching { Base58.decode(address) }.getOrNull()
        return decode?.size == 32
    }

    fun getWalletAvatarRobots(web3Address: String) =
        "https://avatar.iopay.me/${web3Address}/s_30x30/set_set3.jpg"

    fun getWalletAvatarBlockies(web3Address: String) =
        "https://avatar.iopay.me/eth/${web3Address}?size=30.jpg"

    fun getWalletAvatarJazzicons(web3Address: String, size: Int = 100) =
        "${Config.AVATAR_JAZZ_ICON_URL}/${web3Address}/${size}/1"

    fun getAddressAvatar(address: String, size: Int = 100): String {
        return when (SPUtils.getInstance().getInt(SP_WALLET_AVATAR, AVATAR_THEME_ROBOTS)) {
            AVATAR_THEME_BLOCKIES -> {
                getWalletAvatarBlockies(convertWeb3Address(address))
            }

            AVATAR_THEME_JAZZICONS -> {
                getWalletAvatarJazzicons(convertWeb3Address(address), size)
            }

            else -> {
                getWalletAvatarRobots(convertWeb3Address(address))
            }
        }
    }

    fun generateMnemonic(): String {
        val initialEntropy = ByteArray(16)
        SecureRandomUtils.secureRandom().nextBytes(initialEntropy)
        return MnemonicUtils.generateMnemonic(initialEntropy)
    }

    fun nameMnemonic(): String {
        val nameList = AppDatabase.getInstance(Utils.getApp()).mnemonicDao()
            .queryAll().map { it.name }
        var index = 1
        var name = "${Utils.getApp().getString(R.string.recovery_phrase)} ${nameList.size + index}"
        while (nameList.contains(name)) {
            index++
            name = "${Utils.getApp().getString(R.string.recovery_phrase)} ${nameList.size + index}"
        }
        return name
    }

    fun resolvePrivateKeyFromMnemonic(mnemonic: String, path: String): String {
        val seed = MnemonicUtils.generateSeed(mnemonic, null)
        val master = Bip32ECKeyPair.generateKeyPair(seed)
        val pathIntArray = WalletHelper.resolvePath(path)
        val keypair = Bip32ECKeyPair.deriveKeyPair(master, pathIntArray)
        return keypair.privateKey.toString(16)
    }

    fun generateWalletViaMnemonic(
        mnemonic: String,
        path: String,
        @PathType pathType: Int,
        password: String,
        walletName: String
    ): Wallet? {
        if (!validMnemonicPath(path)) {
            throw IllegalArgumentException("The parameter $path is invalid")
        }
        if (password.length < 8) {
            throw IllegalArgumentException("The password $password is too short")
        }
        return kotlin.runCatching {
            val md5Mnemonic = EncryptUtils.encryptMD5ToString(mnemonic)
            val seed = MnemonicUtils.generateSeed(mnemonic, null)
            val master = Bip32ECKeyPair.generateKeyPair(seed)
            val pathIntArray = resolvePath(path)
            val keypair = Bip32ECKeyPair.deriveKeyPair(master, pathIntArray)
            val walletFile = createLight(password, keypair)
            val address = prependHexPrefix(walletFile.address)
            val privateKeyBig = Numeric.toBigInt(keypair.privateKey.toString(16))
            val privateFile = KeystoreUtils.createWalletFileByKey(password, privateKeyBig)
            val encryptedPassword = EncryptUtil.encrypt(password)
            saveWalletFile(Utils.getApp(), privateFile)
            Wallet(
                address.toEvmAddress(),
                walletName,
                encryptedPassword,
                privateFile.id ?: "",
                false,
                timestamp = TimeUtils.getNowMills().toString(),
                mnemonicId = md5Mnemonic,
                addressIndex = pathIntArray.last(),
                addressType = AddressType_NativeSegwit,
                path = path,
                pathType = pathType
            ).apply {
                val bitcoinWalletList = mutableListOf<BitcoinWallet>()
                val networkParams = BitcoinHelper.getBitcoinNetworkParams()

                val legacyWallet = BitcoinHelper.getLegacyWallet(
                    this@apply,
                    networkParams,
                    if (pathType != PATH_BTC) path else Constant.MNEMONIC_PATH_LEGACY,
                    mnemonic
                )
                val segwithBech32Wallet = BitcoinHelper.getSegwitBech32Wallet(
                    this@apply,
                    networkParams,
                    if (pathType != PATH_BTC) path else Constant.MNEMONIC_PATH_NATIVE_SEGWIT,
                    mnemonic
                )
                val segwithBase58Wallet = BitcoinHelper.getSegwitBase58Wallet(
                    this@apply,
                    networkParams,
                    if (pathType != PATH_BTC) path else Constant.MNEMONIC_PATH_NESTED_SEGWIT,
                    mnemonic
                )
                val taprootWallet = BitcoinHelper.getTaprootWallet(
                    this@apply,
                    networkParams,
                    if (pathType != PATH_BTC) path else Constant.MNEMONIC_PATH_TAPROOT,
                    mnemonic
                )
                bitcoinWalletList.add(legacyWallet)
                bitcoinWalletList.add(segwithBech32Wallet)
                bitcoinWalletList.add(segwithBase58Wallet)
                bitcoinWalletList.add(taprootWallet)
                bitcoinWallets = bitcoinWalletList
                generateSolanaWallet(mnemonic, this@apply)
                val solanaW = AppDatabase.getInstance(Utils.getApp()).solanaWalletDao().queryByAddressEvm(<EMAIL>)
                solanaWallet = solanaW
            }
        }.onFailure {
            it.message?.toast()
        }.getOrNull()
    }

    fun validMnemonicPath(path: String): Boolean {
        if (!path.startsWith("m") && !path.startsWith("M")) {
            return false
        }
        val pathArray = path.split("/").toMutableList().apply { removeAt(0) }
        if (pathArray.size <= 1) {
            return false
        }
        return true
    }

    fun resolvePath(path: String): IntArray {
        if (!path.startsWith("m") && !path.startsWith("M")) {
            throw IllegalArgumentException("The parameter $path is invalid")
        }
        val pathArray = path.split("/").toMutableList().apply { removeAt(0) }
        if (pathArray.size <= 1) {
            throw IllegalArgumentException("The parameter $path is invalid")
        }
        return pathArray.map { p ->
            if (p.endsWith("'")) {
                val number = p.substring(0, p.length - 1).toInt()
                number or Bip32ECKeyPair.HARDENED_BIT
            } else {
                p.toInt()
            }
        }.toIntArray()
    }

    fun getMnemonicPath(mnemonicId: String, skipPath: String? = null): String {
        val walletList = AppDatabase.getInstance(Utils.getApp()).walletDao()
            .queryByMnemonic(mnemonicId)
        val temps = walletList.map { it.addressIndex }.sortedBy { it }
        val skipIndex = skipPath?.split("/")?.last()?.toIntOrNull() ?: -1
        val addressIndex = if (temps.isEmpty()) {
            0
        } else {
            val index = temps.indices.firstOrNull {
                temps.indexOf(it) == -1 && it != skipIndex
            }
            index ?: (temps.last() + 1)
        }
        val walletPath = walletList.firstOrNull()?.path
        val path = if (walletPath.isNullOrBlank()) Constant.MNEMONIC_PATH_ETH else walletPath
        return path.replaceRange(path.length - 1, path.length, addressIndex.toString())
    }

    fun verifyMnemonicPhrase(mnemonic: String): Boolean {
        val phraseLib = ResourceUtils.readAssets2List("mnemonic.txt")
        return phraseLib.containsAll(mnemonic.split(" ").map {
            it.lowercase()
        })
    }

    fun saveWallet(wallet: Wallet) {
        val localWallet = AppDatabase.getInstance(Utils.getApp()).walletDao()
            .queryWalletByAddress(wallet.address)
        if (localWallet != null) {
            return
        }

        val latestWallet = AppDatabase.getInstance(Utils.getApp()).walletDao()
            .queryLatestWallet()
        val order = latestWallet?.order?.plus(1) ?: 0
        wallet.timestamp = TimeUtils.getNowMills().toString()
        wallet.order = order
        wallet.addressType = latestWallet?.addressType ?: AddressType_NativeSegwit
        AppDatabase.getInstance(Utils.getApp()).walletDao()
            .insertIfNotExists(
                Wallet(
                    wallet.address, wallet.alias, wallet.password,
                    wallet.file, wallet.isWatch,
                    TimeUtils.getNowMills().toString(), order,
                    wallet.mnemonicId, wallet.addressIndex,
                    wallet.aaSalt, wallet.email,
                    wallet.effective,
                    addressType = wallet.addressType,
                    path = wallet.path,
                    pathType = wallet.pathType
                )
            )
        generateBitcoinWallet(wallet)
        generateSolanaWallet(wallet)
    }

    fun nameWallet(count: Int = 1): List<String> {
        val nameList = mutableListOf<String>()
        val walletNameList = AppDatabase.getInstance(Utils.getApp()).walletDao()
            .queryAllWallets().map { it.alias.trim() }
        repeat((1..count).count()) {
            var index = 1
            var name = Utils.getApp().getString(R.string.auto_wallet_name, index.toString())
            while (walletNameList.contains(name) || nameList.contains(name)) {
                index++
                name = Utils.getApp()
                    .getString(R.string.auto_wallet_name, index.toString())
            }
            nameList.add(name)
        }
        return nameList
    }

    fun gotoExplorerTokenScan(contract: String,chainId:Int = getCurChainId()) {
        doAsync {
            val networkEntry = AppDatabase.getInstance(Utils.getApp()).rpcNetworkDao()
                .queryRPCNetworkByChainId(chainId)
            networkEntry ?: return@doAsync

            uiThread {
                val explorerUrl = if(contract.isEmpty()){
                    networkEntry.explorer
                }else if (chainId == IOTEX_CHAIN_ID) {
                    if (UserStore.getWeb3Type() == Web3AddressType.IO.type
                    ) {
                        "${networkEntry.explorer}token/$contract?format=io"
                    } else {
                        "${networkEntry.explorer}token/$contract?format=0x"
                    }

                } else if (isSolanaNetwork()) {
                    if (chainId == SOLANA_TEST_CHAIN_ID) {
                        "${networkEntry.explorer}address/$contract?cluster=devnet"
                    } else {
                        "${networkEntry.explorer}address/$contract"
                    }
                } else {
                    "${networkEntry.explorer}token/$contract"
                }
                ActivityUtils.getTopActivity()?.let {
                    WebActivity.startActivity(it, explorerUrl)
                }
            }
        }
    }

    fun checkWalletExpired(force: Boolean = false): Boolean {
        if (Constant.currentWallet?.isAAWallet() == true && (force || Constant.currentWallet?.effective != true)) {
            ActivityUtils.getTopActivity()?.let {
                it.runOnUiThread {
                    if (it is AppCompatActivity) {
                        WalletExpiredDialog()
                            .show(it.supportFragmentManager, WalletExpiredDialog::class.java.name)
                    }
                }
            }
            return true
        }
        return false
    }

    fun checkWatchModel(): Boolean {
        if (Constant.currentWallet?.isWatch == true) {
            ToastUtils.showShort(R.string.watch_wallet_warning)
            return true
        }
        return false
    }

    fun isAAWalletSupported(): Boolean {
        val curChainId = getCurChainId()
        if (curChainId == Config.IOTEX_CHAIN_ID) return true
        val list = AppDatabase.getInstance(Utils.getApp()).networkAAConfigDao().queryAll()
        val target = list.firstOrNull {
            it.chainId == curChainId
        }
        return target != null
    }

    fun isValidPrivateKey(key: String):Boolean{
        return isEvmPrivateKey(key) || BitcoinHelper.isWifPrivateKey(key) || isSolanaPrivatakey(key)
    }

    fun isEvmPrivateKey(key: String): Boolean {
        return WalletUtils.isValidPrivateKey(key)
    }

    fun isSolanaPrivatakey(key: String): Boolean {
        return kotlin.runCatching {
            val secure = Base58.decode(key).toHexStringNoPrefix()
            return secure.length == 128 && secure.matches(Regex("^[0-9A-Fa-f]+$"))
        }.getOrNull() ?: false
    }

    fun isEvmTransferLink(link: String): Boolean {
        return link.startsWith(Config.LINK_SCHEME_ETHEREUM_TRANSFER)
    }

    fun isEvmTransferLinkToken(link: String): Boolean{
        return isEvmTransferLink(link) && link.contains(Config.LINK_SCHEME_ETHEREUM_TRANSFER_TOKEN)
    }
}