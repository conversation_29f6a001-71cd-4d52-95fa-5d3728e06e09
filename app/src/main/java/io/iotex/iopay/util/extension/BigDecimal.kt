package io.iotex.iopay.util.extension

import java.math.BigDecimal
import java.text.DecimalFormat

fun BigDecimal.formatScale(scale:Int = 4): String {
    kotlin.runCatching {
        val df = DecimalFormat("#0.##################")
        val decimalString = df.format(this)
        //Some languages(German) use , to .
        val decimalSplitS = decimalString.replace(",", ".").split(".")
        if (decimalSplitS.isNotEmpty() && decimalSplitS.size > 1) {
            val beforeDot = decimalSplitS[0]
            val afterDot = decimalSplitS[1]
            var disPoint = ""
            val charArray = afterDot.toCharArray()
            var length = 0
            for (char in charArray) {
                disPoint += char.toString()
                if (disPoint.toInt() > 0 || beforeDot.toInt() > 0) {
                    length += 1
                    if (length >= scale) break
                }
            }
            return "$beforeDot.$disPoint"
        }
    }
    return this.toString()
}