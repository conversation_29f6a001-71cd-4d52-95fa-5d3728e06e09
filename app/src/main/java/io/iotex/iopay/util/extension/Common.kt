package io.iotex.iopay.util.extension

import com.google.gson.Gson
import org.web3j.utils.Numeric

fun ByteArray.toHexString(): String {
    return Numeric.toHexString(this)
}

fun ByteArray.toHexStringNoPrefix(): String {
    return Numeric.toHexStringNoPrefix(this)
}

fun <T1: Any, T2: Any, R: Any> safeLet(p1: T1?, p2: T2?, block: (T1, T2)->R?): R? {
    return if (p1 != null && p2 != null) block(p1, p2) else null
}

fun <T1: Any, T2: Any, T3: Any, R: Any> safeLet(p1: T1?, p2: T2?, p3: T3?, block: (T1, T2, T3)->R?): R? {
    return if (p1 != null && p2 != null && p3 != null) block(p1, p2, p3) else null
}

fun <T> List<T>.splitList(size: Int): List<List<T>> {
    if (this.isEmpty()) return emptyList()
    if (this.size < size) return arrayListOf(this)
    val result = ArrayList<List<T>>()
    val splitNum = if (this.size % size == 0) this.size / size else this.size / size + 1
    var value: List<T>?
    for (i in 0 until splitNum) {
        value = if (i < splitNum - 1) {
            this.subList(i * size, (i + 1) * size)
        } else {
            this.subList(i * size, this.size)
        }
        result.add(value)
    }
    return result
}

fun Any.toJson(): String {
    return Gson().toJson(this)
}