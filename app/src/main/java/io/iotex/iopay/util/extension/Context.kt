package io.iotex.iopay.util.extension

import android.app.Activity
import android.content.Intent
import io.iotex.iopay.R
import io.iotex.iopay.util.Config
import io.iotex.iopay.util.Constant
import io.iotex.iopay.util.IoPayConstant
import io.iotex.iopay.xapp.XAppsActivity

fun Activity.launchActivityForResult(
    clz: Class<*>,
    requestCode: Int,
    animation: Boolean = true
) {
    val intent = Intent(this, clz)
    startActivityForResult(intent, requestCode)
    if (animation && this is Activity) {
        overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_left)
    }
}

fun Activity.applyPaymasterGas(address: String? = null) {
    val account = address ?: Constant.currentWallet?.address ?: return
    val intent = Intent(this, XAppsActivity::class.java)
    intent.putExtra(IoPayConstant.BROWSER_URL, Config.applyGasLink(account))
    this.startActivity(intent)
}