package io.iotex.iopay.util.extension

import com.blankj.utilcode.util.ConvertUtils
import io.iotex.iopay.util.TokenUtil
import java.math.BigDecimal
import java.math.BigInteger

fun Float.dp2px(): Int {
    return ConvertUtils.dp2px(this)
}

fun Int.dp2px(): Int {
    return ConvertUtils.dp2px(this.toFloat())
}

fun Float.px2dp(): Int {
    return ConvertUtils.px2dp(this)
}

fun Int.px2dp(): Int {
    return ConvertUtils.px2dp(this.toFloat())
}

fun Number.toFixed(decimal: Int = 2): String {
    return String.format("%.${decimal}f", this).replace(",", ".")
}

fun BigInteger.fromSatoshis(): String {
    return TokenUtil.weiToTokenBN(this.toString(), 8)
}