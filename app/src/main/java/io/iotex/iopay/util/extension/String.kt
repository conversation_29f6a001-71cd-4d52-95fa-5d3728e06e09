package io.iotex.iopay.util.extension

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.Typeface
import android.widget.Toast
import androidx.core.content.ContextCompat
import com.blankj.utilcode.constant.RegexConstants
import com.blankj.utilcode.util.AppUtils
import com.blankj.utilcode.util.RegexUtils
import com.blankj.utilcode.util.Utils
import com.machinefi.w3bstream.utils.extension.cleanHexPrefix
import io.iotex.iopay.IoPayApplication
import io.iotex.iopay.R
import io.iotex.iopay.util.Config
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.wallet.web3.crypto.Numeric
import org.jetbrains.anko.runOnUiThread
import timber.log.Timber
import java.math.BigDecimal
import java.math.BigInteger
import java.math.RoundingMode
import java.text.DecimalFormat
import java.util.Locale

fun String.toast() {
    Utils.getApp().runOnUiThread {
        Toast.makeText(IoPayApplication.getAppContext(), this@toast, Toast.LENGTH_SHORT).show()
    }
}

fun String.toastLong() {
    Utils.getApp().runOnUiThread {
        Toast.makeText(IoPayApplication.getAppContext(), this@toastLong, Toast.LENGTH_LONG).show()
    }
}

fun String?.i(tag: String = "") {
    if (AppUtils.isAppDebug()) {
        this?.let {
            Timber.tag("IoPay i $tag -->").i(it)
        }
    }
}

fun String?.d(tag: String = "") {
    if (AppUtils.isAppDebug()) {
        this?.let {
            Timber.tag("IoPay d $tag -->").d(it)
        }
    }
}

fun String?.e(tag: String = "") {
    if (AppUtils.isAppDebug()) {
        this?.let {
            Timber.tag("IoPay e $tag -->").e(it)
        }
    }
}

fun String.isNumeric(): Boolean {
    return if (this.contains("E") || this.contains("e")) {
        this.asNumericStrOrNull()?.isRegexNumeric()?:false
    } else {
        this.isRegexNumeric()
    }
}

fun String.isRegexNumeric(): Boolean {
    return RegexUtils.isMatch(RegexConstants.REGEX_FLOAT, this) || RegexUtils.isMatch(RegexConstants.REGEX_INTEGER, this)
}

fun String.isHex(): Boolean {
    val regex = "^[A-Fa-f0-9]+$"
    return RegexUtils.isMatch(regex, this.cleanHexPrefix())
}

fun String?.asBigDecimal(): BigDecimal {
    val median = this?.replace(",", "")?:""
    return if (median.isNumeric()) {
        median.asNumericStr().toBigDecimalOrNull()?: BigDecimal.ZERO
    } else {
        BigDecimal.ZERO
    }
}

fun String?.asNumericStr(): String {
    if (this == null) return "0"
    return this.asNumericStrOrNull()?:"0"
}

fun String.asNumericStrOrNull(): String? {
    var median = this.replace(",", "")
        .toBigDecimalOrNull()?.toString()
    kotlin.runCatching {
        if (this.contains("E")||this.contains("e")) {
            val format = DecimalFormat.getInstance(Locale.US) as DecimalFormat
            format.applyPattern("#.##################")
            format.roundingMode = RoundingMode.FLOOR
            this.toDoubleOrNull()?.let {
                median = format.format(it)
            }
        }
    }
    return median
}

fun String.trimDecimal(): String {
    var str = this
    if (str.indexOf(".") > 0) {
        str = str.replace("0+?$".toRegex(), "")
        str = str.replace("[.]$".toRegex(), "")
    }
    return str
}

fun String.cleanHexPrefix(): String {
    return org.web3j.utils.Numeric.cleanHexPrefix(this)
}

fun String.prependHexPrefix(): String {
    return org.web3j.utils.Numeric.prependHexPrefix(this)
}

fun String.isAddress(): Boolean {
    return WalletHelper.isValidAddress(this)
}

fun String.toIoAddress(): String {
    return WalletHelper.convertIoAddress(this)
}

fun String.toEvmAddress(): String {
    return WalletHelper.convertWeb3Address(this)
}

fun String.isSVG(): Boolean {
    return contains(".svg",true) || contains(Config.AVATAR_JAZZ_ICON_URL)
}

fun String.trimSpace(): String {
    return this.replace("\u200b", "")
    .replace("　", "").trim()
}

fun String.isSupportedEmail(): Boolean {
    if (!RegexUtils.isEmail(this)) return false
    if (!this.endsWith("qq.com") && !this.endsWith("gmail.com")) return false
    return true
}

fun String.toSatoshis(): BigInteger {
    return this.asBigDecimal().multiply(BigDecimal.TEN.pow(8)).toBigInteger()
}

fun String.toHexByteArray(): ByteArray {
    return Numeric.hexStringToByteArray(this)
}

fun String.toTokenBitmap(context: Context, width: Int = 100, height: Int = 100, size: Float = 30f): Bitmap {
    val bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
    val canvas = Canvas(bitmap)
    val paint = Paint().apply {
        color = ContextCompat.getColor(context,R.color.color_token_back)
        isAntiAlias = true
    }

    canvas.drawRect(0f, 0f, width.toFloat(), height.toFloat(), paint)
    paint.apply {
        color = ContextCompat.getColor(context,R.color.color_title_sub)
        textSize = size
        typeface = Typeface.DEFAULT
    }
    canvas.drawText(this, (width - paint.measureText(this)) / 2f, (height - paint.descent() - paint.ascent()) / 2f, paint)

    return bitmap
}

fun String.toDisplayUnit(): String {
    val billion = this.asBigDecimal().divide(BigDecimal(1000000000))
    if (billion > BigDecimal.ONE) {
        return billion.formatScale() + " Billion"
    }
    val million = this.asBigDecimal().divide(BigDecimal(1000000))
    if (million > BigDecimal.ONE) {
        return million.formatScale() + " Million"
    }
    return this.asBigDecimal().formatScale()
}