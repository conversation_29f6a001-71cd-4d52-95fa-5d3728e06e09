package io.iotex.iopay.util.extension

import com.drakeet.multitype.MultiTypeAdapter

fun <T> MultiTypeAdapter.updateItem(t: T, regex: (T) -> Boolean) {
    val oldList = this.items.toMutableList() as MutableList<T>
    val index = oldList.indexOfFirst {
        regex.invoke(it)
    }

    if (index != -1) {
        oldList[index] = t
        this.items = oldList as List<Any>
        this.notifyItemChanged(index)
    }
}

fun <T> MultiTypeAdapter.removeItem(regex: (T) -> Boolean) {
    val oldList = this.items.toMutableList() as MutableList<T>
    val index = oldList.indexOfFirst {
        regex.invoke(it)
    }

    if (index != -1) {
        oldList.removeAt(index)
        this.items = oldList as List<Any>
        this.notifyItemRemoved(index)
    }
}

