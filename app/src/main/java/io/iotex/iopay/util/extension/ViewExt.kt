package io.iotex.iopay.util.extension

import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Rect
import android.graphics.drawable.Drawable
import android.graphics.drawable.PictureDrawable
import android.view.View
import android.widget.ImageView
import androidx.annotation.DrawableRes
import androidx.appcompat.content.res.AppCompatResources
import androidx.core.graphics.drawable.toDrawable
import com.bumptech.glide.Glide
import com.bumptech.glide.load.Transformation
import com.bumptech.glide.load.resource.gif.GifDrawable
import com.bumptech.glide.request.RequestOptions
import com.bumptech.glide.request.target.CustomTarget
import com.bumptech.glide.request.transition.Transition
import com.caverock.androidsvg.SVG
import io.iotex.iopay.IoPayApplication
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import org.jetbrains.anko.attempt
import java.io.File


fun View.setGone() {
    visibility = View.GONE
}

fun View.setVisible() {
    visibility = View.VISIBLE
}

fun View.setInvisible() {
    visibility = View.INVISIBLE
}

fun View.toBitmap():Bitmap{
    var width = 200
    var height = 200
    if (getWidth() != 0) {
        width = getWidth()
    }
    if (getHeight() != 0) {
        height = getHeight()
    }
    val bitmap =
        Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
    val canvas = Canvas(bitmap)
    draw(canvas)
    canvas.save()
    canvas.restore()
    return bitmap
}

fun ImageView.loadGif(model: Any?, @DrawableRes holder: Int) {
    attempt {
        val drawable = AppCompatResources.getDrawable(this.context, holder)
        Glide.with(this)
            .asGif()
            .load(model)
            .placeholder(drawable)
            .into(this)
    }
}

fun ImageView.loadGifSingle(src: Any) {
    val requestOptions = RequestOptions()
    Glide.with(IoPayApplication.getAppContext())
        .load(src)
        .apply(requestOptions)
        .into(object : CustomTarget<Drawable>(){
            override fun onResourceReady(resource: Drawable, transition: Transition<in Drawable>?) {
                if (resource is GifDrawable) {
                    resource.setLoopCount(1)
                    <EMAIL>(resource)
                    resource.start()
                }
            }

            override fun onLoadCleared(placeholder: Drawable?) {
            }

        })
}

fun ImageView.loadSvgOrImage(url: String?, @DrawableRes holder: Int) {
    if (url?.isSVG() == true) {
        loadSvg(url, holder)
    } else {
        loadImage(url, holder)
    }
}

val mapCache = HashMap<String, String>()
fun ImageView.loadSvg(model: String?, @DrawableRes holder: Int) {
    attempt {
        if (model == null) {
            return
        }
        val drawableCache = mapCache[model]
        if (drawableCache != null) {
            val input = File(drawableCache).inputStream()
            val svg = SVG.getFromInputStream(input)
            val drawable = PictureDrawable(svg.renderToPicture())
            setImageDrawable(drawable)
            return
        }
        Glide.with(this).asFile().load(model)
            .placeholder(holder)
            .error(holder)
            .into(object : CustomTarget<File>(){
            override fun onResourceReady(resource: File, transition: Transition<in File>?) {
                kotlin.runCatching {
                    val input = resource.inputStream()
                    val svg = SVG.getFromInputStream(input)
                    val drawable = PictureDrawable(svg.renderToPicture())
                    mapCache[model] = resource.absolutePath
                    setImageDrawable(drawable)
                }
            }

            override fun onLoadCleared(placeholder: Drawable?) {
            }
        })
    }
}

fun ImageView.loadImage(model: Any?) {
    attempt {
        Glide.with(this)
            .load(model)
            .into(this)
    }
}

fun ImageView.loadImage(model: Any?, @DrawableRes holder: Int) {
    attempt {
        val drawable = AppCompatResources.getDrawable(this.context, holder)
        Glide.with(this)
            .load(model)
            .placeholder(drawable)
            .error(drawable)
            .into(this)
    }
}

fun ImageView.loadImage(model: Any?, bitmap: Bitmap) {
    attempt {
        Glide.with(this)
            .load(model)
            .placeholder(bitmap.toDrawable(context.resources))
            .error(bitmap)
            .into(this)
    }
}

fun ImageView.loadResources(@DrawableRes src: Int) {
    attempt {
        val drawable = AppCompatResources.getDrawable(this.context, src)
        Glide.with(this)
            .load(drawable)
            .placeholder(drawable)
            .error(drawable)
            .into(this)
    }
}

fun ImageView.loadImage(
    model: Any?,
    @DrawableRes holder: Int,
    vararg transformation: Transformation<Bitmap>
) {
    attempt {
        Glide.with(this)
            .load(model)
            .placeholder(holder)
            .error(Glide.with(this).load(holder).transform(*transformation))
            .transform(*transformation)
            .into(this)
    }
}

fun View.locateOnScreen(): Rect? {
    val locate = IntArray(2)
    return try {
        this.getLocationOnScreen(locate)
        Rect().apply {
            left = locate[0]
            top = locate[1]
            right = left + <EMAIL>
            bottom = top + <EMAIL>
        }
    } catch (e: Exception) {
        null
    }
}

val View.viewScope: CoroutineScope
    get() {
        val storedScope = tag as? CoroutineScope
        if (storedScope != null) return storedScope

        return ViewCoroutineScope().apply {
            addOnAttachStateChangeListener(this)
            tag = this
        }
    }

private class ViewCoroutineScope : CoroutineScope, View.OnAttachStateChangeListener {
    override val coroutineContext = SupervisorJob() + Dispatchers.Main

    override fun onViewAttachedToWindow(view: View) = Unit

    override fun onViewDetachedFromWindow(view: View) {
        coroutineContext.cancel()
        view.tag = null
    }
}