package io.iotex.iopay.util.listener

import android.view.View

abstract class SingleClickListener : View.OnClickListener {
    private var mLastClickTime: Long = 0
    private var timeInterval = 1000L
    constructor() {
    }
    constructor(interval: Long) {
        this.timeInterval = interval
    }

    override fun onClick(v: View) {
        val nowTime = System.currentTimeMillis()
        if (nowTime - mLastClickTime > timeInterval) {
            onSingleClick()
            mLastClickTime = nowTime
        } else {
            onFastClick()
        }
    }

    protected abstract fun onSingleClick()
    protected open fun onFastClick() {}
}