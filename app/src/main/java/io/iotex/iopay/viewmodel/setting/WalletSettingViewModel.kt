package io.iotex.iopay.viewmodel.setting

import android.app.Application
import androidx.lifecycle.MutableLiveData
import io.iotex.iopay.base.BaseLaunchVM
import io.iotex.iopay.repo.GiftRepo
import io.iotex.iotex.AppVersionQuery

class WalletSettingViewModel(application: Application) : BaseLaunchVM(application) {

    private val giftRepo by lazy{
        GiftRepo()
    }

    val versionCodeLiveData = MutableLiveData<AppVersionQuery.Version_control_android_2>()

    fun fetchAppVersion() {
        giftRepo.fetchAppVersion{
            versionCodeLiveData.postValue(it)
        }
    }
}
