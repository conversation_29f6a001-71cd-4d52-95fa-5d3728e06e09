package io.iotex.iopay.viewmodel.token

import android.app.Application
import androidx.lifecycle.MutableLiveData
import io.iotex.base.RetrofitClient
import io.iotex.iopay.api.NftApi
import io.iotex.iopay.base.BaseLaunchVM
import io.iotex.iopay.data.db.AppDatabase
import io.iotex.iopay.data.db.NftTokenEntry
import io.iotex.iopay.util.Config.NFT_QUERY_URL

class NftViewModel(application: Application) : BaseLaunchVM(application) {

    private val apiService by lazy {
        RetrofitClient.createApiService(NFT_QUERY_URL, NftApi::class.java)
    }

    val nftLogoLiveData = MutableLiveData<String>()
    val nftLiveData = MutableLiveData<NftTokenEntry>()

    suspend fun getNftList(chainId: Int, address: String, type: String, skip: Int, size: Int): List<NftTokenEntry> {
        return kotlin.runCatching {
            apiService.getNftList(chainId, address.lowercase(), type, skip.toString(), size.toString())
        }.getOrNull() ?: emptyList()
    }

    fun getNftTokenLogo(
        chainId: Int,
        ethAddress: String,
        tokenId: String,
        callBack: ((String) -> Unit)? = null
    ) {
        addLaunch {
            val url = apiService.getNftTokenLogo(chainId, ethAddress, tokenId).string()
            callBack?.invoke(url)
            nftLogoLiveData.postValue(url)
        }
    }

    fun getNftLogo(chainId: Int, nft: NftTokenEntry) {
        addLaunch {
            val url = apiService.getNftTokenLogo(chainId, nft.contract, nft.tokenId).string()
            nft.tokenUrl = url
            AppDatabase.getInstance(getApplication()).nftTokenDao().update(nft)
            nftLiveData.postValue(nft)
        }
    }
}