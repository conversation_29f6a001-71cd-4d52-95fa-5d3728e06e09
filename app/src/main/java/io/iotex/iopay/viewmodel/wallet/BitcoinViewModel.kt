package io.iotex.iopay.viewmodel.wallet

import android.app.Application
import androidx.lifecycle.MutableLiveData
import com.blankj.utilcode.util.TimeUtils
import com.blankj.utilcode.util.Utils
import io.iotex.iopay.base.BaseLaunchVM
import io.iotex.iopay.data.UserStore
import io.iotex.iopay.data.db.ACTION_TYPE_BITCOIN_TRANSFER
import io.iotex.iopay.data.db.AddressType_Legacy
import io.iotex.iopay.data.db.AddressType_NativeSegwit
import io.iotex.iopay.data.db.AddressType_NestedSegwit
import io.iotex.iopay.data.db.AddressType_Taproot
import io.iotex.iopay.data.db.AppDatabase
import io.iotex.iopay.data.db.BitcoinAddressType
import io.iotex.iopay.data.db.STATUS_SUCCESS
import io.iotex.iopay.repo.BitcoinRepo
import io.iotex.iopay.transaction.bean.BitcoinFeeData
import io.iotex.iopay.transaction.bean.BitcoinTransactionData
import io.iotex.iopay.util.TokenUtil
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.util.extension.fromSatoshis
import io.iotex.iopay.util.extension.i
import io.iotex.iopay.util.extension.toHexStringNoPrefix
import io.iotex.iopay.wallet.bitcoin.BitcoinHelper
import io.iotex.iopay.wallet.web3.Web3Delegate
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.bitcoinj.core.Transaction
import java.math.BigInteger

class BitcoinViewModel(application: Application) : BaseLaunchVM(application) {

    private val bitcoinRepo by lazy { BitcoinRepo() }
    private val bitcoinQueryRepo by lazy { BitcoinRepo() }

    val bitcoinFeeDataLD = MutableLiveData<BitcoinFeeData>()
    val transactionDataLD = MutableLiveData<BitcoinTransactionData>()
    val transferResultLD = MutableLiveData<String>()
    val sendMaxLD = MutableLiveData<BigInteger>()

    private var waitTime = 8000L

    suspend fun checkBitcoinAddressType(@BitcoinAddressType type:Int){
        withContext(Dispatchers.IO) {
            val walletList = AppDatabase.getInstance(Utils.getApp()).walletDao().queryAllWallets()
            walletList.forEach {
                it.addressType = type
            }
            AppDatabase.getInstance(Utils.getApp()).walletDao().updateWallet(*walletList.toTypedArray())
        }
    }

    private suspend fun buildTransaction(to: String, value: BigInteger, fee: BigInteger): Transaction? {
        return kotlin.runCatching {
            val bitcoinWallet = WalletHelper.getCurWallet()?.getBitcoinWallet() ?: return null
            val utxoBundle = bitcoinQueryRepo.getUTXOBundle(bitcoinWallet.bitcoinAddress)
            val fromAddress = BitcoinHelper.packAddress(bitcoinWallet.bitcoinAddress) ?: return null
            val toAddress = BitcoinHelper.packAddress(to) ?: return null
            BitcoinHelper.buildTransaction(fromAddress, toAddress, value, utxoBundle.availableUTXOs, fee)
        }.getOrNull()
    }

    fun getBitcoinFeeData() {
        addLaunch {
            val bitcoinWallet = WalletHelper.getCurWallet()?.getBitcoinWallet() ?: return@addLaunch
            val network = WalletHelper.getCurNetwork() ?: return@addLaunch
            val walletCache = AppDatabase.getInstance(Utils.getApp()).walletCacheDao()
                .queryWalletCache(bitcoinWallet.bitcoinAddress, WalletHelper.getCurChainId())
            val bitcoinFee = bitcoinQueryRepo.getBitcoinFee()
            bitcoinFeeDataLD.postValue(
                BitcoinFeeData(
                    walletCache?.balance?.toBigIntegerOrNull() ?: BigInteger.ZERO,
                    network.currencyPrice,
                    bitcoinFee?.hourFee?.toBigInteger() ?: BigInteger.ZERO,
                    bitcoinFee?.halfHourFee?.toBigInteger() ?: BigInteger.ZERO,
                    bitcoinFee?.fastestFee?.toBigInteger() ?: BigInteger.ZERO
                )
            )
        }
    }

    fun getBitcoinTransactionData(to: String, value: BigInteger, fee: BigInteger? = null) {
        addLaunch {
            val bitcoinWallet = WalletHelper.getCurWallet()?.getBitcoinWallet() ?: return@addLaunch
            val network = WalletHelper.getCurNetwork() ?: return@addLaunch
            val walletCache = AppDatabase.getInstance(Utils.getApp()).walletCacheDao()
                .queryWalletCache(bitcoinWallet.bitcoinAddress, WalletHelper.getCurChainId())
            val recommendedFee = fee ?: run {
                val bitcoinFee = bitcoinQueryRepo.getBitcoinFee()
                bitcoinFee?.halfHourFee?.toBigInteger() ?: BigInteger.ZERO
            }
            val transaction = buildTransaction(to, value, recommendedFee)
            val utxoBundle = bitcoinQueryRepo.getUTXOBundle(bitcoinWallet.bitcoinAddress)
            val gasFee = if (transaction != null) BitcoinHelper.estimateGasFee(transaction, recommendedFee) else
                BigInteger.ZERO
            val bitcoinTransactionData = BitcoinTransactionData(
                walletCache?.balance?.toBigIntegerOrNull() ?: BigInteger.ZERO,
                network.currencyPrice,
                gasFee,
                transaction,
                utxoBundle
            )
            transactionDataLD.postValue(bitcoinTransactionData)
        }
    }

    fun sendTransaction(to: String, value: BigInteger, transaction: Transaction) {
        addLaunch(true) {
            val bitcoinWallet = WalletHelper.getCurWallet()?.getBitcoinWallet() ?: return@addLaunch
            when (bitcoinWallet.addressType) {
                AddressType_Legacy -> {
                    val signedTransaction = BitcoinHelper.signLegacyTransaction(transaction) ?: return@addLaunch
                    broadcastTransaction(bitcoinWallet.bitcoinAddress, to, value, signedTransaction)
                }
                AddressType_NativeSegwit -> {
                    val signedTransaction = BitcoinHelper.signSegWitTransaction(transaction, false) ?: return@addLaunch
                    broadcastTransaction(bitcoinWallet.bitcoinAddress, to, value, signedTransaction)
                }
                AddressType_NestedSegwit -> {
                    val signedTransaction = BitcoinHelper.signSegWitTransaction(transaction, true) ?: return@addLaunch
                    broadcastTransaction(bitcoinWallet.bitcoinAddress, to, value, signedTransaction)
                }
                AddressType_Taproot -> {
                    val signedTransaction = BitcoinHelper.signTaprootTransaction(transaction) ?: return@addLaunch
                    broadcastTransaction(bitcoinWallet.bitcoinAddress, to, value, signedTransaction)
                }
            }
        }
    }

    private suspend fun broadcastTransaction(from: String, to: String, value: BigInteger, transaction: Transaction) {
        val txHex = transaction.bitcoinSerialize().toHexStringNoPrefix()
        "txHex --> $txHex".i()
        val transactionId = bitcoinRepo.broadcastTx(txHex)
        val timestamp = TimeUtils.getNowMills().toString()
        if (!transactionId.isNullOrBlank()) {
            Web3Delegate.insertTransaction(
                timestamp,
                transactionId,
                "",
                from,
                to,
                value.fromSatoshis(),
                type = ACTION_TYPE_BITCOIN_TRANSFER,
                decimals = TokenUtil.getCurrencyDecimal().toString(),
                symbol = UserStore.getNetworkSymbol()
            )
            transferResultLD.postValue(transactionId!!)
            iView?.hideLoading()
            val transactionDetail = bitcoinQueryRepo.waitForTransactionDetail(transactionId)
            if (transactionDetail?.status?.confirmed == true) {
                Web3Delegate.updateAction(
                    timestamp,
                    transactionId,
                    STATUS_SUCCESS,
                )
            }
        }
    }

    suspend fun calculateSendMax(to: String) {
        addLaunch {
            val bitcoinWallet = WalletHelper.getCurWallet()?.getBitcoinWallet() ?: return@addLaunch
            val toAddress = BitcoinHelper.packAddress(to) ?: return@addLaunch
            val utxoBundle = bitcoinQueryRepo.getUTXOBundle(bitcoinWallet.bitcoinAddress)
            val bitcoinFee = bitcoinQueryRepo.getBitcoinFee()
            val fee = bitcoinFee?.halfHourFee?.toBigInteger() ?: BigInteger.ZERO

            val gasFee = BitcoinHelper.estimateGasFee(toAddress, utxoBundle.availableUTXOs.size, 2, fee)
            val walletCache = AppDatabase.getInstance(Utils.getApp()).walletCacheDao()
                .queryWalletCache(bitcoinWallet.bitcoinAddress, WalletHelper.getCurChainId()) ?: return@addLaunch
            sendMaxLD.postValue(walletCache.balance.toBigInteger() - gasFee)
        }
    }
}