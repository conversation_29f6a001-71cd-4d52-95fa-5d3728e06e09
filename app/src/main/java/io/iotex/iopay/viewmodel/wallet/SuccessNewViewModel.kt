package io.iotex.iopay.viewmodel.wallet

import android.app.Application
import androidx.lifecycle.MutableLiveData
import com.blankj.utilcode.util.DeviceUtils
import com.google.gson.Gson
import io.iotex.base.RetrofitClient
import io.iotex.iopay.api.IoPayApi
import io.iotex.iopay.base.BaseLaunchVM
import io.iotex.iopay.util.Config.IOPAY_URL

class SuccessNewViewModel(application: Application) : BaseLaunchVM(application) {
    private val apiService by lazy {
        RetrofitClient.createApiService(IOPAY_URL, IoPayApi::class.java)
    }

    val statusLiveData = MutableLiveData<String>()

    fun newWalletClaim(address: String) {
        addLaunch {
            val map = HashMap<String, Any>().apply {
                put("batch", 1)
                put("input", Gson().toJson(HashMap<String, Any>().apply {
                    put("0", HashMap<String, Any>().apply {
                        put("json", HashMap<String, Any>().apply {
                            put("deviceId", DeviceUtils.getAndroidID())
                            put("address", address)
                        })
                    })
                }))
            }
            val claimResponse = apiService.newWalletClaim(map)
            if (claimResponse.isNotEmpty()) {
                statusLiveData.postValue(claimResponse[0].result?.data?.json?.result?.status)
            }
        }
    }
}