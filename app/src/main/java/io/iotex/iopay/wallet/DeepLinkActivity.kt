package io.iotex.iopay.wallet

import android.content.Intent
import android.net.Uri
import android.text.TextUtils
import com.blankj.utilcode.util.LogUtils
import com.github.iotexproject.antenna.account.Account
import com.github.iotexproject.antenna.jwt.EK256K
import com.github.iotexproject.antenna.utils.Numeric
import com.google.gson.Gson
import io.iotex.base.BaseViewModel
import io.iotex.base.bindbase.BaseBindActivity
import io.iotex.iopay.R
import io.iotex.iopay.data.db.AppDatabase
import io.iotex.iopay.data.db.SignApp
import io.iotex.iopay.data.db.Wallet
import io.iotex.iopay.databinding.ActivityDeeplinkBinding
import io.iotex.iopay.util.PBKDF2
import io.iotex.iopay.util.SignUtils
import io.iotex.iopay.util.TokenUtil
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.util.extension.loadSvgOrImage
import io.iotex.iopay.util.extension.setInvisible
import io.iotex.iopay.util.extension.setVisible
import io.iotex.iopay.util.extension.toEvmAddress
import io.iotex.iopay.util.extension.toIoAddress
import io.iotex.iopay.wallet.list.SwitchWalletActivity
import org.jetbrains.anko.doAsync
import org.jetbrains.anko.toast
import org.jetbrains.anko.uiThread
import java.util.Date

class DeepLinkActivity : BaseBindActivity<BaseViewModel,ActivityDeeplinkBinding>(R.layout.activity_deeplink) {

    private val PARAM_BACK_URL = "next"
    private val PARAM_NONCE = "nonce"
    private val PARAM_SOURCE = "source"
    private val PARAM_APPNAME = "appName"
    private var currentAccount: Account? = null
    private var source: String? = null
    private var jti: String? = null
    private var seed: String? = null
    private var appName: String? = null
    private var firstIntent: Int = 0

    override fun initView() {
        val bundle = intent.extras
        if (bundle != null) {
            source = bundle.getString(PARAM_BACK_URL)
            jti = bundle.getString(PARAM_NONCE)
            seed = bundle.getString(PARAM_SOURCE)
            appName = bundle.getString(PARAM_APPNAME)
        }
        if (TextUtils.isEmpty(source)) {
            finish()
        }
        loginWithWalletInit(null)
        if (TextUtils.isEmpty(appName)) {
            mBinding.tvName.text = getString(R.string.app_login_title, getString(R.string.ucam))
        } else {
            mBinding.tvName.text = getString(R.string.app_login_title, appName)
        }
        mBinding.confirmButton.setOnClickListener {
            if (firstIntent <= 1 && !seed.isNullOrBlank() && !source.isNullOrBlank()) {
                val token = loginWithJWT()
                if (currentAccount == null) return@setOnClickListener
                val cameraKeyByte = PBKDF2.derive(Numeric.toHexString(currentAccount?.publicKey()), seed, 1, 16, "HmacSHA1")
                val masterKey = PBKDF2.bytesToHex(cameraKeyByte).lowercase()
                val uri = Uri.parse("$source/?token=$token&key=$masterKey")
                val intent = Intent(Intent.ACTION_VIEW, uri)
                startActivity(intent)
            } else {
                toast(R.string.login_expire_time)
                finish()
            }
        }

        mBinding.cancelButton.setOnClickListener {
            if (firstIntent <= 1) {
                val uri = Uri.parse(source)
                val intent = Intent(Intent.ACTION_VIEW, uri)
                startActivity(intent)
            } else {
                toast(R.string.login_expire_time)
                finish()
            }
        }

        mBinding.changeWallet.setOnClickListener {
            SwitchWalletActivity.startActivity(this, ioWallet = true, ioAddress = currentAccount?.address()?.toEvmAddress()?:""){
                loginWithWalletInit(it)
            }
        }
    }

    private fun loginWithWalletInit(changeWallet: Wallet?) {
        doAsync {
            val wallet = changeWallet ?: WalletHelper.getCurWallet()
            if (wallet != null) {
                val account= WalletHelper.getAccountByWallet(wallet)
                uiThread {
                    if (account != null){
                        currentAccount = account
                        mBinding.walletAlias.text = wallet.alias
                        mBinding.walletThumbnail.loadSvgOrImage(wallet.avatar, R.drawable.icon_wallet_default)
                        if (wallet.address.length > 20) {
                            mBinding.walletAddress.text = TokenUtil.textEllipsis(wallet.address.toIoAddress(), 12, 8)
                        } else {
                            mBinding.walletAddress.text = wallet.address.toIoAddress()
                        }
                        mBinding.relItem.setVisible()
                    } else {
                        currentAccount = null
                        mBinding.relItem.setInvisible()
                    }
                }
            }
        }
    }

    private fun loginWithJWT(): String {
        if (currentAccount == null) return ""
        val payload: LinkedHashMap<String, String> = linkedMapOf()
        val iat = Date().time
        val exp = iat.plus(SignUtils.TIME_LOGIN_TOKEN_EXPIRE)
        val subAddress = currentAccount?.address()

        jti?.let {
            payload["iss"] = Numeric.toHexString(currentAccount?.publicKey())
            payload["jti"] = it
            payload["sub"] = "did:io:$subAddress"
            payload["exp"] = exp.toString()
            payload["iat"] = iat.toString()
        }

        val payloadJson = Gson().toJson(payload)
        val token = EK256K.sign(
            payloadJson,
            Numeric.toHexString(currentAccount?.privateKey())
        )

        if (!source.isNullOrBlank() && !jti.isNullOrBlank()) {
            doAsync {
                try {
                    val exist = AppDatabase.getInstance(<EMAIL>).signAppDao().querySignExist(source!!)
                    val signId = exist?.signId
                    val signApp = SignApp(signId?:0, token, source!!, jti!!, iat, exp)
                    if (signId != null && signId > 0) {
                        AppDatabase.getInstance(<EMAIL>).signAppDao()
                            .updateSignApp(signApp)
                    } else {
                        AppDatabase.getInstance(<EMAIL>)
                            .signAppDao()
                            .insertSignApp(signApp)
                    }
                } catch (e: Exception) {
                    LogUtils.e("Tag", "Save signApp error", e)
                }
            }
        }
        return token
    }
}
