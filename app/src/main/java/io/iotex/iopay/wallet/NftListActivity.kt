package io.iotex.iopay.wallet

import androidx.lifecycle.lifecycleScope
import com.drakeet.multitype.MultiTypeAdapter
import io.iotex.base.bindbase.BaseBindActivity
import io.iotex.iopay.R
import io.iotex.iopay.base.BaseBindToolbarActivity
import io.iotex.iopay.data.db.NftTokenEntry
import io.iotex.iopay.databinding.ActivityNftListBinding
import io.iotex.iopay.ui.binder.*
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.util.extension.setGone
import io.iotex.iopay.util.extension.setVisible
import io.iotex.iopay.util.extension.updateItem
import io.iotex.iopay.viewmodel.token.NftViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.jetbrains.anko.startActivity

class NftListActivity :
    BaseBindToolbarActivity<NftViewModel, ActivityNftListBinding>(R.layout.activity_nft_list),
    OnLoadMoreListener {

    private val nftList by lazy {
        intent.getParcelableArrayListExtra<NftTokenEntry>(KEY_NFT_LIST)
    }
    private val mAdapter = MultiTypeAdapter()
    private lateinit var mLoadMoreDelegate: LoadMoreDelegate
    private val pageSize = 15

    override fun initView() {
        initRecycleView()
        if (nftList?.isNotEmpty() == true) {
            setToolbarTitle(nftList?.get(0)?.name?:"")
        }
    }

    private fun initRecycleView() {
        val binder = NftTokenListBinder().apply {
            setItemClickCallback { nft ->
                startActivity<NftTokenDetailActivity>(
                    NftTokenDetailActivity.KEY_NFT_TOKEN to nft
                )
            }
        }
        mAdapter.register(NftTokenEntry::class, binder)
        mAdapter.register(LoadMoreEntry::class, LoadMoreBinder())
        mBinding.mRvTokens.adapter = mAdapter
        mBinding.mRvTokens.itemAnimator?.changeDuration = 0
        mLoadMoreDelegate = LoadMoreDelegate(mAdapter, this)
        mLoadMoreDelegate.attach(mBinding.mRvTokens)
    }

    override fun initData() {
        lifecycleScope.launch { loadData() }
    }

    private suspend fun loadData() {
        val skip = if (mAdapter.itemCount == 0) 0 else mAdapter.itemCount - 1
        val list = withContext(Dispatchers.IO) {
            delay(500)
            nftList?.let {
                if (it.size > pageSize + skip) {
                    it.subList(skip, pageSize + skip)
                } else {
                    it.subList(skip, it.size)
                }
            } ?: emptyList()
        }
        mLoadMoreDelegate.addData(list)
        mLoadMoreDelegate.loadMoreComplete()
        mLoadMoreDelegate.setNoMoreData(list.size < pageSize)
        if (mAdapter.itemCount > 0) {
            mBinding.mRvTokens.setVisible()
            mBinding.mLlEmpty.setGone()
        } else {
            mBinding.mRvTokens.setGone()
            mBinding.mLlEmpty.setVisible()
        }
        queryNftLogo(list)
    }

    private fun queryNftLogo(tokenList: List<NftTokenEntry>) {
        val chainId = WalletHelper.getCurChainId()
        tokenList.forEach { nftToken ->
            if (nftToken.tokenUrl.isBlank()) {
                mViewModel.getNftLogo(chainId, nftToken)
            }
        }
    }

    override fun onLoadMore() {
        lifecycleScope.launch {
            loadData()
        }
    }

    override fun initEvent() {
        mViewModel.nftLiveData.observe(this) { nft ->
            mAdapter.updateItem(nft) {
                it.contract == nft.contract && nft.tokenId == it.tokenId &&
                        it.chainId == nft.chainId
            }
        }
    }

    companion object {
        const val KEY_NFT_LIST = "key_nft_list"
    }
}