package io.iotex.iopay.wallet

import android.annotation.SuppressLint
import androidx.lifecycle.lifecycleScope
import com.blankj.utilcode.util.KeyboardUtils
import com.blankj.utilcode.util.Utils
import com.drakeet.multitype.MultiTypeAdapter
import io.iotex.base.bindbase.BaseBindActivity
import io.iotex.iopay.R
import io.iotex.iopay.data.db.AppDatabase
import io.iotex.iopay.data.db.NftTokenEntry
import io.iotex.iopay.databinding.ActivityNftSearchBinding
import io.iotex.iopay.ui.binder.*
import io.iotex.iopay.util.Constant
import io.iotex.iopay.util.RxUtil
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.util.extension.setGone
import io.iotex.iopay.util.extension.setVisible
import io.iotex.iopay.util.extension.updateItem
import io.iotex.iopay.viewmodel.token.NftViewModel
import kotlinx.coroutines.*
import org.jetbrains.anko.startActivity
import java.util.concurrent.TimeUnit

class NftSearchActivity: BaseBindActivity<NftViewModel, ActivityNftSearchBinding>(R.layout.activity_nft_search),
    OnLoadMoreListener {

    private val mAdapter = MultiTypeAdapter()
    private lateinit var mLoadMoreDelegate: LoadMoreDelegate

    private val mPageSize = 15
    private var mPage = 1

    @SuppressLint("CheckResult")
    override fun initView() {
        initRecycleView()
        RxUtil.textChange(mBinding.etSearch)
            .debounce(800, TimeUnit.MILLISECONDS)
            .compose(RxUtil.applySchedulers())
            .subscribe {
                if (it.trim().isNotBlank()) {
                    KeyboardUtils.hideSoftInput(this)
                    mBinding.ivClear.setVisible()
                }
                mPage = 1
                loadNftFromLocal(it.trim())
            }
        mBinding.ivClear.setOnClickListener {
            mBinding.etSearch.setText("")
        }
        mBinding.tvCancel.setOnClickListener {
            KeyboardUtils.hideSoftInput(this)
            onBackPressed()
        }
        lifecycleScope.launchWhenResumed {
            delay(500)
            KeyboardUtils.showSoftInput(mBinding.etSearch)
        }
    }

    private fun initRecycleView() {
        val binder = NftTokenListBinder().apply {
            setItemClickCallback { nft ->
                startActivity<NftTokenDetailActivity>(
                    NftTokenDetailActivity.KEY_NFT_TOKEN to nft
                )
            }
        }
        mAdapter.register(NftTokenEntry::class, binder)
        mAdapter.register(LoadMoreEntry::class, LoadMoreBinder())
        mBinding.rvTokens.adapter = mAdapter
        mLoadMoreDelegate = LoadMoreDelegate(mAdapter, this)
        mLoadMoreDelegate.attach(mBinding.rvTokens)
    }

    override fun initData() {
        loadNftFromLocal("")
    }

    private fun loadNftFromLocal(searchStr: String, loadMore: Boolean = false) {
        lifecycleScope.launchWhenResumed {
            val chainId = WalletHelper.getCurChainId()
            val address = Constant.currentWallet?.address?.let {
                WalletHelper.convertWeb3Address(it)
            } ?: return@launchWhenResumed
            val list = withContext(Dispatchers.IO) {
                val skip = if (!loadMore) 0 else (mPage - 1) * mPageSize
                val size = if (!loadMore) mPageSize * mPage else mPageSize
                if (loadMore) delay(1000)
                if(searchStr.isEmpty()){
                    AppDatabase.getInstance(Utils.getApp()).nftTokenDao()
                        .queryTokens(address, chainId, skip, size)
                } else {
                    AppDatabase.getInstance(Utils.getApp()).nftTokenDao()
                        .queryTokensByLike(address, chainId, searchStr, skip, size)
                }
            }
            if (!loadMore) {
                mLoadMoreDelegate.refresh(list)
                mLoadMoreDelegate.setNoMoreData(false)
            } else {
                mLoadMoreDelegate.addData(list)
                mLoadMoreDelegate.loadMoreComplete()
                mLoadMoreDelegate.setNoMoreData(list.size < mPageSize)
            }
            if (mAdapter.itemCount > 0) {
                mBinding.rvTokens.setVisible()
                mBinding.llEmpty.setGone()
            } else {
                mBinding.rvTokens.setGone()
                mBinding.llEmpty.setVisible()
            }
            queryNftLogo(list)
        }
    }

    private fun queryNftLogo(tokenList: List<NftTokenEntry>) {
        val chainId = WalletHelper.getCurChainId()
        tokenList.forEach { nftToken ->
            if (nftToken.tokenUrl.isBlank()) {
//                mViewModel.getNftLogo(chainId, nftToken)
            }
        }
    }

    override fun onLoadMore() {
        mPage++
        val searchStr = mBinding.etSearch.text.toString()
        loadNftFromLocal(searchStr, true)
    }

    override fun initEvent() {
        mViewModel.nftLiveData.observe(this) { nft ->
            mAdapter.updateItem(nft) {
                it.contract == nft.contract && nft.tokenId == it.tokenId &&
                        it.chainId == nft.chainId
            }
        }
    }
}