package io.iotex.iopay.wallet

import android.content.Intent
import android.graphics.drawable.Drawable
import android.os.Bundle
import android.text.method.LinkMovementMethod
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.PopupWindow
import android.widget.ScrollView
import androidx.appcompat.content.res.AppCompatResources
import androidx.appcompat.widget.Toolbar
import androidx.core.content.ContextCompat
import androidx.core.graphics.drawable.DrawableCompat
import androidx.core.view.isVisible
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import com.blankj.utilcode.util.ClipboardUtils
import com.blankj.utilcode.util.ColorUtils
import com.blankj.utilcode.util.ImageUtils
import com.blankj.utilcode.util.Utils
import com.bumptech.glide.Glide
import com.bumptech.glide.request.target.CustomTarget
import com.bumptech.glide.request.transition.Transition
import com.machinefi.w3bstream.utils.extension.ellipsis
import com.machinefi.w3bstream.utils.extension.setClickSpan
import io.iotex.base.BaseViewModel
import io.iotex.base.bindbase.BaseBindActivity
import io.iotex.iopay.R
import io.iotex.iopay.data.UserStore
import io.iotex.iopay.data.db.AppDatabase
import io.iotex.iopay.data.db.NFT_TYPE_1155
import io.iotex.iopay.data.db.NFT_TYPE_721
import io.iotex.iopay.data.db.NftTokenEntry
import io.iotex.iopay.data.db.STATUS_WAITING
import io.iotex.iopay.databinding.ActivityNftTokenDetailBinding
import io.iotex.iopay.databinding.ViewPopSbtBinding
import io.iotex.iopay.support.eventbus.ActionRefreshEvent
import io.iotex.iopay.transaction.NftTransferActivity
import io.iotex.iopay.util.Config
import io.iotex.iopay.util.Config.CONTRACT_STAKE_NFT
import io.iotex.iopay.util.Config.CONTRACT_STAKE_NFT_V2
import io.iotex.iopay.util.Config.CONTRACT_STAKE_NFT_V3
import io.iotex.iopay.util.Config.STAKE_NFT_DETAIL_URL
import io.iotex.iopay.util.Config.STAKE_NFT_V2_DETAIL_URL
import io.iotex.iopay.util.Constant
import io.iotex.iopay.util.Constant.URL_IIP_SBT
import io.iotex.iopay.util.FireBaseEvent
import io.iotex.iopay.util.FireBaseUtil
import io.iotex.iopay.util.IoPayConstant
import io.iotex.iopay.util.PageEventUtil
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.util.extension.asBigDecimal
import io.iotex.iopay.util.extension.loadGif
import io.iotex.iopay.util.extension.loadImage
import io.iotex.iopay.util.extension.setGone
import io.iotex.iopay.util.extension.setVisible
import io.iotex.iopay.util.extension.toast
import io.iotex.iopay.wallet.dialog.NftShareDialog
import io.iotex.iopay.wallet.viewmodel.NftTokenDetailViewModel
import io.iotex.iopay.wallet.web3.Web3Delegate
import io.iotex.iopay.xapp.XAppsActivity
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.jetbrains.anko.doAsync
import org.jetbrains.anko.startActivity
import org.jetbrains.anko.uiThread
import java.io.File
import java.math.BigDecimal


class NftTokenDetailActivity: BaseBindActivity<BaseViewModel,ActivityNftTokenDetailBinding>(R.layout.activity_nft_token_detail) {

    private val mNftToken by lazy {
        intent.getParcelableExtra(KEY_NFT_TOKEN) as? NftTokenEntry
    }

    private val mNftTokenDetailViewModel by lazy {
        ViewModelProvider(this)[NftTokenDetailViewModel::class.java]
            .apply {
                lifecycle.addObserver(this)
            }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        mNftToken ?: finish()
        super.onCreate(savedInstanceState)
        EventBus.getDefault().register(this)
        initView()
        val map = HashMap<String,String>()
        map["name"] = mNftToken?.name?:""
        PageEventUtil.logEvent(PageEventUtil.NFTDETAIL,map)
    }

    override fun initView() {
        val toolbar = findViewById<Toolbar>(R.id.toolbar)
        setSupportActionBar(toolbar)
        supportActionBar?.setDisplayShowTitleEnabled(false)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        toolbar.setNavigationOnClickListener {
            finish()
        }

        mBinding.llRight.setOnClickListener {
            mNftToken?.let { nftToken ->
                NftShareDialog(this, nftToken).show()
            }
            FireBaseUtil.logFireBase("action_nft_share")
        }

        val chainId = UserStore.getChainId()
        if(chainId != Config.IOTEX_CHAIN_ID){
            mBinding.mLlMarket.setGone()
            mBinding.mLlSend.setBackgroundResource(R.drawable.btn_shape_gradient_common)
            mBinding.mTvSend.setTextColor(ColorUtils.getColor(R.color.white))
            AppCompatResources.getDrawable(this, R.drawable.ic_send)?.let {
                DrawableCompat.setTint(it, ColorUtils.getColor(R.color.white))
                mBinding.mIvSend.setImageDrawable(it)
            }
        }


        mBinding.mIvTokenImage.post {
            val params = mBinding.mIvTokenImage.layoutParams
            params.height = mBinding.mIvTokenImage.width
            mBinding.mIvTokenImage.layoutParams = params
        }

        mNftToken?.let {
            loadImage(it.tokenUrl)
            mBinding.toolbarTitle.text = it.name
            mBinding.mTvTokenName.text = it.name
            val contractText = it.contract.ellipsis(6, 8)
            mBinding.mTvContract.text = contractText.setClickSpan(contractText, ContextCompat.getColor(this, R.color.color_title)){
                WalletHelper.gotoExplorerTokenScan(it.contract)
                FireBaseUtil.logFireBase(FireBaseEvent.ACTION_HOME_NFT_CONTRACT_CLICK)
            }
            mBinding.mTvContract.movementMethod = LinkMovementMethod()
            mBinding.mTvTokenId.text = getString(R.string.nft_no, it.tokenId)
            mBinding.mTvProtocol.text = "ERC-${it.type}"
            if (it.type == NFT_TYPE_1155 && it.amount.asBigDecimal() > BigDecimal.ZERO) {
                mBinding.mTvAmount.setVisible()
                mBinding.mTvAmount.text = Utils.getApp().getString(R.string.nft, it.amount)
            }
            if (it.sbt) {
                mBinding.ivSbt.setVisible()
                mBinding.mLlSend.setGone()
                mBinding.ivSbt.setOnClickListener {
                    showPopupWindow()
                }
            } else {
                mBinding.ivSbt.setGone()
                mBinding.mLlSend.setVisible()
            }
            lifecycleScope.launch {
                val network = withContext(Dispatchers.IO) {
                    WalletHelper.getCurNetwork()
                }
                mBinding.mTvChain.text = network?.name
            }
            if (it.type == NFT_TYPE_721) {
                doAsync {
                    val wallet = WalletHelper.getCurWallet()
                    if(wallet?.isAAWallet() == false){
                        val owner = Web3Delegate.nftOwnerOf(it.contract, it.tokenId.toBigInteger())
                        val walletAddress = Constant.currentWallet?.address
                        if (walletAddress != owner) {
                            uiThread {
                                mBinding.mLlSend.isEnabled = false
                                mBinding.mLlSend.alpha = 0.5f
                            }
                        }
                    }
                }
            }
            if (it.type == NFT_TYPE_1155) {
                doAsync {
                    val wallet = WalletHelper.getCurWallet()
                    if(wallet?.isAAWallet() == false){
                        val balance = Web3Delegate.nftBalanceOf(it.contract, it.tokenId.toBigInteger())
                        if (balance < (it.amount.toIntOrNull() ?: 0)) {
                            uiThread {
                                mBinding.mLlSend.isEnabled = false
                                mBinding.mLlSend.alpha = 0.5f
                            }
                        }
                    }
                }
            }

            doAsync {
                val address = Constant.currentWallet?.address?:""
                val action = AppDatabase.getInstance(Utils.getApp()).actionRecordDao().queryByNftTokenId(address, chainId, it.contract, it.tokenId)
                if(STATUS_WAITING == action?.status){
                    uiThread {
                        mBinding.mLlSend.isEnabled = false
                        mBinding.mLlSend.alpha = 0.5f
                    }
                }
            }

            if(it.contract == CONTRACT_STAKE_NFT || it.contract == CONTRACT_STAKE_NFT_V2 || it.contract == CONTRACT_STAKE_NFT_V3){
                mNftTokenDetailViewModel.getBucketInfo(it.contract,it.tokenId)
            }
            mBinding.tvViewScan.isVisible = it.contract == CONTRACT_STAKE_NFT || it.contract == CONTRACT_STAKE_NFT_V2 || it.contract == CONTRACT_STAKE_NFT_V3
            mNftTokenDetailViewModel.bucketInfoLiveData.observe(this){ bucket ->
                mBinding.llBucket.setVisible()
                mBinding.ivDown.setVisible()
                mBinding.tvStakeAmount.text = bucket.amount
                mBinding.tvStakeDuration.text = bucket.duration
                mBinding.tvStakeLock.text = bucket.lock
                mBinding.tvStakeStatus.text = bucket.status
                mBinding.tvStakeVote.text = bucket.vote
                if (bucket.status == Utils.getApp().getString(R.string.stake_status_unstaking)) {
                    mBinding.mLlSend.isEnabled = false
                    mBinding.mLlSend.alpha = 0.5f
                }
                mBinding.tvViewScan.setOnClickListener { _->
                    val url = when (it.contract) {
                        CONTRACT_STAKE_NFT -> {
                            STAKE_NFT_DETAIL_URL + mNftToken?.tokenId
                        }
                        CONTRACT_STAKE_NFT_V2 -> {
                            STAKE_NFT_V2_DETAIL_URL + mNftToken?.tokenId
                        }
                        else -> {
                            Config.IOTEX_URL
                        }
                    }
                    val intent = Intent(this, XAppsActivity::class.java)
                    intent.putExtra(
                        IoPayConstant.BROWSER_URL,
                        url
                    )
                    startActivity(intent)
                }
            }
        }

        mBinding.mIvCopyAddress.setOnClickListener {
            ClipboardUtils.copyText(mNftToken?.contract)
            getString(R.string.copy_success).toast()
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_HOME_NFT_CONTRACT_COPY)
        }
        mBinding.mIvCopyTokenId.setOnClickListener {
            ClipboardUtils.copyText(mNftToken?.tokenId)
            getString(R.string.copy_success).toast()
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_HOME_NFT_TOKEN_ID_COPY)
        }

        mBinding.mLlSend.setOnClickListener {
            if (Constant.currentWallet?.isWatch == true) {
                WalletHelper.showWatchAddress(this)
                return@setOnClickListener
            }
            startActivity<NftTransferActivity>(
                NftTransferActivity.KEY_NFT_TOKEN to mNftToken
            )
            FireBaseUtil.logFireBase("action_nft_send")
        }

        mBinding.mLlMarket.setOnClickListener {
            val intent = Intent(this, XAppsActivity::class.java)
            intent.putExtra(IoPayConstant.BROWSER_URL, Config.MIMO_NFT_MARKET_PLACE_URL+
                mNftToken?.contract+"/"+mNftToken?.tokenId)
            startActivity(intent)
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_HOME_NFT_MARKETPLACE)
        }

        mBinding.scrollView.setOnScrollChangeListener { _, _, scrollY, _, _ ->
            val onlyChild = mBinding.scrollView.getChildAt(0)
            if (onlyChild.height <= scrollY + mBinding.scrollView.height) {
                mBinding.ivDown.setGone()
            }
        }

        mBinding.ivDown.setOnClickListener {
            mBinding.scrollView.fullScroll(ScrollView.FOCUS_DOWN)
        }
    }

    private fun showPopupWindow() {

        val bind = ViewPopSbtBinding.inflate(LayoutInflater.from(this))
        PopupWindow(
            bind.root,
            ViewGroup.LayoutParams.WRAP_CONTENT,
            ViewGroup.LayoutParams.WRAP_CONTENT
        ).apply {
            val more = getString(R.string.learn_more_short)
            val sbtInfo = getString(R.string.sbts_are_issued_by)
            bind.tvInfo.text = "$sbtInfo$more"
                .setClickSpan(more, ColorUtils.getColor(R.color.color_617AFF)) {
                    val intent = Intent(this@NftTokenDetailActivity, XAppsActivity::class.java)
                    intent.putExtra(IoPayConstant.BROWSER_URL,URL_IIP_SBT)
                    startActivity(intent)
                }
            bind.tvInfo.movementMethod = LinkMovementMethod()
            isOutsideTouchable = true
            showAsDropDown(mBinding.ivSbt)
        }
    }

    private fun loadImage(url: String) {
        Glide.with(this)
            .downloadOnly()
            .load(url)
            .into(object : CustomTarget<File>() {
                override fun onResourceReady(resource: File, transition: Transition<in File>?) {
                    val type = ImageUtils.getImageType(resource)
                    if (type == ImageUtils.ImageType.TYPE_GIF) {
                        mBinding.mIvTokenImage.loadGif(resource, R.drawable.icon_nft_default)
                    } else {
                        mBinding.mIvTokenImage.loadImage(resource, R.drawable.icon_nft_default)
                    }
                }

                override fun onLoadCleared(placeholder: Drawable?) {
                    //nothing.
                }
            })
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onWalletSwitchEvent(event: ActionRefreshEvent) {
        initView()
    }

    override fun onDestroy() {
        EventBus.getDefault().unregister(this)
        super.onDestroy()
    }

    companion object {
        const val KEY_NFT_TOKEN = "key_nft_token"
    }
}