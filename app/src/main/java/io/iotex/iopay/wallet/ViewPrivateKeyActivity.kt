package io.iotex.iopay.wallet

import android.content.Context
import android.content.Intent
import android.view.View
import android.view.WindowManager
import androidx.core.view.isVisible
import androidx.lifecycle.lifecycleScope
import io.iotex.iopay.R
import io.iotex.iopay.base.BaseBindToolbarActivity
import io.iotex.iopay.data.db.Wallet
import io.iotex.iopay.databinding.ActivityViewPrivatekeyBinding
import io.iotex.iopay.util.QRCodeUtil
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.wallet.bitcoin.BitcoinHelper
import io.iotex.iopay.wallet.dialog.CopyPrivateKeyDialog
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.web3j.crypto.WalletUtils

class ViewPrivateKeyActivity : BaseBindToolbarActivity<Nothing, ActivityViewPrivatekeyBinding>(R.layout.activity_view_privatekey) {

    private var isHexView = false
    private var isWifView = false

    private val wallet by lazy {
        intent.getParcelableExtra<Wallet>(KEY_WALLET)
    }

    override fun initView() {
        window.setFlags(WindowManager.LayoutParams.FLAG_SECURE, WindowManager.LayoutParams.FLAG_SECURE)
        setToolbarTitle(getString(R.string.private_key))
    }

    override fun initData() {
        lifecycleScope.launch {
            val hexKey = withContext(Dispatchers.IO) {
                WalletHelper.getWalletPrivateKey(wallet)
            }
            if (!WalletHelper.isValidPrivateKey(hexKey)) <EMAIL>()
            initUI(hexKey)
        }
    }

    private fun initUI(hexKey: String) {
        if (WalletHelper.isBitcoinNetwork()) {
            mBinding.llWifContainer.isVisible = true
            mBinding.tvHexKeyLabel.isVisible = true
            val wifKey = BitcoinHelper.hexPrivateKeyToWif(hexKey)
            mBinding.tvWifPrivateKey.text = "*".repeat(wifKey.length)
            mBinding.btnWifCopy.setOnClickListener {
                mBinding.tvWifPrivateKey.text = "*".repeat(wifKey.length)
                mBinding.btnWifView.background = getDrawable(R.drawable.icon_hide)
                isWifView = false
                CopyPrivateKeyDialog(wifKey).show(supportFragmentManager,System.currentTimeMillis().toString())
            }
            mBinding.btnWifView.setOnClickListener{
                if (!isWifView) {
                    mBinding.tvWifPrivateKey.text = wifKey
                    mBinding.btnWifView.background = getDrawable(R.drawable.icon_eye)
                    isWifView = true
                } else {
                    mBinding.tvWifPrivateKey.text = "*".repeat(wifKey.length)
                    mBinding.btnWifView.background = getDrawable(R.drawable.icon_hide)
                    isWifView = false
                }
            }

            mBinding.tvHexPrivateKey.text = "*".repeat(wifKey.length)
            val mBitmap = QRCodeUtil.createQRCodeBitmap(wifKey, 480, 480)
            mBinding.ivWifQrCode.setImageBitmap(mBitmap)

            var isQRView = false
            mBinding.btnWifQrCode.setOnClickListener {
                if (!isQRView) {
                    mBinding.btnWifQrCode.background = getDrawable(R.drawable.icon_qr_code_gray)
                    mBinding.ivWifQrCode.visibility = View.VISIBLE
                    isQRView = true
                } else {
                    mBinding.btnWifQrCode.background = getDrawable(R.drawable.icon_qr_code)
                    mBinding.ivWifQrCode.visibility = View.GONE
                    isQRView = false
                }
            }
        }


        mBinding.tvHexPrivateKey.text = "*".repeat(hexKey.length)
        val mBitmap = QRCodeUtil.createQRCodeBitmap(hexKey, 480, 480)
        mBinding.ivHexQrCode.setImageBitmap(mBitmap)
        mBinding.btnHexCopy.setOnClickListener {
            mBinding.tvHexPrivateKey.text = "*".repeat(hexKey.length)
            mBinding.btnHexView.background = getDrawable(R.drawable.icon_hide)
            isHexView = false
            CopyPrivateKeyDialog(hexKey).show(supportFragmentManager,System.currentTimeMillis().toString())
        }

        mBinding.btnHexView.setOnClickListener {
            if (!isHexView) {
                mBinding.tvHexPrivateKey.text = hexKey
                mBinding.btnHexView.background = getDrawable(R.drawable.icon_eye)
                isHexView = true
            } else {
                mBinding.tvHexPrivateKey.text = "*".repeat(hexKey.length)
                mBinding.btnHexView.background = getDrawable(R.drawable.icon_hide)
                isHexView = false
            }
        }

        var isQRView = false
        mBinding.btnHexQrCode.setOnClickListener {
            if (!isQRView) {
                mBinding.btnHexQrCode.background = getDrawable(R.drawable.icon_qr_code_gray)
                mBinding.ivHexQrCode.visibility = View.VISIBLE
                isQRView = true
            } else {
                mBinding.btnHexQrCode.background = getDrawable(R.drawable.icon_qr_code)
                mBinding.ivHexQrCode.visibility = View.GONE
                isQRView = false
            }
        }
    }

    companion object {
        const val KEY_WALLET = "key_wallet"

        fun start(context: Context, wallet: Wallet? = null) {
            val i = Intent(context, ViewPrivateKeyActivity::class.java).apply {
                putExtra(KEY_WALLET, wallet)
            }
            context.startActivity(i)
        }
    }
}