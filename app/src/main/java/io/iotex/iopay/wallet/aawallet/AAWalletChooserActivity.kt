package io.iotex.iopay.wallet.aawallet

import androidx.lifecycle.lifecycleScope
import com.blankj.utilcode.util.Utils
import io.iotex.iopay.R
import io.iotex.iopay.base.BaseBindToolbarActivity
import io.iotex.iopay.data.db.AppDatabase
import io.iotex.iopay.databinding.ActivityAaWalletChooserBinding
import io.iotex.iopay.util.FireBaseEvent
import io.iotex.iopay.util.FireBaseUtil
import io.iotex.iopay.util.PageEventUtil
import io.iotex.iopay.util.extension.setVisible
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.jetbrains.anko.startActivity

class AAWalletChooserActivity :
    BaseBindToolbarActivity<Nothing, ActivityAaWalletChooserBinding>(R.layout.activity_aa_wallet_chooser) {

    override fun initView() {
        setToolbarTitle(getString(R.string.aa_wallet))

        mBinding.run {
            llCreateAAWallet.setOnClickListener {
                startActivity<SetupRecoverEmailActivityV2>()
                FireBaseUtil.logFireBase(FireBaseEvent.ACTION_IMPORT_WALLET_AA_CREATE)
                PageEventUtil.logEvent(PageEventUtil.CREATEAAWALLET)
            }
            llRecoverAAWallet.setOnClickListener {
                startActivity<RecoverAAWalletActivity>()
                FireBaseUtil.logFireBase(FireBaseEvent.ACTION_IMPORT_WALLET_AA_RECOVER)
                PageEventUtil.logEvent(PageEventUtil.RECOVERAAWALLET)
            }
        }
    }

    override fun initData() {
        lifecycleScope.launch {
            val list = withContext(Dispatchers.IO) {
                AppDatabase.getInstance(Utils.getApp()).walletDao()
                    .queryAAWallets()
            }
            if (list.isEmpty()) {
                mBinding.llCreateAAWallet.setVisible()
            }
        }
    }

}