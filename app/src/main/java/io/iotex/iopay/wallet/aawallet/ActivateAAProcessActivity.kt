package io.iotex.iopay.wallet.aawallet

import android.content.Intent
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.SPUtils
import com.drakeet.multitype.MultiTypeAdapter
import io.iotex.iopay.MainActivity
import io.iotex.iopay.R
import io.iotex.iopay.base.BaseBindToolbarActivity
import io.iotex.iopay.data.db.Wallet
import io.iotex.iopay.databinding.ActivityActivateAaProcessBinding
import io.iotex.iopay.network.FirstNetworkSwitchActivity
import io.iotex.iopay.support.eventbus.SwitchWalletEvent
import io.iotex.iopay.ui.binder.ActivateAAProcessBinder
import io.iotex.iopay.ui.binder.ActivateAAProcessWrapper
import io.iotex.iopay.ui.binder.ActivateAAStatus
import io.iotex.iopay.ui.binder.ActivateAAStatus_FAILED
import io.iotex.iopay.ui.binder.ActivateAAStatus_LOADING
import io.iotex.iopay.ui.binder.ActivateAAStatus_SUCCESS
import io.iotex.iopay.ui.binder.ActivateAAStep
import io.iotex.iopay.ui.binder.ActivateAAStep_APPLY_GAS
import io.iotex.iopay.ui.binder.ActivateAAStep_BIND_EMAIL
import io.iotex.iopay.ui.binder.ActivateAAStep_CREATE_ACCOUNT
import io.iotex.iopay.ui.binder.ActivateAAStep_VERIFY_EMAIL
import io.iotex.iopay.util.FireBaseEvent
import io.iotex.iopay.util.FireBaseUtil
import io.iotex.iopay.util.SPConstant
import io.iotex.iopay.util.extension.asBigDecimal
import io.iotex.iopay.util.extension.loadGif
import io.iotex.iopay.util.extension.setGone
import io.iotex.iopay.util.extension.setVisible
import io.iotex.iopay.wallet.aawallet.dialog.TryAgainDialog
import io.iotex.iopay.wallet.viewmodel.AAWalletViewModel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus
import java.math.BigDecimal

class ActivateAAProcessActivity :
    BaseBindToolbarActivity<AAWalletViewModel, ActivityActivateAaProcessBinding>(R.layout.activity_activate_aa_process) {

    private val wallet by lazy {
        intent.getParcelableExtra(KEY_WALLET) as? Wallet
    }

    private val adapter = MultiTypeAdapter()
    private val statusList = mutableListOf<ActivateAAProcessWrapper>()

    override fun initView() {
        setupBar()
        val binder = ActivateAAProcessBinder().apply {
            onTryAgainListener = {
                tryAgain()
            }
        }
        adapter.register(ActivateAAProcessWrapper::class, binder)
        mBinding.rvStatus.adapter = adapter
        adapter.items = statusList
        mBinding.rvStatus.layoutManager = LinearLayoutManager(this, LinearLayoutManager.VERTICAL, true)

        mBinding.llStatusNotAvailable.setOnClickListener {
            TryAgainDialog().apply {
                onTryAgainListener = {
                    mViewModel.checkService()
                    FireBaseUtil.logFireBase(FireBaseEvent.ACTION_CREATE_AA_WALLET_STATUS_BIND_EMAIL_FAIL_CLICK_TRY_AGAIN)
                }
            }.show(supportFragmentManager, TryAgainDialog::class.java.name)
        }
    }

    private fun setupBar() {
        hideToolbarBack()
        setToolbarTitle(getString(R.string.iopay_aa_wallet))
        mBinding.run {
            ivWalletIcon.loadGif(R.drawable.icon_aa_process_gif, R.drawable.icon_aa_process_gif)
        }
    }

    override fun initData() {
        verifyEmail()
        mViewModel.checkService()
    }

    private fun tryAgain() {
        val aaWallet = wallet ?: return
        updateProcess(ActivateAAStatus_LOADING)
        val statusWrapper = statusList.firstOrNull()
        when (statusWrapper?.step) {
            ActivateAAStep_VERIFY_EMAIL -> {
                mViewModel.queryRemainFeeGas(aaWallet.address)
            }
            ActivateAAStep_APPLY_GAS -> {
                mViewModel.applyGasFee(aaWallet.address)
            }
            ActivateAAStep_CREATE_ACCOUNT -> {
            }
            ActivateAAStep_BIND_EMAIL -> {
                TryAgainDialog().apply {
                    onTryAgainListener = {
                        finish()
                    }
                    icon = R.drawable.icon_aa_bind_failed
                    title = <EMAIL>(R.string.bind_email_failed)
                    description = <EMAIL>(R.string.bind_email_failed_description)
                }.show(supportFragmentManager, TryAgainDialog::class.java.name)
            }
        }
    }

    private fun tryBindEmail(wallet: Wallet) {
        val email = intent.getStringExtra(KEY_EMAIL)
        val signature = intent.getStringExtra(KEY_SIGNATURE)
        if (!email.isNullOrBlank() && !signature.isNullOrBlank()) {
            mViewModel.bindEmailWithoutAuth(wallet, email, signature)
        }
    }

    private fun verifyEmail() {
        val address = wallet?.address ?: return
        startProcess(getString(R.string.verify_recovery_email), ActivateAAStep_VERIFY_EMAIL)
        mViewModel.queryRemainFeeGas(address)
    }

    private fun finishVerifyEmail() {
        updateProcess(ActivateAAStatus_SUCCESS)
    }

    private fun applyGas(freeGas: String) {
        lifecycleScope.launch {
            val address = wallet?.address ?: return@launch
            startProcess(getString(R.string.apply_gas_automatically_1), ActivateAAStep_APPLY_GAS)
            val remainFreeGas = freeGas.asBigDecimal()
            if (remainFreeGas <= BigDecimal.ZERO) {
                mViewModel.applyGasFee(address)
            } else {
                delay(1000)
                finishApplyGas(true)
            }
        }
    }

    private fun finishApplyGas(success: Boolean) {
        val status = if (success) ActivateAAStatus_SUCCESS else ActivateAAStatus_FAILED
        updateProcess(status)
        if (success) {
            createAccount()
        }
    }

    private fun createAccount() {
        lifecycleScope.launch {
            startProcess(getString(R.string.create_your_aa_wallet), ActivateAAStep_CREATE_ACCOUNT)
            delay(1000)
            updateProcess(ActivateAAStatus_SUCCESS)
            bindEmail()
        }
    }

    private fun bindEmail() {
        val aaWallet = wallet ?: return
        val email = intent.getStringExtra(KEY_EMAIL)
        val signature = intent.getStringExtra(KEY_SIGNATURE)
        if (!email.isNullOrBlank() && !signature.isNullOrBlank()) {
            startProcess(getString(R.string.bind_email_to_aa_wallet), ActivateAAStep_BIND_EMAIL)
            mViewModel.bindEmailWithoutAuth(aaWallet, email, signature)
        }
    }

    private fun finishBindEmail(success: Boolean) {
        lifecycleScope.launch {
            val status = if (success) ActivateAAStatus_SUCCESS else ActivateAAStatus_FAILED
            updateProcess(status)
            if (success) {
                delay(500)
                ActivityUtils.getActivityList().forEach { activity -> activity.finish() }
                if (SPUtils.getInstance().getBoolean(SPConstant.SP_FIRST_SWITCH_NET,false)){
                    val newIntent = Intent(this@ActivateAAProcessActivity, FirstNetworkSwitchActivity::class.java)
                    newIntent.flags = Intent.FLAG_ACTIVITY_CLEAR_TASK or Intent.FLAG_ACTIVITY_NEW_TASK
                    startActivity(newIntent)
                } else {
                    MainActivity.startActivity(this@ActivateAAProcessActivity)
                    EventBus.getDefault().post(SwitchWalletEvent())
                }
                FireBaseUtil.logFireBase(FireBaseEvent.ACTION_IMPORT_WALLET_AA_CREATE_SUCCESS_ENTER)
            }
        }
    }

    private fun startProcess(title: String, @ActivateAAStep step: Int) {
        val status = ActivateAAProcessWrapper(title, step, ActivateAAStatus_LOADING)
        statusList.add(0, status)
        adapter.notifyItemInserted(0)
    }

    private fun updateProcess(@ActivateAAStatus status: Int) {
        statusList.first().status = status
        adapter.notifyItemChanged(0)
    }

    override fun initEvent() {
        mViewModel.freeGasLD.observe(this) {
            finishVerifyEmail()
            applyGas(it)
        }
        mViewModel.applyGasLD.observe(this) {
            finishApplyGas(it)
        }
        mViewModel.waitingEmailLD.observe(this) {
            finishBindEmail(it)
        }
        mViewModel.aaServiceLD.observe(this) {
            if (it) {
                mBinding.tvStatusAvailable.setVisible()
                mBinding.llStatusNotAvailable.setGone()
            } else {
                mBinding.tvStatusAvailable.setGone()
                mBinding.llStatusNotAvailable.setVisible()
            }
        }
    }

    companion object {
        const val KEY_EMAIL = "key_email"
        const val KEY_WALLET = "key_wallet"
        const val KEY_SIGNATURE = "key_signature"
    }
}