package io.iotex.iopay.wallet.aawallet

import android.annotation.SuppressLint
import android.view.Gravity
import android.view.inputmethod.EditorInfo
import androidx.lifecycle.lifecycleScope
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.KeyboardUtils
import com.blankj.utilcode.util.ScreenUtils
import com.blankj.utilcode.util.Utils
import com.drakeet.multitype.MultiTypeAdapter
import io.iotex.base.bindbase.BaseBindActivity
import io.iotex.iopay.MainActivity
import io.iotex.iopay.R
import io.iotex.iopay.api.EmailAccount
import io.iotex.iopay.base.BaseBindToolbarActivity
import io.iotex.iopay.data.db.AppDatabase
import io.iotex.iopay.databinding.ActivityRecoverAaWalletBinding
import io.iotex.iopay.ui.binder.AAWalletItemBinder
import io.iotex.iopay.util.FireBaseEvent
import io.iotex.iopay.util.FireBaseUtil
import io.iotex.iopay.util.RxUtil
import io.iotex.iopay.util.TokenUtil
import io.iotex.iopay.util.extension.dp2px
import io.iotex.iopay.util.extension.isSupportedEmail
import io.iotex.iopay.util.extension.setGone
import io.iotex.iopay.util.extension.setInvisible
import io.iotex.iopay.util.extension.setVisible
import io.iotex.iopay.util.extension.toEvmAddress
import io.iotex.iopay.util.extension.toast
import io.iotex.iopay.wallet.aawallet.dialog.EmailSupportedDialog
import io.iotex.iopay.wallet.viewmodel.AAWalletViewModel
import io.iotex.iopay.wallet.web3.Web3Delegate
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class RecoverAAWalletActivity :
    BaseBindToolbarActivity<AAWalletViewModel, ActivityRecoverAaWalletBinding>(R.layout.activity_recover_aa_wallet) {

    private val adapter = MultiTypeAdapter()

    private var selectAddress = ""
    private val emailSupportedDialog by lazy {
        EmailSupportedDialog(this, ScreenUtils.getScreenWidth() - 30.dp2px())
    }

    @SuppressLint("CheckResult")
    override fun initView() {
        setToolbarTitle(getString(R.string.recover_aa_wallet))

        val binder = AAWalletItemBinder().apply {
            setItemClickCallback {
                checkWalletExist(it.account)
            }
        }
        adapter.register(EmailAccount::class.java, binder)
        mBinding.rvWallets.adapter = adapter

        mBinding.ivClear.setOnClickListener {
            mBinding.etEmail.setText("")
        }

        mBinding.etEmail.imeOptions = EditorInfo.IME_ACTION_DONE
        mBinding.etEmail.postDelayed({
            KeyboardUtils.showSoftInput(mBinding.etEmail)
        }, 500)

        emailSupportedDialog.onItemClick = {
            mBinding.etEmail.setText(it)
            mBinding.etEmail.setSelection(it.length)
        }

        RxUtil.textChange(mBinding.etEmail)
            .compose(RxUtil.applySchedulers())
            .subscribe {
                lifecycleScope.launch {
                    popSupportedEmail(it)
                    verifyEmail(it)
                }
            }

        mBinding.btnTryAgain.setOnClickListener {
            lifecycleScope.launch {
                val email = mBinding.etEmail.text.toString().trim()
                verifyEmail(email)
            }
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_IMPORT_WALLET_AA_RECOVER_EMAIL_QUERY_DATA_TRY_AGAIN)
        }
    }

    private fun popSupportedEmail(email: String) {
        val inputs = email.split("@")
        val input = if (inputs.isNotEmpty()) inputs[0] else ""
        emailSupportedDialog.updateEmail(input)
        if (!emailSupportedDialog.isShowing) {
            emailSupportedDialog
                .showAsDropDown(mBinding.llEmailContainer, 0, 10, Gravity.BOTTOM)
        }
    }

    private suspend fun verifyEmail(email: String) {
        val isEmail = email.isSupportedEmail()
        if (!isEmail) {
            mBinding.tvEmailError.setVisible()
            mBinding.tvEmailError.text = getString(R.string.invalid_email)
            return
        }
        FireBaseUtil.logFireBase(FireBaseEvent.ACTION_IMPORT_WALLET_AA_RECOVER_EMAIL_INPUT)
        emailSupportedDialog.dismiss()
        delay(500)
        mBinding.tvEmailError.setInvisible()
        mViewModel.queryBoundAddresses(email.trim())
    }

    private fun checkWalletExist(address: String) {
        lifecycleScope.launch(Dispatchers.IO) {
            val wallet = AppDatabase.getInstance(Utils.getApp()).walletDao()
                .queryWalletByAddress(address)
            if (wallet == null) {
                selectAddress = address
                mViewModel.isEffectiveAddress(address)
                FireBaseUtil.logFireBase(FireBaseEvent.ACTION_RECOVERY_AA_WALLET)
            } else {
                getString(R.string.wallet_exists).toast()
            }
        }
    }

    override fun initEvent() {
        mViewModel.emailAccountsLD.observe(this) {
            if (it.isEmpty()) {
                mBinding.llEmpty.setVisible()
                mBinding.llContent.setGone()
                mBinding.tvEmailError.setVisible()
                mBinding.tvEmailError.text = getString(R.string.has_not_bound_wallet)
            } else {
                mBinding.tvEmailError.setInvisible()
                mBinding.llEmpty.setGone()
                mBinding.llContent.setVisible()
                queryBalance(it)
            }
        }

        mViewModel.effectiveAddressLD.observe(this) { effective ->
            val email = mBinding.etEmail.text.toString()
            if (effective) {
                mViewModel.saveAAWallet(selectAddress, email, true)
                ActivityUtils.getActivityList().forEach { activity -> activity.finish() }
                MainActivity.startActivity(this)
            } else {
                SendRecoverEmailActivity.start(this@RecoverAAWalletActivity, selectAddress, email)
            }
        }
    }

    private fun queryBalance(accounts: List<EmailAccount>) {
        lifecycleScope.launch {
            runCatching {
                val addressList = accounts.map {
                    it.account
                }
                val balanceList = withContext(Dispatchers.IO) {
                    Web3Delegate.getCurrencyBalance(*addressList.toTypedArray())
                }
                val exists = withContext(Dispatchers.IO) {
                    accounts.map {
                        val wallet =
                            AppDatabase.getInstance(this@RecoverAAWalletActivity).walletDao()
                                .queryWalletByAddress(it.account.toEvmAddress())
                        wallet != null
                    }
                }
                if (balanceList.isNotEmpty()) {
                    accounts.forEachIndexed { index, emailAccount ->
                        emailAccount.balance = TokenUtil.weiToTokenBN(balanceList[index].toString())
                        emailAccount.exists = exists[index]
                    }
                }

            }
            adapter.items = accounts
            adapter.notifyDataSetChanged()
        }
    }

    override fun onStop() {
        super.onStop()
        KeyboardUtils.hideSoftInput(this)
    }
}