package io.iotex.iopay.wallet.aawallet

import android.app.Activity
import android.content.Intent
import android.net.Uri
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.ClipboardUtils
import com.machinefi.lockscreen.PFSecurityUtils
import io.iotex.base.bindbase.BaseBindActivity
import io.iotex.iopay.MainActivity
import io.iotex.iopay.R
import io.iotex.iopay.base.BaseBindToolbarActivity
import io.iotex.iopay.databinding.ActivitySendRecoverEmailBinding
import io.iotex.iopay.util.Config.AA_WALLET_RECOVER_EMAIL
import io.iotex.iopay.util.FireBaseEvent
import io.iotex.iopay.util.FireBaseUtil
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.util.extension.cleanHexPrefix
import io.iotex.iopay.util.extension.toast
import io.iotex.iopay.wallet.viewmodel.AAWalletViewModel
import io.iotex.iopay.wallet.web3.aawallet.P256KeyManager
import org.jetbrains.anko.startActivity

class SendRecoverEmailActivity :
    BaseBindToolbarActivity<AAWalletViewModel, ActivitySendRecoverEmailBinding>(R.layout.activity_send_recover_email) {

    private val address by lazy {
        intent.getStringExtra(KEY_WALLET_ADDRESS) ?: ""
    }

    private val email by lazy {
        intent.getStringExtra(KEY_WALLET_EMAIL) ?: ""
    }

    override fun initView() {
        setToolbarTitle(getString(R.string.recover_aa_wallet))

        mBinding.tvReceiptEmail.text = AA_WALLET_RECOVER_EMAIL
        mBinding.btnSend.setOnClickListener {
            sendRecoverEmail()
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_WALLET_RECOVER_AA_SEND)
        }
        mBinding.ivCopyEmail.setOnClickListener {
            ClipboardUtils.copyText(AA_WALLET_RECOVER_EMAIL)
            getString(R.string.success).toast()
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_WALLET_RECOVER_AA_TO_EMAIL_COPY)
        }
        mBinding.ivCopySubject.setOnClickListener {
            ClipboardUtils.copyText(mBinding.tvEmailSubject.text)
            getString(R.string.success).toast()
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_WALLET_RECOVER_AA_SUBJECT_COPY)
        }

        mBinding.btnHasSent.setOnClickListener {
            mViewModel.saveAAWallet(address, email, false)
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_WALLET_RECOVER_AA_HAVE_SENT_EMAIL)
        }

        prepareRecoverEmail()
    }

    private fun prepareRecoverEmail() {
        val pubKey = P256KeyManager.getPubKey()
//        val message = "Send an email with below text as <NAME_EMAIL>\n014690${address.lowercase()}${pubKey?.cleanHexPrefix()}"
        val chainId = WalletHelper.getCurChainId()
        val message = "01${chainId}${address.lowercase()}${pubKey?.cleanHexPrefix()}"
        mBinding.tvEmailSubject.text = message
    }

    private fun sendRecoverEmail() {
        val uri: Uri = Uri.parse("mailto:<EMAIL>")
        val intent = Intent(Intent.ACTION_SENDTO, uri)
        val content = mBinding.tvEmailSubject.text
        intent.putExtra(Intent.EXTRA_SUBJECT, content)
        startActivity(Intent.createChooser(intent, getString(R.string.recover_aa_wallet)))
    }

    override fun initEvent() {
        mViewModel.saveWalletLD.observe(this) {
            ActivityUtils.getActivityList().forEach { it?.finish() }
            MainActivity.startActivity(this)
        }
    }

    companion object {
        const val KEY_WALLET_ADDRESS = "key_wallet_address"
        const val KEY_WALLET_EMAIL = "key_wallet_email"
        fun start(context: Activity, address: String, email: String) {
            context.startActivity<SendRecoverEmailActivity>(
                KEY_WALLET_ADDRESS to address,
                KEY_WALLET_EMAIL to email
            )
        }
    }
}