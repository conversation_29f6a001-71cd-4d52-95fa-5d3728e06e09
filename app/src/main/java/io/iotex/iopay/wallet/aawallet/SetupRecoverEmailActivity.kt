package io.iotex.iopay.wallet.aawallet

import android.annotation.SuppressLint
import android.content.Intent
import android.view.Gravity
import android.view.inputmethod.EditorInfo
import com.blankj.utilcode.util.KeyboardUtils
import com.blankj.utilcode.util.RegexUtils
import com.blankj.utilcode.util.ScreenUtils
import com.blankj.utilcode.util.Utils
import io.iotex.iopay.R
import io.iotex.iopay.base.BaseBindToolbarActivity
import io.iotex.iopay.databinding.ActivitySetupRecoverEmailBinding
import io.iotex.iopay.transaction.TransactionDialog
import io.iotex.iopay.transaction.bean.OptionEntry
import io.iotex.iopay.util.Config
import io.iotex.iopay.util.Constant
import io.iotex.iopay.util.FireBaseEvent
import io.iotex.iopay.util.FireBaseUtil
import io.iotex.iopay.util.IoPayConstant
import io.iotex.iopay.util.RxUtil
import io.iotex.iopay.util.TokenUtil
import io.iotex.iopay.util.extension.dp2px
import io.iotex.iopay.util.extension.isSupportedEmail
import io.iotex.iopay.util.extension.setGone
import io.iotex.iopay.util.extension.setInvisible
import io.iotex.iopay.util.extension.setVisible
import io.iotex.iopay.xapp.XAppsActivity
import io.iotex.iopay.wallet.aawallet.dialog.EmailSupportedDialog
import io.iotex.iopay.wallet.viewmodel.AAWalletViewModel
import io.iotex.iopay.wallet.web3.FunctionEncodeUtil
import io.reactivex.Observable
import io.reactivex.disposables.Disposable
import java.math.BigInteger
import java.util.concurrent.TimeUnit

class SetupRecoverEmailActivity :
    BaseBindToolbarActivity<AAWalletViewModel, ActivitySetupRecoverEmailBinding>(R.layout.activity_setup_recover_email) {

    private val count = 60L
    private var disposable: Disposable? = null
    private val emailSupportedDialog by lazy {
        EmailSupportedDialog(this, ScreenUtils.getScreenWidth() - 40.dp2px())
    }
    private var transactionDialog: TransactionDialog? = null

    @SuppressLint("CheckResult")
    override fun initView() {
        setToolbarTitle(getString(R.string.setup_email))
        setToolbarSubmitText(getString(R.string.next_pf))

        mBinding.etCode.imeOptions = EditorInfo.IME_ACTION_DONE
        mBinding.etEmail.imeOptions = EditorInfo.IME_ACTION_DONE
        mBinding.etEmail.postDelayed({
            KeyboardUtils.showSoftInput(mBinding.etEmail)
        }, 600)

        RxUtil.textChange(mBinding.etEmail)
            .compose(RxUtil.applySchedulers())
            .subscribe {
                checkEmail(it.trim())
            }

        emailSupportedDialog.onItemClick = {
            mBinding.etEmail.setText(it)
            mBinding.etEmail.setSelection(it.length)
            KeyboardUtils.hideSoftInput(this)
        }

        mBinding.tvSendCode.setOnClickListener {
            mBinding.tvSendCode.isEnabled = false
            KeyboardUtils.hideSoftInput(this)
            val email = mBinding.etEmail.text.toString().trim()
            if (email.isNotBlank()) {
                mViewModel.sendCode(email)
            }
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_RECOVER_WALLET_UNSUCCESS_SETUP_EMAIL_SEND_CODE)
        }

        setToolbarSubmitClick {
            KeyboardUtils.hideSoftInput(this)
            val email = mBinding.etEmail.text.toString().trim()
            val code = mBinding.etCode.text.toString().trim()
            if (email.isNotBlank() && code.isNotBlank()) {
                mViewModel.verifyCode(email, code)
                mViewModel.verifyCodeLD.observe(this){
                    val address = Constant.currentWallet?.address ?: ""
                    val data = FunctionEncodeUtil.addEmailGuardianData(email, it)
                    transactionDialog = TransactionDialog(address, BigInteger.ZERO,data,arrayListOf(
                        OptionEntry(
                        Utils.getApp().getString(R.string.method),
                        FunctionEncodeUtil.FUN_ADD_EMAIL_GUARDIAN
                    )
                    )).apply {
                        onTransactionConfirm = {_: Long, _: String, _, _ ->
                            mViewModel.bindEmail(email,it)
                        }
                    }
                    transactionDialog?.show(supportFragmentManager,System.currentTimeMillis().toString())
                }
            }
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_RECOVER_WALLET_UNSUCCESS_SETUP_EMAIL_CONFIRM)
        }

        mBinding.llApplyGasFee.setOnClickListener {
            KeyboardUtils.hideSoftInput(this)
            val address = Constant.currentWallet?.address ?: return@setOnClickListener
            val intent = Intent(this, XAppsActivity::class.java)
            intent.putExtra(IoPayConstant.BROWSER_URL, Config.applyGasLink(address))
            startActivity(intent)
        }
    }

    private fun checkEmail(email: String) {
        val inputs = email.split("@")
        val input = if (inputs.isNotEmpty()) inputs[0] else ""
        emailSupportedDialog.updateEmail(input)
        if (!emailSupportedDialog.isShowing) {
            emailSupportedDialog
                .showAsDropDown(mBinding.llEmailContainer, 0, 10, Gravity.BOTTOM)
        }
        val isEmail = email.isSupportedEmail()
        mBinding.tvSendCode.isEnabled = isEmail
        if (!isEmail) {
            mBinding.tvEmailError.setVisible()
            mBinding.tvEmailTips.setGone()
            return
        }
        emailSupportedDialog.dismiss()
        mBinding.tvEmailError.setGone()
        mBinding.tvEmailTips.setVisible()
    }

    override fun initData() {
        verifyForm()
    }

    override fun onResume() {
        super.onResume()
        mViewModel.getRemainFeeGas()
    }

    @SuppressLint("CheckResult")
    private fun verifyForm() {
        val emailOb = RxUtil.textChange(mBinding.etEmail)
            .debounce(600, TimeUnit.MILLISECONDS)
            .compose(RxUtil.applySchedulers())
        val codeOb = RxUtil.textChange(mBinding.etCode)
            .debounce(600, TimeUnit.MILLISECONDS)
            .compose(RxUtil.applySchedulers())
        Observable.combineLatest(emailOb, codeOb) { email, code ->
            val validEmail = RegexUtils.isEmail(email.trim())
            val validCode = code.trim().length >= 6

            if (!validCode) {
                mBinding.tvCodeError.setVisible()
            } else {
                mBinding.tvCodeError.setInvisible()
            }

            return@combineLatest validEmail && validCode
        }.compose(RxUtil.applySchedulers())
            .subscribe {
                setToolbarSubmit(it)
            }
    }

    override fun initEvent() {
        mViewModel.sendCodeLD.observe(this) {
            mBinding.tvSendCode.isEnabled = true
            if (it) {
                mBinding.tvSendCode.setGone()
                mBinding.tvCountDown.setVisible()
                countDown()
            }
        }
        mViewModel.confirmLD.observe(this) {
            transactionDialog?.dismiss()
            finish()
        }
        mViewModel.freeGasLD.observe(this) {
            val symbol = TokenUtil.getNativeCurrencySymbol()
            mBinding.tvRemainFreeGasFee.text = "${getString(R.string.remain_free_gas_fee)} $it $symbol"
        }
    }

    private fun countDown() {
        disposable = Observable.interval(0,1, TimeUnit.SECONDS)
            .take(count + 1)
            .map {
                count - it
            }
            .compose(RxUtil.applySchedulers())
            .doOnComplete {
                mBinding.tvCountDown.setGone()
                mBinding.tvSendCode.setVisible()
            }
            .subscribe {
                mBinding.tvCountDown.text = "${it}s"
            }
    }

    override fun onStop() {
        super.onStop()
        KeyboardUtils.hideSoftInput(this)
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        disposable?.dispose()
    }

}