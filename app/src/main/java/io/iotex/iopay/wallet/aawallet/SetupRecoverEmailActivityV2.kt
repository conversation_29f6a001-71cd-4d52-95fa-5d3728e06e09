package io.iotex.iopay.wallet.aawallet

import android.annotation.SuppressLint
import android.view.Gravity
import android.view.inputmethod.EditorInfo
import com.blankj.utilcode.util.KeyboardUtils
import com.blankj.utilcode.util.RegexUtils
import com.blankj.utilcode.util.ScreenUtils
import com.blankj.utilcode.util.Utils
import io.iotex.iopay.R
import io.iotex.iopay.base.BaseBindToolbarActivity
import io.iotex.iopay.data.db.AppDatabase
import io.iotex.iopay.databinding.ActivitySetupRecoverEmailV2Binding
import io.iotex.iopay.network.dialog.NetworkSwitchDialog
import io.iotex.iopay.util.FireBaseEvent
import io.iotex.iopay.util.FireBaseUtil
import io.iotex.iopay.util.RxUtil
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.util.extension.dp2px
import io.iotex.iopay.util.extension.isSupportedEmail
import io.iotex.iopay.util.extension.loadSvgOrImage
import io.iotex.iopay.util.extension.setGone
import io.iotex.iopay.util.extension.setInvisible
import io.iotex.iopay.util.extension.setVisible
import io.iotex.iopay.wallet.aawallet.dialog.EmailSupportedDialog
import io.iotex.iopay.wallet.viewmodel.AAWalletViewModel
import io.reactivex.Observable
import io.reactivex.disposables.Disposable
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import org.jetbrains.anko.startActivity
import java.util.concurrent.TimeUnit

class SetupRecoverEmailActivityV2 :
    BaseBindToolbarActivity<AAWalletViewModel, ActivitySetupRecoverEmailV2Binding>(R.layout.activity_setup_recover_email_v2) {

    private val count = 60L
    private var disposable: Disposable? = null
    private val emailSupportedDialog by lazy {
        EmailSupportedDialog(this, ScreenUtils.getScreenWidth() - 40.dp2px())
    }

    override fun initView() {
        setToolbarTitle(getString(R.string.create_new_aa_wallet))

        mBinding.etCode.imeOptions = EditorInfo.IME_ACTION_DONE
        mBinding.etEmail.imeOptions = EditorInfo.IME_ACTION_DONE
        setupNetwork()

        mBinding.etEmail.postDelayed({
            KeyboardUtils.showSoftInput(mBinding.etEmail)
        }, 500)
        mBinding.etEmail.isFocusable = true
        val sub = RxUtil.textChange(mBinding.etEmail)
            .compose(RxUtil.applySchedulers())
            .subscribe {
                checkEmail(it.trim())
            }

        emailSupportedDialog.onItemClick = {
            mBinding.etEmail.setText(it)
            mBinding.etEmail.setSelection(it.length)
            KeyboardUtils.hideSoftInput(this)
        }

        mBinding.tvNext.setOnClickListener {
            KeyboardUtils.hideSoftInput(this)
            val email = mBinding.etEmail.text.toString().trim()
            val code = mBinding.etCode.text.toString().trim()
            val address = mViewModel.walletLD.value?.address ?: return@setOnClickListener
            mViewModel.verifyCode(address, email, code)
        }

        mBinding.tvSendCode.setOnClickListener {
            mBinding.tvSendCode.isEnabled = false
            KeyboardUtils.hideSoftInput(this)
            val email = mBinding.etEmail.text.toString().trim()
            if (email.isNotBlank()) {
                mViewModel.sendCode(email)
            }
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_IMPORT_WALLET_AA_CREATE_SEND_CODE)
        }
    }

    private fun checkEmail(email: String) {
        val inputs = email.split("@")
        val input = if (inputs.isNotEmpty()) inputs[0] else ""
        emailSupportedDialog.updateEmail(input)
        if (!emailSupportedDialog.isShowing) {
            emailSupportedDialog
                .showAsDropDown(mBinding.llEmailContainer, 0, 10, Gravity.BOTTOM)
        }
        val isEmail = email.isSupportedEmail()
        mBinding.tvSendCode.isEnabled = isEmail
        if (!isEmail) {
            mBinding.tvEmailError.setVisible()
            mBinding.tvEmailTips.setGone()
            return
        }
        emailSupportedDialog.dismiss()
        mBinding.tvEmailError.setGone()
        mBinding.tvEmailTips.setVisible()
    }

    private fun setupNetwork() {
        CoroutineScope(Dispatchers.IO).launch {
            val network = WalletHelper.getCurNetwork()
            val allNetwork = AppDatabase.getInstance(Utils.getApp()).rpcNetworkDao().queryAllRPCNetwork()
            val aaNetworkList = AppDatabase.getInstance(Utils.getApp()).networkAAConfigDao().queryAll()
            val aaNetwork = allNetwork.filter {
                aaNetworkList.firstOrNull { config ->
                    config.chainId == it.chainId
                } != null
            }
            MainScope().launch {
                mBinding.ivNetwork.loadSvgOrImage(network?.logo, R.drawable.ic_network_default)
                mBinding.tvNetworkName.text = network?.name
                mBinding.llChooseNetwork.setOnClickListener {
                    KeyboardUtils.hideSoftInput(this@SetupRecoverEmailActivityV2)
                    NetworkSwitchDialog(items = aaNetwork)
                        .apply {
                            onItemClick = {
                                mBinding.ivNetwork.loadSvgOrImage(it.logo, R.drawable.ic_network_default)
                                mBinding.tvNetworkName.text = it.name
                            }
                        }
                        .show(supportFragmentManager, System.currentTimeMillis().toString())
                }
            }
        }
    }

    override fun initData() {
        verifyForm()
        mViewModel.createWallet()
    }

    @SuppressLint("CheckResult")
    private fun verifyForm() {
        val emailOb = RxUtil.textChange(mBinding.etEmail)
            .debounce(600, TimeUnit.MILLISECONDS)
            .compose(RxUtil.applySchedulers())
        val codeOb = RxUtil.textChange(mBinding.etCode)
            .debounce(1000, TimeUnit.MILLISECONDS)
            .compose(RxUtil.applySchedulers())
        Observable.combineLatest(emailOb, codeOb) { email, code ->
            val validEmail = RegexUtils.isEmail(email.trim())
            val validCode = code.trim().length >= 6

            if (!validCode) {
                mBinding.tvCodeError.setVisible()
            } else {
                mBinding.tvCodeError.setInvisible()
            }

            return@combineLatest validEmail && validCode
        }.compose(RxUtil.applySchedulers())
            .subscribe {
                mBinding.tvNext.isEnabled = it
            }
    }

    override fun initEvent() {
        mViewModel.walletLD.observe(this) { wallet ->
            if (wallet != null) {
                mViewModel.getRemainFeeGas(wallet.address)
            }
        }
        mViewModel.sendCodeLD.observe(this) {
            mBinding.tvSendCode.isEnabled = true
            if (it) {
                mBinding.tvSendCode.setGone()
                mBinding.tvCountDown.setVisible()
                countDown()
            }
        }
        mViewModel.signatureLD.observe(this) {
            if (it.isNotBlank()) {
                val email = mBinding.etEmail.text.toString().trim()
                val wallet = mViewModel.walletLD.value ?: return@observe
                startActivity<ActivateAAProcessActivity>(
                    ActivateAAProcessActivity.KEY_EMAIL to email,
                    ActivateAAProcessActivity.KEY_WALLET to wallet,
                    ActivateAAProcessActivity.KEY_SIGNATURE to it,
                )
            }
        }
    }

    private fun countDown() {
        disposable = Observable.interval(0,1, TimeUnit.SECONDS)
            .take(count + 1)
            .map {
                count - it
            }
            .compose(RxUtil.applySchedulers())
            .doOnComplete {
                mBinding.tvCountDown.setGone()
                mBinding.tvSendCode.setVisible()
            }
            .subscribe {
                mBinding.tvCountDown.text = "${it}s"
            }
    }

    override fun onStop() {
        super.onStop()
        KeyboardUtils.hideSoftInput(this)
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        disposable?.dispose()
    }

}