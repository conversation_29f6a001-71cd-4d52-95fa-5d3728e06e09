package io.iotex.iopay.wallet.aawallet.dialog

import androidx.appcompat.app.AppCompatActivity
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.ToastUtils
import io.iotex.iopay.R
import io.iotex.iopay.wallet.dialog.NoteDialog

object AAReceiveDialog {
    fun showAAReceiveCopy(){
        val activity = ActivityUtils.getTopActivity() as? AppCompatActivity
        if(activity!=null&& ActivityUtils.isActivityAlive(activity)){
            NoteDialog().apply {
                icon = R.drawable.icon_aa_copy_warning
                title = activity.getString(R.string.the_address_is_copied_to_the_clipboard)
                message = activity.getString(R.string.aa_wallet_operates_on_smart_contracts_only)
                onConfirm = {
                    ToastUtils.showShort(R.string.copy_success)
                }
            }.show(activity.supportFragmentManager,System.currentTimeMillis().toString())
        }

    }
}