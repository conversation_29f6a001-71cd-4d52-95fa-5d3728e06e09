package io.iotex.iopay.wallet.aawallet.dialog

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.drawable.ColorDrawable
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.WindowManager
import android.widget.LinearLayout
import android.widget.PopupWindow
import androidx.databinding.DataBindingUtil
import com.blankj.utilcode.util.ScreenUtils
import io.iotex.iopay.R
import io.iotex.iopay.databinding.PopupEmailSupportedBinding
import io.iotex.iopay.util.extension.dp2px

class EmailSupportedDialog(context: Context, val contentWidth: Int) : PopupWindow(context) {

    var onItemClick: ((String) -> Unit)? = null

    private val mBinding = DataBindingUtil.inflate<PopupEmailSupportedBinding>(
        LayoutInflater.from(context),
        R.layout.popup_email_supported,
        null,
        false
    )

    init {
        setupPopupWindow()

        mBinding.llGmailContainer.setOnClickListener {
            onItemClick?.invoke(mBinding.tvGmail.text.toString() + mBinding.tvGmailSuffix.text.toString())
        }
        mBinding.llQQContainer.setOnClickListener {
            onItemClick?.invoke(mBinding.tvQQ.text.toString() + mBinding.tvQQSuffix.text.toString())
        }
    }

    fun updateEmail(email: String) = apply {
        mBinding.tvGmail.text = email
        mBinding.tvQQ.text = email
    }

    @SuppressLint("ClickableViewAccessibility")
    private fun setupPopupWindow() {
        this.contentView = mBinding.root
        this.width = contentWidth
        this.height = LinearLayout.LayoutParams.WRAP_CONTENT
        this.setBackgroundDrawable(ColorDrawable(0x00000000))
    }

}