package io.iotex.iopay.wallet.aawallet.dialog

import android.view.Gravity
import com.blankj.utilcode.util.ScreenUtils
import io.iotex.base.bindbase.BaseBindDialog
import io.iotex.iopay.R
import io.iotex.iopay.databinding.DialogNotSupportAaWalletBinding
import io.iotex.iopay.databinding.DialogWalletExpiredBinding
import io.iotex.iopay.util.FireBaseEvent
import io.iotex.iopay.util.FireBaseUtil
import io.iotex.iopay.wallet.list.SwitchWalletActivity
import io.iotex.iopay.wallet.list.SwitchWalletDialog
import io.iotex.iopay.wallet.viewmodel.AAWalletViewModel

class NotSupportAAWalletDialog:
    BaseBindDialog<AAWalletViewModel, DialogNotSupportAaWalletBinding>(R.layout.dialog_not_support_aa_wallet) {

    override fun initView() {
        mBinding.run {
            btnSwitchWallet.setOnClickListener {
                dismiss()
                SwitchWalletDialog(false).show(requireActivity().supportFragmentManager,System.currentTimeMillis().toString())
                FireBaseUtil.logFireBase(FireBaseEvent.ACTION_AA_WALLET_PERSONAL_SIGN_NOTE_SWITCH_WALLET)
            }
        }
    }

    override fun initData() {
    }

    override fun onStart() {
        super.onStart()
        val attributes = dialog?.window?.attributes
        attributes?.width = ScreenUtils.getScreenWidth()
        attributes?.gravity = Gravity.BOTTOM
        dialog?.window?.attributes = attributes
    }
}