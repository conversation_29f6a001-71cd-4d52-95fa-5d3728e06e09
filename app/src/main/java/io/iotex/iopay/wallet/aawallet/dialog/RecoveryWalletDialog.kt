package io.iotex.iopay.wallet.aawallet.dialog

import android.view.Gravity
import androidx.lifecycle.lifecycleScope
import com.blankj.utilcode.util.ScreenUtils
import com.blankj.utilcode.util.TimeUtils
import io.iotex.base.bindbase.BaseBindDialog
import io.iotex.iopay.R
import io.iotex.iopay.databinding.DialogRecoveryWalletBinding
import io.iotex.iopay.util.FireBaseEvent
import io.iotex.iopay.util.FireBaseUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onCompletion
import kotlinx.coroutines.flow.onEach
import java.util.concurrent.TimeUnit

class RecoveryWalletDialog(val timestamp: Long) :
    BaseBindDialog<Nothing, DialogRecoveryWalletBinding>(R.layout.dialog_recovery_wallet) {

    private val interval = 24 * 60 * 60 * 1000L
    private var mCountdownJob: Job? = null

    override fun initView() {
        mBinding.btnConfirm.setOnClickListener {
            dismiss()
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_AA_WALLET_RECOVERING_COUNTDOWN_CONFIRM)
        }
    }

    override fun initData() {
        countdown()
    }

    private fun countdown() {
        mCountdownJob = flow {
            val curTimestamp = TimeUtils.getNowMills()
            val gap = timestamp * 1000 + interval - curTimestamp
            for (timestamp in gap downTo 0 step 1000) {
                emit(timestamp)
                delay(1000)
            }
        }.flowOn(Dispatchers.Main)
            .onCompletion {  }
            .onEach {
                val hours = TimeUnit.MILLISECONDS.toHours(it).toString()
                val minutes = (TimeUnit.MILLISECONDS.toMinutes(it) % 60).toString()
                val seconds = (TimeUnit.MILLISECONDS.toSeconds(it) % 60).toString()
                mBinding.run {
                    tvHours.text = if (hours.length < 2) "0$hours" else hours
                    tvMins.text = if (minutes.length < 2) "0$minutes" else minutes
                    tvSecs.text = if (seconds.length < 2) "0$seconds" else seconds
                }
            }
            .launchIn(lifecycleScope)
    }

    override fun onStart() {
        super.onStart()
        val attributes = dialog?.window?.attributes
        attributes?.width = ScreenUtils.getScreenWidth()
        attributes?.gravity = Gravity.BOTTOM
        dialog?.window?.attributes = attributes
    }

    override fun onDestroyView() {
        mCountdownJob?.cancel()
        super.onDestroyView()
    }

}