package io.iotex.iopay.wallet.aawallet.dialog

import io.iotex.base.bindbase.BaseBindDialog
import io.iotex.iopay.R
import io.iotex.iopay.databinding.DialogSetupEmailBinding
import io.iotex.iopay.util.FireBaseEvent
import io.iotex.iopay.util.FireBaseUtil
import io.iotex.iopay.wallet.aawallet.SetupRecoverEmailActivity
import org.jetbrains.anko.startActivity

class SetupEmailDialog:
    BaseBindDialog<Nothing, DialogSetupEmailBinding>(R.layout.dialog_setup_email) {

    override fun initView() {
        mBinding.run {
            ivClose.setOnClickListener { dismissAllowingStateLoss() }
            btnConfirm.setOnClickListener {
                requireActivity().startActivity<SetupRecoverEmailActivity>()
                dismissAllowingStateLoss()
                FireBaseUtil.logFireBase(FireBaseEvent.ACTION_HOME_AA_WALLET_SETUP_EMAIL_REMINDER_CONFIRM)
            }
        }
    }
}