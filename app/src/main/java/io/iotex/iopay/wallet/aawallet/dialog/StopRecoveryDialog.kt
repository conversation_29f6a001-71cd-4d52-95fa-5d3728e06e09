package io.iotex.iopay.wallet.aawallet.dialog

import android.view.Gravity
import com.blankj.utilcode.util.ScreenUtils
import com.blankj.utilcode.util.Utils
import io.iotex.base.bindbase.BaseBindDialog
import io.iotex.iopay.R
import io.iotex.iopay.databinding.DialogStopRecoveryBinding
import io.iotex.iopay.transaction.TransactionDialog
import io.iotex.iopay.transaction.bean.OptionEntry
import io.iotex.iopay.util.Constant
import io.iotex.iopay.util.FireBaseEvent
import io.iotex.iopay.util.FireBaseUtil
import io.iotex.iopay.wallet.viewmodel.AAWalletViewModel
import io.iotex.iopay.wallet.web3.FunctionEncodeUtil
import java.math.BigInteger

class StopRecoveryDialog:
    BaseBindDialog<AAWalletViewModel, DialogStopRecoveryBinding>(R.layout.dialog_stop_recovery) {

    private var transactionDialog: TransactionDialog? = null
    override fun initView() {
        mBinding.run {
            tvContinue.setOnClickListener {
                dismissAllowingStateLoss()
                FireBaseUtil.logFireBase(FireBaseEvent.ACTION_AA_WALLET_RECOVERING_ON_NEW_DEVICE_CONTINUE)
            }
            btnStop.setOnClickListener {
                val account = Constant.currentWallet?.address ?: ""
                val data = FunctionEncodeUtil.stopRecoveryData()
                transactionDialog = TransactionDialog(account, BigInteger.ZERO,data,arrayListOf(
                    OptionEntry(
                        Utils.getApp().getString(R.string.method),
                        FunctionEncodeUtil.FUN_STOP_RECOVERY
                    )
                )).apply {
                    onTransactionConfirm = { _: Long, _: String, _, _ ->
                        transactionDialog?.dismiss()
                        transactionDialog = null
                        mViewModel.stopRecovery(account)
                    }
                    onCancel = {
                        transactionDialog = null
                    }
                }
                transactionDialog?.show(childFragmentManager,System.currentTimeMillis().toString())
                FireBaseUtil.logFireBase(FireBaseEvent.ACTION_AA_WALLET_RECOVERING_ON_NEW_DEVICE_STOP)
            }
        }
    }

    override fun initData() {
        mViewModel.stopRecoveryLD.observe(this) {
            dismissAllowingStateLoss()
        }
    }

    override fun onStart() {
        super.onStart()
        val attributes = dialog?.window?.attributes
        attributes?.width = ScreenUtils.getScreenWidth()
        attributes?.gravity = Gravity.BOTTOM
        dialog?.window?.attributes = attributes
    }
}