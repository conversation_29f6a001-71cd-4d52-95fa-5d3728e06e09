package io.iotex.iopay.wallet.aawallet.dialog

import io.iotex.base.bindbase.BaseBindDialog
import io.iotex.iopay.R
import io.iotex.iopay.databinding.DialogNetworkErrorBinding
import io.iotex.iopay.util.extension.loadImage

class TryAgainDialog:
    BaseBindDialog<Nothing, DialogNetworkErrorBinding>(R.layout.dialog_network_error) {

    var onTryAgainListener: (() -> Unit)? = null
    var icon: Int? = null
    var title: String? = null
    var description: String? = null

    override fun initView() {
        mBinding.run {
            ivClose.setOnClickListener { dismissAllowingStateLoss() }
            btnTryAgain.setOnClickListener {
                dismissAllowingStateLoss()
                onTryAgainListener?.invoke()
            }
            ivIcon.loadImage(icon ?: R.drawable.ic_network_error , R.drawable.ic_network_error)
            tvTitle.text = title ?: getString(R.string.network_error)
            tvDescription.text = description ?: getString(R.string.network_error_tips)
        }
    }
}