package io.iotex.iopay.wallet.aawallet.dialog

import android.content.Intent
import android.net.Uri
import android.text.method.LinkMovementMethod
import android.view.Gravity
import androidx.lifecycle.lifecycleScope
import com.blankj.utilcode.util.ColorUtils
import com.blankj.utilcode.util.ScreenUtils
import com.blankj.utilcode.util.Utils
import com.machinefi.w3bstream.utils.extension.setClickSpan
import io.iotex.base.bindbase.BaseBindDialog
import io.iotex.iopay.R
import io.iotex.iopay.databinding.DialogWalletExpiredBinding
import io.iotex.iopay.support.eventbus.RefreshAAWalletStatusEvent
import io.iotex.iopay.util.Config
import io.iotex.iopay.util.FireBaseEvent
import io.iotex.iopay.util.FireBaseUtil
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.wallet.aawallet.SendRecoverEmailActivity
import io.iotex.iopay.wallet.list.SwitchWalletActivity
import io.iotex.iopay.wallet.viewmodel.AAWalletViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.greenrobot.eventbus.EventBus
import org.jetbrains.anko.startActivity

class WalletExpiredDialog:
    BaseBindDialog<AAWalletViewModel, DialogWalletExpiredBinding>(R.layout.dialog_wallet_expired) {

    var onRefreshListener: (() -> Unit)? = null

    override fun initView() {
        mBinding.run {
            btnSwitchWallet.setOnClickListener {
                SwitchWalletActivity.startActivity(requireContext())
                dismiss()
                FireBaseUtil.logFireBase(FireBaseEvent.ACTION_HOME_AA_WALLET_EXPIRED_SWITCH_WALLET)
            }
            tvSendRecoveryEmail.setOnClickListener {
                lifecycleScope.launch {
                    val wallet = withContext(Dispatchers.IO) {
                        WalletHelper.getCurWallet()
                    } ?: return@launch
                    requireActivity().startActivity<SendRecoverEmailActivity>(
                        SendRecoverEmailActivity.KEY_WALLET_ADDRESS to wallet.address,
                        SendRecoverEmailActivity.KEY_WALLET_EMAIL to wallet.email,
                    )
                    dismiss()
                }
                FireBaseUtil.logFireBase(FireBaseEvent.ACTION_HOME_AA_WALLET_EXPIRED_SEND_RECOVERY_EMAIL)
            }
            tvRecoveryNote.text = (getString(R.string.send_recovery_email_alert) + getString(R.string.have_received_recovery_email))
                .setClickSpan(getString(R.string.have_received_recovery_email), ColorUtils.getColor(R.color.color_617AFF)){
                    dismiss()
                    EventBus.getDefault().post(RefreshAAWalletStatusEvent())
                }
            tvRecoveryNote.movementMethod = LinkMovementMethod()
            tvContractUs.setOnClickListener {
                val uri: Uri = Uri.parse("mailto:${Config.FEEDBACK_EMAIL}")
                val intent = Intent(Intent.ACTION_SENDTO, uri)
                startActivity(Intent.createChooser(intent, getString(R.string.ioPay)))
                FireBaseUtil.logFireBase(FireBaseEvent.ACTION_AA_WALLET_EXPIRED_REVOCERY_CONTACT_US)
            }
        }
    }

    override fun initData() {
    }

    override fun onStart() {
        super.onStart()
        val attributes = dialog?.window?.attributes
        attributes?.width = ScreenUtils.getScreenWidth()
        attributes?.gravity = Gravity.BOTTOM
        dialog?.window?.attributes = attributes
    }
}