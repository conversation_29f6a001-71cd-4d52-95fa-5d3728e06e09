package io.iotex.iopay.wallet.add

import android.app.Activity
import android.os.Bundle
import android.text.method.LinkMovementMethod
import android.view.WindowManager
import androidx.annotation.IntDef
import androidx.core.content.ContextCompat
import androidx.lifecycle.lifecycleScope
import com.drakeet.multitype.MultiTypeAdapter
import com.machinefi.w3bstream.utils.extension.setClickSpan
import io.iotex.base.BaseViewModel
import io.iotex.iopay.R
import io.iotex.iopay.base.BaseBindToolbarActivity
import io.iotex.iopay.databinding.ActivityMnemonicBinding
import io.iotex.iopay.ui.binder.MnemonicBinder
import io.iotex.iopay.ui.binder.MnemonicItemWrapper
import io.iotex.iopay.util.Config
import io.iotex.iopay.util.Config.BIP_44_URL
import io.iotex.iopay.util.Constant
import io.iotex.iopay.util.Constant.MNEMONIC_PATH_ETH
import io.iotex.iopay.util.Constant.MNEMONIC_PATH_IOTEX
import io.iotex.iopay.util.Constant.MNEMONIC_PATH_LEGACY
import io.iotex.iopay.util.Constant.MNEMONIC_PATH_SOLANA
import io.iotex.iopay.util.FireBaseEvent
import io.iotex.iopay.util.FireBaseUtil
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.util.extension.setGone
import io.iotex.iopay.util.extension.toast
import io.iotex.iopay.xapp.WebActivity
import io.iotex.iopay.wallet.add.MnemonicActivity.Companion.CREATE_MNEMONIC
import io.iotex.iopay.wallet.add.MnemonicActivity.Companion.IMPORT_EVM_MNEMONIC
import io.iotex.iopay.wallet.add.MnemonicActivity.Companion.IMPORT_IOTEX_MNEMONIC
import io.iotex.iopay.wallet.dialog.SelectMnemonicPathDialog
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.jetbrains.anko.startActivity

class MnemonicActivity : BaseBindToolbarActivity<BaseViewModel,ActivityMnemonicBinding>(R.layout.activity_mnemonic) {

    private val mAdapter = MultiTypeAdapter()

    private val mMnemonicMethod by lazy {
        intent.getIntExtra(KEY_MNEMONIC_METHOD, CREATE_MNEMONIC)
    }
    private val mName by lazy {
        intent.getStringExtra(KEY_WALLET_NAME)
    }

    private lateinit var mPath: String
    private var mPathType: Int = PATH_IOTEX

    override fun onCreate(savedInstanceState: Bundle?) {
        window.addFlags(WindowManager.LayoutParams.FLAG_SECURE)
        super.onCreate(savedInstanceState)
    }

    override fun initView() {
        setToolbarTitle(getString(R.string.mnemonic))
        mBinding.llEye.setOnClickListener {
            mBinding.llEye.setGone()
        }
        mAdapter.register(
            MnemonicItemWrapper::class,
            MnemonicBinder(showIndex = true, indexAlpha = 1f)
        )
        mBinding.mRvMnemonic.adapter = mAdapter
        if (mMnemonicMethod == CREATE_MNEMONIC) {
            generateMnemonic()
        }
        mBinding.mBtnNext.setOnClickListener {
            val mnemonicPhrases = mAdapter.items.map { (it as MnemonicItemWrapper).phrase ?: "" }
            if (mnemonicPhrases.isEmpty()) return@setOnClickListener
            MnemonicVerifyActivity.start(this, mName, mnemonicPhrases, mPath, mPathType)
        }
        mBinding.mTvRegenerate.setOnClickListener {
            generateMnemonic()
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_START_CREATE_RECOVERY_REGENERATE)
        }
        mBinding.mLlPath.setOnClickListener {
            val dialog = SelectMnemonicPathDialog(mPathType)
            dialog.setOnSelectedListener { pathType ->
                setPathType(pathType)
                val bundle = Bundle()
                bundle.putString("path", mPathType.toString())
                FireBaseUtil.logFireBase(FireBaseEvent.ACTION_START_CREATE_RECOVERY_HD, bundle)
            }
            dialog.commitAddValues()
            dialog.showAllowingStateLoss(this)
        }
        val chainId = WalletHelper.getCurChainId()
        if (chainId == Config.ETH_CHAIN_ID) {
            setPathType(PATH_ETH)
        } else if(WalletHelper.isBitcoinNetwork()){
            setPathType(PATH_BTC)
        } else {
            setPathType(PATH_IOTEX)
        }
        mBinding.tvPathCaption.text = getString(R.string.iopay_supports_the_default_bip44_standard)
            .setClickSpan(getString(R.string.learn_more_lowercase), ContextCompat.getColor(mBinding.tvPathCaption.context, R.color.color_title)) {
                WebActivity.startActivity(this, BIP_44_URL)
            }
        mBinding.tvPathCaption.movementMethod = LinkMovementMethod()
    }

    private fun setPathType(pathType:Int){
        mPathType = pathType
        when (mPathType) {
            PATH_ETH -> {
                mBinding.ivPathLogo.setImageResource(R.drawable.icon_eth_network_logo)
                mBinding.mTvPath.text = Constant.COIN_TYPE_EVM_DESC
                mPath = MNEMONIC_PATH_ETH
            }
            PATH_BTC -> {
                mBinding.ivPathLogo.setImageResource(R.drawable.icon_bitcoin_logo)
                mBinding.mTvPath.text = Constant.COIN_TYPE_BITCOIN_DESC
                mPath = MNEMONIC_PATH_LEGACY
            }
            PATH_SOLANA -> {
                mBinding.ivPathLogo.setImageResource(R.drawable.icon_solana_logo)
                mBinding.mTvPath.text = Constant.COIN_TYPE_SOLANA_DESC
                mPath = MNEMONIC_PATH_SOLANA
            }
            else -> {
                mBinding.ivPathLogo.setImageResource(R.drawable.icon_iotex_network_logo)
                mBinding.mTvPath.text = Constant.COIN_TYPE_IOTEX_DESC
                mPath = MNEMONIC_PATH_IOTEX
            }
        }
    }

    private fun generateMnemonic() {
        val errorHandler = CoroutineExceptionHandler { _, exception ->
            exception.message?.toast()
            hideLoading()
        }
        showLoading()
        lifecycleScope.launch(errorHandler) {
            val mnemonicList = withContext(Dispatchers.IO) {
                WalletHelper.generateMnemonic().split(" ")
            }
            hideLoading()
            mAdapter.items = mnemonicList.map { MnemonicItemWrapper(it) }
            mAdapter.notifyDataSetChanged()
        }
    }

    companion object {
        const val KEY_MNEMONIC_METHOD = "key_mnemonic_method"
        const val KEY_WALLET_NAME = "key_wallet_name"
        const val KEY_MNEMONIC= "key_mnemonic"

        const val CREATE_MNEMONIC = 1
        const val IMPORT_IOTEX_MNEMONIC = 2
        const val IMPORT_EVM_MNEMONIC = 3

        fun start(context: Activity, name: String, @GetMnemonicMethod method: Int, mnemonic: String? = null) {
            context.startActivity<MnemonicActivity>(
                KEY_MNEMONIC_METHOD to method,
                KEY_WALLET_NAME to name,
                KEY_MNEMONIC to mnemonic
            )
        }
    }
}

@Target(AnnotationTarget.VALUE_PARAMETER, AnnotationTarget.FIELD, AnnotationTarget.FUNCTION)
@MustBeDocumented
@IntDef(CREATE_MNEMONIC, IMPORT_IOTEX_MNEMONIC, IMPORT_EVM_MNEMONIC)
@Retention(AnnotationRetention.SOURCE)
annotation
class GetMnemonicMethod