package io.iotex.iopay.wallet.add

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.view.WindowManager
import androidx.lifecycle.lifecycleScope
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.Utils
import com.drakeet.multitype.MultiTypeAdapter
import io.iotex.base.BaseViewModel
import io.iotex.iopay.R
import io.iotex.iopay.base.BaseBindToolbarActivity
import io.iotex.iopay.data.db.AppDatabase
import io.iotex.iopay.data.db.Mnemonic
import io.iotex.iopay.data.db.Wallet
import io.iotex.iopay.databinding.ActivityMnemonicVerifyBinding
import io.iotex.iopay.ui.binder.MnemonicBinder
import io.iotex.iopay.ui.binder.MnemonicItemWrapper
import io.iotex.iopay.util.Constant
import io.iotex.iopay.util.Constant.MNEMONIC_PATH_ETH
import io.iotex.iopay.util.Constant.MNEMONIC_PATH_IOTEX
import io.iotex.iopay.util.Constant.MNEMONIC_PATH_SOLANA
import io.iotex.iopay.util.EncryptUtil
import io.iotex.iopay.util.FireBaseEvent
import io.iotex.iopay.util.FireBaseUtil
import io.iotex.iopay.util.TokenUtil
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.util.extension.setInvisible
import io.iotex.iopay.util.extension.setVisible
import io.iotex.iopay.util.extension.toast
import io.iotex.iopay.wallet.add.SuccessNewActivity.Companion.TYPE_CREATE
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class MnemonicVerifyActivity : BaseBindToolbarActivity<BaseViewModel,ActivityMnemonicVerifyBinding>(R.layout.activity_mnemonic_verify) {

    private val mAdapter01 = MultiTypeAdapter()
    private val mAdapter02 = MultiTypeAdapter()

    private val mMnemonicPhrases by lazy {
        intent.getStringArrayListExtra(KEY_MNEMONIC_PHRASE) ?: arrayListOf()
    }
    private val mPath by lazy {
        intent.getStringExtra(KEY_MNEMONIC_PATH) ?: Constant.MNEMONIC_PATH_IOTEX
    }
    private val mPathType by lazy {
        intent.getIntExtra(KEY_MNEMONIC_PATH_TYPE, PATH_IOTEX)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        window.addFlags(WindowManager.LayoutParams.FLAG_SECURE)
        super.onCreate(savedInstanceState)
    }

    override fun initView() {
        setToolbarTitle(getString(R.string.mnemonic_verify))
        mAdapter01.register(MnemonicItemWrapper::class, MnemonicBinder(mMnemonicPhrases, true).apply {
            setOnDeleteListener { phrase ->
                val index = mAdapter02.items.indexOfFirst {
                    (it as MnemonicItemWrapper).phrase == phrase
                }
                if (index != -1) {
                    (mAdapter02.items[index] as MnemonicItemWrapper).isSelected = false
                    mAdapter02.notifyItemChanged(index)
                    mBinding.mBtnNext.isEnabled = false
                }
                FireBaseUtil.logFireBase(FireBaseEvent.ACTION_START_CREATE_RECOVERY_VERIFY_DELETE)
            }
        })
        mBinding.mRvMnemonic01.adapter = mAdapter01
        mBinding.mRvMnemonic01.itemAnimator?.changeDuration = 0
        mAdapter01.items = mMnemonicPhrases.map { MnemonicItemWrapper() }
        mAdapter01.notifyDataSetChanged()

        mAdapter02.register(MnemonicItemWrapper::class, MnemonicBinder(mMnemonicPhrases).apply {
            setOnSelectedListener { mnemonicPhrase ->
                val index = mAdapter01.items.indexOfFirst {
                    (it as MnemonicItemWrapper).phrase.isNullOrBlank()
                }
                if (index == -1) {
                    return@setOnSelectedListener
                }
                if (index > 1 && (mAdapter01.items[index] as MnemonicItemWrapper).phrase == mnemonicPhrase)
                    return@setOnSelectedListener
                (mAdapter01.items[index] as MnemonicItemWrapper).phrase = mnemonicPhrase
                mAdapter01.notifyItemChanged(index)
                if (index - 1 > -1) {
                    mAdapter01.notifyItemChanged(index - 1)
                }
                mBinding.mBtnNext.isEnabled =
                    !(mAdapter01.items.last() as MnemonicItemWrapper).phrase.isNullOrBlank()
            }
            setOnSelectStatusListener {
                if (it) {
                    mBinding.mTvVerifyError.setInvisible()
                } else {
                    mBinding.mTvVerifyError.setVisible()
                }
            }
        })
        mBinding.mRvMnemonic02.adapter = mAdapter02
        mAdapter02.items = mMnemonicPhrases.map { MnemonicItemWrapper(it) }.shuffled()
        mAdapter02.notifyDataSetChanged()

        mBinding.mBtnNext.setOnClickListener {
            doNext()
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_START_CREATE_RECOVERY_VERIFY_CONFIRM)
        }
    }

    private fun doNext() {
        val errorHandler = CoroutineExceptionHandler { _, exception ->
            exception.message?.toast()
            hideLoading()
        }
        lifecycleScope.launch(errorHandler) {
            showLoading()
            val result = verifyMnemonic()
            if (result) {
                val walletCount = withContext(Dispatchers.IO) {
                    AppDatabase.getInstance(Utils.getApp()).walletDao().count()
                }
                withContext(Dispatchers.IO) {
                    val wallet = generateWallet() ?: throw IllegalArgumentException("Can't generate a wallet")
                    saveWallet(wallet)
                    Constant.currentWallet = wallet
                }
                if (walletCount <= 0) {
                    ActivityUtils.getActivityList().forEach { it?.finish() }
                }
                SuccessNewActivity.startActivity(this@MnemonicVerifyActivity, TYPE_CREATE)
            }
            hideLoading()
        }
    }

    private fun verifyMnemonic(): Boolean {
        val destList = mAdapter01.items.map { (it as MnemonicItemWrapper).phrase }
        if (destList.size != mMnemonicPhrases.size) {
            mBinding.mTvVerifyError.setVisible()
            return false
        }
        mMnemonicPhrases.forEachIndexed { index, s ->
            if (!destList[index].equals(s, true)) {
                mBinding.mTvVerifyError.setVisible()
                return false
            }
        }
        mBinding.mTvVerifyError.setInvisible()
        return true
    }

    private fun generateWallet(): Wallet? {
        if (mMnemonicPhrases.isEmpty()) return null
        val mnemonicStr = mMnemonicPhrases.joinToString(" ")
        val password = TokenUtil.createRandomPassword()
        val name = intent.getStringExtra(KEY_WALLET_NAME) ?: "${getString(R.string.wallet)}1"
        return WalletHelper.generateWalletViaMnemonic(mnemonicStr, mPath, mPathType, password, name)
    }

    private fun saveWallet(wallet: Wallet) {
        if (wallet.mnemonicId.isBlank()) return
        if (mMnemonicPhrases.isEmpty()) return
        val mnemonicStr = mMnemonicPhrases.joinToString(" ")
        val encryptedMnemonic = EncryptUtil.encrypt(mnemonicStr)
        val coinType = when (mPath) {
            MNEMONIC_PATH_IOTEX -> {
                Constant.COIN_TYPE_IOTEX
            }
            MNEMONIC_PATH_ETH -> {
                Constant.COIN_TYPE_EVM
            }
            MNEMONIC_PATH_SOLANA -> {
                Constant.COIN_TYPE_SOLANA
            }
            else -> {
                Constant.COIN_TYPE_BITCOIN
            }
        }
        val mnemonic = Mnemonic(
            wallet.mnemonicId, encryptedMnemonic, coinType,
            WalletHelper.nameMnemonic()
        )
        AppDatabase.getInstance(Utils.getApp()).mnemonicDao().insertIfNotExists(mnemonic)
        WalletHelper.saveWallet(wallet)
        WalletHelper.switchWallet(wallet)
    }

    companion object {
        const val KEY_MNEMONIC_PHRASE = "key_mnemonic_phrase"
        const val KEY_WALLET_NAME = "key_wallet_name"
        const val KEY_MNEMONIC_PATH = "key_mnemonic_path"
        const val KEY_MNEMONIC_PATH_TYPE = "key_mnemonic_path_type"

        fun start(context: Activity, name: String?, mnemonicPhrases: List<String>, path: String, @PathType pathType: Int) {
            val intent = Intent(context, MnemonicVerifyActivity::class.java)
            intent.putStringArrayListExtra(KEY_MNEMONIC_PHRASE, mnemonicPhrases as ArrayList<String>)
            intent.putExtra(KEY_WALLET_NAME, name)
            intent.putExtra(KEY_MNEMONIC_PATH, path)
            intent.putExtra(KEY_MNEMONIC_PATH_TYPE, pathType)
            context.startActivity(intent)
        }
    }

}