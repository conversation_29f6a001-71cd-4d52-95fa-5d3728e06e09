package io.iotex.iopay.wallet.add

import android.content.Context
import androidx.annotation.IntDef
import androidx.core.widget.addTextChangedListener
import androidx.lifecycle.lifecycleScope
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.EncryptUtils
import com.blankj.utilcode.util.KeyboardUtils
import com.blankj.utilcode.util.SPUtils
import com.blankj.utilcode.util.TimeUtils
import com.blankj.utilcode.util.Utils
import com.machinefi.lockscreen.LockAuthHelper
import com.machinefi.lockscreen.PFSecurityUtils
import io.iotex.base.BaseViewModel
import io.iotex.iopay.R
import io.iotex.iopay.base.BaseBindToolbarActivity
import io.iotex.iopay.data.db.AppDatabase
import io.iotex.iopay.data.db.Wallet
import io.iotex.iopay.databinding.ActivityNameWalletBinding
import io.iotex.iopay.support.eventbus.RenameWalletEvent
import io.iotex.iopay.util.EncryptUtil
import io.iotex.iopay.util.RxUtil
import io.iotex.iopay.util.SPConstant
import io.iotex.iopay.util.TokenUtil
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.util.extension.setInvisible
import io.iotex.iopay.util.extension.setVisible
import io.iotex.iopay.util.extension.toast
import io.iotex.iopay.wallet.add.MnemonicActivity.Companion.CREATE_MNEMONIC
import io.iotex.iopay.wallet.add.NameWalletActivity.Companion.ACTION_RENAME
import io.iotex.iopay.wallet.add.NameWalletActivity.Companion.CREATE_VIA_MNEMONIC
import io.iotex.iopay.wallet.add.NameWalletActivity.Companion.CREATE_VIA_PRIVATE_KEY
import io.iotex.iopay.wallet.add.NameWalletActivity.Companion.IMPORT_VIA_ADDRESS
import io.iotex.iopay.wallet.add.NameWalletActivity.Companion.IMPORT_VIA_KEYSTORE
import io.iotex.iopay.wallet.add.NameWalletActivity.Companion.IMPORT_VIA_PRIVATE_KEY
import io.iotex.iopay.wallet.add.SuccessNewActivity.Companion.TYPE_CREATE
import io.iotex.iopay.wallet.add.SuccessNewActivity.Companion.TYPE_IMPORT
import io.iotex.iopay.wallet.add.SuccessNewActivity.Companion.TYPE_WATCH
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.greenrobot.eventbus.EventBus
import org.jetbrains.anko.startActivity
import java.util.concurrent.TimeUnit

class NameWalletActivity : BaseBindToolbarActivity<BaseViewModel,ActivityNameWalletBinding>(R.layout.activity_name_wallet) {

    private val mMethod by lazy {
        intent.getIntExtra(KEY_CREATE_METHOD, CREATE_VIA_PRIVATE_KEY)
    }
    private val mPayload by lazy {
        intent.getStringExtra(KEY_PAYLOAD)
    }

    private val mErrorHandler = CoroutineExceptionHandler { _, exception ->
        exception.message?.toast()
    }

    override fun initView() {
        setToolbarTitle(getString(R.string.create_wallet_button))
        setToolbarSubmitText(getString(R.string.next_pf))
        setToolbarSubmitClick {
            KeyboardUtils.hideSoftInput(this)
            checkAuthentication()
        }
        when(mMethod) {
            IMPORT_VIA_PRIVATE_KEY, IMPORT_VIA_ADDRESS, IMPORT_VIA_KEYSTORE -> {
                setToolbarTitle(getString(R.string.import_wallet_button))
                autoNameWallet()
            }
            ACTION_RENAME -> {
                setToolbarTitle(getString(R.string.edit_wallet_name))
                mBinding.mEtWalletName.editText?.setText(mPayload)
                mBinding.mEtWalletName.editText?.setSelection(mPayload?.length ?: 0)
            }
            else -> {
                setToolbarTitle(getString(R.string.create_wallet_button))
                autoNameWallet()
            }
        }
        mBinding.mEtWalletName.editText?.addTextChangedListener {
            validateName(it.toString().trim())
        }
    }

    private fun autoNameWallet() {
        lifecycleScope.launch {
            val name = withContext(Dispatchers.IO) {
                WalletHelper.nameWallet()[0]
            }
            mBinding.mEtWalletName.editText?.setText(name)
            mBinding.mEtWalletName.editText?.setSelection(name.length)
        }
    }

    private fun validateName(name: String) {
        lifecycleScope.launch {
            if (name.isBlank()) {
                showError(getString(R.string.validate_not_null))
                return@launch
            }
            if (name.length > 20) {
                showError(getString(R.string.validate_wallet_name_length))
                return@launch
            }
            val wallet = withContext(Dispatchers.IO) {
                AppDatabase.getInstance(this@NameWalletActivity).walletDao()
                    .queryWalletByAlias(name)
            }
            if (wallet != null) {
                showError(getString(R.string.validate_alias_exist))
                return@launch
            }
            mBinding.mTvError.setInvisible()
            setToolbarSubmit(true)
        }
    }

    private fun checkAuthentication() {
        lifecycleScope.launch {
            val walletCount = withContext(Dispatchers.IO + mErrorHandler) {
                AppDatabase.getInstance(Utils.getApp()).walletDao().count()
            }
            if (walletCount <= 0 && !PFSecurityUtils.isCreatePinCode()) {
                SPUtils.getInstance().put(SPConstant.SP_FIRST_SWITCH_NET, true)
                LockAuthHelper.showAuthLock(this@NameWalletActivity){
                    onAuthSuccess()
                }
            } else {
                onAuthSuccess()
            }
        }
    }

    private fun createWalletViaPrivateKey(name: String) {
        lifecycleScope.launch(Dispatchers.IO + mErrorHandler) {
            withContext(Dispatchers.Main) {
                showLoading()
            }
            val walletCount = AppDatabase.getInstance(Utils.getApp()).walletDao().count()
            val password = TokenUtil.createRandomPassword()
            val encodedPassword = EncryptUtil.encrypt(password)
            val wallet = WalletHelper.createWallet(
                this@NameWalletActivity,
                name,
                encodedPassword,
                password
            )
            insertWallet(wallet)
            withContext(Dispatchers.Main) {
                hideLoading()
                if (walletCount <= 0) {
                    ActivityUtils.getActivityList().forEach { it?.finish() }
                }
                SuccessNewActivity.startActivity(this@NameWalletActivity,TYPE_CREATE)
            }
        }
    }

    private fun importWalletViaPrivateKey(name: String) {
        lifecycleScope.launch(Dispatchers.IO + mErrorHandler) {
            if (!WalletHelper.isValidPrivateKey(mPayload?:"")) {
                Utils.getApp().getString(R.string.key_invalid).toast()
                onBackPressed()
                return@launch
            }
            withContext(Dispatchers.Main) {
                showLoading()
            }
            val walletCount = AppDatabase.getInstance(Utils.getApp()).walletDao().count()
            val password = TokenUtil.createRandomPassword()
            val encodedPassword = EncryptUtil.encrypt(password)
            val wallet = WalletHelper.importWalletByPK(
                Utils.getApp(),
                name,
                mPayload!!,
                encodedPassword,
                password
            )
            val oldWallet = AppDatabase.getInstance(Utils.getApp()).walletDao()
                .queryWalletByAddress(wallet.address)
            if (oldWallet?.isWatch == true) {
                AppDatabase.getInstance(Utils.getApp()).walletDao().deleteWallet(oldWallet)
            }
            insertWallet(wallet)
            withContext(Dispatchers.Main) {
                hideLoading()
                if (walletCount <= 0) {
                    ActivityUtils.getActivityList().forEach { it?.finish() }
                }
                SuccessNewActivity.startActivity(this@NameWalletActivity,TYPE_IMPORT)
            }
        }
    }

    private fun importWalletViaAddress(name: String) {
        lifecycleScope.launch(Dispatchers.IO) {
            if (mPayload.isNullOrBlank()) {
                Utils.getApp().getString(R.string.address_invalid).toast()
                onBackPressed()
                return@launch
            }
            withContext(Dispatchers.Main) {
                showLoading()
            }
            val walletCount = AppDatabase.getInstance(Utils.getApp()).walletDao().count()
            val password = TokenUtil.createRandomPassword()
            val encodedPassword = EncryptUtil.encrypt(password)
            val wallet = Wallet(
                WalletHelper.convertWeb3Address(mPayload?:""),
                name, encodedPassword, "", true,
                TimeUtils.getNowMills().toString(),
            )
            insertWallet(wallet)
            withContext(Dispatchers.Main) {
                hideLoading()
                if (walletCount <= 0) {
                    ActivityUtils.getActivityList().forEach { it?.finish() }
                }
                SuccessNewActivity.startActivity(this@NameWalletActivity,TYPE_WATCH)
            }
        }
    }

    private fun insertWallet(wallet: Wallet) {
        WalletHelper.saveWallet(wallet)
        WalletHelper.switchWallet(wallet)
    }

    private fun createWalletViaMnemonic(name: String) {
        val errorHandler = CoroutineExceptionHandler { _, e ->
            e.message?.toast()
            showLoading()
        }
        lifecycleScope.launch(errorHandler) {
            if (mPayload.isNullOrBlank()) {
                MnemonicActivity.start(this@NameWalletActivity, name, CREATE_MNEMONIC)
            } else {
                showLoading()
                val wallet = withContext(Dispatchers.IO) {
                    generateWallet(name, mPayload!!)
                }
                if (wallet == null) {
                    hideLoading()
                    return@launch
                }
                val walletCount = withContext(Dispatchers.IO + mErrorHandler) {
                    AppDatabase.getInstance(Utils.getApp()).walletDao().count()
                }
                withContext(Dispatchers.IO) {
                    insertWallet(wallet)
                }
                if (walletCount <= 0) {
                    ActivityUtils.getActivityList().forEach { it?.finish() }
                }
                SuccessNewActivity.startActivity(this@NameWalletActivity,TYPE_IMPORT)
                hideLoading()
            }
        }
    }

    private fun generateWallet(name: String, encryptedMnemonic: String, skipPath: String? = null): Wallet? {
        val mnemonic = EncryptUtil.decrypt(encryptedMnemonic)
        val mnemonicId = EncryptUtils.encryptMD5ToString(mnemonic)
        val path = WalletHelper.getMnemonicPath(mnemonicId, skipPath)
        val mnemonicWallet = AppDatabase.getInstance(Utils.getApp()).walletDao()
            .queryByMnemonic(mnemonicId).firstOrNull()
        val pathType = mnemonicWallet?.pathType ?: PATH_ETH
        val password = TokenUtil.createRandomPassword()
        val wallet = WalletHelper.generateWalletViaMnemonic(mnemonic, path, pathType, password, name) ?: return null
        val w = AppDatabase.getInstance(Utils.getApp()).walletDao()
            .queryWalletByAddress(wallet.address)
        if (w != null) {
            return generateWallet(name, encryptedMnemonic, path)
        }
        return wallet
    }

    private fun onAuthSuccess() {
        val name = mBinding.mEtWalletName.editText?.text?.toString()?.trim() ?: return
        when (mMethod) {
            CREATE_VIA_PRIVATE_KEY -> createWalletViaPrivateKey(name)
            CREATE_VIA_MNEMONIC -> createWalletViaMnemonic(name)
            IMPORT_VIA_PRIVATE_KEY -> importWalletViaPrivateKey(name)
            IMPORT_VIA_ADDRESS -> importWalletViaAddress(name)
            ACTION_RENAME -> {
                renameWallet(name)
                finish()
            }
        }
    }

    private fun renameWallet(newName: String) {
        lifecycleScope.launch(Dispatchers.IO) {
            val list =  AppDatabase.getInstance(Utils.getApp()).rpcNetworkDao()
                .queryAllRPCNetwork()
            list.forEach { _ ->
                val wallet = AppDatabase.getInstance(Utils.getApp()).walletDao()
                    .queryWalletByAlias(mPayload ?: "") ?: return@forEach
                wallet.alias = newName
                AppDatabase.getInstance(Utils.getApp()).walletDao().updateWallet(wallet)
                EventBus.getDefault().post(RenameWalletEvent(wallet))
            }
        }
    }

    private fun showError(error: String) {
        mBinding.mTvError.setVisible()
        mBinding.mTvError.text = error
        setToolbarSubmit(false)
    }

    companion object {
        const val KEY_CREATE_METHOD = "key_create_method"
        const val KEY_PAYLOAD = "key_payload"
        const val KEY_TITLE = "key_title"

        const val CREATE_VIA_PRIVATE_KEY = 1
        const val CREATE_VIA_MNEMONIC = 2
        const val IMPORT_VIA_PRIVATE_KEY = 3
        const val IMPORT_VIA_KEYSTORE = 4
        const val IMPORT_VIA_ADDRESS = 5
        const val ACTION_RENAME = 6

        fun start(context: Context, @CreateWalletMethod method: Int, payload: String? = null, title: String? = null) {
            context.startActivity<NameWalletActivity>(
                KEY_CREATE_METHOD to method,
                KEY_PAYLOAD to payload,
                KEY_TITLE to title
            )
        }
    }
}

@Target(AnnotationTarget.VALUE_PARAMETER, AnnotationTarget.FIELD, AnnotationTarget.FUNCTION)
@MustBeDocumented
@IntDef(
    CREATE_VIA_PRIVATE_KEY,
    CREATE_VIA_MNEMONIC,
    IMPORT_VIA_PRIVATE_KEY,
    IMPORT_VIA_KEYSTORE,
    IMPORT_VIA_ADDRESS,
    ACTION_RENAME
)
@Retention(AnnotationRetention.SOURCE)
annotation
class CreateWalletMethod