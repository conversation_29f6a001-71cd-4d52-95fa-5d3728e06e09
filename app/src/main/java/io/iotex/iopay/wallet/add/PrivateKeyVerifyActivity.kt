package io.iotex.iopay.wallet.add

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.WindowManager
import androidx.core.widget.addTextChangedListener
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.ClipboardUtils
import com.blankj.utilcode.util.ToastUtils
import com.blankj.utilcode.util.Utils
import io.iotex.iopay.MainActivity
import io.iotex.iopay.R
import io.iotex.iopay.base.BaseBindToolbarActivity
import io.iotex.iopay.databinding.ActivityPrivateKeyVerifyBinding
import io.iotex.iopay.util.extension.setGone
import io.iotex.iopay.util.extension.toast

class PrivateKeyVerifyActivity :
    BaseBindToolbarActivity<PrivateKeyVerifyViewModel, ActivityPrivateKeyVerifyBinding>(R.layout.activity_private_key_verify) {

    override fun onCreate(savedInstanceState: Bundle?) {
        window.addFlags(WindowManager.LayoutParams.FLAG_SECURE)
        super.onCreate(savedInstanceState)
    }

    override fun initView() {
        setToolbarTitle(getString(R.string.create_wallet_with_private_key))
        mBinding.etPrivateKey.addTextChangedListener {
            if (it.isNullOrEmpty()) {
                mBinding.etPrivateKey.alpha = 0.5f
            } else {
                mBinding.etPrivateKey.alpha = 1f
            }
            if (mBinding.etPrivateKey.text.toString() == mBinding.tvPrivateKey.text.toString()) {
                mBinding.tvSave.alpha = 1f
            } else {
                mBinding.tvSave.alpha = 0.5f
            }
        }
        mBinding.llEye.setOnClickListener {
            mBinding.llEye.setGone()
        }
        mBinding.ivCopy.setOnClickListener {
            ClipboardUtils.copyText(mBinding.tvPrivateKey.text.toString())
            Utils.getApp().getString(R.string.copy_success).toast()
        }
        mBinding.tvSave.setOnClickListener {
            if (mBinding.etPrivateKey.text.toString() == mBinding.tvPrivateKey.text.toString()) {
                mViewModel.saveWallet(mBinding.etPrivateKey.text.toString())
            } else {
                ToastUtils.showShort(R.string.input_verify_private_key)
            }
        }
    }

    override fun initData() {
        mViewModel.getNewPrivateKet()
        mViewModel.privateLiveDate.observe(this) {
            mBinding.tvPrivateKey.text = it
        }
        mViewModel.saveLiveDate.observe(this) { reboot ->
            if (reboot) {
                ActivityUtils.getActivityList().forEach { it?.finish() }
            }
            MainActivity.startActivity(this)
        }
    }

    companion object {
        fun startActivity(context: Context) {
            context.startActivity(Intent(context, PrivateKeyVerifyActivity::class.java))
        }
    }
}