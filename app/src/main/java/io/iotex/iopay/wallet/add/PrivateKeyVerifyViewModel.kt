package io.iotex.iopay.wallet.add

import android.app.Application
import androidx.lifecycle.MutableLiveData
import com.blankj.utilcode.util.Utils
import com.github.iotexproject.antenna.account.Account
import com.github.iotexproject.antenna.account.IotexAccount
import io.iotex.iopay.base.BaseLaunchVM
import io.iotex.iopay.data.db.AppDatabase
import io.iotex.iopay.util.EncryptUtil
import io.iotex.iopay.util.TokenUtil
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.util.extension.toHexStringNoPrefix

class PrivateKeyVerifyViewModel(application: Application) : BaseLaunchVM(application) {

    val privateLiveDate = MutableLiveData<String>()
    val saveLiveDate = MutableLiveData<Boolean>()
    fun getNewPrivateKet() {
        var account: Account? = null
        try {
            account = IotexAccount.create()
        } catch (e: Exception) {
            e.printStackTrace()
        }
        privateLiveDate.postValue(account?.privateKey()?.toHexStringNoPrefix())
    }

    fun saveWallet(privateKey: String) {
        addLaunch(true) {
            val walletCount = AppDatabase.getInstance(Utils.getApp()).walletDao().count()
            val name = WalletHelper.nameWallet()[0]
            val password = TokenUtil.createRandomPassword()
            val encodedPassword = EncryptUtil.encrypt(password)
            val wallet = WalletHelper.importWalletByPK(
                Utils.getApp(),
                name,
                privateKey,
                encodedPassword,
                password
            )
            WalletHelper.saveWallet(wallet)
            WalletHelper.switchWallet(wallet)
            saveLiveDate.postValue(walletCount == 0)
        }
    }
}