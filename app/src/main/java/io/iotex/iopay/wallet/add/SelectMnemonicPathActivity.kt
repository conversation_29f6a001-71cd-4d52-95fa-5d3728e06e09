package io.iotex.iopay.wallet.add

import android.annotation.SuppressLint
import android.app.Activity
import android.text.method.LinkMovementMethod
import android.view.ViewConfiguration
import android.widget.ScrollView
import androidx.annotation.IntDef
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.lifecycle.lifecycleScope
import com.blankj.utilcode.util.KeyboardUtils
import com.blankj.utilcode.util.SPUtils
import com.blankj.utilcode.util.Utils
import com.machinefi.w3bstream.utils.extension.setClickSpan
import io.iotex.iopay.R
import io.iotex.iopay.base.BaseBindToolbarActivity
import io.iotex.iopay.data.db.AppDatabase
import io.iotex.iopay.databinding.ActivitySelectMnemonicPathBinding
import io.iotex.iopay.util.Config
import io.iotex.iopay.util.Config.BIP_44_URL
import io.iotex.iopay.util.Constant
import io.iotex.iopay.util.RxUtil
import io.iotex.iopay.util.SPConstant.SP_MNEMONIC_CUSTOMIZE_PATH
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.util.extension.toast
import io.iotex.iopay.xapp.WebActivity
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.jetbrains.anko.startActivity
import java.util.concurrent.TimeUnit

class SelectMnemonicPathActivity :
    BaseBindToolbarActivity<Nothing, ActivitySelectMnemonicPathBinding>(R.layout.activity_select_mnemonic_path) {

    private val mMnemonic by lazy {
        intent.getStringExtra(KEY_MNEMONIC) ?: ""
    }

    private var mPath = Constant.MNEMONIC_PATH_IOTEX
    private var mPathType = PATH_IOTEX
    private var mPathName = "IoTeX Standard (IoTeX HD PATH)"

    @SuppressLint("CheckResult")
    override fun initView() {
        initToolbar()
        performSelected()
        mBinding.tvPathCaption.text = getString(R.string.iopay_supports_the_default_bip44_standard)
            .setClickSpan(getString(R.string.learn_more_lowercase), ContextCompat.getColor(this, R.color.color_title)) {
                WebActivity.startActivity(this, BIP_44_URL)
            }
        mBinding.tvPathCaption.movementMethod = LinkMovementMethod()
        RxUtil.textChange(mBinding.etCustomizePath)
            .debounce(800, TimeUnit.MILLISECONDS)
            .compose(RxUtil.applySchedulers())
            .subscribe {
                if (WalletHelper.validMnemonicPath(it)) {
                    mBinding.tvError.isVisible = false
                    mBinding.tvCustomizeConfirm.isEnabled = true
                } else {
                    mBinding.tvError.isVisible = true
                    mBinding.tvError.text = getString(R.string.path_invalid)
                    mBinding.tvCustomizeConfirm.isEnabled = false
                }
            }

        KeyboardUtils.registerSoftInputChangedListener(this) {
            if (it > ViewConfiguration.get(this).scaledTouchSlop) {
                mBinding.svContainer.fullScroll(ScrollView.FOCUS_DOWN)
            }
        }
    }

    private fun initToolbar() {
        setToolbarTitle(getString(R.string.hd_path))
        setToolbarSubmitText(getString(R.string.next_pf))
        setToolbarSubmitClick {
            submit()
        }
        hideToolbarBack()
    }

    private fun submit() {
        lifecycleScope.launch {
            if (WalletHelper.validMnemonicPath(mPath)) {
                val network =
                    withContext(Dispatchers.IO) {
                        if (mPathType == PATH_BTC) {
                            AppDatabase.getInstance(Utils.getApp()).rpcNetworkDao()
                                .queryRPCNetworkByChainId(Config.BITCOIN_MAIN_CHAIN_ID)
                        } else if (mPathType == PATH_SOLANA) {
                            AppDatabase.getInstance(Utils.getApp()).rpcNetworkDao()
                                .queryRPCNetworkByChainId(Config.SOLANA_MAIN_CHAIN_ID)
                        } else {
                            AppDatabase.getInstance(Utils.getApp()).rpcNetworkDao()
                                .queryRPCNetworkByChainId(Config.IOTEX_CHAIN_ID)
                        }
                    }
                network?.let {
                    WalletHelper.switchNetwork(it)
                }
                SelectMnemonicWalletActivity.start(
                    this@SelectMnemonicPathActivity,
                    mMnemonic,
                    mPath,
                    mPathType
                )
            } else {
                getString(R.string.path_invalid).toast()
            }
        }
    }

    private fun performSelected() {
        mBinding.run {
            llEthPath.setOnClickListener {
                performUnselect()
                llEthPath.setBackgroundResource(R.drawable.shape_card_back_stroke_gradient)
                mPath = Constant.MNEMONIC_PATH_ETH
                mPathType = PATH_ETH
                mPathName = "ETH Standard (ETH HD PATH)"
            }
            llIotexPath.setOnClickListener {
                performUnselect()
                llIotexPath.setBackgroundResource(R.drawable.shape_card_back_stroke_gradient)
                mPath = Constant.MNEMONIC_PATH_IOTEX
                mPathType = PATH_IOTEX
                mPathName = "IoTeX Standard (IoTeX HD PATH)"
            }
            mLlPathSolana.setOnClickListener {
                performUnselect()
                mLlPathSolana.setBackgroundResource(R.drawable.shape_card_back_stroke_gradient)
                mPath = Constant.MNEMONIC_PATH_SOLANA
                mPathType = PATH_SOLANA
                mPathName = "Solana Standard (Solana HD PATH)"
            }
            llNativeSegwit.setOnClickListener {
                performUnselect()
                llNativeSegwit.setBackgroundResource(R.drawable.shape_card_back_stroke_gradient)
                mPath = Constant.MNEMONIC_PATH_LEGACY
                mPathType = PATH_BTC
                mPathName = "BTC Standard (BTC HD PATH)"
            }
            llCustomizePath.setOnClickListener {
                performUnselect()
                llCustomizePath.setBackgroundResource(R.drawable.shape_card_back_stroke_gradient)
                selectCustomizePath()
            }
            ivArrow.setOnClickListener {
                performUnselect()
                llCustomizePath.setBackgroundResource(R.drawable.shape_card_back_stroke_gradient)
                selectCustomizePath()
                if (llCustomizePanel.isVisible) {
                    ivArrow.rotation = 360f
                } else {
                    ivArrow.rotation = 180f
                }
                llCustomizePanel.isVisible = !llCustomizePanel.isVisible
            }
            tvCustomizeConfirm.setOnClickListener {
                mPath = etCustomizePath.text.toString().trim()
                mPathName = getString(R.string.customize)
                SPUtils.getInstance().put(SP_MNEMONIC_CUSTOMIZE_PATH, mPath)
            }
        }
    }

    private fun selectCustomizePath() {
        val customizePath = SPUtils.getInstance().getString(SP_MNEMONIC_CUSTOMIZE_PATH)
        if (!customizePath.isNullOrBlank()) {
            mBinding.tvCustomizePath.text = customizePath
            mPath = customizePath
            mPathType = PATH_BTC
            mPathName = getString(R.string.customize)
        } else {
            mPath = ""
            mPathName = ""
        }
    }

    private fun performUnselect() {
        mBinding.run {
            llEthPath.setBackgroundResource(R.drawable.shape_card_back)
            llIotexPath.setBackgroundResource(R.drawable.shape_card_back)
            llNativeSegwit.setBackgroundResource(R.drawable.shape_card_back)
            llCustomizePath.setBackgroundResource(R.drawable.shape_card_back)
            mLlPathSolana.setBackgroundResource(R.drawable.shape_card_back)
        }
    }

    companion object {
        const val KEY_MNEMONIC = "key_mnemonic"

        fun start(context: Activity, mnemonic: String) {
            context.startActivity<SelectMnemonicPathActivity>(
                KEY_MNEMONIC to mnemonic,
            )
        }
    }

}

const val PATH_ETH = 1
const val PATH_IOTEX = 2
const val PATH_BTC = 3
const val PATH_CUSTOMIZE = 4
const val PATH_SOLANA = 5

@Target(AnnotationTarget.VALUE_PARAMETER, AnnotationTarget.FIELD, AnnotationTarget.FUNCTION)
@MustBeDocumented
@IntDef(
    PATH_ETH,
    PATH_IOTEX,
    PATH_BTC,
    PATH_SOLANA,
    PATH_CUSTOMIZE,
)
@Retention(AnnotationRetention.SOURCE)
annotation
class PathType