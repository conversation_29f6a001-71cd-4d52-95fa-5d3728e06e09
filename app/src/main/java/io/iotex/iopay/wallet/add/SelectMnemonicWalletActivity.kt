package io.iotex.iopay.wallet.add

import android.app.Activity
import android.os.Bundle
import androidx.lifecycle.lifecycleScope
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.SPUtils
import com.blankj.utilcode.util.Utils
import com.drakeet.multitype.MultiTypeAdapter
import com.machinefi.lockscreen.LockAuthHelper
import com.machinefi.lockscreen.PFSecurityUtils
import io.iotex.iopay.R
import io.iotex.iopay.base.BaseBindToolbarActivity
import io.iotex.iopay.data.db.AppDatabase
import io.iotex.iopay.data.db.Mnemonic
import io.iotex.iopay.data.db.RPCNetwork
import io.iotex.iopay.data.db.Wallet
import io.iotex.iopay.databinding.ActivitySelectMnemonicWalletBinding
import io.iotex.iopay.network.dialog.NetworkSwitchDialog
import io.iotex.iopay.ui.NoticePopupWindow
import io.iotex.iopay.ui.binder.SelectMnemonicWalletBinder
import io.iotex.iopay.ui.binder.SelectMnemonicWalletWrapper
import io.iotex.iopay.util.Constant
import io.iotex.iopay.util.Constant.MNEMONIC_PATH_ETH
import io.iotex.iopay.util.Constant.MNEMONIC_PATH_IOTEX
import io.iotex.iopay.util.Constant.MNEMONIC_PATH_SOLANA
import io.iotex.iopay.util.EncryptUtil
import io.iotex.iopay.util.FireBaseEvent
import io.iotex.iopay.util.FireBaseUtil
import io.iotex.iopay.util.SPConstant
import io.iotex.iopay.util.TokenUtil
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.util.extension.loadSvgOrImage
import io.iotex.iopay.util.extension.setGone
import io.iotex.iopay.util.extension.setVisible
import io.iotex.iopay.util.extension.toast
import io.iotex.iopay.wallet.add.SuccessNewActivity.Companion.TYPE_IMPORT
import io.iotex.iopay.wallet.bitcoin.BitcoinBookBinder
import io.iotex.iopay.wallet.solana.SolanaWeb3
import io.iotex.iopay.wallet.web3.Web3Delegate
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.jetbrains.anko.startActivity

class SelectMnemonicWalletActivity :
    BaseBindToolbarActivity<Nothing, ActivitySelectMnemonicWalletBinding>(R.layout.activity_select_mnemonic_wallet) {

    private val mMnemonic by lazy {
        intent.getStringExtra(KEY_MNEMONIC)
    }
    private val mPath by lazy {
        intent.getStringExtra(KEY_PATH) ?: Constant.MNEMONIC_PATH_ETH
    }
    private val mPathType by lazy {
        intent.getIntExtra(KEY_PATH_TYPE, PATH_ETH)
    }

    private val mWalletCount = 5
    private val mBitcoinWalletCount = 1
    private var mCoinType = Constant.COIN_TYPE_EVM

    private val mAdapter = MultiTypeAdapter()

    private var mSelectedWalletList = listOf<Wallet>()

    override fun initView() {
        initToolbar()

        mBinding.ivHdPath.setOnClickListener {
            NoticePopupWindow(
                null,
                getString(R.string.if_the_wallets_you_are_expecting_are_not_displayed),
                R.drawable.icon_tips_notice,
                confirmAction = {
                    finish()
                },
                confirmText = getString(R.string.switch_now)
            ).show()
        }
    }

    private fun initToolbar() {
        setToolbarTitle(getString(R.string.select_wallet))
        setToolbarSubmitText(getString(R.string.next_pf))
        setToolbarSubmitClick {
            checkAuthentication()
        }
        setToolbarSubmit(!WalletHelper.isBitcoinNetwork())
    }

    private fun checkAuthentication() {
        lifecycleScope.launch {
            if (mSelectedWalletList.isEmpty()) return@launch
            val walletCount = withContext(Dispatchers.IO) {
                AppDatabase.getInstance(Utils.getApp()).walletDao().count()
            }
            if (walletCount <= 0) {
                if (!PFSecurityUtils.isCreatePinCode()) {
                    LockAuthHelper.showAuthLock(this@SelectMnemonicWalletActivity) {
                        saveWallet(mSelectedWalletList, true)
                    }
                } else {
                    saveWallet(mSelectedWalletList, true)
                }
            } else {
                saveWallet(mSelectedWalletList)
            }
        }
    }



    override fun initData() {
        mBinding.run {
            mNslContainer.isNestedScrollingEnabled = false
            mAdapter.register(Wallet::class.java, BitcoinBookBinder().apply {
                onSelectWallet = { wallet, _ ->
                    mSelectedWalletList = listOf(wallet)
                    checkAuthentication()
                }
            })
            mAdapter.register(SelectMnemonicWalletWrapper::class.java, SelectMnemonicWalletBinder().apply {
                setOnClickListener {
                    mSelectedWalletList = getOnSelectedWallets()
                    setToolbarSubmit(mSelectedWalletList.isNotEmpty())
                }
            })
            mRvWallet.adapter = mAdapter
            mRvWallet.itemAnimator?.changeDuration = 0
            renderNetwork()
            mLlNetwork.setOnClickListener {
                MainScope().launch {
                    val network = withContext(Dispatchers.IO){
                        AppDatabase.getInstance(Utils.getApp()).rpcNetworkDao().queryAllRPCNetwork()
                    }
                    NetworkSwitchDialog(items = network).apply {
                        onItemClick = { network ->
                            val isNextBitcoinNetwork = WalletHelper.isBitcoinNetwork(network.chainId)
                            setToolbarSubmit(!isNextBitcoinNetwork)
                            createMnemonicWallet(false)
                            renderNetwork(network)
                            getWalletsBalance(mAdapter.items, false)
                        }
                    }.show(supportFragmentManager, System.currentTimeMillis().toString())
                }
            }

            mTvLoadMore.setOnClickListener {
                mTvLoadMore.setGone()
                mLlLoadingContainer.setVisible()
                createMnemonicWallet(true)
            }
            createMnemonicWallet(false)
        }
    }

    private fun renderNetwork(network: RPCNetwork? = null) {
        lifecycleScope.launch {
            val curNetwork = network ?: withContext(Dispatchers.IO) {
                WalletHelper.getCurNetwork()
            }
            curNetwork?.let {
                SPUtils.getInstance().put(SPConstant.SP_FIRST_SWITCH_NET, false)
                mBinding.mIvNetwork.loadSvgOrImage(it.logo, R.drawable.ic_network_default)
                mBinding.mTvNetwork.text = it.name
            }
        }
    }

    private fun createMnemonicWallet(loadMore: Boolean) {
        val errorHandler = CoroutineExceptionHandler { _, exception ->
            exception.message?.toast()
            hideLoading()
        }
        val pathList = resolvePath(loadMore)
        lifecycleScope.launch(errorHandler) {
            if (mMnemonic.isNullOrBlank()) {
                getString(R.string.mnemonic_invalid).toast()
                finish()
                return@launch
            }
            var count = mBinding.mRvWallet.adapter?.itemCount ?: 0
            if (!loadMore) {
                count = 0
                showLoading()
            }
            if (!loadMore) showLoading()
            val dataList = withContext(Dispatchers.IO) {
                val list = pathList.mapIndexed { index, path ->
                    async {
                        val password = TokenUtil.createRandomPassword()
                        val nameList = WalletHelper.nameWallet(pathList.size + count)
                            .subList(count, pathList.size + count)
                        val wallet = WalletHelper.generateWalletViaMnemonic(
                            mMnemonic!!,
                            path,
                            mPathType,
                            password,
                            nameList[index]
                        )
                        wallet
                    }
                }.mapNotNull {
                    it.await()
                }
                Constant.currentWallet =list.first()
                if (WalletHelper.isBitcoinNetwork()) {
                    list
                } else {
                    list.map { SelectMnemonicWalletWrapper(it, false) }
                }
            }
            val `data` = if (loadMore) {
                mutableListOf<Any>().apply {
                    addAll((mBinding.mRvWallet.adapter as? MultiTypeAdapter)?.items ?: emptyList())
                    addAll(dataList)
                }
            } else dataList
            mAdapter.items = `data`
            if (`data`.size == dataList.size) {
                mAdapter.notifyDataSetChanged()
            } else {
                mAdapter.notifyItemRangeInserted(mAdapter.items.size - 1, dataList.size)
            }
            getWalletsBalance(dataList, loadMore)
            hideLoading()
            mBinding.mTvLoadMore.setVisible()
            mBinding.mLlLoadingContainer.setGone()
        }
    }

    private fun getWalletsBalance(walletList: List<Any>, loadMore: Boolean) {
        if (WalletHelper.isBitcoinNetwork()) return
        val walletWrapperList = walletList.filterIsInstance<SelectMnemonicWalletWrapper>()
        lifecycleScope.launch {
            if (!loadMore) {
                walletWrapperList.forEach {
                    it.wallet.curBalance = "0"
                }
                mAdapter.notifyItemRangeChanged(0, walletWrapperList.size)
            }
            delay(500)
            val balanceList = withContext(Dispatchers.IO) {
                if (WalletHelper.isSolanaNetwork()) {
                    val balances = ArrayList<String>()
                    walletWrapperList.map {
                        async {
                            balances.add(SolanaWeb3.getBalance(it.wallet.getCurNetworkAddress(), WalletHelper.getCurChainId())?.toString()?:"")
                        }
                    }.awaitAll()
                    balances
                } else {
                    val addressList = walletWrapperList.map {
                        it.wallet.address
                    }
                    Web3Delegate.getCurrencyBalance(*addressList.toTypedArray())
                }
            }
            balanceList.forEachIndexed { index, balance ->
                walletWrapperList[index].wallet.curBalance = balance.toString()
            }
            val start = if (mAdapter.itemCount > walletWrapperList.size && loadMore)
                mAdapter.itemCount - walletWrapperList.size else 0
            mAdapter.notifyItemRangeChanged(start, walletWrapperList.size)
        }
    }

    private fun resolvePath(loadMore: Boolean): List<String> {
        val walletCount = if (WalletHelper.isBitcoinNetwork()) {
            mBitcoinWalletCount
        } else {
            mWalletCount
        }
        val itemCount = mBinding.mRvWallet.adapter?.itemCount ?: 0
        return if (loadMore) {
            (itemCount until itemCount + walletCount).map {
                mPath.replaceRange(mPath.length - 1, mPath.length, it.toString())
            }
        } else {
            (0 until walletCount).map {
                mPath.replaceRange(mPath.length - 1, mPath.length, it.toString())
            }
        }
    }

    private fun saveWallet(walletList: List<Wallet>,reBoot:Boolean = false) {
        if (mCoinType == Constant.COIN_TYPE_EVM) {
            val bundle = Bundle()
            bundle.putString("path", mCoinType.toString())
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_START_IMPORT_RECOVERY_HD, bundle)
        }
        mCoinType = when (mPath) {
            MNEMONIC_PATH_IOTEX -> {
                Constant.COIN_TYPE_IOTEX
            }
            MNEMONIC_PATH_ETH -> {
                Constant.COIN_TYPE_EVM
            }
            MNEMONIC_PATH_SOLANA -> {
                Constant.COIN_TYPE_SOLANA
            }
            else -> {
                Constant.COIN_TYPE_BITCOIN
            }
        }
        lifecycleScope.launch(Dispatchers.IO) {
            if (walletList.isEmpty()) return@launch
            val curWallet = walletList[0]

            val encryptedMnemonic = EncryptUtil.encrypt(mMnemonic!!)
            val mnemonic = Mnemonic(
                curWallet.mnemonicId,
                encryptedMnemonic,
                mCoinType,
                WalletHelper.nameMnemonic()
            )
            AppDatabase.getInstance(Utils.getApp()).mnemonicDao().insertIfNotExists(mnemonic)
            val nameList = WalletHelper.nameWallet(walletList.size)
            walletList.forEachIndexed { index, wallet ->
                wallet.alias = nameList[index]
                WalletHelper.saveWallet(wallet)
            }

            WalletHelper.switchWallet(curWallet)

            withContext(Dispatchers.Main) {
                if (reBoot) ActivityUtils.getActivityList().forEach { it?.finish() }
                SuccessNewActivity.startActivity(this@SelectMnemonicWalletActivity, TYPE_IMPORT)
            }
        }
    }

    companion object {
        const val KEY_MNEMONIC = "key_mnemonic"
        const val KEY_PATH = "key_path"
        const val KEY_PATH_TYPE = "key_path_type"

        fun start(context: Activity, mnemonic: String, path: String,  @PathType pathType: Int) {
            context.startActivity<SelectMnemonicWalletActivity>(
                KEY_MNEMONIC to mnemonic,
                KEY_PATH to path,
                KEY_PATH_TYPE to pathType,
            )
        }
    }
}