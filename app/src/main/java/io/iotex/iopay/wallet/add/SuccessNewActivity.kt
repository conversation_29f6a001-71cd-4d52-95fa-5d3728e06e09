package io.iotex.iopay.wallet.add

import android.content.Context
import android.content.Intent
import com.blankj.utilcode.util.SPUtils
import io.iotex.iopay.MainActivity
import io.iotex.iopay.R
import io.iotex.iopay.base.BaseBindToolbarActivity
import io.iotex.iopay.databinding.ActivitySuccessNewBinding
import io.iotex.iopay.network.FirstNetworkSwitchActivity
import io.iotex.iopay.support.eventbus.SwitchWalletEvent
import io.iotex.iopay.util.Constant
import io.iotex.iopay.util.FireBaseUtil
import io.iotex.iopay.util.SPConstant
import io.iotex.iopay.viewmodel.wallet.SuccessNewViewModel
import org.greenrobot.eventbus.EventBus

class SuccessNewActivity :
    BaseBindToolbarActivity<SuccessNewViewModel, ActivitySuccessNewBinding>(R.layout.activity_success_new) {

    private val type by lazy {
        intent.getStringExtra("type")
    }

    override fun initView() {
        hideToolbarBack()

        when (type) {
            TYPE_CREATE -> {
                setToolbarTitle(getString(R.string.wallet_create_toolbar_title))
                mBinding.tvMessage.text = getString(R.string.created_wallet_success)
                Constant.currentWallet?.address?.let {
                    mViewModel.newWalletClaim(it)
                }
            }
            TYPE_IMPORT -> {
                setToolbarTitle(getString(R.string.wallet_import_toolbar_title))
                mBinding.tvMessage.text = getString(R.string.import_wallet_success)
            }
            TYPE_WATCH -> {
                setToolbarTitle(getString(R.string.wallet_import_toolbar_title))
                mBinding.tvMessage.text = getString(R.string.import_watch_address_success)
            }
        }

        mBinding.viewWallet.setOnClickListener {
            if (SPUtils.getInstance().getBoolean(SPConstant.SP_FIRST_SWITCH_NET, false)) {
                val newIntent =
                    Intent(this@SuccessNewActivity, FirstNetworkSwitchActivity::class.java)
                newIntent.flags = Intent.FLAG_ACTIVITY_CLEAR_TASK or Intent.FLAG_ACTIVITY_NEW_TASK
                startActivity(newIntent)
            } else {
                MainActivity.startActivity(this)
                EventBus.getDefault().post(SwitchWalletEvent())
            }
            FireBaseUtil.logFireBase("click_view_wallet")
            finish()
        }

        mViewModel.statusLiveData.observe(this) {
            FireBaseUtil.logFireBase("action_claim_iotx")
        }

    }

    override fun onBackPressed() {
        MainActivity.startActivity(this)
    }

    companion object {

        const val TYPE_CREATE = "type_create"
        const val TYPE_IMPORT = "type_import"
        const val TYPE_WATCH = "type_watch"
        fun startActivity(context: Context, type: String) {
            val intent = Intent(context, SuccessNewActivity::class.java)
            intent.putExtra("type", type)
            context.startActivity(intent)
        }
    }
}