package io.iotex.iopay.wallet.add

import android.os.Bundle
import android.widget.ScrollView
import com.blankj.utilcode.util.AppUtils
import com.blankj.utilcode.util.SPUtils
import com.blankj.utilcode.util.ScreenUtils
import io.iotex.base.BaseViewModel
import io.iotex.iopay.MainActivity
import io.iotex.iopay.R
import io.iotex.iopay.base.BaseBindToolbarActivity
import io.iotex.iopay.databinding.ActivityUserAgreementBinding
import io.iotex.iopay.util.FireBaseEvent
import io.iotex.iopay.util.FireBaseParam
import io.iotex.iopay.util.FireBaseUtil
import io.iotex.iopay.util.IoPayConstant
import io.iotex.iopay.util.extension.setGone
import io.iotex.iopay.util.extension.setVisible

class UserAgreementActivity :
    BaseBindToolbarActivity<BaseViewModel, ActivityUserAgreementBinding>(R.layout.activity_user_agreement) {

    override fun initView() {
        setToolbarTitle(getString(R.string.terms_of_use))
        hideToolbarBack()

        mBinding.agreementContent.text = getString(R.string.user_agreement_content) +
                "\n" + getString(R.string.background_location) +
                "\n" + getString(R.string.geo_will_upload_location_data_to_the_blockchain)

        mBinding.scrollView.setOnScrollChangeListener { _, _, scrollY, _, _ ->
            if (mBinding.btnNext.top < scrollY + ScreenUtils.getScreenHeight()) {
                mBinding.tvScroll.setGone()
            } else {
                mBinding.tvScroll.setVisible()
            }
        }
        mBinding.tvScroll.setOnClickListener {
            mBinding.scrollView.fullScroll(ScrollView.FOCUS_DOWN)
        }

        mBinding.checkbox.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked) {
                mBinding.btnNext.alpha = 1F
                mBinding.btnNext.isEnabled = true
            } else {
                mBinding.btnNext.alpha = 0.5F
                mBinding.btnNext.isEnabled = false
            }
        }
        mBinding.llCheck.setOnClickListener {
            mBinding.checkbox.isChecked = ! mBinding.checkbox.isChecked
            if (mBinding.checkbox.isChecked) {
                mBinding.btnNext.alpha = 1F
                mBinding.btnNext.isEnabled = true
            } else {
                mBinding.btnNext.alpha = 0.5F
                mBinding.btnNext.isEnabled = false
            }
        }
        mBinding.btnNext.alpha = 0.5F
        mBinding.btnNext.isEnabled = false
        mBinding.btnNext.setOnClickListener {
            SPUtils.getInstance().put(IoPayConstant.AGREEMENT, true)
            MainActivity.startActivity(this, isVisitor = true)
            finish()
        }
        val bundleEvent = Bundle()
        bundleEvent.putString(FireBaseParam.VERSION, AppUtils.getAppVersionName())
        FireBaseUtil.logFireBase(FireBaseEvent.ACTION_NEWER_INSTALL_START)
    }
}
