package io.iotex.iopay.wallet.add

import android.content.Context
import android.content.Intent
import io.iotex.base.BaseViewModel
import io.iotex.iopay.R
import io.iotex.iopay.base.BaseBindToolbarActivity
import io.iotex.iopay.databinding.ActivityWalletAddBinding
import io.iotex.iopay.util.FireBaseEvent
import io.iotex.iopay.util.FireBaseUtil
import io.iotex.iopay.util.PageEventUtil
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.util.extension.setVisible
import io.iotex.iopay.wallet.aawallet.AAWalletChooserActivity
import io.iotex.iopay.wallet.add.NameWalletActivity.Companion.CREATE_VIA_MNEMONIC
import io.iotex.iopay.wallet.add.WalletImportActivity.Companion.IMPORT_METHOD_ADDRESS
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.jetbrains.anko.startActivity

class WalletAddActivity : BaseBindToolbarActivity<BaseViewModel,ActivityWalletAddBinding>(R.layout.activity_wallet_add) {

    override fun initView() {
        setToolbarTitle(getString(R.string.add_wallet))
        mBinding.mSelectorCreateWallet.setOnClickListener{
            NameWalletActivity.start(this, CREATE_VIA_MNEMONIC)
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_START_CREATE_WALLET)
            PageEventUtil.logEvent(PageEventUtil.CREATEWALLET)
        }

        mBinding.clPriKeyWallet.setOnClickListener {
            PrivateKeyVerifyActivity.startActivity(this)
        }

        mBinding.mSelectorImportWallet.setOnClickListener{
            WalletImportMethodActivity.startActivity(this)
            FireBaseUtil.logFireBase(FireBaseEvent.CLICK_IMPORT_WALLET)
            PageEventUtil.logEvent(PageEventUtil.IMPORTWALLET)
        }
        mBinding.mSelectorWatchMode.setOnClickListener{
            WalletImportActivity.start(this, IMPORT_METHOD_ADDRESS)
            PageEventUtil.logEvent(PageEventUtil.WATCHMODEADDRESS)
        }
        mBinding.clAAWallet.setOnClickListener{
            startActivity<AAWalletChooserActivity>()
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_IMPORT_WALLET_AA)
            PageEventUtil.logEvent(PageEventUtil.ADDAAWALLET)
        }
        MainScope().launch {
            val isSupportedAAWallet = withContext(Dispatchers.IO) {
                WalletHelper.isAAWalletSupported()
            }
            if (isSupportedAAWallet) {
                mBinding.clAAWallet.setVisible()
            }
        }
    }

    companion object {
        fun startActivity(context: Context) {
            val intent = Intent(context, WalletAddActivity::class.java)
            context.startActivity(intent)
        }
    }

}