package io.iotex.iopay.wallet.add

import android.content.Context
import android.content.Intent
import androidx.lifecycle.lifecycleScope
import com.blankj.utilcode.util.Utils
import io.iotex.base.BaseViewModel
import io.iotex.iopay.R
import io.iotex.iopay.base.BaseBindToolbarActivity
import io.iotex.iopay.data.db.AppDatabase
import io.iotex.iopay.databinding.ActivityWalletAddMnemonicBinding
import io.iotex.iopay.util.extension.setGone
import io.iotex.iopay.wallet.add.NameWalletActivity.Companion.CREATE_VIA_MNEMONIC
import io.iotex.iopay.wallet.add.WalletImportActivity.Companion.IMPORT_METHOD_MNEMONIC
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class WalletAddMnemonicActivity :
    BaseBindToolbarActivity<BaseViewModel, ActivityWalletAddMnemonicBinding>(R.layout.activity_wallet_add_mnemonic) {

    override fun initView() {
        setToolbarTitle(getString(R.string.add_recovery_phrase))
        mBinding.mSelectorCreateWallet.setOnClickListener {
            NameWalletActivity.start(this, CREATE_VIA_MNEMONIC)
        }
        mBinding.mSelectorImportWallet.setOnClickListener {
            WalletImportActivity.start(this, IMPORT_METHOD_MNEMONIC)
        }
        lifecycleScope.launch {
            val count = withContext(Dispatchers.IO) {
                AppDatabase.getInstance(Utils.getApp()).mnemonicDao().count()
            }
            if (count > 0) {
                mBinding.mSelectorCreateWallet.setGone()
            }
        }
    }

    companion object{
        fun startActivity(context: Context){
            context.startActivity(Intent(context,WalletAddMnemonicActivity::class.java))
        }
    }

}