package io.iotex.iopay.wallet.add

import android.content.Context
import android.content.Intent
import androidx.lifecycle.lifecycleScope
import io.iotex.base.BaseViewModel
import io.iotex.iopay.R
import io.iotex.iopay.base.BaseBindToolbarActivity
import io.iotex.iopay.databinding.ActivityWalletAddPrivateKeyBinding
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.util.extension.setVisible
import io.iotex.iopay.wallet.aawallet.AAWalletChooserActivity
import io.iotex.iopay.wallet.add.WalletImportActivity.Companion.IMPORT_METHOD_ADDRESS
import io.iotex.iopay.wallet.add.WalletImportActivity.Companion.IMPORT_METHOD_KEYSTORE
import io.iotex.iopay.wallet.add.WalletImportActivity.Companion.IMPORT_METHOD_PRIVATE_KEY
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.jetbrains.anko.startActivity

class WalletAddPrivateKeyActivity :
    BaseBindToolbarActivity<BaseViewModel, ActivityWalletAddPrivateKeyBinding>(R.layout.activity_wallet_add_private_key) {
    override fun initView() {
        setToolbarTitle(getString(R.string.add_wallet))
        mBinding.clPriKeyWallet.setOnClickListener {
            PrivateKeyVerifyActivity.startActivity(this)
        }
        mBinding.mSelectorPrivateKey.setOnClickListener {
            WalletImportActivity.start(this, IMPORT_METHOD_PRIVATE_KEY)
        }
        mBinding.mSelectorKeystore.setOnClickListener {
            WalletImportActivity.start(this, IMPORT_METHOD_KEYSTORE)
        }
        mBinding.mSelectorWatchMode.setOnClickListener {
            WalletImportActivity.start(this, IMPORT_METHOD_ADDRESS)
        }
        mBinding.clAAWallet.setOnClickListener {
            startActivity<AAWalletChooserActivity>()
        }

        lifecycleScope.launch {
            val isSupportedAAWallet = withContext(Dispatchers.IO) {
                WalletHelper.isAAWalletSupported()
            }
            if (isSupportedAAWallet) {
                mBinding.clAAWallet.setVisible()
            }
        }
    }

    companion object{
        fun startActivity(context: Context){
            context.startActivity(Intent(context,WalletAddPrivateKeyActivity::class.java))
        }
    }

}