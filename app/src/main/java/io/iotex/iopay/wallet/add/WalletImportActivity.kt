package io.iotex.iopay.wallet.add

import android.app.Activity
import android.os.Bundle
import android.text.method.HideReturnsTransformationMethod
import android.view.WindowManager
import androidx.annotation.IntDef
import androidx.core.widget.addTextChangedListener
import androidx.lifecycle.lifecycleScope
import com.blankj.utilcode.util.KeyboardUtils
import com.blankj.utilcode.util.Utils
import com.solana.core.HotAccount
import io.iotex.base.BaseViewModel
import io.iotex.iopay.R
import io.iotex.iopay.base.BaseBindToolbarActivity
import io.iotex.iopay.data.db.AppDatabase
import io.iotex.iopay.databinding.ActivityWalletImportBinding
import io.iotex.iopay.ui.widget.AsteriskTransformationMethod
import io.iotex.iopay.util.EncryptUtil
import io.iotex.iopay.util.FireBaseEvent
import io.iotex.iopay.util.FireBaseUtil
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.util.extension.setGone
import io.iotex.iopay.util.extension.setInvisible
import io.iotex.iopay.util.extension.setVisible
import io.iotex.iopay.util.extension.toast
import io.iotex.iopay.wallet.add.NameWalletActivity.Companion.IMPORT_VIA_ADDRESS
import io.iotex.iopay.wallet.add.NameWalletActivity.Companion.IMPORT_VIA_PRIVATE_KEY
import io.iotex.iopay.wallet.add.WalletImportActivity.Companion.IMPORT_METHOD_ADDRESS
import io.iotex.iopay.wallet.add.WalletImportActivity.Companion.IMPORT_METHOD_KEYSTORE
import io.iotex.iopay.wallet.add.WalletImportActivity.Companion.IMPORT_METHOD_MNEMONIC
import io.iotex.iopay.wallet.add.WalletImportActivity.Companion.IMPORT_METHOD_PRIVATE_KEY
import io.iotex.iopay.wallet.bitcoin.BitcoinHelper
import io.iotex.iopay.wallet.dialog.ImportWatchDialog
import io.iotex.iopay.wallet.qrcode.IoScanQRCodeActivity
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.bitcoinj.core.Base58
import org.jetbrains.anko.startActivity
import org.json.JSONException
import org.json.JSONObject
import org.web3j.utils.Numeric

class WalletImportActivity : BaseBindToolbarActivity<BaseViewModel,ActivityWalletImportBinding>(R.layout.activity_wallet_import) {

    private val mImportMethod by lazy {
        intent.getIntExtra(KEY_IMPORT_WALLET_METHOD, IMPORT_METHOD_PRIVATE_KEY)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        window.addFlags(WindowManager.LayoutParams.FLAG_SECURE)
        super.onCreate(savedInstanceState)
    }

    override fun initView() {
        setToolbarTitle(getString(R.string.wallet_import_toolbar_title))

        mBinding.mEtInput.transformationMethod = AsteriskTransformationMethod()
        mBinding.ivKeyEye.setOnClickListener {
            if (mBinding.mEtInput.transformationMethod == HideReturnsTransformationMethod.getInstance()) {
                mBinding.ivKeyEye.setImageResource(R.drawable.icon_eye_close_white)
                mBinding.mEtInput.transformationMethod = AsteriskTransformationMethod()
            } else {
                mBinding.ivKeyEye.setImageResource(R.drawable.icon_eye_open_white)
                mBinding.mEtInput.transformationMethod =
                    HideReturnsTransformationMethod.getInstance()
            }
            mBinding.mEtInput.setSelection(mBinding.mEtInput.text.toString().length)
            if (mImportMethod == IMPORT_METHOD_PRIVATE_KEY) FireBaseUtil.logFireBase(FireBaseEvent.ACTION_ADD_WALLET_IMPORT_PRIVATE_KEY_VISIBLE_ICON)
            if (mImportMethod == IMPORT_METHOD_MNEMONIC) FireBaseUtil.logFireBase(FireBaseEvent.ACTION_ADD_WALLET_IMPORT_RECOVERY_PHRASE_VISIBLE_ICON)
        }

        mBinding.mEtPassword.transformationMethod = AsteriskTransformationMethod()
        mBinding.ivPasswordEye.setOnClickListener {
            if (mBinding.mEtPassword.transformationMethod == HideReturnsTransformationMethod.getInstance()) {
                mBinding.ivPasswordEye.setImageResource(R.drawable.icon_eye_close_white)
                mBinding.mEtPassword.transformationMethod = AsteriskTransformationMethod()
            } else {
                mBinding.ivPasswordEye.setImageResource(R.drawable.icon_eye_open_white)
                mBinding.mEtPassword.transformationMethod =
                    HideReturnsTransformationMethod.getInstance()
            }
            mBinding.mEtPassword.setSelection(mBinding.mEtPassword.text.toString().length)
        }

        mBinding.tvNext.alpha = 0.5f
        mBinding.mEtInput.addTextChangedListener {
            if (!it.isNullOrBlank()) {
                mBinding.mEtInput.alpha = 1f
                mBinding.tvNext.alpha = 1f
            } else {
                mBinding.mEtInput.alpha = 0.5f
                mBinding.tvNext.alpha = 0.5f
            }
        }
    }

    override fun initData() {
        when (mImportMethod) {
            IMPORT_METHOD_PRIVATE_KEY -> {
                mBinding.tvImportMethod.text = getString(R.string.private_key)
                mBinding.mEtInput.hint = getString(R.string.input_private_key)
                mBinding.tvDesc.text = getString(R.string.iopay_now_supports_evm_solana_and_btc_private_key)
            }
            IMPORT_METHOD_MNEMONIC -> {
                mBinding.tvImportMethod.text = getString(R.string.recovery_phrase)
                mBinding.mEtInput.hint = getString(R.string.input_mnemonic)
                mBinding.tvDesc.text = getString(R.string.iopay_now_supports_evm_solana_and_btc_recovery_phrase)
            }
            IMPORT_METHOD_KEYSTORE -> {
                mBinding.tvImportMethod.text = getString(R.string.key_store)
                mBinding.mLlPasswordContainer.setVisible()
                mBinding.tvDesc.setInvisible()
                mBinding.mEtInput.hint = getString(R.string.input_keystore_json)
            }
            IMPORT_METHOD_ADDRESS -> {
                mBinding.tvImportMethod.text = getString(R.string.wallet_address_only)
                mBinding.mEtInput.hint = getString(R.string.input_address)
                mBinding.tvDesc.text = getString(R.string.the_watch_mode_not_support_switch_between_evm_solana_and_btc_networks)
                mBinding.ivKeyEye.setGone()
                mBinding.ivKeyEye.performClick()
            }
        }
        mBinding.tvNext.setOnClickListener {
            val input = mBinding.mEtInput.text.toString()
            KeyboardUtils.hideSoftInput(this)
            when (mImportMethod) {
                IMPORT_METHOD_PRIVATE_KEY -> validatePrivateKey(input)
                IMPORT_METHOD_MNEMONIC -> validateMnemonic(input)
                IMPORT_METHOD_KEYSTORE -> validateKeystore(input)
                IMPORT_METHOD_ADDRESS -> validateAddress(input)
            }
        }
        mBinding.ivScan.setOnClickListener {
            IoScanQRCodeActivity.startActivity(this){
                mBinding.mEtInput.setText(it)
            }
        }
        mBinding.mEtInput.postDelayed({
            KeyboardUtils.showSoftInput(mBinding.mEtInput)
        }, 600)
    }

    private fun validatePrivateKey(input: String) {
        lifecycleScope.launch {
            var privateKey = Numeric.cleanHexPrefix(input.trim())
            if (privateKey.isBlank()) {
                Utils.getApp().getString(R.string.validate_private_key).toast()
                return@launch
            }
            if (!WalletHelper.isValidPrivateKey(privateKey)) {
                Utils.getApp().getString(R.string.key_invalid).toast()
                return@launch
            }
            if (BitcoinHelper.isWifPrivateKey(privateKey)) {
                privateKey = BitcoinHelper.wifToHexPrivateKey(privateKey)
            }
            if (WalletHelper.isSolanaPrivatakey(privateKey)) {
                val wallet = withContext(Dispatchers.IO) {
                    val account = HotAccount(Base58.decode(privateKey))
                    val publicKeyBase58 = account.publicKey.toBase58()
                    AppDatabase.getInstance(Utils.getApp()).walletDao()
                        .queryWalletByAddress(publicKeyBase58)
                }
                if (wallet != null && !wallet.isWatch) {
                    Utils.getApp().getString(R.string.validate_wallet_exist).toast()
                    return@launch
                }
            } else {
                val address = withContext(Dispatchers.IO) {
                    WalletHelper.getAddressByPk(privateKey)
                }
                val walletList = withContext(Dispatchers.IO) {
                    AppDatabase.getInstance(Utils.getApp()).walletDao()
                        .queryWalletByAddress(WalletHelper.convertWeb3Address(address.trim()))
                }
                if (walletList != null && !walletList.isWatch) {
                    Utils.getApp().getString(R.string.validate_wallet_exist).toast()
                    return@launch
                }
            }

            NameWalletActivity.start(this@WalletImportActivity, IMPORT_VIA_PRIVATE_KEY, privateKey)
        }
    }

    private fun validateMnemonic(input: String) {
        lifecycleScope.launch {
            val mnemonicList = input.lowercase().trim()
                .replace(" ", " ")
                .split(" ").filter { it.isNotBlank() }
            if (mnemonicList.size < 12) {
                getString(R.string.mnemonic_invalid).toast()
                return@launch
            }
            val mnemonicStr = mnemonicList.joinToString(" ")
            val isValid = withContext(Dispatchers.IO) {
                WalletHelper.verifyMnemonicPhrase(mnemonicStr)
            }
            if (!isValid) {
                getString(R.string.mnemonic_invalid).toast()
                return@launch
            }

            val target = withContext(Dispatchers.IO) {
                val mnemonicEntryList =
                    AppDatabase.getInstance(Utils.getApp()).mnemonicDao().queryAll()
                mnemonicEntryList.firstOrNull {
                    val mnemonic = EncryptUtil.decrypt(it.encryptedMnemonic)
                    mnemonic.equals(mnemonicStr, true)
                }
            }

            if (target != null) {
                getString(R.string.mnemonic_exist).toast()
                return@launch
            }
            KeyboardUtils.hideSoftInput(this@WalletImportActivity)
            SelectMnemonicPathActivity.start(this@WalletImportActivity, mnemonicStr)
        }
    }

    private fun validateKeystore(keystoreJson: String) {
        val errorHandler = CoroutineExceptionHandler { _, e ->
            hideLoading()
            e.message?.toast()
        }
        lifecycleScope.launch(errorHandler) {
            val password = mBinding.mEtPassword.text.toString()
            if (keystoreJson.isBlank()) {
                Utils.getApp().getString(R.string.validate_keystore_json).toast()
                return@launch
            }
            if (password.isBlank()) {
                Utils.getApp().getString(R.string.validate_password).toast()
                return@launch
            }
            if (!isValidKeystoreJson(keystoreJson)) {
                Utils.getApp().getString(R.string.keystore_json_invalid).toast()
                return@launch
            }
            showLoading()
            val privateKey = withContext(Dispatchers.IO) {
                WalletHelper.getWalletPrivateKey(keystoreJson, password)
            }
            validatePrivateKey(privateKey)
            hideLoading()
        }
    }

    private fun isValidKeystoreJson(keystoreJson: String): Boolean {
        return try {
            val keystoreJsonObject = JSONObject(keystoreJson)
            keystoreJsonObject.has("version") &&
                    keystoreJsonObject.has("id") &&
                    keystoreJsonObject.has("address") &&
                    keystoreJsonObject.has("crypto")
        } catch (ex: JSONException) {
            false
        }
    }

    private fun validateAddress(input: String) {
        lifecycleScope.launch {
            if (!WalletHelper.isValidAddress(input.trim())) {
                Utils.getApp().getString(R.string.address_invalid).toast()
                return@launch
            }
            val walletList = withContext(Dispatchers.IO) {
                val address = if (WalletHelper.isBitcoinAddress(input)) {
                    input.trim()
                } else if (WalletHelper.isSolanaAddress(input)) {
                    input.trim()
                } else {
                    WalletHelper.convertWeb3Address(input.trim())
                }
                AppDatabase.getInstance(Utils.getApp()).walletDao()
                    .queryWalletByAddress(address)
            }
            if (walletList != null) {
                Utils.getApp().getString(R.string.validate_address_exist).toast()
                return@launch
            }

            ImportWatchDialog().apply {
                onConfirmClick = {
                    NameWalletActivity.start(this@WalletImportActivity, IMPORT_VIA_ADDRESS, input)
                }
            }.show(supportFragmentManager,System.currentTimeMillis().toString())
        }
    }

    companion object {
        const val KEY_IMPORT_WALLET_METHOD = "key_import_wallet_method"

        const val IMPORT_METHOD_PRIVATE_KEY = 1
        const val IMPORT_METHOD_MNEMONIC = 2
        const val IMPORT_METHOD_KEYSTORE = 4
        const val IMPORT_METHOD_ADDRESS = 5

        fun start(context: Activity, @ImportWalletMethod method: Int) {
            context.startActivity<WalletImportActivity>(
                KEY_IMPORT_WALLET_METHOD to method
            )
        }

    }
}

@Target(AnnotationTarget.VALUE_PARAMETER, AnnotationTarget.FIELD, AnnotationTarget.FUNCTION)
@MustBeDocumented
@IntDef(
    IMPORT_METHOD_PRIVATE_KEY,
    IMPORT_METHOD_MNEMONIC,
    IMPORT_METHOD_KEYSTORE,
    IMPORT_METHOD_ADDRESS
)
@Retention(AnnotationRetention.SOURCE)
annotation
class ImportWalletMethod