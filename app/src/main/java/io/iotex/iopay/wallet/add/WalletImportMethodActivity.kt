package io.iotex.iopay.wallet.add

import android.content.Context
import android.content.Intent
import android.view.View
import io.iotex.base.BaseViewModel
import io.iotex.iopay.R
import io.iotex.iopay.base.BaseBindToolbarActivity
import io.iotex.iopay.databinding.ActivityWalletImportMethodBinding
import io.iotex.iopay.util.FireBaseEvent
import io.iotex.iopay.util.FireBaseUtil
import io.iotex.iopay.util.PageEventUtil
import io.iotex.iopay.wallet.add.WalletImportActivity.Companion.IMPORT_METHOD_KEYSTORE
import io.iotex.iopay.wallet.add.WalletImportActivity.Companion.IMPORT_METHOD_MNEMONIC
import io.iotex.iopay.wallet.add.WalletImportActivity.Companion.IMPORT_METHOD_PRIVATE_KEY

class WalletImportMethodActivity : BaseBindToolbarActivity<BaseViewModel,ActivityWalletImportMethodBinding>(R.layout.activity_wallet_import_method), View.OnClickListener {

    override fun initView() {
        setToolbarTitle(getString(R.string.import_wallet_button))
        mBinding.mSelectorPrivateKey.setOnClickListener(this)
        mBinding.mSelectorMnemonic.setOnClickListener(this)
        mBinding.mSelectorKeystore.setOnClickListener(this)
    }

    override fun onClick(v: View?) {
        when(v?.id) {
            R.id.mSelectorPrivateKey -> {
                WalletImportActivity.start(this, IMPORT_METHOD_PRIVATE_KEY)
                PageEventUtil.logEvent(PageEventUtil.IMPORTPRIVATEKEY)
            }
            R.id.mSelectorMnemonic -> {
                WalletImportActivity.start(this, IMPORT_METHOD_MNEMONIC)
                PageEventUtil.logEvent(PageEventUtil.IMPORTRECOVERY)
                FireBaseUtil.logFireBase(FireBaseEvent.ACTION_START_IMPORT_RECOVERY)
            }
            R.id.mSelectorKeystore -> {
                WalletImportActivity.start(this, IMPORT_METHOD_KEYSTORE)
                PageEventUtil.logEvent(PageEventUtil.IMPORTKEYSTORE)
            }
        }
    }

    companion object {
        fun startActivity(context: Context) {
            context.startActivity(Intent(context, WalletImportMethodActivity::class.java))
        }
    }

}