package io.iotex.iopay.wallet.bitcoin

import android.view.Gravity
import android.view.WindowManager
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.drakeet.multitype.MultiTypeAdapter
import io.iotex.base.bindbase.BaseBindDialog
import io.iotex.iopay.R
import io.iotex.iopay.data.db.BitcoinWallet
import io.iotex.iopay.databinding.DialogBitcoinAddressBinding
import io.iotex.iopay.util.Constant
import io.iotex.iopay.viewmodel.wallet.BitcoinViewModel
import kotlinx.coroutines.launch

class BitcoinAddressDialog(private val checkType:Int) :
    BaseBindDialog<BitcoinViewModel, DialogBitcoinAddressBinding>(R.layout.dialog_bitcoin_address) {

    var onCheckAddress:((BitcoinWallet)->Unit)? = null

    private val mAdapter = MultiTypeAdapter()
    override fun initView() {
        mAdapter.register(BitcoinWallet::class.java, BitcoinAddressBinder(checkType).apply {
            onItemClick = {
                actionConfirm(it)
            }
        })
        mBinding.recyclerView.layoutManager = LinearLayoutManager(context)
        mBinding.recyclerView.adapter = mAdapter
        val bitcoinWallets = Constant.currentWallet?.bitcoinWallets ?: return
        mAdapter.items = bitcoinWallets
        mAdapter.notifyDataSetChanged()
    }

    private fun actionConfirm(bitcoinWallet: BitcoinWallet) {
        lifecycleScope.launch {
            mViewModel.checkBitcoinAddressType(bitcoinWallet.addressType)
            onCheckAddress?.invoke(bitcoinWallet)
            dismiss()
        }
    }

    override fun onStart() {
        super.onStart()
        val attributes = dialog?.window?.attributes
        attributes?.width = WindowManager.LayoutParams.MATCH_PARENT
        attributes?.gravity = Gravity.BOTTOM
        dialog?.window?.attributes = attributes
    }
}