package io.iotex.iopay.wallet.bitcoin

import android.view.LayoutInflater
import android.view.ViewGroup
import com.blankj.utilcode.util.ClipboardUtils
import com.blankj.utilcode.util.ToastUtils
import com.blankj.utilcode.util.Utils
import com.drakeet.multitype.ItemViewBinder
import com.machinefi.w3bstream.utils.extension.ellipsis
import io.iotex.base.bindbase.BaseBindVH
import io.iotex.iopay.R
import io.iotex.iopay.data.db.AppDatabase
import io.iotex.iopay.data.db.BitcoinWallet
import io.iotex.iopay.databinding.ItemBitcoinAddressBinding
import io.iotex.iopay.repo.BitcoinRepo
import io.iotex.iopay.util.TokenUtil
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.util.extension.fromSatoshis
import io.iotex.iopay.util.extension.loadSvgOrImage
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.math.BigInteger

class BitcoinBookAddressBinder :
    ItemViewBinder<BitcoinWallet, BaseBindVH<ItemBitcoinAddressBinding>>() {
    private val bitcoinRepo = BitcoinRepo()
    var onItemClick: ((BitcoinWallet) -> Unit)? = null
    override fun onBindViewHolder(
        holder: BaseBindVH<ItemBitcoinAddressBinding>,
        item: BitcoinWallet
    ) {
        holder.bind.ivLogo.loadSvgOrImage(
            WalletHelper.getAddressAvatar(item.bitcoinAddress),
            R.drawable.icon_bitcoin_logo
        )
        holder.bind.tvTitle.text = item.typeName.split("(")[0]
        holder.bind.tvAddress.text = item.bitcoinAddress.lowercase().ellipsis()
        holder.bind.ivCopy.setOnClickListener {
            ClipboardUtils.copyText(item.bitcoinAddress)
            ToastUtils.showShort(R.string.copy_success)
        }
        holder.bind.llContent.setOnClickListener {
            onItemClick?.invoke(item)
        }
        getWalletBalance(item, holder.bind)
    }

    private fun getWalletBalance(bitcoinWallet: BitcoinWallet, bind: ItemBitcoinAddressBinding) {
        MainScope().launch(Dispatchers.IO) {
            val walletCache = AppDatabase.getInstance(Utils.getApp()).walletCacheDao()
                .queryWalletCache(bitcoinWallet.bitcoinAddress, WalletHelper.getCurChainId())
            val balance = if (walletCache != null) {
                walletCache.balance.toBigIntegerOrNull() ?: BigInteger.ZERO
            } else {
                bitcoinRepo.getBitcoinBalance(bitcoinWallet.bitcoinAddress) ?: BigInteger.ZERO
            }
            withContext(Dispatchers.Main) {
                bind.tvBalance.text =
                    "${balance.fromSatoshis()} ${TokenUtil.getNativeCurrencySymbol()}"
            }
        }
    }

    override fun onCreateViewHolder(
        inflater: LayoutInflater,
        parent: ViewGroup
    ): BaseBindVH<ItemBitcoinAddressBinding> {
        val bind = ItemBitcoinAddressBinding.inflate(inflater, parent, false)
        return BaseBindVH(bind)
    }
}