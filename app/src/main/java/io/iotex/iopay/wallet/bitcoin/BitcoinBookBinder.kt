package io.iotex.iopay.wallet.bitcoin

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.LinearLayoutManager
import com.blankj.utilcode.util.Utils
import com.drakeet.multitype.ItemViewBinder
import com.drakeet.multitype.MultiTypeAdapter
import io.iotex.base.bindbase.BaseBindVH
import io.iotex.iopay.data.db.AppDatabase
import io.iotex.iopay.data.db.BitcoinWallet
import io.iotex.iopay.data.db.Wallet
import io.iotex.iopay.databinding.ItemBitcoinBookAddressBinding
import io.iotex.iopay.repo.BitcoinRepo
import io.iotex.iopay.util.TokenUtil
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.util.extension.fromSatoshis
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.math.BigInteger

class BitcoinBookBinder :
    ItemViewBinder<Wallet, BaseBindVH<ItemBitcoinBookAddressBinding>>() {

    private val bitcoinRepo = BitcoinRepo()
    var onSelectWallet: ((wallet: Wallet, bitcoinWallet: BitcoinWallet) -> Unit)? = null

    override fun onBindViewHolder(
        holder: BaseBindVH<ItemBitcoinBookAddressBinding>,
        item: Wallet
    ) {
        val bind = holder.bind
        bind.tvName.text = item.alias
        bind.recyclerView.layoutManager = LinearLayoutManager(bind.recyclerView.context)
        val mAdapter = MultiTypeAdapter()
        mAdapter.register(BitcoinWallet::class.java, BitcoinBookAddressBinder().apply {
            onItemClick = {
                item.addressType = it.addressType
                onSelectWallet?.invoke(item, it)
            }
        })
        mAdapter.items = item.bitcoinWallets
        bind.recyclerView.adapter = mAdapter
        getAllWalletBalance(item, bind)
    }

    private fun getAllWalletBalance(wallet: Wallet, bind: ItemBitcoinBookAddressBinding) {
        MainScope().launch {
            val totalBalance = withContext(Dispatchers.IO) {
                val balanceList = wallet.bitcoinWallets.map {
                    val walletCache = AppDatabase.getInstance(Utils.getApp()).walletCacheDao()
                        .queryWalletCache(it.bitcoinAddress, WalletHelper.getCurChainId())
                    if (walletCache != null) {
                        walletCache.balance.toBigIntegerOrNull() ?: BigInteger.ZERO
                    } else {
                        bitcoinRepo.getBitcoinBalance(it.bitcoinAddress) ?: BigInteger.ZERO
                    }
                }
                balanceList.sumOf { it }
            }
            bind.tvBalance.text = "${totalBalance.fromSatoshis()} ${TokenUtil.getNativeCurrencySymbol()}"
        }
    }

    override fun onCreateViewHolder(
        inflater: LayoutInflater,
        parent: ViewGroup
    ): BaseBindVH<ItemBitcoinBookAddressBinding> {
        val bind = ItemBitcoinBookAddressBinding.inflate(inflater, parent, false)
        return BaseBindVH(bind)
    }
}