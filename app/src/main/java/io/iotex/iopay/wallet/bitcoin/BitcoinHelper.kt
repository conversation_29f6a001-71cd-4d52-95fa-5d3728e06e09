package io.iotex.iopay.wallet.bitcoin

import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.Utils
import com.machinefi.lockscreen.LockAuthHelper
import io.iotex.iopay.R
import io.iotex.iopay.data.db.AddressType_Legacy
import io.iotex.iopay.data.db.AddressType_NativeSegwit
import io.iotex.iopay.data.db.AddressType_NestedSegwit
import io.iotex.iopay.data.db.AddressType_Taproot
import io.iotex.iopay.data.db.AppDatabase
import io.iotex.iopay.data.db.BitcoinWallet
import io.iotex.iopay.data.db.RPCNetwork
import io.iotex.iopay.data.db.Wallet
import io.iotex.iopay.util.Config
import io.iotex.iopay.util.Constant
import io.iotex.iopay.util.EncryptUtil
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.util.extension.cleanHexPrefix
import io.iotex.iopay.util.extension.toHexByteArray
import io.iotex.iopay.util.extension.toHexString
import io.iotex.iopay.util.extension.toHexStringNoPrefix
import io.iotex.iopay.util.extension.toast
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.bitcoinj.core.Address
import org.bitcoinj.core.BIP341
import org.bitcoinj.core.Base58
import org.bitcoinj.core.Bech32
import org.bitcoinj.core.Coin
import org.bitcoinj.core.ECKey
import org.bitcoinj.core.LegacyAddress
import org.bitcoinj.core.NestedSegwitAddress
import org.bitcoinj.core.NetworkParameters
import org.bitcoinj.core.SegwitAddress
import org.bitcoinj.core.Sha256Hash
import org.bitcoinj.core.TaprootAddress
import org.bitcoinj.core.TaprootTransaction
import org.bitcoinj.core.Transaction
import org.bitcoinj.core.TransactionInput
import org.bitcoinj.core.TransactionOutPoint
import org.bitcoinj.core.TransactionOutput
import org.bitcoinj.core.TransactionWitness
import org.bitcoinj.core.UTXO
import org.bitcoinj.params.MainNetParams
import org.bitcoinj.params.TestNet3Params
import org.bitcoinj.script.Script
import org.bitcoinj.script.Script.ScriptType
import org.bitcoinj.script.ScriptBuilder
import org.bitcoinj.script.ScriptOpCodes.OP_1
import org.bitcoinj.script.ScriptPattern
import org.bouncycastle.util.encoders.Hex
import org.psbt.PartialSig
import org.psbt.Psbt
import org.psbt.PsbtUtil
import org.psbt.UserToSignInput
import java.math.BigInteger
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException
import kotlin.coroutines.suspendCoroutine

object BitcoinHelper {

    fun getECKey(wallet: Wallet, path: String? = null, mnemonic: String? = null): ECKey {
        val mnemonicPhrase = if (mnemonic.isNullOrBlank()) {
            val mnemonicEntry = AppDatabase.getInstance(Utils.getApp()).mnemonicDao()
                .queryByMnemonicId(wallet.mnemonicId)
            if (mnemonicEntry != null) {
                EncryptUtil.decrypt(mnemonicEntry.encryptedMnemonic)
            } else null
        } else mnemonic

        val privateKey = if (!mnemonicPhrase.isNullOrBlank() && !path.isNullOrBlank()) {
            WalletHelper.resolvePrivateKeyFromMnemonic(mnemonicPhrase, path)
        } else {
            val account = WalletHelper.getAccountByWallet(wallet)
                ?: throw IllegalArgumentException("wallet not found")
            account.privateKey().toHexString().cleanHexPrefix()
        }
        return ECKey.fromPrivate(BigInteger(privateKey, 16))
    }

    suspend fun getECKeyWithAuth(): ECKey {
        return suspendCoroutine { continuation ->
            val wallet = WalletHelper.getCurWallet()
            val context = ActivityUtils.getTopActivity()
            if (wallet == null || context == null) {
                Utils.getApp().getString(R.string.error_get_account).toast()
                continuation.resumeWithException(
                    IllegalArgumentException(
                        Utils.getApp().getString(R.string.error_get_account)
                    )
                )
                return@suspendCoroutine
            }
            LockAuthHelper.showTransferAuth(
                context,
                onSuccess = {
                    MainScope().launch(Dispatchers.IO) {
                        val path = when (wallet.addressType) {
                            AddressType_Legacy -> Constant.MNEMONIC_PATH_LEGACY
                            AddressType_NativeSegwit -> Constant.MNEMONIC_PATH_NATIVE_SEGWIT
                            AddressType_NestedSegwit -> Constant.MNEMONIC_PATH_NESTED_SEGWIT
                            AddressType_Taproot -> Constant.MNEMONIC_PATH_TAPROOT
                            else -> Constant.MNEMONIC_PATH_NATIVE_SEGWIT
                        }
                        continuation.resume(getECKey(wallet, path))
                    }
                },
                onCancel = {
                    continuation.resumeWithException(
                        IllegalArgumentException(
                            Utils.getApp().getString(R.string.error_get_account)
                        )
                    )
                }
            )
        }
    }

    fun getLegacyWallet(
        wallet: Wallet,
        networkParams: NetworkParameters,
        path: String = Constant.MNEMONIC_PATH_LEGACY,
        mnemonic: String? = null
    ): BitcoinWallet {
        val convertedPath = convertPath(wallet, path)
        val ecKey = getECKey(wallet, convertedPath, mnemonic)
        val bitcoinAddress = LegacyAddress.fromKey(networkParams, ecKey).toBase58()
        return BitcoinWallet(
            bitcoinAddress,
            wallet.address,
            getChainIdFromNetworkParams(networkParams),
            AddressType_Legacy,
            getTypeNameByType(AddressType_Legacy),
            convertedPath
        )
    }

    fun getSegwitBech32Wallet(
        wallet: Wallet,
        networkParams: NetworkParameters,
        path: String = Constant.MNEMONIC_PATH_NATIVE_SEGWIT,
        mnemonic: String? = null
    ): BitcoinWallet {
        val convertedPath = convertPath(wallet, path)
        val ecKey = getECKey(wallet, convertedPath, mnemonic)
        val bitcoinAddress = SegwitAddress.fromKey(networkParams, ecKey).toBech32()
        return BitcoinWallet(
            bitcoinAddress,
            wallet.address,
            getChainIdFromNetworkParams(networkParams),
            AddressType_NativeSegwit,
            getTypeNameByType(AddressType_NativeSegwit),
            convertedPath
        )
    }

    fun getSegwitBase58Wallet(
        wallet: Wallet,
        networkParams: NetworkParameters,
        path: String = Constant.MNEMONIC_PATH_NESTED_SEGWIT,
        mnemonic: String? = null
    ): BitcoinWallet {
        val convertedPath = convertPath(wallet, path)
        val ecKey = getECKey(wallet, convertedPath, mnemonic)
        val bitcoinAddress = NestedSegwitAddress.fromKey(networkParams, ecKey).toBase58()
        return BitcoinWallet(
            bitcoinAddress,
            wallet.address,
            getChainIdFromNetworkParams(networkParams),
            AddressType_NestedSegwit,
            getTypeNameByType(AddressType_NestedSegwit),
            convertedPath
        )
    }

    fun getTaprootWallet(
        wallet: Wallet,
        networkParams: NetworkParameters,
        path: String = Constant.MNEMONIC_PATH_TAPROOT,
        mnemonic: String? = null
    ): BitcoinWallet {
        val convertedPath = convertPath(wallet, path)
        val ecKey = getECKey(wallet, convertedPath, mnemonic)
        val bitcoinAddress = TaprootAddress.fromKey(networkParams, ecKey).toBech32M()
        return BitcoinWallet(
            bitcoinAddress,
            wallet.address,
            getChainIdFromNetworkParams(networkParams),
            AddressType_Taproot,
            getTypeNameByType(AddressType_Taproot),
            convertedPath
        )
    }

    fun getTypeNameByType(type: Int): String {
        return when (type) {
            AddressType_Legacy -> "Legacy (P2PKH)"
            AddressType_NativeSegwit -> "Native Segwit (P2WPKH)"
            AddressType_NestedSegwit -> "Nested Segwit (P2SH-P2WPKH)"
            AddressType_Taproot -> "Taproot (P2TR)"
            else -> ""
        }
    }

    private fun convertPath(wallet: Wallet, defPath: String): String {
        return kotlin.runCatching {
            defPath.replaceRange(defPath.length - 1, defPath.length, wallet.addressIndex.toString())
        }.getOrNull() ?: defPath
    }

    @Throws
    fun resolveBitcoinAddress(
        address: String,
        networkParams: NetworkParameters = getBitcoinNetworkParams()
    ): Int {
        if (!address.startsWith("m") && !address.startsWith("n") &&
            !address.startsWith("2") && !address.startsWith("1") &&
            !address.startsWith("tb1") && !address.startsWith("bc1") &&
            !address.startsWith("3")
        ) {
            throw IllegalStateException("Invalid Bitcoin address")
        }
        kotlin.runCatching {
            val versionAndDataBytes = Base58.decodeChecked(address)
            val version = versionAndDataBytes[0].toInt() and 0xFF
            if (version == networkParams.addressHeader) {
                return AddressType_Legacy
            } else if (version == networkParams.p2SHHeader) {
                return AddressType_NestedSegwit
            }
        }.onFailure {
            val bechData = Bech32.decode(address)
            if (bechData.encoding == Bech32.Encoding.BECH32) {
                return AddressType_NativeSegwit
            } else if (bechData.encoding == Bech32.Encoding.BECH32M) {
                return AddressType_Taproot
            }
        }
        throw IllegalStateException("Invalid Bitcoin address")
    }

    fun isCurNetBitcoinAddress(address: String, curChainId: Int = WalletHelper.getCurChainId()): Boolean {
        return kotlin.runCatching {
            resolveBitcoinAddress(address)
            val chainId = if (address.startsWith("1") || address.startsWith("3")
                || address.startsWith("bc1")) {
                Config.BITCOIN_MAIN_CHAIN_ID
            } else if (address.startsWith("tb1") || address.startsWith("m")
                || address.startsWith("n") || address.startsWith("2")){
                Config.BITCOIN_TEST_CHAIN_ID
            } else {
                throw IllegalStateException("Invalid Bitcoin address")
            }
            chainId == curChainId
        }.getOrNull() ?: false
    }

    fun resolveBitcoinAddress(script: Script): String? {
        val params = getBitcoinNetworkParams()
        return if (ScriptPattern.isP2PKH(script)) {
            LegacyAddress.fromPubKeyHash(params, ScriptPattern.extractHashFromP2PKH(script)).toBase58()
        } else if (ScriptPattern.isP2WH(script)) {
            SegwitAddress.fromHash(params, ScriptPattern.extractHashFromP2WH(script)).toBech32()
        } else if (ScriptPattern.isP2SH(script)) {
            NestedSegwitAddress.fromHash(params, ScriptPattern.extractHashFromP2SH(script)).toBase58()
        } else if (ScriptPattern.isP2TR(script)) {
            TaprootAddress.fromHash(params, ScriptPattern.extractOutputKeyFromP2TR(script)).toBech32M()
        } else null
    }

    fun resolveAddressType(address: String): String {
        return when (resolveBitcoinAddress(address)) {
            AddressType_Legacy -> "Legacy"
            AddressType_NativeSegwit -> "Native SegWit"
            AddressType_NestedSegwit -> "Nested SegWit"
            AddressType_Taproot -> "Taproot"
            else -> throw IllegalStateException("Invalid Bitcoin address")
        }
    }

    fun packAddress(address: String): Address? {
        val networkParams = getBitcoinNetworkParams()
        return kotlin.runCatching {
            when (resolveBitcoinAddress(address, getBitcoinNetworkParams())) {
                AddressType_Legacy -> LegacyAddress.fromBase58(networkParams, address);
                AddressType_NativeSegwit -> SegwitAddress.fromBech32(networkParams, address)
                AddressType_NestedSegwit -> NestedSegwitAddress.fromBase58(networkParams, address)
                AddressType_Taproot -> TaprootAddress.fromBech32(networkParams, address)
                else -> null
            }
        }.getOrNull()
    }

    suspend fun buildTransaction(
        from: Address,
        to: Address,
        value: BigInteger,
        utxoList: List<UTXO>,
        fee: BigInteger
    ): Transaction? {
        val usedUtxoList = confirmUsedUtxoList(from, to, value, utxoList, fee)
        if (usedUtxoList.isEmpty()) return null
        val transaction = if (from is TaprootAddress) {
            TaprootTransaction(getBitcoinNetworkParams()).apply { setVersion(2) }
        } else {
            Transaction(getBitcoinNetworkParams()).apply { setVersion(2) }
        }
        usedUtxoList.forEach {
            val input = TransactionInput(
                getBitcoinNetworkParams(),
                transaction,
                Script(Hex.decode("")).program,
                TransactionOutPoint(getBitcoinNetworkParams(), it.index, it.hash),
                Coin.valueOf(it.value.value)
            )
            transaction.addInput(input)
        }

        val coin = Coin.valueOf(value.toLong())
        transaction.addOutput(
            TransactionOutput(
                getBitcoinNetworkParams(),
                transaction,
                coin,
                lockingScript(to).program
            )
        )
        val totalValue = usedUtxoList.sumOf { it.value.value }.toBigInteger()
        val gasFee = estimateGasFee(transaction.inputs.size, transaction.outputs, fee)
        val charge = totalValue - value - gasFee
        transaction.addOutput(
            TransactionOutput(
                getBitcoinNetworkParams(),
                transaction,
                Coin.valueOf(charge.toLong()),
                lockingScript(from).program
            )
        )
        return transaction
    }

    private fun lockingScript(address: Address): Script {
        return when (address) {
            is LegacyAddress -> {
                ScriptBuilder.createP2PKHOutputScript(address.getHash())
            }

            is SegwitAddress -> {
                ScriptBuilder().apply {
                    smallNum(address.witnessVersion)
                    data(address.witnessProgram)
                }.build()
            }

            is NestedSegwitAddress -> {
                address.lockingScript()
            }

            is TaprootAddress -> {
                address.lockingScript()
            }

            else -> throw java.lang.IllegalStateException("Cannot handle locking script for $address")
        }
    }

    suspend fun signLegacyTransaction(transaction: Transaction, signHashList: List<Transaction.SigHash>? = null): Transaction? {
        return kotlin.runCatching {
            val ecKey = getECKeyWithAuth()
            val originInputs = transaction.inputs.toList()
            transaction.clearInputs()
            originInputs.forEachIndexed { index, input ->
                val outPoint =
                    TransactionOutPoint(getBitcoinNetworkParams(), input.outpoint.index, input.outpoint.hash)
                transaction.addSignedInput(
                    outPoint,
                    ScriptBuilder.createP2PKHOutputScript(ecKey),
                    ecKey,
                    signHashList?.get(index) ?: Transaction.SigHash.ALL,
                    true
                )
            }
            return@runCatching transaction
        }.onFailure {
            it.printStackTrace()
        }
            .getOrNull()
    }

    suspend fun signSegWitTransaction(transaction: Transaction, isP2SH: Boolean, signHashList: List<Transaction.SigHash>? = null): Transaction? {
        return kotlin.runCatching {
            val ecKey = getECKeyWithAuth()
            transaction.inputs.forEachIndexed { index, input ->
                val txSignature = transaction.calculateWitnessSignature(
                    input.index,
                    ecKey,
                    ScriptBuilder.createP2PKHOutputScript(ecKey),
                    input.value,
                    signHashList?.get(index) ?: Transaction.SigHash.ALL,
                    false
                )
                input.witness = TransactionWitness.redeemP2WPKH(txSignature, ecKey)
                if (isP2SH) {
                    input.scriptSig = ScriptBuilder().data(ScriptBuilder.createP2WPKHOutputScript(ecKey).program).build()
                }
            }
            return@runCatching transaction
        }.onFailure {
            it.printStackTrace()
        }.getOrNull()
    }

    suspend fun signTaprootTransaction(transaction: Transaction, signHashList: List<Transaction.SigHash>? = null): Transaction? {
        return kotlin.runCatching {
            if (transaction !is TaprootTransaction) return null
            val ecKey = getECKeyWithAuth()
            transaction.inputs.forEachIndexed { index, input ->
                val txSignature = transaction.calculateTaprootSignature(
                    input.index,
                    ecKey,
                    ScriptBuilder().apply {
                        op(OP_1)
                        data(BIP341.tweakKey(ecKey.pubKey, null))
                    }.build(),
                    input.value,
                    signHashList?.get(index) ?: Transaction.SigHash.ALL,
                    false,
                    null,
                    null
                )
                input.witness = TransactionWitness(1).apply {
                    setPush(0, txSignature)
                }
            }
            return@runCatching transaction
        }.onFailure {
            it.printStackTrace()
        }.getOrNull()
    }

    suspend fun signPbst(psbt: Psbt, options: List<UserToSignInput>? = null): Psbt? {
        return kotlin.runCatching {
            val bitcoinWallet = WalletHelper.getCurWallet()?.getBitcoinWallet() ?: return null
            val address = packAddress(bitcoinWallet.bitcoinAddress) ?: return null
            val signHashList = psbt.inputUpdates.map { inputUpdate ->
                inputUpdate.sighashType?.let { hashType ->
                    PsbtUtil.resolveSignHashType(hashType)
                } ?: Transaction.SigHash.ALL
            }
            when(address) {
                is LegacyAddress -> {
                    val signedTx = signLegacyTransaction(psbt.globalMap.unsignedTx, signHashList)
                    signLegacyAndSegwitPsbt(psbt, signedTx ?: return null)
                }
                is SegwitAddress -> {
                    val signedTx = signSegWitTransaction(psbt.globalMap.unsignedTx, false, signHashList)
                    signLegacyAndSegwitPsbt(psbt, signedTx ?: return null)
                }
                is NestedSegwitAddress -> {
                    val signedTx = signSegWitTransaction(psbt.globalMap.unsignedTx, true, signHashList)
                    signLegacyAndSegwitPsbt(psbt, signedTx ?: return null)
                }
                is TaprootAddress -> {
                    val signedTx = signTaprootTransaction(psbt.globalMap.unsignedTx, signHashList)
                    signTaprootPsbt(psbt, signedTx ?: return null)
                }
                else -> throw IllegalStateException("Unsupported address type")
            }
            return psbt
        }.getOrNull()
    }

    private fun signLegacyAndSegwitPsbt(psbt: Psbt, signedTx: Transaction) {
        psbt.inputUpdates.forEachIndexed { index, psbtInputUpdate ->
            val signature = signedTx.inputs[index].witness.getPush(0)
            val pubKey = signedTx.inputs[index].witness.getPush(1)
            if (signature != null && pubKey != null) {
                psbtInputUpdate.partialSig?.add(PartialSig(signature, pubKey))
            }
        }
    }

    private fun signTaprootPsbt(psbt: Psbt, signedTx: Transaction){
        psbt.inputUpdates.forEachIndexed { index, psbtInputUpdate ->
            val tapKeySig = signedTx.inputs[index].witness.getPush(0)
            if (tapKeySig != null) {
                psbtInputUpdate.tapKeySig = tapKeySig
            }
        }
    }

    private suspend fun confirmUsedUtxoList(
        from: Address,
        to: Address,
        value: BigInteger,
        utxoList: List<UTXO>,
        fee: BigInteger
    ): List<UTXO> {
        val usedUtxoList = findUsedUtxoList(utxoList, value.toLong())
        if (usedUtxoList.isEmpty()) {
            return emptyList()
        }
        val gasFee = estimateGasFee(to, usedUtxoList.size, 2, fee)
        val money = usedUtxoList.sumOf { it.value.value }.toBigInteger()
        return if (money - value - gasFee < BigInteger.ZERO) {
            confirmUsedUtxoList(from, to, value + gasFee, utxoList, fee)
        } else {
            usedUtxoList
        }
    }

    private fun findUsedUtxoList(list: List<UTXO>, value: Long): List<UTXO> {
        val sortedList = list.sortedByDescending { it.value.value }
        var sum = 0L
        var index = -1

        for ((i, utxo) in sortedList.withIndex()) {
            sum += utxo.value.value
            if (sum >= value) {
                index = i
                break
            }
        }

        return if (index != -1) {
            list.subList(0, index + 1)
        } else emptyList()
    }

    private suspend fun byteSizeOfTransaction(inputSize: Int, outputs: List<TransactionOutput>): BigInteger {
        val fromAddress = withContext(Dispatchers.IO) {
            val bitcoinWallet = WalletHelper.getCurWallet()?.getBitcoinWallet() ?: return@withContext null
            packAddress(bitcoinWallet.bitcoinAddress)
        } ?: return BigInteger.ZERO
        val inputBytes = if (fromAddress is SegwitAddress || fromAddress is NestedSegwitAddress) {
            inputSize.toBigInteger().multiply(BigInteger("102"))
        } else {
            inputSize.toBigInteger().multiply(BigInteger("148"))
        }
        var outputBytes = BigInteger.ZERO
        outputs.forEach {
            val script = it.scriptPubKey.scriptType
            outputBytes += if (script == ScriptType.P2WPKH || script == ScriptType.P2SH || script == ScriptType.P2WSH) {
                BigInteger("32")
            } else {
                BigInteger("34")
            }
        }
        return inputBytes.add(outputBytes).add(BigInteger("10"))
    }

    suspend fun estimateGasFee(transaction: Transaction, fee: BigInteger): BigInteger {
        return estimateGasFee(transaction.inputs.size, transaction.outputs, fee)
    }

    suspend fun estimateGasFee(inputSize: Int, outputs: List<TransactionOutput>, fee: BigInteger): BigInteger {
        val byteSize = byteSizeOfTransaction(inputSize, outputs)
        return byteSize * fee
    }

    suspend fun estimateGasFee(to: Address, inputSize: Int, outputSize: Int, fee: BigInteger): BigInteger {
        val bitcoinWallet = WalletHelper.getCurWallet()?.getBitcoinWallet() ?: return BigInteger.ZERO
        val fromAddress = packAddress(bitcoinWallet.bitcoinAddress) ?: return BigInteger.ZERO
        val outputs = mutableListOf<TransactionOutput>()
        val transaction = Transaction(getBitcoinNetworkParams()).apply { setVersion(2) }
        if (outputSize > 1) {
            TransactionOutput(
                getBitcoinNetworkParams(),
                transaction,
                Coin.ZERO,
                lockingScript(fromAddress).program
            )
        }
        (0 until outputSize - 1).forEach { _ ->
            outputs.add(
                TransactionOutput(
                    getBitcoinNetworkParams(),
                    transaction,
                    Coin.ZERO,
                    lockingScript(to).program
                )
            )
        }
        val byteSize = byteSizeOfTransaction(inputSize, outputs)
        return byteSize * fee
    }

    fun getBitcoinNetworkParams(network: RPCNetwork? = null): NetworkParameters {
        val chainId = network?.chainId ?: WalletHelper.getCurChainId()
        return if (chainId == Config.BITCOIN_TEST_CHAIN_ID) {
            TestNet3Params.get()
        } else {
            MainNetParams.get()
        }
    }

    private fun getChainIdFromNetworkParams(networkParams: NetworkParameters): Int {
        return if (networkParams.id == NetworkParameters.ID_TESTNET) {
            Config.BITCOIN_TEST_CHAIN_ID
        } else if (networkParams.id == NetworkParameters.ID_MAINNET) {
            Config.BITCOIN_MAIN_CHAIN_ID
        } else {
            throw IllegalArgumentException("Unsupported chain id")
        }
    }

    fun wifToHexPrivateKey(wif: String): String {
        return kotlin.runCatching {
            var bytes = Base58.decode(wif)
            bytes = bytes.dropLast(4).toByteArray()
            bytes = bytes.drop(1).toByteArray()
            if (wif.length == 52) {
                bytes = bytes.dropLast(1).toByteArray()
            }
            bytes.toHexStringNoPrefix()
        }.getOrNull() ?: wif
    }

    fun hexPrivateKeyToWif(privateKey: String): String {
        return kotlin.runCatching {
            val noPrefixHex = privateKey.cleanHexPrefix()
            var hex = if (WalletHelper.getCurChainId() == Config.BITCOIN_TEST_CHAIN_ID) {
                "ef${noPrefixHex}01"
            } else {
                "80${noPrefixHex}01"
            }
            val bytes = Sha256Hash.hashTwice(hex.toHexByteArray())
            val code = bytes.copyOfRange(0, 4)
            hex = "${hex}${code.toHexStringNoPrefix()}"
            Base58.encode(hex.toHexByteArray())
        }.getOrNull() ?: privateKey
    }

    fun isWifPrivateKey(wif: String): Boolean {
        return kotlin.runCatching {
            val hex = Base58.decode(wif).toHexStringNoPrefix()
            val code = hex.takeLast(8)
            val hex1 = hex.dropLast(8)
            val hex2 = Sha256Hash.hashTwice(hex1.toHexByteArray()).toHexStringNoPrefix()
            val code2 = hex2.take(8)
            return code == code2
        }.getOrNull() ?: false
    }

}