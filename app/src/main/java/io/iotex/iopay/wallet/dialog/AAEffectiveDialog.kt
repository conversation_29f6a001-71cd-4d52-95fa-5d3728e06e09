package io.iotex.iopay.wallet.dialog

import io.iotex.base.bindbase.BaseBindDialog
import io.iotex.iopay.R
import io.iotex.iopay.databinding.DialogAaEffectiveBinding
import io.iotex.iopay.util.FireBaseEvent
import io.iotex.iopay.util.FireBaseUtil
import io.iotex.iopay.wallet.aawallet.SendRecoverEmailActivity
import org.jetbrains.anko.startActivity

class AAEffectiveDialog:
    BaseBindDialog<Nothing, DialogAaEffectiveBinding>(R.layout.dialog_aa_effective) {

    override fun initView() {
        mBinding.run {
            ivClose.setOnClickListener { dismissAllowingStateLoss() }
            btnConfirm.setOnClickListener {
                requireActivity().startActivity<SendRecoverEmailActivity>()
                dismissAllowingStateLoss()
                FireBaseUtil.logFireBase(FireBaseEvent.ACTION_WALLET_RECOVER_AA_UNSUCCESS_RESULT_CONFIRM)
            }
        }
    }
}