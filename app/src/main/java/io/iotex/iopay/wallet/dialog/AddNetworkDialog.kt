package io.iotex.iopay.wallet.dialog

import android.os.Bundle
import android.view.View
import android.widget.TextView
import androidx.appcompat.widget.AppCompatButton
import io.iotex.iopay.R
import io.iotex.iopay.fragment.base.BaseDialogFragment
import io.iotex.iopay.util.IoPayConstant
import io.iotex.iopay.xapp.XAppsActivity
import org.jetbrains.anko.startActivity

class AddNetworkDialog : BaseDialogFragment() {

    override val dialogLayoutID: Int
        get() = R.layout.dialog_add_network

    override fun initView(view: View, savedInstanceState: Bundle?) {
        arguments?.let {
            val chainId = it.getInt(KEY_CHAIN_ID)
            val chainName = it.getString(KEY_CHAIN_NAME, "")
            val rpcUrl = it.getString(KEY_RPC_URL, "")
            val blockExplorerUrl = it.getString(KEY_BLOCK_EXPLORER_URL, "")
            val symbol = it.getString(KEY_RPC_SYMBOL, "")

            view.findViewById<TextView>(R.id.mTvNetwork).text = chainName
            view.findViewById<TextView>(R.id.mTvRpcUrl).text = rpcUrl
            view.findViewById<TextView>(R.id.mTvChainId).text = chainId.toString()
            view.findViewById<TextView>(R.id.mTvSymbol).text = symbol
            view.findViewById<TextView>(R.id.mTvDetail).setOnClickListener {
                requireActivity().startActivity<XAppsActivity>(
                    IoPayConstant.BROWSER_URL to blockExplorerUrl
                )
            }
        }

        view.findViewById<AppCompatButton>(R.id.mCancel).setOnClickListener {
            dismiss(CANCEL_ACTION)
        }
        view.findViewById<AppCompatButton>(R.id.mConfirm).setOnClickListener {
            dismiss(AGREE_ACTION)
        }
    }

    companion object {
        const val AGREE_ACTION = 200
        const val CANCEL_ACTION = 500
        const val KEY_CHAIN_ID = "KEY_CHAIN_ID"
        const val KEY_CHAIN_NAME = "KEY_CHAIN_NAME"
        const val KEY_RPC_URL = "KEY_CHAIN_RPC_URL"
        const val KEY_RPC_SYMBOL = "KEY_CHAIN_SYMBOL"
        const val KEY_BLOCK_EXPLORER_URL = "KEY_BLOCK_EXPLORER_URL"
    }
}