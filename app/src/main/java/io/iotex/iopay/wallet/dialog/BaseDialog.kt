package io.iotex.iopay.wallet.dialog

import android.app.Activity
import android.app.Dialog
import android.content.Context
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.WindowManager
import android.widget.FrameLayout
import android.widget.ProgressBar
import com.blankj.utilcode.util.ScreenUtils
import io.iotex.iopay.R
import io.iotex.iopay.util.extension.dp2px

abstract class BaseDialog(val context: Context, resId: Int) {

    protected val mDialog: Dialog
    private val mContentView: View
    private val mProgressContainer by lazy {
        val progress = ProgressBar(context).apply {
            val lp = FrameLayout.LayoutParams(
                40.dp2px(), 40.dp2px()
            )
            layoutParams = lp
            lp.gravity = Gravity.CENTER
        }
        FrameLayout(context).apply {
            val lp = FrameLayout.LayoutParams(
                FrameLayout.LayoutParams.MATCH_PARENT,
                FrameLayout.LayoutParams.MATCH_PARENT
            )
            layoutParams = lp
            visibility = View.GONE
            setBackgroundColor(context.getColor(R.color.color_8C000000))
            addView(progress)
        }
    }

    init {
        val root = FrameLayout(context).apply {
            val lp = FrameLayout.LayoutParams(
                FrameLayout.LayoutParams.MATCH_PARENT,
                FrameLayout.LayoutParams.MATCH_PARENT
            )
            layoutParams = lp
        }
        mContentView = LayoutInflater.from(context).inflate(resId, null)
        root.addView(mContentView)
        root.addView(mProgressContainer)
        mDialog = Dialog(context, R.style.CommonDialog)
        mDialog.setContentView(root)

        val params = mDialog.window?.attributes
        params?.width = WindowManager.LayoutParams.MATCH_PARENT
        params?.height = WindowManager.LayoutParams.WRAP_CONTENT
        params?.gravity = Gravity.BOTTOM
        params?.windowAnimations = R.style.DialogAnimation
        mDialog.window?.attributes = params
    }

    protected fun <T: View> findView(id: Int): T {
        return mContentView.findViewById(id)
    }

    fun cancellableOnTouchOutside(cancellable: Boolean) = apply {
        mDialog.setCanceledOnTouchOutside(cancellable)
    }

    fun showLoading() {
        mProgressContainer.visibility = View.VISIBLE
    }

    fun hideLoading() {
        mProgressContainer.visibility = View.GONE
    }

    fun isShowing(): Boolean {
        return mDialog.isShowing
    }

    fun show() {
        if (context is Activity && !context.isFinishing && !mDialog.isShowing) {
            mDialog.show()
        }
    }

    fun dismiss() {
        if (mDialog.isShowing){
            mDialog.dismiss()
        }
    }

}