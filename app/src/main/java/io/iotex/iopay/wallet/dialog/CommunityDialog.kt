package io.iotex.iopay.wallet.dialog

import android.content.Intent
import android.os.Bundle
import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import com.google.firebase.analytics.FirebaseAnalytics
import io.iotex.iopay.R
import io.iotex.iopay.fragment.base.BaseDialogFragment
import io.iotex.iopay.util.Config
import io.iotex.iopay.util.IoPayConstant
import io.iotex.iopay.xapp.XAppsActivity

open class CommunityDialog : BaseDialogFragment() {
    private lateinit var firebaseAnalytics: FirebaseAnalytics

    override val dialogLayoutID: Int
        get() = R.layout.dialog_community

    override fun initView(view: View, savedInstanceState: Bundle?) {
        firebaseAnalytics = FirebaseAnalytics.getInstance(view.context)
        initData(view)
    }

    fun initData(view: View) {
        view.findViewById<LinearLayout>(R.id.llTwitter).setOnClickListener {
            communityUrlOpen(Config.TWITTER_URL)
        }
        view.findViewById<LinearLayout>(R.id.llTelegram).setOnClickListener {
            communityUrlOpen(Config.TELEGRAM_URL)
        }
        view.findViewById<LinearLayout>(R.id.llFacebook).setOnClickListener {
            communityUrlOpen(Config.FACEBOOK_URL)
        }
        view.findViewById<LinearLayout>(R.id.llMedium).setOnClickListener {
            communityUrlOpen(Config.MEDIUM_URL)
        }
        view.findViewById<LinearLayout>(R.id.llReddit).setOnClickListener {
            communityUrlOpen(Config.REDDIT_URL)
        }
        view.findViewById<LinearLayout>(R.id.llYoutube).setOnClickListener {
            communityUrlOpen(Config.YOUTOBE_URL)
        }
        view.findViewById<LinearLayout>(R.id.tvForum).setOnClickListener {
            communityUrlOpen(Config.FORUM_URL)
        }
    }
    private fun communityUrlOpen(url: String) {
        val intent = Intent(context, XAppsActivity::class.java)
        intent.putExtra(IoPayConstant.BROWSER_URL, url)
        startActivity(intent)
        dismissAllowingStateLoss()
    }
    override fun onStart() {
        super.onStart()
        val dialogWindow = dialog?.window
        val lp = dialogWindow?.attributes
        dialogWindow?.setGravity(Gravity.BOTTOM)
        lp?.width = ViewGroup.LayoutParams.MATCH_PARENT
        lp?.height = ViewGroup.LayoutParams.WRAP_CONTENT
        dialogWindow?.attributes = lp
    }
}
