package io.iotex.iopay.wallet.dialog

import android.view.WindowManager
import androidx.core.view.isVisible
import com.blankj.utilcode.util.ClipboardUtils
import com.blankj.utilcode.util.ConvertUtils
import com.blankj.utilcode.util.ScreenUtils
import com.blankj.utilcode.util.Utils
import com.drakeet.multitype.MultiTypeAdapter
import io.iotex.base.BaseViewModel
import io.iotex.base.bindbase.BaseBindDialog
import io.iotex.iopay.R
import io.iotex.iopay.databinding.DialogCopyMnemonicBinding
import io.iotex.iopay.ui.binder.MnemonicBinder
import io.iotex.iopay.ui.binder.MnemonicItemWrapper
import io.iotex.iopay.util.FireBaseEvent
import io.iotex.iopay.util.FireBaseUtil
import io.iotex.iopay.util.extension.toast

class CopyMnemonicDialog(private val mnemonic: String) :
    BaseBindDialog<BaseViewModel, DialogCopyMnemonicBinding>(R.layout.dialog_copy_mnemonic) {

    private val section1Adapter = MultiTypeAdapter()
    private val section2Adapter = MultiTypeAdapter()

    private var visible1 = false
    private var visible2 = false

    override fun initView() {
        section1Adapter.register(MnemonicItemWrapper::class, MnemonicBinder(showIndex = true))
        section2Adapter.register(MnemonicItemWrapper::class, MnemonicBinder(showIndex = true))
        val section1List = mnemonic.split(" ").subList(0, 6)
        val section2List = mnemonic.split(" ").subList(6, 12)
        section1Adapter.items = section1List.map { MnemonicItemWrapper(it) }
        section2Adapter.items = section2List.map { MnemonicItemWrapper(it) }
        mBinding.rvSection1.adapter = section1Adapter
        mBinding.rvSection2.adapter = section2Adapter

        mBinding.ivClose.setOnClickListener {
            dismiss()
        }
        mBinding.tvCopyAll.setOnClickListener {
            copy(mnemonic)
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_SETTINGS_BACKUP_MNEMOIC_ALL_COPY)
        }
        mBinding.ivCopyOne.setOnClickListener {
            copy(section1List.joinToString(" "))
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_SETTINGS_BACKUP_MNEMOIC_SECTION1_COPY)
        }
        mBinding.ivCopyTwo.setOnClickListener {
            copy(section2List.joinToString(" "))
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_SETTINGS_BACKUP_MNEMOIC_SECTION2_COPY)
        }
        mBinding.ivViewerOne.setOnClickListener {
            changeEye1()
        }
        mBinding.llEye1.setOnClickListener {
            changeEye1()
        }
        mBinding.ivViewerTwo.setOnClickListener {
            changeEye2()
        }
        mBinding.llEye2.setOnClickListener {
            changeEye2()
        }
    }

    private fun changeEye1() {
        visible1 = !visible1
        if (visible1) {
            mBinding.ivViewerOne.setImageResource(R.drawable.ic_eyes_open)
        } else {
            mBinding.ivViewerOne.setImageResource(R.drawable.ic_eyes_closed)
        }
        mBinding.llEye1.isVisible = !visible1
    }

    private fun changeEye2() {
        visible2 = !visible2
        if (visible2) {
            mBinding.ivViewerTwo.setImageResource(R.drawable.ic_eyes_open)
        } else {
            mBinding.ivViewerTwo.setImageResource(R.drawable.ic_eyes_closed)
        }
        mBinding.llEye2.isVisible = !visible2
    }

    private fun copy(str: String) {
        ClipboardUtils.copyText(str)
        Utils.getApp().getString(R.string.copy_success).toast()
    }

    override fun onStart() {
        super.onStart()
        val attributes = dialog?.window?.attributes
        attributes?.width = ScreenUtils.getScreenWidth() - ConvertUtils.dp2px(30f)
        dialog?.window?.attributes = attributes
        dialog?.window?.addFlags(WindowManager.LayoutParams.FLAG_SECURE)
    }

}