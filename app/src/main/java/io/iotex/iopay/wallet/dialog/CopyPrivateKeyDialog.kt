package io.iotex.iopay.wallet.dialog

import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import com.blankj.utilcode.util.ToastUtils
import io.iotex.base.BaseViewModel
import io.iotex.base.bindbase.BaseBindDialog
import io.iotex.iopay.R
import io.iotex.iopay.databinding.DialogCopyPrivateKeyBinding

class CopyPrivateKeyDialog @JvmOverloads constructor(val privateKey: String? = "") :
    BaseBindDialog<BaseViewModel, DialogCopyPrivateKeyBinding>(R.layout.dialog_copy_private_key) {

    override fun initView() {
        val keyOne = privateKey?.subSequence(0, (privateKey.length / 2)).toString()
        val keyTwo = privateKey?.subSequence((privateKey.length / 2), privateKey.length).toString()
        mBinding.tvKeyOne.text = "*".repeat(keyOne.length)
        mBinding.tvKeyTwo.text = "*".repeat(keyTwo.length)

        mBinding.ivClose.setOnClickListener {
            dismiss()
        }
        mBinding.ivCopyOne.setOnClickListener {
            copy(keyOne)
        }
        mBinding.ivCopyTwo.setOnClickListener {
            copy(keyTwo)
        }
        mBinding.tvCopyAll.setOnClickListener {
            copy(privateKey?:"")
        }

        mBinding.ivEyeOne.setOnClickListener {
            if(mBinding.tvKeyOne.text == keyOne){
                mBinding.ivEyeOne.setImageResource(R.drawable.icon_eye_close)
                mBinding.tvKeyOne.text = "*".repeat(keyOne.length)
            }else{
                mBinding.ivEyeOne.setImageResource(R.drawable.icon_eye_open)
                mBinding.tvKeyOne.text = keyOne
            }
        }
        mBinding.ivEyeTwo.setOnClickListener {
            if(mBinding.tvKeyTwo.text == keyTwo){
                mBinding.ivEyeTwo.setImageResource(R.drawable.icon_eye_close)
                mBinding.tvKeyTwo.text = "*".repeat(keyTwo.length)
            }else{
                mBinding.ivEyeTwo.setImageResource(R.drawable.icon_eye_open)
                mBinding.tvKeyTwo.text = keyTwo
            }
        }
    }

    private fun copy(key: String) {
        val cm: ClipboardManager =
            activity?.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
        cm.setPrimaryClip(
            ClipData.newPlainText(null, key)
        )
        ToastUtils.showShort(R.string.copy_success)
    }
}