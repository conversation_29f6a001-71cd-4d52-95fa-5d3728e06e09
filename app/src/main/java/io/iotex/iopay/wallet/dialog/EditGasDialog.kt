package io.iotex.iopay.wallet.dialog

import android.os.Bundle
import android.view.View
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.appcompat.widget.AppCompatButton
import com.blankj.utilcode.util.SPUtils
import com.google.firebase.crashlytics.FirebaseCrashlytics
import io.iotex.iopay.R
import io.iotex.iopay.fragment.base.BaseDialogFragment
import io.iotex.iopay.ui.widget.EditTextStateful
import io.iotex.iopay.util.*
import io.iotex.iopay.util.extension.asBigDecimal
import org.web3j.utils.Convert
import java.math.BigDecimal

class EditGasDialog : BaseDialogFragment() {

    private val defGasLimit by lazy {
        arguments?.getLong("defGasLimit", 0) ?: 0
    }

    private val defGasPrice by lazy {
        arguments?.getLong("defGasPrice", 0) ?: 0
    }

    private var gasLimit: Long = 0
    private var gasPrice: String = "0"

    private var onTextChangeCallback: ((Long, String) -> Unit)? = null

    override val dialogLayoutID: Int
        get() = R.layout.dialog_edit_gas

    override fun initView(view: View, savedInstanceState: Bundle?) {
        view.findViewById<ImageView>(R.id.mIvBack).setOnClickListener {
            dismissAllowingStateLoss()
        }

        gasLimit = arguments?.getLong("gasLimit", 0) ?: 0
        gasPrice = arguments?.getString("gasPrice", "0") ?: "0"

        view.findViewById<EditTextStateful>(R.id.mEtGasLimit).setText(gasLimit.toString())

        val chainId = WalletHelper.getCurChainId()

        val price = if (chainId == Config.IOTEX_CHAIN_ID || chainId == Config.IOTEX_TEST_CHAIN_ID) {
            TokenUtil.weiToTokenBN(gasPrice, 12)
        } else {
            TokenUtil.weiToTokenBN(gasPrice, 9)
        }
        view.findViewById<EditTextStateful>(R.id.mEtGasPrice).setText(price)

        val unit = if (chainId == Config.IOTEX_CHAIN_ID) {
            getString(R.string.qev)
        } else {
            getString(R.string.gwei)
        }
        view.findViewById<TextView>(R.id.mTvGasPriceUnit).text = unit

        calculateFee(view)

        view.findViewById<EditTextStateful>(R.id.mEtGasLimit).addTextChangedListener(0) {
            kotlin.runCatching {
                if (view.findViewById<EditTextStateful>(R.id.mEtGasLimit).text().isNotBlank()) {
                    gasLimit = view.findViewById<EditTextStateful>(R.id.mEtGasLimit).text().toLong()
                    calculateFee(view)
                    if (gasLimit < defGasLimit) {
                        view.findViewById<EditTextStateful>(R.id.mEtGasLimit).notifyError(getString(R.string.lower_limit, getString(R.string.gas_limit), defGasLimit.toString()))
                    } else {
                        view.findViewById<EditTextStateful>(R.id.mEtGasLimit).notifyNormal()
                    }
                }
            }
        }

        view.findViewById<EditTextStateful>(R.id.mEtGasPrice).addTextChangedListener(0) {
            runCatching {
                val p = view.findViewById<EditTextStateful>(R.id.mEtGasPrice).text().ifBlank {
                    "0"
                }
                gasPrice = kotlin.runCatching {
                    if (chainId == Config.IOTEX_CHAIN_ID) {
                        Convert.toWei(p, Convert.Unit.SZABO).toString()
                    } else {
                        Convert.toWei(p, Convert.Unit.GWEI).toString()
                    }
                }.getOrNull() ?: ""
                calculateFee(view)
                if (gasPrice.asBigDecimal() < defGasPrice.toBigDecimal()) {
                    val leastGasPrice = if (chainId == Config.IOTEX_CHAIN_ID) {
                        Convert.fromWei(defGasPrice.toString().asBigDecimal(), Convert.Unit.SZABO).toPlainString()
                    } else {
                        Convert.fromWei(defGasPrice.toString().asBigDecimal(), Convert.Unit.GWEI).toPlainString()
                    }
                    view.findViewById<EditTextStateful>(R.id.mEtGasPrice).notifyError(getString(R.string.lower_limit_2, getString(R.string.gas_price), "$leastGasPrice $unit"))
                } else {
                    view.findViewById<EditTextStateful>(R.id.mEtGasPrice).notifyNormal()
                }
            }.onFailure {
                it.printStackTrace()
            }
        }

        view.findViewById<AppCompatButton>(R.id.mBtnSave).setOnClickListener {
            if (this.gasLimit < defGasLimit || gasPrice.asBigDecimal() < defGasPrice.toBigDecimal()) {
                return@setOnClickListener
            }
            onTextChangeCallback?.invoke(gasLimit, gasPrice)
            dismissAllowingStateLoss()
        }
    }

    private fun calculateFee(view: View) {
        try {
            val gasFeeIotx = TokenUtil.weiToTokenBN(gasPrice.asBigDecimal().multiply(BigDecimal(gasLimit)).toString(), 18)
            val fee = "$gasFeeIotx ${SPUtils.getInstance().getString(SPConstant.SP_RPC_NETWORK_NATIVE_CURRENCY, IoPayConstant.IOTX)}"
            view.findViewById<TextView>(R.id.mTvTotalAmount).text = fee

            if (gasFeeIotx.asBigDecimal() >= BigDecimal(10)) {
                view.findViewById<LinearLayout>(R.id.mLlWarn).visibility = View.VISIBLE
            } else {
                view.findViewById<LinearLayout>(R.id.mLlWarn).visibility = View.GONE
            }
        } catch (e: Exception) {
            FirebaseCrashlytics.getInstance().recordException(e)
        }
    }

    fun setOnTextChangeCallback(cb: (Long, String) -> Unit) {
        this.onTextChangeCallback = cb
    }
}