package io.iotex.iopay.wallet.dialog

import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import androidx.appcompat.widget.AppCompatButton
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.FragmentActivity
import com.blankj.utilcode.util.ActivityUtils
import io.iotex.iopay.R
import io.iotex.iopay.util.extension.dp2px


class FailureAlertDialog : DialogFragment() {

    private var mOnConfirmCallback: (() -> Unit)? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(DialogFragment.STYLE_NO_FRAME, R.style.Theme_dialog)
        val window = dialog?.window
        val wlp = window?.attributes
        wlp?.gravity = Gravity.CENTER
        window?.setLayout(300.dp2px(), 278.dp2px())
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.dialog_failure_alert, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        view.findViewById<ImageView>(R.id.mIvClose).setOnClickListener {
            dismissAllowingStateLoss()
        }
        view.findViewById<AppCompatButton>(R.id.mBtnConfirm).setOnClickListener {
            mOnConfirmCallback?.invoke()
            dismissAllowingStateLoss()
        }
    }

    fun setOnTextChangeCallback(cb: () -> Unit): FailureAlertDialog {
        this.mOnConfirmCallback = cb
        return this
    }

    fun show() {
        if (ActivityUtils.getTopActivity() is FragmentActivity) {
            val fm = (ActivityUtils.getTopActivity() as FragmentActivity).supportFragmentManager
            val ft = fm.beginTransaction()
            if (!fm.executePendingTransactions()) {
                ft.add(this, tag)
                ft.commitAllowingStateLoss()
            }
        }
    }

}