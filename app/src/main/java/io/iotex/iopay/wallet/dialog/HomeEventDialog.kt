package io.iotex.iopay.wallet.dialog

import android.graphics.Bitmap
import com.blankj.utilcode.util.ConvertUtils
import com.blankj.utilcode.util.ScreenUtils
import io.iotex.base.bindbase.BaseBindDialog
import io.iotex.iopay.IoPayApplication
import io.iotex.iopay.R
import io.iotex.iopay.base.BaseLaunchVM
import io.iotex.iopay.databinding.DialogHomeEventBinding
import io.iotex.iopay.reactnative.NativeEventEmitter
import io.iotex.iopay.support.eventbus.MainPageEvent
import io.iotex.iopay.util.FireBaseUtil
import io.iotex.iopay.util.extension.setGone
import io.iotex.iopay.util.extension.setVisible
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.greenrobot.eventbus.EventBus

class HomeEventDialog(val title: String?, val bitmap: Bitmap) :
    BaseBindDialog<BaseLaunchVM, DialogHomeEventBinding>(R.layout.dialog_home_event) {

    override fun initView() {
        if (title.isNullOrEmpty()) {
            mBinding.llImage.setGone()
            val layoutParams = mBinding.ivImageBig.layoutParams
            val width = ScreenUtils.getScreenWidth() - ConvertUtils.dp2px(50f)
            val height = width.toFloat() / bitmap.width * bitmap.height
            layoutParams.width = width
            layoutParams.height = height.toInt()
            mBinding.ivImageBig.layoutParams = layoutParams
            mBinding.ivImageBig.setImageBitmap(bitmap)
        } else {
            mBinding.llImage.setVisible()
            mBinding.tvTitle.text = title
            val layoutParams = mBinding.ivImage.layoutParams
            val width = ScreenUtils.getScreenWidth() - ConvertUtils.dp2px(80f)
            val height = width.toFloat() / bitmap.width * bitmap.height
            layoutParams.width = width
            layoutParams.height = height.toInt()
            mBinding.ivImage.layoutParams = layoutParams
            mBinding.ivImage.setImageBitmap(bitmap)
        }
        mBinding.root.setOnClickListener {
            dismiss()
            EventBus.getDefault().post(MainPageEvent(3))
            val context = IoPayApplication.getInstance().reactNativeHost.reactInstanceManager.currentReactContext
            val eventEmitter = context?.getNativeModule(NativeEventEmitter::class.java)
            CoroutineScope(Dispatchers.Default).launch {
                withContext(Dispatchers.Default) { delay(200) }
//                eventEmitter?.publishNewsSegmentEvent(1)
            }
            FireBaseUtil.logFireBase("action_home_adv_event")
        }
        mBinding.ivClose.setOnClickListener {
            dismiss()
        }
    }

}