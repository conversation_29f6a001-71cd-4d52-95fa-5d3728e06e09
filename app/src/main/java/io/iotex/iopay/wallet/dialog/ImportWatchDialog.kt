package io.iotex.iopay.wallet.dialog

import io.iotex.base.bindbase.BaseBindDialog
import io.iotex.iopay.R
import io.iotex.iopay.base.BaseLaunchVM
import io.iotex.iopay.databinding.DialogImportWatchBinding

class ImportWatchDialog :
    BaseBindDialog<BaseLaunchVM, DialogImportWatchBinding>(R.layout.dialog_import_watch) {

    var onConfirmClick:(()->Unit)? = null
    override fun initView() {
        mBinding.ivClose.setOnClickListener {
            dismiss()
        }
        mBinding.mBtnConfirm.setOnClickListener {
            onConfirmClick?.invoke()
            dismiss()
        }
    }
}