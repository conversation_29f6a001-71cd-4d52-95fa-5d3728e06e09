package io.iotex.iopay.wallet.dialog

import android.content.Context
import android.content.Intent
import android.view.Gravity
import android.view.WindowManager
import android.widget.Button
import android.widget.CheckBox
import android.widget.ImageView
import com.blankj.utilcode.util.*
import io.iotex.iopay.R
import io.iotex.iopay.util.FireBaseUtil
import io.iotex.iopay.util.SPConstant
import io.iotex.iopay.util.extension.dp2px
import io.iotex.iopay.network.NetworkNodeSettingActivity

class NetworkAnomalyDialog(context: Context): BaseDialog(context, R.layout.dialog_network_anomaly) {

    private val mIvClose: ImageView = findView(R.id.mIvClose)
    private val mBtnSetting: Button = findView(R.id.mBtnSetting)
    private val checkbox: CheckBox = findView(R.id.checkbox)

    init {
        val params = mDialog.window?.attributes
        params?.width = ScreenUtils.getScreenWidth() - 38.dp2px() * 2
        params?.height = WindowManager.LayoutParams.WRAP_CONTENT
        params?.windowAnimations = 0
        params?.gravity = Gravity.CENTER
        mDialog.window?.attributes = params

        mIvClose.setOnClickListener {
            dismiss()
        }
        mBtnSetting.setOnClickListener {
            dismiss()
            ActivityUtils.getTopActivity()?.let { activity ->
                val i = Intent(activity, NetworkNodeSettingActivity::class.java)
                i.addFlags(Intent.FLAG_ACTIVITY_NO_HISTORY)
                activity.startActivity(i)
            }
        }
    }

    fun setDismissCallback(l: () -> Unit) = apply {
        mDialog.setOnDismissListener {
            if(checkbox.isChecked){
                SPUtils.getInstance().put(SPConstant.SP_NETWORK_TIPS_UNDERSTOOD_TIME,System.currentTimeMillis())
                FireBaseUtil.logFireBase("action_network_anomaly_understood_click")
            }
            l.invoke()
        }
    }
}