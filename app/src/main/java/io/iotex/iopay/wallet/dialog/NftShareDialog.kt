package io.iotex.iopay.wallet.dialog

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Color
import android.net.Uri
import android.view.Gravity
import android.view.View
import android.view.WindowManager
import android.widget.ImageView
import android.widget.TextView
import com.blankj.utilcode.util.*
import io.iotex.iopay.R
import io.iotex.iopay.data.db.NftTokenEntry
import io.iotex.iopay.util.FireBaseEvent
import io.iotex.iopay.util.FireBaseUtil
import io.iotex.iopay.util.QRCodeUtil
import io.iotex.iopay.util.extension.*

class NftShareDialog(context: Context, private val nftToken: NftTokenEntry) :
    BaseDialog(context, R.layout.dialog_nft_share) {

    private val mIvContent: ImageView = findView(R.id.mIvContent)
    private val mIvDownload: ImageView = findView(R.id.mIvDownload)
    private val mIvShare: ImageView = findView(R.id.mIvShare)
    private val mTvTokenName: TextView = findView(R.id.mTvTokenName)
    private val mTvTokenId: TextView = findView(R.id.mTvTokenId)
    private val mLlPoster: View = findView(R.id.mLlPoster)

    init {
        setup()
        mTvTokenName.text = nftToken.name
        mTvTokenId.text = context.getString(R.string.nft_no, nftToken.tokenId)
        mIvContent.loadImage(nftToken.tokenUrl, R.drawable.icon_nft_default)
        mIvDownload.setOnClickListener {
            savePoster { fileUri ->
                if (fileUri != null) {
                    Utils.getApp().getString(R.string.saved_successfully).toast()
                }
            }
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_HOME_NFT_SHARE_DOWNLOAD)
        }
        mIvShare.setOnClickListener {
            savePoster { fileUri ->
                if (fileUri != null) {
                    val i = IntentUtils.getShareImageIntent(fileUri)
                    Utils.getApp().startActivity(i)
                }
            }
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_HOME_NFT_SHARE_SHARE)
        }
    }

    private fun setup() {
        val params = mDialog.window?.attributes
        params?.width = ScreenUtils.getScreenWidth() - 20.dp2px() * 2
        params?.height = WindowManager.LayoutParams.WRAP_CONTENT
        params?.gravity = Gravity.TOP
        params?.windowAnimations = R.style.DialogFadeAnimation
        params?.verticalMargin = 0.1f
        mDialog.window?.attributes = params
    }

    private fun savePoster(cb: ((Uri?) -> Unit)? = null) {
        val bmp = captureView(mLlPoster)
        val filename = "${nftToken.name}_${Utils.getApp().getString(R.string.nft_no, nftToken.tokenId)}_${System.currentTimeMillis()}.jpg"
        val uri = QRCodeUtil.savePhoto(bmp, filename)
        if (uri != null) {
            cb?.invoke(uri)
        } else {
            Utils.getApp().getString(R.string.saved_fail).toast()
            cb?.invoke(null)
        }
    }

    private fun captureView(view: View): Bitmap {
        val bitmap: Bitmap = Bitmap.createBitmap(
            view.width,
            view.height,
            Bitmap.Config.RGB_565
        )
        val canvas = Canvas(bitmap)
        canvas.drawColor(Color.WHITE)
        view.draw(canvas)
        return bitmap
    }
}
