package io.iotex.iopay.wallet.dialog

import android.content.DialogInterface
import android.view.Gravity
import android.view.ViewGroup
import androidx.appcompat.app.AppCompatActivity
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.Utils
import io.iotex.base.BaseViewModel
import io.iotex.base.bindbase.BaseBindDialog
import io.iotex.iopay.R
import io.iotex.iopay.databinding.DialogNoteBinding

class NoteDialog :
    BaseBindDialog<BaseViewModel, DialogNoteBinding>(R.layout.dialog_note) {

    var icon: Int = R.drawable.ic_note
    var title: String = Utils.getApp().getString(R.string.note)
    var message: String = Utils.getApp().getString(R.string.dapp_not_support_bitcoin_network)
    var confirmText: String = Utils.getApp().getString(R.string.ok)


    var onCancel: (() -> Unit)? = null
    var onConfirm: (() -> Unit)? = null

    override fun initView() {
        mBinding.ivClose.setOnClickListener {
            dismiss()
        }
        mBinding.btnOK.setOnClickListener {
            onConfirm?.invoke()
            dismiss()
        }
        mBinding.image.setImageResource(icon)
        mBinding.title.text = title
        mBinding.message.text = message
        mBinding.btnOK.text = confirmText
    }

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        onCancel?.invoke()
    }

    override fun onStart() {
        super.onStart()
        val dialogWindow = dialog?.window
        val lp = dialogWindow?.attributes
        dialogWindow?.setGravity(Gravity.CENTER)
        lp?.width = ViewGroup.LayoutParams.MATCH_PARENT
        lp?.height = ViewGroup.LayoutParams.WRAP_CONTENT
        dialogWindow?.attributes = lp
    }

    fun show() {
        val activity = ActivityUtils.getTopActivity() as? AppCompatActivity ?: return
        if (ActivityUtils.isActivityAlive(activity)) {
            show(
                activity.supportFragmentManager,
                System.currentTimeMillis().toString()
            )
        }
    }
}
