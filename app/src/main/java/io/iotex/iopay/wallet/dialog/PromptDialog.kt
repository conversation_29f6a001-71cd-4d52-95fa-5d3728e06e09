package io.iotex.iopay.wallet.dialog

import android.content.Context
import android.view.KeyEvent
import android.view.View
import android.widget.TextView
import io.iotex.iopay.R
import io.iotex.iopay.util.extension.setGone
import io.iotex.iopay.util.extension.setVisible

class PromptDialog(context: Context) : BaseDialog(context, R.layout.dialog_prompt) {

    private val mTvTitle: TextView = findView(R.id.mTvTitle)
    private val mTvContent: TextView = findView(R.id.mTvContent)
    private val mTvConfirm: TextView = findView(R.id.mTvConfirm)
    private val mTvCaption: TextView = findView(R.id.mTvCaption)
    private val mIvClose: View = findView(R.id.mIvClose)

    init {
        mIvClose.setOnClickListener {
            dismiss()
        }
    }

    fun setCloseable(closeable: <PERSON>olean) = apply {
        cancellableOnTouchOutside(closeable)
        if (closeable) {
            mIvClose.setVisible()
        } else {
            mIvClose.setGone()
        }
        mDialog.setOnKeyListener { _, _, event ->
            if (event.keyCode == KeyEvent.KEYCODE_BACK) {
                return@setOnKeyListener !closeable
            }
            return@setOnKeyListener false
        }
    }

    fun setTitle(title: String) = apply {
        mTvTitle.text = title
    }

    fun setContent(content: String) = apply {
        mTvContent.text = content
    }

    fun setCaption(capture: String, visible: Boolean) = apply {
        mTvCaption.text = capture
        mTvCaption.visibility = if (visible) View.VISIBLE else View.GONE
    }

    fun setPositiveButton(positive: String, callback: (() -> Unit)? = null) = apply {
        mTvConfirm.text = positive
        mTvConfirm.setOnClickListener {
            callback?.invoke()
            dismiss()
        }
    }

}
