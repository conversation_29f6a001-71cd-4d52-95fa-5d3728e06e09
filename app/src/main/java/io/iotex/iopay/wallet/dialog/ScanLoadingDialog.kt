package io.iotex.iopay.wallet.dialog

import android.content.Context
import android.os.Bundle
import android.widget.ImageView
import androidx.appcompat.app.AppCompatDialog
import com.bumptech.glide.Glide
import io.iotex.iopay.R

class ScanLoadingDialog @JvmOverloads constructor(context: Context, themeId: Int = R.style.LoadingDialog) : AppCompatDialog(context, themeId) {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.dialog_scan_loading)
        val ivLoad = findViewById<ImageView>(R.id.iv_load)
        ivLoad?.let {
            Glide.with(ivLoad).asGif().load(R.drawable.dialog_loading).into(ivLoad)
        }
    }

    class Builder(private val context: Context) {
        private var isCancelable = true
        private var isCancelOutside = true


        fun setCancelable(isCancelable: Boolean): Builder {
            this.isCancelable = isCancelable
            return this
        }

        fun setCancelOutside(isCancelOutside: Boolean): Builder {
            this.isCancelOutside = isCancelOutside
            return this
        }

        fun create(): ScanLoadingDialog {
            val loadingDialog = ScanLoadingDialog(context)
            loadingDialog.setCancelable(isCancelable)
            loadingDialog.setCanceledOnTouchOutside(isCancelOutside)
            return loadingDialog
        }

    }
}