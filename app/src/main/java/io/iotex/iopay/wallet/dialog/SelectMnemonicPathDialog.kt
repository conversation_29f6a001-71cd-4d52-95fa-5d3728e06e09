package io.iotex.iopay.wallet.dialog

import android.os.Bundle
import android.view.View
import android.widget.LinearLayout
import io.iotex.iopay.R
import io.iotex.iopay.fragment.base.BaseDialogFragment
import io.iotex.iopay.wallet.add.PATH_BTC
import io.iotex.iopay.wallet.add.PATH_ETH
import io.iotex.iopay.wallet.add.PATH_IOTEX
import io.iotex.iopay.wallet.add.PATH_SOLANA

class SelectMnemonicPathDialog(val mPath:Int) : BaseDialogFragment() {

    private var onSelectedCallback: ((Int) -> Unit)? = null

    override val dialogLayoutID: Int
        get() = R.layout.dialog_select_mnemonic_path

    override fun initView(view: View, savedInstanceState: Bundle?) {
        when (mPath) {
            PATH_ETH -> {
                view.findViewById<LinearLayout>(R.id.mLlPathEvm)
                    .setBackgroundResource(R.drawable.shape_card_back_stroke_gradient)
                view.findViewById<LinearLayout>(R.id.mLlPathIotex)
                    .setBackgroundResource(R.drawable.shape_card_back)
                view.findViewById<LinearLayout>(R.id.mLlPathBitCoin)
                    .setBackgroundResource(R.drawable.shape_card_back)
                view.findViewById<LinearLayout>(R.id.mLlPathSolana)
                    .setBackgroundResource(R.drawable.shape_card_back)
            }
            PATH_BTC -> {
                view.findViewById<LinearLayout>(R.id.mLlPathEvm)
                    .setBackgroundResource(R.drawable.shape_card_back)
                view.findViewById<LinearLayout>(R.id.mLlPathIotex)
                    .setBackgroundResource(R.drawable.shape_card_back)
                view.findViewById<LinearLayout>(R.id.mLlPathBitCoin)
                    .setBackgroundResource(R.drawable.shape_card_back_stroke_gradient)
                view.findViewById<LinearLayout>(R.id.mLlPathSolana)
                    .setBackgroundResource(R.drawable.shape_card_back)
            }
            PATH_SOLANA -> {
                view.findViewById<LinearLayout>(R.id.mLlPathSolana)
                    .setBackgroundResource(R.drawable.shape_card_back)
                view.findViewById<LinearLayout>(R.id.mLlPathIotex)
                    .setBackgroundResource(R.drawable.shape_card_back)
                view.findViewById<LinearLayout>(R.id.mLlPathBitCoin)
                    .setBackgroundResource(R.drawable.shape_card_back)
                view.findViewById<LinearLayout>(R.id.mLlPathSolana)
                    .setBackgroundResource(R.drawable.shape_card_back_stroke_gradient)
            }
            else -> {
                view.findViewById<LinearLayout>(R.id.mLlPathEvm)
                    .setBackgroundResource(R.drawable.shape_card_back)
                view.findViewById<LinearLayout>(R.id.mLlPathBitCoin)
                    .setBackgroundResource(R.drawable.shape_card_back)
                view.findViewById<LinearLayout>(R.id.mLlPathIotex)
                    .setBackgroundResource(R.drawable.shape_card_back_stroke_gradient)
                view.findViewById<LinearLayout>(R.id.mLlPathSolana)
                    .setBackgroundResource(R.drawable.shape_card_back)
            }
        }
        view.findViewById<LinearLayout>(R.id.mLlPathIotex).setOnClickListener {
            this.onSelectedCallback?.invoke(PATH_IOTEX)
            dismissAllowingStateLoss()
        }
        view.findViewById<LinearLayout>(R.id.mLlPathEvm).setOnClickListener {
            this.onSelectedCallback?.invoke(PATH_ETH)
            dismissAllowingStateLoss()
        }
        view.findViewById<LinearLayout>(R.id.mLlPathBitCoin).setOnClickListener {
            this.onSelectedCallback?.invoke(PATH_BTC)
            dismissAllowingStateLoss()
        }
        view.findViewById<LinearLayout>(R.id.mLlPathSolana).setOnClickListener {
            this.onSelectedCallback?.invoke(PATH_SOLANA)
            dismissAllowingStateLoss()
        }
    }

    fun setOnSelectedListener(l: (Int) -> Unit) = apply {
        this.onSelectedCallback = l
    }
}