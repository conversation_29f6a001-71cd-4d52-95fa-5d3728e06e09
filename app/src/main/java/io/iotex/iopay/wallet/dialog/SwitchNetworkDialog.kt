package io.iotex.iopay.wallet.dialog

import android.os.Bundle
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.appcompat.widget.AppCompatButton
import io.iotex.iopay.R
import io.iotex.iopay.fragment.base.BaseDialogFragment
import io.iotex.iopay.util.extension.loadSvgOrImage

class SwitchNetworkDialog : BaseDialogFragment() {

    companion object {
        const val AGREE_ACTION = 200
        const val CANCEL_ACTION = 500
        const val KEY_FROM_CHAIN = "key_from_chain"
        const val KEY_TO_CHAIN = "key_to_chain"
        const val KEY_DAPP_HOST = "key_dapp_host"
        const val KEY_DAPP_LOGO = "key_dapp_logo"
    }

    override val dialogLayoutID: Int
        get() = R.layout.dialog_switch_network

    override fun initView(view: View, savedInstanceState: Bundle?) {
        initData(view)
    }

    private fun initData(view: View) {
        arguments?.let {
            val fromChain = it.getString(KEY_FROM_CHAIN)
            val toChain = it.getString(KEY_TO_CHAIN)
            val dappHost = it.getString(KEY_DAPP_HOST, "")
            val dappLogo = it.getString(KEY_DAPP_LOGO, "")

            if (fromChain.isNullOrBlank() || toChain.isNullOrBlank() || fromChain == toChain) {
                dismiss(CANCEL_ACTION)
                return
            }

            view.findViewById<ImageView>(R.id.mIvLogo).loadSvgOrImage(dappLogo, R.drawable.ic_dapp_placeholder)
            view.findViewById<TextView>(R.id.mTvDappHost).text = dappHost
            view.findViewById<TextView>(R.id.mTvDappChain).text = fromChain
            view.findViewById<TextView>(R.id.mTvFromChain).text = fromChain
            view.findViewById<TextView>(R.id.mTvToChain).text = toChain

            view.findViewById<AppCompatButton>(R.id.mCancel).setOnClickListener {
                dismiss(CANCEL_ACTION)
            }
            view.findViewById<AppCompatButton>(R.id.mConfirm).setOnClickListener {
                dismiss(AGREE_ACTION)
            }
        } ?: dismiss(CANCEL_ACTION)
    }
}