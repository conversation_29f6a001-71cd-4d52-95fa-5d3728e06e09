package io.iotex.iopay.wallet.dialog

import androidx.recyclerview.widget.LinearLayoutManager
import com.drakeet.multitype.MultiTypeAdapter
import io.iotex.base.bindbase.BaseBindDialog
import io.iotex.iopay.R
import io.iotex.iopay.data.db.*
import io.iotex.iopay.databinding.DialogTransferEmptyHashBinding
import io.iotex.iopay.transaction.bean.OptionEntry
import io.iotex.iopay.transaction.item.EntryItemBinder
import io.iotex.iopay.util.TokenUtil
import io.iotex.iopay.util.extension.asBigDecimal
import io.iotex.iopay.viewmodel.token.NftViewModel
import java.math.BigInteger

class TransferEmptyHashDialog(private val actionRecordEntry: ActionRecordEntry) :
    BaseBindDialog<NftViewModel, DialogTransferEmptyHashBinding>(R.layout.dialog_transfer_empty_hash) {

    private val mAdapter = MultiTypeAdapter()
    private val mAdapterGas = MultiTypeAdapter()
    private val list = ArrayList<OptionEntry>()
    private val listGas = ArrayList<OptionEntry>()

    override fun initData() {
        mBinding.ivClose.setOnClickListener {
            dismissAllowingStateLoss()
        }

        actionRecordEntry.getMethodName{
            list.add(0, OptionEntry(getString(R.string.method), it))
            mAdapter.notifyDataSetChanged()
        }
        list.add(OptionEntry(getString(R.string.status), actionRecordEntry.getStatusName()))
        list.add(OptionEntry(getString(R.string.from), actionRecordEntry.from))
        list.add(OptionEntry(getString(R.string.to), actionRecordEntry.to))
        list.add(
            OptionEntry(
                getString(R.string.amount),
                actionRecordEntry.value + " " + actionRecordEntry.symbol
            )
        )
        listGas.add(
            OptionEntry(
                getString(R.string.gas_fee_only),
                getGasFree() + " " + TokenUtil.getNativeCurrencySymbol()
            )
        )
        listGas.add(OptionEntry(getString(R.string.gas_price), actionRecordEntry.gasPrice))
        listGas.add(
            OptionEntry(
                getString(R.string.gas_limit),
                actionRecordEntry.gasLimit
            )
        )

        mAdapter.register(OptionEntry::class.java, EntryItemBinder())
        mBinding.recyclerView.layoutManager = LinearLayoutManager(context)
        mAdapter.items = list
        mBinding.recyclerView.adapter = mAdapter

        mAdapterGas.register(OptionEntry::class.java, EntryItemBinder())
        mBinding.recyclerGas.layoutManager = LinearLayoutManager(context)
        mAdapterGas.items = listGas
        mBinding.recyclerGas.adapter = mAdapterGas
    }

    private fun getGasFree(): String {
        return if (actionRecordEntry.maxFeePerGas.isNotEmpty()) {
            val gas = ((actionRecordEntry.maxPriorityFee.toBigIntegerOrNull()
                ?: BigInteger.ZERO) + (actionRecordEntry.maxFeePerGas.toBigIntegerOrNull()
                ?: BigInteger.ZERO)).multiply(
                actionRecordEntry.gasLimit.toBigIntegerOrNull() ?: BigInteger.ZERO
            )
            TokenUtil.weiToTokenBN(gas.toString())
        } else {
            TokenUtil.weiToTokenBN(
                (actionRecordEntry.gasPrice.asBigDecimal().toBigInteger()).multiply(
                    actionRecordEntry.gasLimit.toBigIntegerOrNull() ?: BigInteger.ZERO
                ).toString(), 18
            )
        }
    }
}