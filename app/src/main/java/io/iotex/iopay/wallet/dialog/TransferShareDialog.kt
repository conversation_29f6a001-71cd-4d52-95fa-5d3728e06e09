package io.iotex.iopay.wallet.dialog

import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Color
import android.net.Uri
import android.text.method.LinkMovementMethod
import android.view.View
import com.blankj.utilcode.util.*
import com.machinefi.w3bstream.utils.extension.setClickSpan
import io.iotex.base.bindbase.BaseBindDialog
import io.iotex.iopay.R
import io.iotex.iopay.data.db.*
import io.iotex.iopay.databinding.DialogTransferShareBinding
import io.iotex.iopay.util.*
import io.iotex.iopay.util.extension.asBigDecimal
import io.iotex.iopay.util.extension.loadSvgOrImage
import io.iotex.iopay.util.extension.setGone
import io.iotex.iopay.util.extension.setVisible
import io.iotex.iopay.util.extension.toast
import io.iotex.iopay.viewmodel.token.NftViewModel
import org.jetbrains.anko.doAsync
import org.jetbrains.anko.uiThread
import java.math.BigDecimal
import java.text.SimpleDateFormat
import java.util.*

class TransferShareDialog(private val actionRecordEntry: ActionRecordEntry) :
    BaseBindDialog<NftViewModel, DialogTransferShareBinding>(R.layout.dialog_transfer_share) {

    override fun initData() {
        if (actionRecordEntry.type == ACTION_TYPE_TRANSFER) {
            mBinding.llTrans.setVisible()
            mBinding.llNft.setGone()

            var nativeCurrency: String
            if (actionRecordEntry.contract.isNotBlank()) {
                doAsync {
                    val erc20Entry = AppDatabase.getInstance(Utils.getApp()).tokenDao().queryByAddress(WalletHelper.getCurChainId(),
                        actionRecordEntry.contract)
                    nativeCurrency = erc20Entry?.symbol
                        ?: SPUtils.getInstance()
                            .getString(
                                SPConstant.SP_RPC_NETWORK_NATIVE_CURRENCY,
                                IoPayConstant.IOTX
                            )
                    uiThread {
                        showSymbol(nativeCurrency)
                        val price = erc20Entry?.price.asBigDecimal()
                        setMoney(actionRecordEntry.value, price)
                    }
                }
            } else {
                nativeCurrency = SPUtils.getInstance()
                    .getString(SPConstant.SP_RPC_NETWORK_NATIVE_CURRENCY, IoPayConstant.IOTX)
                showSymbol(nativeCurrency)
                doAsync {
                    val network = AppDatabase.getInstance(Utils.getApp()).rpcNetworkDao()
                        .queryRPCNetworkByChainId(WalletHelper.getCurChainId())
                    val price = network?.currencyPrice.asBigDecimal()
                    uiThread {
                        setMoney(actionRecordEntry.value, price)
                    }
                }
            }
            showTransStatus(actionRecordEntry)
        } else {
            mBinding.llTrans.setGone()
            mBinding.llNft.setVisible()

            mBinding.tvNftName.text = actionRecordEntry.name
            showNftStatus(actionRecordEntry)
            mBinding.tvNftId.text = getString(R.string.nft_no, actionRecordEntry.nftTokenId)
            val chainId = WalletHelper.getCurChainId()
            mViewModel.getNftTokenLogo(chainId, actionRecordEntry.contract, actionRecordEntry.nftTokenId)
            mViewModel.nftLogoLiveData.observe(this) {
                mBinding.ivNft.loadSvgOrImage(
                    it,R.drawable.icon_nft_default,
                )
            }
        }

        val hash = WalletHelper.formatWalletAddress(actionRecordEntry.hash)
        mBinding.tvTxId.text = hash
            .setClickSpan(hash,ColorUtils.getColor(R.color.color_617AFF)) {
                WalletHelper.gotoExplorer(actionRecordEntry.hash)
            }
        mBinding.tvTxId.movementMethod = LinkMovementMethod()
        mBinding.tvTo.text =
            TokenUtil.textEllipsis(WalletHelper.formatWalletAddress(actionRecordEntry.to), 6, 8)
        mBinding.tvFrom.text =
            TokenUtil.textEllipsis(WalletHelper.formatWalletAddress(actionRecordEntry.from), 6, 8)
        mBinding.tvTime.text = TimeUtils.millis2String(
            actionRecordEntry.timestamp.toLong(),
            SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.ENGLISH)
        )

        mBinding.ivClose.setOnClickListener {
            dismissAllowingStateLoss()
        }

        mBinding.mIvDownload.setOnClickListener {
            savePoster {
                Utils.getApp().getString(R.string.saved_successfully).toast()
            }
        }
        mBinding.mIvShare.setOnClickListener {
            savePoster { fileUri ->
                if (fileUri != null) {
                    val i = IntentUtils.getShareImageIntent(fileUri)
                    Utils.getApp().startActivity(i)
                    FireBaseUtil.logFireBase(FireBaseEvent.ACTION_ACTION_TXN_SHARE)
                }
            }
        }

    }

    private fun showSymbol(nativeCurrency: String) {
        if (actionRecordEntry.to == WalletHelper.getWeb3Address()) {
            mBinding.tvTransBlock.text =
                "+ ${TokenUtil.displayBalance(actionRecordEntry.value)} $nativeCurrency"
        } else {
            mBinding.tvTransBlock.text =
                "- ${TokenUtil.displayBalance(actionRecordEntry.value)} $nativeCurrency"
        }
    }

    private fun showTransStatus(item: ActionRecordEntry) {
        when (item.status) {
            STATUS_SUCCESS -> {
                mBinding.tvTransStatus.setText(R.string.success)
                mBinding.tvTransStatus.setTextColor(ColorUtils.getColor(R.color.color_617AFF))
            }

            STATUS_FAILED -> {
                mBinding.tvTransStatus.setText(R.string.failed)
                mBinding.tvTransStatus.setTextColor(ColorUtils.getColor(R.color.color_E53737))
            }

            STATUS_WAITING -> {
                mBinding.tvTransStatus.setText(R.string.waiting)
                mBinding.tvTransStatus.setTextColor(ColorUtils.getColor(R.color.color_617AFF))
            }
        }
    }

    private fun showNftStatus(item: ActionRecordEntry) {
        when (item.status) {
            STATUS_SUCCESS -> {
                mBinding.tvNftStatus.setText(R.string.sent_nft_success)
                mBinding.tvNftStatus.setTextColor(ColorUtils.getColor(R.color.color_617AFF))
            }

            STATUS_FAILED -> {
                mBinding.tvNftStatus.setText(R.string.sent_nft_failed)
                mBinding.tvNftStatus.setTextColor(ColorUtils.getColor(R.color.color_E53737))
            }

            STATUS_WAITING -> {
                mBinding.tvNftStatus.setText(R.string.waiting)
                mBinding.tvNftStatus.setTextColor(ColorUtils.getColor(R.color.color_617AFF))
            }
        }
    }

    private fun setMoney(amount: String, price: BigDecimal) {
        val valueStr = (amount.asBigDecimal() * price).toString()
        val balance = TokenUtil.displayBalance(valueStr)
        if (actionRecordEntry.to == WalletHelper.getWeb3Address()) {
            mBinding.tvTransMoney.text = "+ \$ $balance"
        } else {
            mBinding.tvTransMoney.text = "- \$ $balance"
        }
    }

    private fun savePoster(cb: ((Uri?) -> Unit)? = null) {
        val bmp = captureView(mBinding.llPoster)
        val filename = "${actionRecordEntry.symbol}_${System.currentTimeMillis()}.jpg"
        val uri = QRCodeUtil.savePhoto(bmp, filename)
        if (uri != null) {
            cb?.invoke(uri)
        } else {
            Utils.getApp().getString(R.string.saved_fail).toast()
            cb?.invoke(null)
        }
    }

    private fun captureView(view: View): Bitmap {
        val bitmap: Bitmap = Bitmap.createBitmap(
            view.width,
            view.height,
            Bitmap.Config.RGB_565
        )
        val canvas = Canvas(bitmap)
        canvas.drawColor(Color.WHITE)
        view.draw(canvas)
        return bitmap
    }
}