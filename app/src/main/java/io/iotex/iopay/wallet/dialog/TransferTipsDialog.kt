package io.iotex.iopay.wallet.dialog

import android.content.Intent
import android.os.Bundle
import android.text.Spannable
import android.text.SpannableString
import android.text.style.ForegroundColorSpan
import android.text.style.UnderlineSpan
import android.view.View
import android.widget.TextView
import androidx.appcompat.widget.AppCompatButton
import com.blankj.utilcode.util.ColorUtils
import com.blankj.utilcode.util.SPUtils
import io.iotex.base.language.LanguageType
import io.iotex.base.language.MultiLanguage
import io.iotex.iopay.R
import io.iotex.iopay.fragment.base.BaseDialogFragment
import io.iotex.iopay.util.Config
import io.iotex.iopay.util.FireBaseUtil
import io.iotex.iopay.util.IoPayConstant
import io.iotex.iopay.util.SPConstant
import io.iotex.iopay.xapp.XAppsActivity

open class TransferTipsDialog : BaseDialogFragment() {
    override val dialogLayoutID: Int
        get() = R.layout.dialog_transfer_tips

    override fun initView(view: View, savedInstanceState: Bundle?) {
        val content = getString(R.string.transfer_tips_content2)
        val spanString = SpannableString(content)
        val span = ForegroundColorSpan(ColorUtils.getColor(R.color.colorPrimary))
        val span2 = UnderlineSpan()
        var start = 0
        var end = 0
        if (MultiLanguage.getSetLanguageType(requireContext()) == LanguageType.CHINESE) {
            start = content.length - 7
            end = content.length - 3
        } else {
            start = content.length - 21
            end = content.length - 11
        }
        spanString.setSpan(span, start, end, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        spanString.setSpan(span2, start, end, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        view.findViewById<TextView>(R.id.tv_here).append(spanString)
        view.findViewById<TextView>(R.id.tv_here).setOnClickListener {
            val intent = Intent(activity, XAppsActivity::class.java)
            intent.putExtra(IoPayConstant.BROWSER_URL, Config.TRANSFER_TIP_URL)
            startActivity(intent)
        }
        view.findViewById<AppCompatButton>(R.id.btn_ok).setOnClickListener {
            FireBaseUtil.logFireBase("action_iotex_alert_page_click_verify")
            SPUtils.getInstance().put(SPConstant.TRANSFER_TIPS_DIALOG, true)
            dismissAllowingStateLoss()
        }
    }
}
