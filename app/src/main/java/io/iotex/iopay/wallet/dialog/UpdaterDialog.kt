package io.iotex.iopay.wallet.dialog

import android.content.DialogInterface
import android.content.Intent
import android.net.Uri
import com.blankj.utilcode.util.AppUtils
import com.blankj.utilcode.util.GsonUtils
import com.blankj.utilcode.util.Utils
import com.drakeet.multitype.MultiTypeAdapter
import io.iotex.base.BaseViewModel
import io.iotex.base.bindbase.BaseBindDialog
import io.iotex.base.language.LanguageType
import io.iotex.base.language.MultiLanguage
import io.iotex.iopay.BuildConfig
import io.iotex.iopay.R
import io.iotex.iopay.data.UserStore
import io.iotex.iopay.databinding.DialogUpdaterBinding
import io.iotex.iopay.setting.ManageActionActivity
import io.iotex.iopay.ui.binder.UpgradeContentItemBinder
import io.iotex.iopay.util.Constant
import io.iotex.iopay.util.FireBaseEvent
import io.iotex.iopay.util.FireBaseUtil
import io.iotex.iopay.util.extension.setGone
import io.iotex.iopay.util.extension.setInvisible
import io.iotex.iopay.util.extension.setVisible
import io.iotex.iotex.AppVersionQuery
import java.net.URL

class UpdaterDialog @JvmOverloads constructor(val version: AppVersionQuery.Version_control_android_2? = null) :
    BaseBindDialog<BaseViewModel, DialogUpdaterBinding>(R.layout.dialog_updater) {

    var onDismiss: (() -> Unit)? = null

    private val adapter = MultiTypeAdapter()

    override fun initView() {
        if (version == null) {
            dismiss()
            return
        }
        val cancelable = AppUtils.getAppVersionCode() > version.forced_code()
        dialog?.setCancelable(cancelable)
        if (!cancelable) mBinding.ivClose.setInvisible()
        if (!cancelable) {
            mBinding.llBackUp.setVisible()
            mBinding.tvCancel.text = getString(R.string.back_up)
            mBinding.tvCancel.setOnClickListener {
                startActivity(Intent(requireContext(), ManageActionActivity::class.java))
            }
        } else {
            mBinding.llBackUp.setGone()
            mBinding.tvCancel.text = getString(R.string.ignore_this_version)
            UserStore.setIgnoreVersion(version.target_version_code())
            mBinding.tvCancel.setOnClickListener { dismissAllowingStateLoss() }
        }
        mBinding.ivClose.setOnClickListener { dismissAllowingStateLoss() }
        val versionCode = version.version_code().toString().toInt()
        val versionName = version.version_name()
        mBinding.tvVersion.text = "$versionName ($versionCode)"
        val urlToDownload = URL(version.url())
        initRecycler()
        mBinding.tvConfirm.setOnClickListener {
            try {
                var url = urlToDownload.toString()
                if (!url.startsWith("https://") && !url.startsWith("http://")) {
                    url = "http://$url"
                }
                if (!BuildConfig.AUTO_UPDATE_APK) {
                    url = Constant.URL_GOOGLE_PLAY_APK
                }
                val openUrlIntent = Intent(Intent.ACTION_VIEW, Uri.parse(url))
                startActivity(openUrlIntent)
                if (cancelable) {
                    dismissAllowingStateLoss()
                }
                FireBaseUtil.logFireBase(
                    FireBaseEvent.ACTION_UPDATE_APP
                )
            } catch (e: Exception) {
                dismissAllowingStateLoss()
            }
        }
    }

    private fun initRecycler() {
        if (version == null) {
            dismiss()
            return
        }
        adapter.register(String::class, UpgradeContentItemBinder())
        mBinding.recyclerView.adapter = adapter
        val content =
            if (MultiLanguage.getSetLanguageType(Utils.getApp()) == LanguageType.CHINESE) {
                version.upgrade_content_cn()
            } else {
                version.upgrade_content_en()
            }
        val contentList =
            GsonUtils.fromJson<List<String>>(content, GsonUtils.getListType(String::class.java))
        adapter.items = contentList
        adapter.notifyDataSetChanged()
    }

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        onDismiss?.invoke()
    }
}
