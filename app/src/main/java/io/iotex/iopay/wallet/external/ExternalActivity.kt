package io.iotex.iopay.wallet.external

import android.content.Intent
import android.os.Bundle
import com.blankj.utilcode.util.ActivityUtils
import com.machinefi.lockscreen.LockAuthHelper
import io.iotex.base.BaseViewModel
import io.iotex.base.bindbase.BaseBindActivity
import io.iotex.iopay.LoadActivity
import io.iotex.iopay.MainActivity
import io.iotex.iopay.MainActivity.Companion.KEY_IS_VISITOR
import io.iotex.iopay.R
import io.iotex.iopay.databinding.ActivityExternalBinding
import io.iotex.iopay.util.IoPayConstant
import io.iotex.iopay.xapp.XAppsActivity
import org.jetbrains.anko.startActivity

class ExternalActivity : BaseBindActivity<BaseViewModel, ActivityExternalBinding>(R.layout.activity_external) {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        dispatchAction()
    }

    private fun dispatchAction() {
        val exist = ActivityUtils.isActivityExistsInStack(MainActivity::class.java)
        if (exist) {
            MainActivity.getIsVisitor {
                LockAuthHelper.showAuthLock(this, false) {
                    startActivity(Intent(this, MainActivity::class.java).apply {
                        data = <EMAIL>
                        putExtra(KEY_IS_VISITOR,it)
                    })
                }
                //share data
                val text = intent.extras?.getString(Intent.EXTRA_TEXT)
                if (!text.isNullOrEmpty() && text.contains("http")) {
                    val url = text.subSequence(text.indexOf("http"), text.length)
                    ActivityUtils.getTopActivity().startActivity<XAppsActivity>(
                        IoPayConstant.BROWSER_URL to url
                    )
                }
            }
        } else {
            startActivity(Intent(this, LoadActivity::class.java).apply {
                data = <EMAIL>
            })
        }
        finish()
    }
}