package io.iotex.iopay.wallet.home

import android.os.Bundle
import android.view.View
import androidx.core.view.isVisible
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import com.blankj.utilcode.util.ScreenUtils
import com.drakeet.multitype.MultiTypeAdapter
import com.youth.banner.indicator.CircleIndicator
import io.iotex.api.HomeBannerQuery
import io.iotex.base.bindbase.BaseBindFragment
import io.iotex.erc20.HomeVisitorQuery
import io.iotex.iopay.R
import io.iotex.iopay.SchemeUtil
import io.iotex.iopay.data.UserStore
import io.iotex.iopay.databinding.FragmentWalletVisitorBinding
import io.iotex.iopay.home.item.HomeBannerAdapter
import io.iotex.iopay.network.viewmodel.NetworkSwitchViewModel
import io.iotex.iopay.support.eventbus.MessageRedDotEvent
import io.iotex.iopay.token.TokenDetailActivity
import io.iotex.iopay.util.FireBaseEvent
import io.iotex.iopay.util.FireBaseParam
import io.iotex.iopay.util.FireBaseUtil
import io.iotex.iopay.util.PageEventUtil
import io.iotex.iopay.wallet.add.WalletAddActivity
import io.iotex.iopay.wallet.home.item.VisitorTokenBinder
import io.iotex.iopay.setting.SwapMenuActivity
import io.iotex.iopay.support.eventbus.GiftPageEvent
import io.iotex.iopay.util.extension.dp2px
import io.iotex.iopay.wallet.home.bean.fromHomeVisitorQuery
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

class WalletVisitorFragment :
    BaseBindFragment<WalletVisitorViewModel, FragmentWalletVisitorBinding>(R.layout.fragment_wallet_visitor) {
    companion object {
        fun newInstance(): WalletVisitorFragment {
            return WalletVisitorFragment()
        }
    }

    private val mNetworkSwitchViewModel by lazy {
        ViewModelProvider(this)[NetworkSwitchViewModel::class.java]
            .apply {
                lifecycle.addObserver(this)
            }
    }

    private val mAdapter = MultiTypeAdapter()

    override fun initView() {
        EventBus.getDefault().register(this)
        mAdapter.register(HomeVisitorQuery.Token_list_v4::class.java, VisitorTokenBinder().apply {
            onItemClick = { item ->
                TokenDetailActivity.startActivity(requireContext(), fromHomeVisitorQuery(item))
                val bundleEvent = Bundle()
                bundleEvent.putString(FireBaseParam.TOKEN_NAME, item.name())
                FireBaseUtil.logFireBase(FireBaseEvent.ACTION_VISITOR_HOME_DEPINSCAN_TOKEN_LIST, bundleEvent)
            }
        })
        mBinding.recyclerView.layoutManager = LinearLayoutManager(requireContext())
        mBinding.recyclerView.adapter = mAdapter

        mBinding.addLayout.tvAddWallet.setOnClickListener {
            WalletAddActivity.startActivity(requireContext())
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_VISITOR_HOME_ADD_WALLET)
        }

        mBinding.ivSetting.setOnClickListener {
            SwapMenuActivity.startActivity(requireActivity(),true)
        }

        mBinding.rlGift.isVisible = UserStore.getSwitchGiftCenter()
        mBinding.ivGift.setOnClickListener {
            EventBus.getDefault().post(GiftPageEvent(true))
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_HOME_GIFT_CENTER)
        }

        mBinding.refreshLayout.setOnRefreshListener {
            mNetworkSwitchViewModel.fetchNetworkList()
            mViewModel.getToken()
            mViewModel.getHomeVisitorBanner()
        }

        val ivParams = mBinding.banner.layoutParams
        ivParams.width = ScreenUtils.getScreenWidth() - 30.dp2px()
        ivParams.height = (ivParams.width * 110 / 345)
        mBinding.banner.layoutParams = ivParams

        PageEventUtil.logEvent(PageEventUtil.STARTHOME)
    }

    override fun initEvent() {
        mViewModel.getToken()
        mViewModel.tokenLiveData.observe(this) {
            mBinding.refreshLayout.finishRefresh()
            mAdapter.items = it
            mAdapter.notifyDataSetChanged()
        }
        mViewModel.errorLiveData.observe(this){
            mBinding.refreshLayout.finishRefresh()
        }
        mViewModel.getHomeVisitorBanner()
        mViewModel.bannerLiveData.observe(this) {
            handleBanners(it)
        }
        showRed()
    }

    private fun handleBanners(banners: List<HomeBannerQuery.Home_banner>) {
        mBinding.banner.setBackgroundResource(0)
        if (banners.isEmpty()) {
            mBinding.banner.visibility = View.GONE
        } else {
            mBinding.banner.visibility = View.VISIBLE
        }
        mBinding.banner.setTouchSlop(0)
        mBinding.banner.setLoopTime(4500)
        mBinding.banner.addBannerLifecycleObserver(viewLifecycleOwner)
            .setIndicator(CircleIndicator(requireActivity()))
            .setAdapter(HomeBannerAdapter(banners).apply {
                setOnBannerListener { data, _ ->
                    SchemeUtil.goto(requireContext(), data.link()?:"")
                }
            })
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMessageRedDotEvent(event: MessageRedDotEvent) {
        showRed()
    }

    override fun onDestroy() {
        EventBus.getDefault().unregister(this)
        super.onDestroy()
    }
    private fun showRed() {
        val message = UserStore.getMessageCount() != 0
        val update = UserStore.getUpdateApp()
        mBinding.viewMenuRed.isVisible = message || update
    }
}