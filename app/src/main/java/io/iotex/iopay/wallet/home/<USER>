package io.iotex.iopay.wallet.home

import android.app.Application
import androidx.lifecycle.MutableLiveData
import com.apollographql.apollo.ApolloClient
import com.apollographql.apollo.coroutines.await
import com.blankj.utilcode.util.AppUtils
import com.blankj.utilcode.util.GsonUtils
import com.blankj.utilcode.util.Utils
import io.iotex.api.HomeBannerQuery
import io.iotex.base.okHttpClient
import io.iotex.erc20.HomeVisitorQuery
import io.iotex.iopay.base.BaseLaunchVM
import io.iotex.iopay.data.db.AppDatabase
import io.iotex.iopay.util.Config
import io.iotex.iopay.util.Config.IOTEX_URL
import io.iotex.iopay.wallet.home.bean.HomeConfig

class WalletVisitorViewModel(application: Application) : BaseLaunchVM(application) {

    private val ioPayApolloClient by lazy {
        ApolloClient.builder()
            .serverUrl(Config.IoPayUrl)
            .okHttpClient(okHttpClient)
            .build()
    }

    val tokenLiveData = MutableLiveData<List<HomeVisitorQuery.Token_list_v4>>()
    val bannerLiveData = MutableLiveData<List<HomeBannerQuery.Home_banner>>()
    val errorLiveData = MutableLiveData<Boolean>()

    fun getToken() {
        addLaunch(onError = {
            kotlin.runCatching {
                val type = GsonUtils.getListType(HomeVisitorQuery.Token_list_v4::class.java)
                val result = GsonUtils.fromJson<ArrayList<HomeVisitorQuery.Token_list_v4>>(
                    HomeConfig.HOME_VISITOR_JSON,
                    type
                )
                tokenLiveData.postValue(result)
            }.getOrElse {
                errorLiveData.postValue(true)
            }
        }) {
            val homeVisitorQuery = HomeVisitorQuery.builder().build()
            val list = ArrayList<HomeVisitorQuery.Token_list_v4>()
            ioPayApolloClient.query(homeVisitorQuery).await().data?.token_list_v4()?.let {
                val network = AppDatabase.getInstance(Utils.getApp()).rpcNetworkDao()
                    .queryRPCNetworkByChainId(Config.IOTEX_CHAIN_ID)
                val iotex = HomeVisitorQuery.Token_list_v4(
                    "",
                    "iotex",
                    "{iotex:\"\"}",
                    network?.currencySymbol,
                    network?.currencyName,
                    network?.logo,
                    0,
                    network?.currencyPrice,
                    true,
                    true,
                    network?.sparkline_in_7d,
                    network?.price_change_24h,
                    IOTEX_URL,
                    ""
                )
                list.add(0, iotex)
                list.addAll(it)
                tokenLiveData.postValue(list)
            }
        }
    }

    fun getHomeVisitorBanner() {
        addLaunch {
            val homeBannerQuery = HomeBannerQuery.builder().build()
            ioPayApolloClient.query(homeBannerQuery).await().data?.home_banner()
                ?.filter { it.display() }?.filter {
                    it.chainIds().isNullOrEmpty() || it.chainIds()?.split(",")?.contains(Config.IOTEX_CHAIN_ID.toString()) == true
                }?.filter {
                    (it.min_android_version()?.toIntOrNull() ?: 0) < AppUtils.getAppVersionCode()
                }?.sortedBy {
                    it.order() ?: 0
                }?.let {
                    bannerLiveData.postValue(it)
                }
        }
    }
}