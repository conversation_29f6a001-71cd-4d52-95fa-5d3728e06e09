package io.iotex.iopay.wallet.home

import android.app.Application
import androidx.lifecycle.MutableLiveData
import com.blankj.utilcode.util.Utils
import io.iotex.iopay.base.BaseLaunchVM
import io.iotex.iopay.data.UserStore
import io.iotex.iopay.data.db.AppDatabase
import io.iotex.iopay.data.db.RPCNetwork
import io.iotex.iopay.data.db.Wallet
import io.iotex.iopay.support.eventbus.SwitchAllNetworkEvent
import io.iotex.iopay.util.Constant
import io.iotex.iopay.util.WalletHelper
import org.greenrobot.eventbus.EventBus


class WalletCardViewModel(application: Application) : BaseLaunchVM(application) {

    val networkLiveData = MutableLiveData<RPCNetwork>()
    val walletLiveData = MutableLiveData<Wallet>()
    val isValidAccountLiveData = MutableLiveData<Boolean>()

    fun curNetWork() {
        addLaunch {
            val network = WalletHelper.getCurNetwork()
            network?.let {
                networkLiveData.postValue(it)
            }
        }
    }

    fun isValidAccount() {
        addLaunch {
            val wallet = WalletHelper.getCurWallet() ?: return@addLaunch
            if (wallet.isAAWallet() || wallet.isSolanaPrivateWallet()) {
                isValidAccountLiveData.postValue(true)
            } else {
                isValidAccountLiveData.postValue(WalletHelper.isValidAccount())
            }
        }
    }

    fun curWallet() {
        addLaunch {
            val wallet = WalletHelper.getCurWallet() ?: return@addLaunch
            walletLiveData.postValue(wallet)
        }
    }

    fun switchNetworkOnAllNet(chainId: Int) {
        addLaunch {
            val network = AppDatabase.getInstance(Utils.getApp()).rpcNetworkDao()
                .queryRPCNetworkByChainId(chainId)
            network?.let {
                WalletHelper.switchNetwork(network)
                UserStore.setAllNetwork(true)
                EventBus.getDefault().post(SwitchAllNetworkEvent())
            }
        }
    }
}