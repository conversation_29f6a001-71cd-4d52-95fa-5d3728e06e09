package io.iotex.iopay.wallet.home

import android.app.Application
import androidx.lifecycle.MutableLiveData
import com.apollographql.apollo.ApolloClient
import com.apollographql.apollo.coroutines.await
import com.blankj.utilcode.util.AppUtils
import com.blankj.utilcode.util.GsonUtils
import com.blankj.utilcode.util.SPUtils
import com.blankj.utilcode.util.Utils
import io.iotex.api.HomeBannerQuery
import io.iotex.api.HomePostEventQuery
import io.iotex.api.type.Order_by
import io.iotex.base.okHttpClient
import io.iotex.iopay.base.BaseLaunchVM
import io.iotex.iopay.data.UserStore
import io.iotex.iopay.data.db.*
import io.iotex.iopay.meta.SP_KEY_FIRST_SWITCH_TO_SOLANA
import io.iotex.iopay.repo.GiftRepo
import io.iotex.iopay.util.*
import io.iotex.iopay.util.SPConstant.SP_GUIDE_STEP
import io.iotex.iotex.AppVersionQuery
import kotlinx.coroutines.delay

class HomeViewModel(application: Application) : BaseLaunchVM(application) {

    val homeBannerLiveData = MutableLiveData<List<HomeBannerQuery.Home_banner>>()
    val networkLiveData = MutableLiveData<RPCNetwork>()
    val tabLiveData = MutableLiveData<ArrayList<String>>()
    val walletLiveData = MutableLiveData<Wallet>()
    val postLiveData = MutableLiveData<HomePostEventQuery.Home_post_event>()
    val versionCodeLiveData = MutableLiveData<AppVersionQuery.Version_control_android_2>()
    val firstSolanaLiveData = MutableLiveData<Boolean>()
    val showGuideLiveData = MutableLiveData<Boolean>()

    private val giftRepo by lazy{
        GiftRepo()
    }

    private val mApolloClient = ApolloClient.builder()
        .serverUrl(Config.IoPayUrl)
        .okHttpClient(okHttpClient)
        .build()

    fun curNetWork() {
        addLaunch {
            val network = WalletHelper.getCurNetwork()
            network?.let {
                networkLiveData.postValue(it)
                val type = GsonUtils.getListType(String::class.java)
                val tabs = GsonUtils.fromJson<ArrayList<String>>(it.token_category, type)?: arrayListOf()
                tabLiveData.postValue(tabs)
                UserStore.saveNetwork(network)
            }
        }
    }

    fun curWallet() {
        addLaunch {
            val wallet = WalletHelper.getCurWallet()
            wallet?.let {
                Constant.currentWallet = wallet
                walletLiveData.postValue(it)
            }
        }
    }

    fun fetchHomeBanner() {
        val homeBannerQuery = HomeBannerQuery.builder().build()

        addLaunch {
            mApolloClient.query(homeBannerQuery).await().data
                ?.home_banner()?.filter { it.display() }?.filter {
                    it.chainIds()?.split(",")?.contains(UserStore.getChainId().toString()) == true
                            || UserStore.getAllNetwork() || it.chainIds().isNullOrEmpty()
                }?.filter {
                    (it.min_android_version()?.toIntOrNull()?:0) < AppUtils.getAppVersionCode()
                }?.sortedBy {
                    it.order() ?: 0
                }?.let {
                    homeBannerLiveData.postValue(it)
                }
        }
    }

    fun fetchHomePost() {
        val homePostEventQuery = HomePostEventQuery.builder().order_by(Order_by.DESC).build()
        addLaunch {
            mApolloClient.query(homePostEventQuery).await().data
                ?.home_post_event()?.firstOrNull {
                    DateTimeUtils.isActiveTime(it.start_at() as String?, it.end_at() as String?)
                }?.let {
                    postLiveData.postValue(it)
                }
        }
    }

    fun checkFirstSolana() {
        addLaunch {
            val count = AppDatabase.getInstance(Utils.getApp()).mnemonicDao().count()
            val firstSwitchSolana = SPUtils.getInstance().getBoolean(
                SP_KEY_FIRST_SWITCH_TO_SOLANA, true)
            firstSolanaLiveData.postValue(firstSwitchSolana && count == 0)
        }
    }

    fun fetchAppVersion() {
        giftRepo.fetchAppVersion {
            versionCodeLiveData.postValue(it)
        }
    }

    fun fetchGuide(){
        addLaunch {
            val show = !SPUtils.getInstance()
                .getBoolean(SP_GUIDE_STEP, false) && WalletHelper.isIoTexNetWork()
            delay(1000)
            showGuideLiveData.postValue(show)
        }
    }
}