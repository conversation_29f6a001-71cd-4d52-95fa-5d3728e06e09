package io.iotex.iopay.wallet.home.dialog

import io.iotex.base.BaseViewModel
import io.iotex.base.bindbase.BaseBindDialog
import io.iotex.iopay.R
import io.iotex.iopay.databinding.DialogHomeNetworkErrorBinding

class HomeNetworkErrorDialog :
    BaseBindDialog<BaseViewModel, DialogHomeNetworkErrorBinding>(R.layout.dialog_home_network_error) {

    override fun initView() {
        mBinding.ivClose.setOnClickListener {
            dismiss()
        }
        mBinding.btnTryAgain.setOnClickListener {
            dismiss()
        }
    }
}