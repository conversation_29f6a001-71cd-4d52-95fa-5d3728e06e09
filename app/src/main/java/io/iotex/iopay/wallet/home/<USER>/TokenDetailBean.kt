package io.iotex.iopay.wallet.home.bean

import android.os.Parcelable
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import io.iotex.erc20.HomeVisitorQuery
import io.iotex.iopay.data.db.TokenEntry
import io.iotex.iopay.util.Config
import kotlinx.parcelize.Parcelize

@Parcelize
data class TokenDetailBean(
    val id:String,
    val name:String,
    val logo:String,
    val chainId:Int,
    val address:String,
    val symbol:String,
    val decimals:String,
    val price:String,
    val price_change_24h:String,
    val sparkline_in_7d:String,
    val website: String,
    val chains: String = "",
    val isVisitorToken: Boolean = false
): Parcelable

fun fromToken(
    erC20Entry: TokenEntry,
    isVisitorToken: Boolean = false
): TokenDetailBean {
    return TokenDetailBean(
        erC20Entry.id,
        erC20Entry.name,
        erC20Entry.logo,
        erC20Entry.chainId,
        erC20Entry.address,
        erC20Entry.symbol,
        erC20Entry.decimals.toString(),
        erC20Entry.price,
        erC20Entry.price_change_24h,
        erC20Entry.sparkline_in_7d,
        erC20Entry.website,
        "",
        isVisitorToken
    )
}

fun fromHomeVisitorQuery(token: HomeVisitorQuery.Token_list_v4):TokenDetailBean{
    return TokenDetailBean(
        token.id()?:"",
        token.name()?:"",
        token.logo()?:"",
        getContractChainIdFromPlatforms(token.platforms().toString()),
        getContractFromPlatforms(token.platforms().toString()),
        token.symbol()?:"",
        "18",
        token.current_price().toString(),
        token.price_change_24h().toString(),
        token.sparkline_in_7d().toString(),
        token.website().toString(),
        getContractChainsFromPlatforms(token.platforms().toString()),
        isVisitorToken = true
    )
}

fun getContractChainIdFromPlatforms(platforms:String):Int{
    var chainId = 1
    runCatching {
        val type = object : TypeToken<HashMap<String, String>>() {}.type
        val map = Gson().fromJson<HashMap<String, String>>(platforms, type)
        val polygon = map["polygon-pos"]
        val ethereum = map["ethereum"]
        val other = map.firstNotNullOf { it.key }
        if (!polygon.isNullOrEmpty()) {
            chainId = Config.POLYGON_CHAIN_ID
        } else if (!ethereum.isNullOrEmpty()) {
            chainId = Config.ETH_CHAIN_ID
        } else if (other == "solana") {
            chainId = Config.SOLANA_MAIN_CHAIN_ID
        } else if (other == "bitcoin") {
            chainId = Config.BITCOIN_MAIN_CHAIN_ID
        } else if (other == "binance-smart-chain") {
            chainId = Config.BSC_CHAIN_ID
        } else if (other == "avalanche") {
            chainId = Config.AVAX_CHAIN_ID
        } else if (other == "fantom") {
            chainId = Config.FTM_CHAIN_ID
        } else if (other == "arbitrum-one") {
            chainId = Config.ARB_CHAIN_ID
        } else {
            chainId = Config.BASE_CHAIN_ID
        }
    }
    return chainId
}

fun getContractChainsFromPlatforms(platforms: String): String {
    var chains = ""
    runCatching {
        val type = object : TypeToken<HashMap<String, String>>() {}.type
        val map = Gson().fromJson<HashMap<String, String>>(platforms, type)
        map.forEach { (key, _) ->
            when (key) {
                "iotex" -> chains += "iotex"
                "ethereum" -> chains += "ETH"
                "bitcoin" -> chains += "bitcoin"
                "solana" -> chains += "solana"
                "binance-smart-chain" -> chains += "BSC"
                "polygon-pos" -> chains += "polygon"
                "avalanche" -> chains += "Avax"
                "fantom" -> chains += "FTM"
                "arbitrum-one" -> chains += "ARB"
                "base" -> chains += "base"
            }
        }
    }
    return chains
}

fun getContractFromPlatforms(platforms:String):String{
    var contract = ""
    runCatching {
        val type = object : TypeToken<HashMap<String, String>>(){}.type
        val map = Gson().fromJson<HashMap<String,String>>(platforms,type)
        val polygon = map["polygon-pos"]
        val ethereum = map["ethereum"]
        val other = map.firstNotNullOf { it.value}
        contract = polygon?:ethereum?:other
    }
    return contract
}