package io.iotex.iopay.wallet.home.item

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isInvisible
import com.drakeet.multitype.ItemViewBinder
import io.iotex.base.bindbase.BaseBindVH
import io.iotex.erc20.HomeVisitorQuery
import io.iotex.iopay.R
import io.iotex.iopay.data.bean.getStockList
import io.iotex.iopay.databinding.ItemVisitorTokenBinding
import io.iotex.iopay.util.TokenUtil
import io.iotex.iopay.util.extension.loadSvgOrImage
import io.iotex.iopay.wallet.home.bean.fromHomeVisitorQuery

class VisitorTokenBinder : ItemViewBinder<HomeVisitorQuery.Token_list_v4, BaseBindVH<ItemVisitorTokenBinding>>() {

    var onItemClick: ((HomeVisitorQuery.Token_list_v4) -> Unit)? = null

    override fun onBindViewHolder(
        holder: BaseBindVH<ItemVisitorTokenBinding>,
        item: HomeVisitorQuery.Token_list_v4
    ) {
        val bind = holder.bind
        bind.ivLogo.loadSvgOrImage(item.logo(), R.drawable.icon_token_default)
        bind.tvName.text = item.name()
        bind.tags.setChains(fromHomeVisitorQuery(item).chains, visitor = true)
        bind.ivOfficial.isInvisible = item.is_official() != true
        bind.tvSymbol.text = item.symbol()
        bind.tvPrice.text = "$ " + TokenUtil.displayPrice(item.current_price().toString())
        bind.root.setOnClickListener {
            onItemClick?.invoke(item)
        }

        holder.bind.executePendingBindings()
    }

    override fun onCreateViewHolder(
        inflater: LayoutInflater,
        parent: ViewGroup
    ): BaseBindVH<ItemVisitorTokenBinding> {
        return BaseBindVH(ItemVisitorTokenBinding.inflate(inflater, parent, false))
    }
}