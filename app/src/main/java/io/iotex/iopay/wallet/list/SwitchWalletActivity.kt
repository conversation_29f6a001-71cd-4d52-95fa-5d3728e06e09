package io.iotex.iopay.wallet.list

import android.content.Context
import android.content.Intent
import androidx.core.view.isVisible
import androidx.core.widget.addTextChangedListener
import androidx.recyclerview.widget.LinearLayoutManager
import com.blankj.utilcode.util.ActivityUtils
import com.drakeet.multitype.MultiTypeAdapter
import io.iotex.base.bindbase.BaseBindActivity
import io.iotex.iopay.MainActivity
import io.iotex.iopay.R
import io.iotex.iopay.data.UserStore
import io.iotex.iopay.data.db.Mnemonic
import io.iotex.iopay.data.db.Wallet
import io.iotex.iopay.databinding.ActivitySwitchWalletBinding
import io.iotex.iopay.setting.ManageActionActivity
import io.iotex.iopay.ui.ErrorPopupWindow
import io.iotex.iopay.ui.NoticePopupWindow
import io.iotex.iopay.ui.RemoveWarningDialog
import io.iotex.iopay.util.Config
import io.iotex.iopay.util.Constant
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.util.extension.contentEquals
import io.iotex.iopay.wallet.ViewPrivateKeyActivity
import io.iotex.iopay.wallet.add.WalletAddActivity
import io.iotex.iopay.wallet.add.WalletAddPrivateKeyActivity
import io.iotex.iopay.wallet.list.item.PrivateKeyWalletItemBinder
import io.iotex.iopay.wallet.list.item.RecoveryItemBinder
import org.jetbrains.anko.startActivity

class SwitchWalletActivity :
    BaseBindActivity<SwitchWalletViewModel, ActivitySwitchWalletBinding>(R.layout.activity_switch_wallet) {

    private val mAdapter = MultiTypeAdapter()

    companion object {
        private const val KEY_MANAGER = "manager"
        private const val KEY_IO_WALLET = "ioWallet"
        private const val KEY_IO_ADDRESS = "ioAddress"
        private var ioBack:((wallet: Wallet)->Unit)? = null
        fun startActivity(context: Context, manager: Boolean = false, ioWallet:Boolean = false, ioAddress:String = "", ioBack:((wallet: Wallet)->Unit)? = null) {
            val intent = Intent(context, SwitchWalletActivity::class.java)
            intent.putExtra(KEY_MANAGER, manager)
            intent.putExtra(KEY_IO_WALLET, ioWallet)
            intent.putExtra(KEY_IO_ADDRESS, ioAddress)
            this.ioBack = ioBack
            context.startActivity(intent)
        }
    }

    private val manager by lazy {
        intent.getBooleanExtra(KEY_MANAGER, false)
    }

    private val ioWallet by lazy {
        intent.getBooleanExtra(KEY_IO_WALLET, false)
    }

    private val ioAddress by lazy {
        intent.getStringExtra(KEY_IO_ADDRESS)
    }

    private val recoveryItemBinder by lazy {
        RecoveryItemBinder(manager, ioWallet,ioAddress)
            .apply {
                onSelectRecoveryWallet = {
                    if (manager) {
                        viewPrivateKey(it)
                    } else {
                        changeWallet(it)
                    }
                }

                onDeleteRecoveryWallet = {
                    NoticePopupWindow(
                        getString(R.string.notice),
                        getString(R.string.delete_wallet_confirm, it.alias),
                        R.drawable.icon_tips_notice,
                        {
                            mViewModel.deleteWallet(it)
                        }
                    ).show()
                }
            }
    }

    override fun initView() {
        mViewModel.ioWallet = ioWallet
        mBinding.ivSetting.isVisible = !manager && !ioWallet
        mBinding.tvAddWallet.isVisible = !ioWallet
        mBinding.llIoWarning.isVisible = ioWallet
        mBinding.tvToolbarTitle.text = getString(R.string.wallets)
        mBinding.ivBack.setOnClickListener {
            finish()
        }

        mBinding.tvAddWallet.setOnClickListener {
            if (mViewModel.recoveryLiveData.value.isNullOrEmpty()) {
                WalletAddActivity.startActivity(this)
            } else {
                WalletAddPrivateKeyActivity.startActivity(this)
            }
        }

        mBinding.ivSetting.setOnClickListener {
            startActivity<ManageActionActivity>()
        }

        mBinding.etSearch.addTextChangedListener {
            mViewModel.search = it.toString()
            recoveryItemBinder.showAll = it.toString().isNotEmpty()
            mBinding.ivDelete.isVisible = it.toString().isNotEmpty()
            mViewModel.getPrivateKeyAndAAWallet()
            mViewModel.getRecoveryWallet()
        }

        mBinding.ivDelete.setOnClickListener {
            mBinding.etSearch.setText("")
        }

        mAdapter.register(Wallet::class.java, PrivateKeyWalletItemBinder(manager, ioWallet, ioAddress)
            .apply {
                onSelectWallet = {
                    if (manager) {
                        viewPrivateKey(it)
                    } else {
                        changeWallet(it)
                    }
                }

                onDeleteWallet = {
                    RemoveWarningDialog(getString(R.string.delete_wallet_confirm, it.alias))
                        .apply {
                            onConfirmClick = {
                                dismiss()
                                mViewModel.deleteWallet(it)
                            }
                        }.show(supportFragmentManager, System.currentTimeMillis().toString())
                }
            })

        mAdapter.register(Mnemonic::class.java, recoveryItemBinder)

        mBinding.recyclerView.layoutManager = LinearLayoutManager(this)
        mBinding.recyclerView.adapter = mAdapter

    }

    private fun viewPrivateKey(wallet: Wallet) {
        if (wallet.isWatch) {
            WalletHelper.showWatchAddress(this)
            return
        }
        if (wallet.isAAWallet()) {
            ErrorPopupWindow(
                this,
                getString(R.string.error),
                getString(R.string.not_supporting_action)
            ) {
            }.show()
            return
        }
        ViewPrivateKeyActivity.start(this, wallet)
    }

    private fun changeWallet(wallet: Wallet) {
        if (ioBack != null) {
            ioBack?.invoke(wallet)
            finish()
            return
        }
        WalletHelper.checkWalletDialog(wallet){
            WalletHelper.switchWallet(wallet)
            finish()
        }
    }

    override fun onResume() {
        super.onResume()
        mViewModel.getPrivateKeyAndAAWallet()
        mViewModel.getRecoveryWallet()
    }

    override fun initData() {
        mViewModel.walletLiveData.observe(this) {
            val list = ArrayList<Any>()
            list.addAll(it)
            list.addAll(mViewModel.recoveryLiveData.value ?: emptyList())
            val sortList = sortWallet(list)
            if(!mAdapter.items.contentEquals(sortList)){
                mAdapter.items = sortWallet(list)
                mAdapter.notifyDataSetChanged()
            }
        }

        mViewModel.recoveryLiveData.observe(this) { mnemonics->
            val list = ArrayList<Any>()
            list.addAll(mViewModel.walletLiveData.value ?: emptyList())
            mnemonics?.let {
                list.addAll(it)
            }
            mAdapter.items = sortWallet(list)
            mAdapter.notifyDataSetChanged()
        }

        mViewModel.emptyLiveData.observe(this){
            UserStore.setWalletAddress("")
            Constant.currentWallet = null
            UserStore.setChainId(Config.IOTEX_CHAIN_ID)
            ActivityUtils.getActivityList().forEach { it?.finish() }
            MainActivity.startActivity(this, isVisitor = true)
        }
    }

    private fun sortWallet(wallets: ArrayList<Any>): List<Any> {
        val sortWallets = wallets.sortedWith { l1, l2 ->
            getWalletOrder(l1) - getWalletOrder(l2)
        }
        return sortWallets
    }

    private fun getWalletOrder(any: Any): Int {
        return when (any) {
            is Mnemonic -> {
                any.walletList?.firstOrNull()?.order ?: 0
            }

            is Wallet -> {
                any.order
            }

            else -> {
                0
            }
        }
    }

}