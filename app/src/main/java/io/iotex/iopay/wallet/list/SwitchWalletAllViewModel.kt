package io.iotex.iopay.wallet.list

import android.app.Application
import androidx.lifecycle.MutableLiveData
import com.blankj.utilcode.util.Utils
import io.iotex.iopay.base.BaseLaunchVM
import io.iotex.iopay.data.db.AppDatabase
import io.iotex.iopay.data.db.Wallet
import io.iotex.iopay.repo.NativeTokenRepo
import io.iotex.iopay.util.WalletHelper

class SwitchWalletAllViewModel(application: Application) : BaseLaunchVM(application) {
    val walletLiveData = MutableLiveData<List<Wallet>>()

    private val nativeTokenRepo by lazy {
        NativeTokenRepo()
    }

    fun getAllWallet(update: Boolean = true) {
        addLaunch {
            val wallets = AppDatabase.getInstance(Utils.getApp()).walletDao()
                .queryAllWallets()
            wallets.forEach {
                if (WalletHelper.isBitcoinNetwork()) {
                    it.bitcoinWallets = AppDatabase.getInstance(Utils.getApp()).bitcoinWalletDao()
                        .queryByEvmAddress(it.address, WalletHelper.getCurChainId())
                }
                if (WalletHelper.isSolanaNetwork()) {
                    it.solanaWallet = AppDatabase.getInstance(Utils.getApp()).solanaWalletDao()
                        .queryByAddressEvm(it.address)
                }
                WalletHelper.generateSolanaWallet(it)
                val walletCache = AppDatabase.getInstance(Utils.getApp()).walletCacheDao()
                    .queryWalletCache(it.getCurNetworkAddress(), WalletHelper.getCurChainId())
                it.curBalance = walletCache?.balance ?: "0"
            }
            walletLiveData.postValue(wallets)
            if (update) updateWalletBalance(wallets)
        }
    }

    private fun updateWalletBalance(wallets: List<Wallet>) {
        wallets.map { wallet ->
            val network = WalletHelper.getCurNetwork()
            addLaunch {
                val balance = nativeTokenRepo.getNetworkTokenBalance(network,wallet)
                if (wallet.curBalance != balance.toString()) {
                    getAllWallet(false)
                }
            }
        }
    }

}