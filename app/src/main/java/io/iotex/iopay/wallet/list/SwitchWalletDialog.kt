package io.iotex.iopay.wallet.list

import android.content.res.Configuration
import android.view.Gravity
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.blankj.utilcode.util.ScreenUtils
import com.blankj.utilcode.util.Utils
import com.drakeet.multitype.MultiTypeAdapter
import io.iotex.base.bindbase.BaseBindDialog
import io.iotex.iopay.R
import io.iotex.iopay.data.UserStore
import io.iotex.iopay.data.db.AppDatabase
import io.iotex.iopay.data.db.BitcoinWallet
import io.iotex.iopay.data.db.Wallet
import io.iotex.iopay.databinding.DialogSwitchWalletBinding
import io.iotex.iopay.support.eventbus.SwitchWalletEvent
import io.iotex.iopay.util.Constant
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.wallet.bitcoin.BitcoinBookBinder
import io.iotex.iopay.wallet.list.item.SwitchWalletBinder
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.greenrobot.eventbus.EventBus

class SwitchWalletDialog(private val supportAA: Boolean = true) :
    BaseBindDialog<SwitchWalletAllViewModel, DialogSwitchWalletBinding>(R.layout.dialog_switch_wallet) {

    private val mAdapter = MultiTypeAdapter()
    var onSwitchWallet: ((oldAddress: String, address: String) -> Unit)? = null

    override fun onStart() {
        super.onStart()
        val attributes = dialog?.window?.attributes
        val orientation = resources.configuration.orientation
        if (orientation == Configuration.ORIENTATION_LANDSCAPE) {
            attributes?.width = ScreenUtils.getScreenWidth()
            attributes?.height = ScreenUtils.getScreenWidth()
        } else {
            attributes?.width = ScreenUtils.getScreenWidth()
            attributes?.height = (ScreenUtils.getScreenHeight() * 0.6).toInt()
        }
        attributes?.gravity = Gravity.BOTTOM
        dialog?.window?.attributes = attributes
    }

    override fun initView() {
        mBinding.recyclerView.layoutManager = LinearLayoutManager(context)
        mBinding.recyclerView.adapter = mAdapter
        if (WalletHelper.isBitcoinNetwork()) {
            val oldBitcoinWallet = Constant.currentWallet?.getBitcoinWallet()
            mAdapter.register(Wallet::class.java, BitcoinBookBinder().apply {
                onSelectWallet = { _, bitcoinWallet ->
                    switchBitcoinWallet(bitcoinWallet)
                    if (oldBitcoinWallet != null) {
                        onSwitchWallet?.invoke(oldBitcoinWallet.bitcoinAddress, bitcoinWallet.bitcoinAddress)
                    }
                }
            })
        } else {
            val oldWallet = Constant.currentWallet
            mAdapter.register(SwitchWalletBinder().apply {
                onSelectWallet = {
                    WalletHelper.checkWalletDialog(it){
                        WalletHelper.switchWallet(it)
                        if (oldWallet != null) {
                            onSwitchWallet?.invoke(oldWallet.address, it.address)
                        }
                        dismiss()
                    }
                }
            })
        }
    }

    private fun switchBitcoinWallet(bitcoinWallet: BitcoinWallet) {
        lifecycleScope.launch {
            withContext(Dispatchers.IO) {
                if (bitcoinWallet.evmAddress != UserStore.getWalletAddress()) {
                    UserStore.setWalletAddress(bitcoinWallet.evmAddress)
                }
                val wallet = WalletHelper.getCurWallet()
                if (wallet != null) {
                    wallet.addressType = bitcoinWallet.addressType
                    Constant.currentWallet = wallet
                    AppDatabase.getInstance(Utils.getApp()).walletDao().updateWallet(wallet)
                    EventBus.getDefault().post(SwitchWalletEvent())
                }
            }
            dismiss()
        }
    }

    override fun initData() {
        mViewModel.getAllWallet()
        mViewModel.walletLiveData.observe(this) {
            val walletList = if (!supportAA) {
                it.filter { wallet ->
                    !wallet.isAAWallet()
                }
            } else it
            mAdapter.items = walletList
            mAdapter.notifyDataSetChanged()
        }
    }

}