package io.iotex.iopay.wallet.list

import android.app.Application
import androidx.lifecycle.MutableLiveData
import com.blankj.utilcode.util.Utils
import io.iotex.iopay.base.BaseLaunchVM
import io.iotex.iopay.data.UserStore
import io.iotex.iopay.data.db.AppDatabase
import io.iotex.iopay.data.db.Mnemonic
import io.iotex.iopay.data.db.Wallet
import io.iotex.iopay.repo.NativeTokenRepo
import io.iotex.iopay.util.Config
import io.iotex.iopay.util.Config.IOTEX_CHAIN_ID
import io.iotex.iopay.util.Constant
import io.iotex.iopay.util.TokenUtil
import io.iotex.iopay.util.WalletHelper
import org.jetbrains.anko.collections.forEachReversedWithIndex
import java.math.BigDecimal

class SwitchWalletViewModel(application: Application) : BaseLaunchVM(application) {
    val walletLiveData = MutableLiveData<List<Wallet>>()
    val recoveryLiveData = MutableLiveData<List<Mnemonic>?>()
    val emptyLiveData = MutableLiveData<Boolean>()
    private val tokenRepo by lazy { NativeTokenRepo() }
    var ioWallet = false
    var search = ""

    fun getPrivateKeyAndAAWallet(update: Boolean = true) {
        addLaunch {
            val wallets = AppDatabase.getInstance(Utils.getApp()).walletDao()
                .searchPrivateKeyAndAAWallets(search).filterNot {
                    if (ioWallet) {
                        it.isAAWallet() || it.isWatch || it.isSolanaPrivateWallet()
                    } else {
                        false
                    }
                }

            wallets.forEach {
                if (WalletHelper.isBitcoinNetwork()) {
                    it.bitcoinWallets = AppDatabase.getInstance(Utils.getApp()).bitcoinWalletDao()
                        .queryByEvmAddress(it.address, WalletHelper.getCurChainId())
                }  else {
                    it.bitcoinWallets =
                        AppDatabase.getInstance(Utils.getApp()).bitcoinWalletDao()
                            .queryByEvmAddress(it.address, Config.BITCOIN_MAIN_CHAIN_ID)
                }
                it.solanaWallet = AppDatabase.getInstance(Utils.getApp()).solanaWalletDao()
                    .queryByAddressEvm(it.address)
                val address = if (ioWallet) it.address else it.getCurNetworkAddress()
                val chainId = if(ioWallet) IOTEX_CHAIN_ID else WalletHelper.getCurChainId()
                val walletCache = AppDatabase.getInstance(Utils.getApp()).walletCacheDao()
                    .queryWalletCache(address, chainId)
                it.curBalance = walletCache?.balance ?: "0"
            }

            if (UserStore.getAllNetwork()) {
                wallets.forEach { wallet ->
                    tokenRepo.getAllNetworkToken(
                        wallet = wallet,
                        callBackTotal = { balance ->
                            if(!UserStore.getAllNetwork())return@getAllNetworkToken
                            wallet.totalBalance = TokenUtil.displayPrice(balance.toString())
                            walletLiveData.postValue(wallets)
                        })
                }
            }else{
                walletLiveData.postValue(wallets)
            }
            if (update) updateWalletBalance(wallets)
        }
    }

    private fun updateWalletBalance(wallets: List<Wallet>) {

        wallets.map { wallet ->
            val chainId = if(ioWallet) IOTEX_CHAIN_ID else WalletHelper.getCurChainId()
            val network = AppDatabase.getInstance(Utils.getApp()).rpcNetworkDao().queryRPCNetworkByChainId(chainId)
            addLaunch {
                val balance = tokenRepo.getNetworkTokenBalance(network,wallet)
                if (wallet.curBalance != balance.toString()) {
                    if (wallet.isMnemonicWallet()) {
                        getRecoveryWallet(false)
                    } else {
                        getPrivateKeyAndAAWallet(false)
                    }
                }
            }
        }
    }

    fun getRecoveryWallet(update: Boolean = true) {
        addLaunch {
            val list = AppDatabase.getInstance(Utils.getApp()).mnemonicDao().queryAll()
            val searchList = ArrayList<Mnemonic>()
            list.forEach {
                val walletList =
                    AppDatabase.getInstance(Utils.getApp()).walletDao().queryByMnemonic(it.id)
                if(walletList.isEmpty()){
                    AppDatabase.getInstance(Utils.getApp()).mnemonicDao().delete(it)
                    return@forEach
                }
                val searchWallets = walletList.filter { wallet->
                    wallet.alias.contains(search,true) ||wallet.address.contains(search,true)
                }
                searchWallets.forEach { wallet ->
                    if (WalletHelper.isBitcoinNetwork()) {
                        wallet.bitcoinWallets =
                            AppDatabase.getInstance(Utils.getApp()).bitcoinWalletDao()
                                .queryByEvmAddress(wallet.address, WalletHelper.getCurChainId())
                    } else {
                        wallet.bitcoinWallets =
                            AppDatabase.getInstance(Utils.getApp()).bitcoinWalletDao()
                                .queryByEvmAddress(wallet.address, Config.BITCOIN_MAIN_CHAIN_ID)
                    }
                    wallet.solanaWallet =
                        AppDatabase.getInstance(Utils.getApp()).solanaWalletDao()
                            .queryByAddressEvm(wallet.address)
                    val address = if (ioWallet) wallet.address else wallet.getCurNetworkAddress()
                    val chainId = if(ioWallet) IOTEX_CHAIN_ID else WalletHelper.getCurChainId()
                    val walletCache = AppDatabase.getInstance(Utils.getApp()).walletCacheDao()
                        .queryWalletCache(address, chainId)
                    wallet.curBalance = walletCache?.balance ?: "0"
                }
                it.walletList = searchWallets
                if(searchWallets.isNotEmpty()){
                    searchList.add(it)
                }
                if (update) updateWalletBalance(walletList)
            }
            if (UserStore.getAllNetwork()) {
                searchList.forEach {
                    var totalBalance = BigDecimal.ZERO
                    it.walletList?.forEach { wallet ->
                        tokenRepo.getAllNetworkToken(
                            wallet = wallet,
                            callBackTotal = { balance ->
                                if(!UserStore.getAllNetwork())return@getAllNetworkToken
                                totalBalance += balance
                                wallet.totalBalance =
                                    TokenUtil.displayPrice(balance.toString())
                                it.totalBalance =
                                    TokenUtil.displayPrice(totalBalance.toString())
                                recoveryLiveData.postValue(searchList)
                            })
                    }
                }
            }else {
                recoveryLiveData.postValue(searchList)
            }
        }
    }

    fun deleteWallet(wallet: Wallet) {
        addLaunch {
            AppDatabase.getInstance(Utils.getApp()).bitcoinWalletDao()
                .queryByEvmAddress(wallet.address, WalletHelper.getCurChainId())
                .forEach {
                    AppDatabase.getInstance(Utils.getApp()).bitcoinWalletDao().deleteWallet(it)
                }
            AppDatabase.getInstance(Utils.getApp()).walletDao()
                .queryWalletByAddress(wallet.address)
                ?.let {
                    AppDatabase.getInstance(Utils.getApp()).walletDao().deleteWallet(it)
                }
            if (wallet.isMnemonicWallet()) {
                val walletList = AppDatabase.getInstance(Utils.getApp()).walletDao()
                    .queryByMnemonic(wallet.mnemonicId)
                if (walletList.isEmpty()) {
                    val mnemonic = AppDatabase.getInstance(Utils.getApp()).mnemonicDao()
                        .queryByMnemonicId(wallet.mnemonicId)
                    mnemonic?.let {
                        AppDatabase.getInstance(Utils.getApp()).mnemonicDao().delete(it)
                    }
                }
            }
            if (wallet.address == Constant.currentWallet?.address) {
                val walletList = AppDatabase.getInstance(Utils.getApp()).walletDao()
                    .queryAllWallets()
                if (walletList.isNotEmpty()) {
                    var changeWallet = walletList[0]
                    walletList.forEachReversedWithIndex { _, wallet ->
                        if (WalletHelper.isIoTexNetWork()) {
                            if (!wallet.isWatch) {
                                changeWallet = wallet
                            }
                        } else {
                            if (!wallet.isWatch && !wallet.isAAWallet()) {
                                changeWallet = wallet
                            }
                        }
                    }
                    WalletHelper.switchWallet(changeWallet)
                } else {
                    emptyLiveData.postValue(true)
                }
            }
            if (wallet.isMnemonicWallet()) {
                getRecoveryWallet()
            } else {
                getPrivateKeyAndAAWallet()
            }
        }
    }

}