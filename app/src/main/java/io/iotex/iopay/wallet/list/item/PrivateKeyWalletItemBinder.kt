package io.iotex.iopay.wallet.list.item

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import com.blankj.utilcode.util.ClipboardUtils
import com.blankj.utilcode.util.ColorUtils
import com.blankj.utilcode.util.ToastUtils
import com.blankj.utilcode.util.Utils
import com.daimajia.swipe.SimpleSwipeListener
import com.daimajia.swipe.SwipeLayout
import com.drakeet.multitype.ItemViewBinder
import io.iotex.base.bindbase.BaseBindVH
import io.iotex.iopay.R
import io.iotex.iopay.data.UserStore
import io.iotex.iopay.data.db.Wallet
import io.iotex.iopay.databinding.ItemPrivateKeyWalletBinding
import io.iotex.iopay.util.FireBaseEvent
import io.iotex.iopay.util.FireBaseUtil
import io.iotex.iopay.util.IoPayConstant
import io.iotex.iopay.util.TokenUtil
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.util.extension.fromSatoshis
import io.iotex.iopay.util.extension.loadSvgOrImage
import io.iotex.iopay.util.extension.toIoAddress
import io.iotex.iopay.wallet.aawallet.dialog.AAReceiveDialog.showAAReceiveCopy
import io.iotex.iopay.wallet.add.NameWalletActivity

class PrivateKeyWalletItemBinder(private val manager: Boolean, private val ioWallet:Boolean,val ioAddress:String?) :
    ItemViewBinder<Wallet, BaseBindVH<ItemPrivateKeyWalletBinding>>() {

    var onSelectWallet: ((Wallet) -> Unit)? = null
    var onDeleteWallet: ((Wallet) -> Unit)? = null

    private var lastSwipeLayout:SwipeLayout? = null
    override fun onCreateViewHolder(
        inflater: LayoutInflater,
        parent: ViewGroup
    ): BaseBindVH<ItemPrivateKeyWalletBinding> {
        val bind = ItemPrivateKeyWalletBinding.inflate(inflater, parent, false)
        return BaseBindVH(bind)
    }

    override fun onBindViewHolder(holder: BaseBindVH<ItemPrivateKeyWalletBinding>, item: Wallet) {
        val bind = holder.bind
        bind.swipeLayout.addSwipeListener(object : SimpleSwipeListener() {
            override fun onOpen(layout: SwipeLayout?) {
                if (lastSwipeLayout != holder.bind.swipeLayout) {
                    lastSwipeLayout?.close(true)
                    lastSwipeLayout = holder.bind.swipeLayout
                }
            }
        })
        checkColor(bind, item)
        bind.swipeLayout.isSwipeEnabled = manager
        bind.ivEdit.isVisible = manager
        bind.ivExpired.isVisible = item.isAAWallet() && !item.effective
        bind.walletAvatar.loadSvgOrImage(item.avatar, R.drawable.icon_wallet_default)
        bind.walletName.text = item.alias

        val address: String
        val balance: String

        if (ioWallet) {
            address = item.address.toIoAddress()
            val value = TokenUtil.weiToTokenBN(item.curBalance)
            balance = TokenUtil.displayBalance(value)
        } else if (WalletHelper.isBitcoinNetwork()) {
            address = item.getBitcoinWallet()?.bitcoinAddress ?: item.address
            val value = item.curBalance.toBigIntegerOrNull()?.fromSatoshis() ?: "0"
            balance = TokenUtil.displayBalance(value)
        } else if (WalletHelper.isSolanaNetwork()) {
            address = item.solanaWallet?.publicKeyBase58 ?: item.address
            val value =
                TokenUtil.weiToTokenBN(item.curBalance, UserStore.getNetworkDecimals().toLong())
            balance = TokenUtil.displayBalance(value)
        } else {
            address = WalletHelper.formatWalletAddress(item.address)
            val value = TokenUtil.weiToTokenBN(item.curBalance)
            balance = TokenUtil.displayBalance(value)
        }

        val pre = if (item.isSolanaPrivateWallet()) {
            "Solana:"
        } else if (item.isEvmPrivateWallet()) {
            "EVM:"
        } else {
            ""
        }
        bind.walletAddress.text = pre + TokenUtil.textEllipsis(address, 6, 8)
        if (item.isWatch) {
            bind.tvType.setText(R.string.watch_mode)
            bind.tvType.setTextColor(ColorUtils.getColor(R.color.color_00dc9c))
            bind.tvType.setBackgroundResource(R.drawable.shape_watch_back_r2)
        } else if (item.isAAWallet()) {
            bind.tvType.setText(R.string.aa_wallet)
            bind.tvType.setTextColor(ColorUtils.getColor(R.color.color_855eff))
            bind.tvType.setBackgroundResource(R.drawable.shape_aa_back_r2)
        } else {
            bind.tvType.setText(R.string.private_key_wallet)
            bind.tvType.setTextColor(ColorUtils.getColor(R.color.color_ffcd29))
            bind.tvType.setBackgroundResource(R.drawable.shape_wallet_pk_back_r2)
        }

        val nativeCurrency = if (ioWallet) IoPayConstant.IOTX else UserStore.getNetworkSymbol()

        if (UserStore.getAllNetwork()) {
            bind.walletAmount.text = "$"+item.totalBalance
        } else {
            bind.walletAmount.text = "$balance $nativeCurrency"
        }

        bind.ivCopy.setOnClickListener {
            ClipboardUtils.copyText(address)
            if (item.isAAWallet()) {
                showAAReceiveCopy()
            } else {
                ToastUtils.showShort(R.string.copy_success)
            }
        }

        bind.ivEdit.setOnClickListener {
            NameWalletActivity.start(
                bind.root.context,
                NameWalletActivity.ACTION_RENAME,
                item.alias,
                title = Utils.getApp().getString(R.string.edit_wallet_name)
            )
            if(item.isAAWallet())
                FireBaseUtil.logFireBase(FireBaseEvent.ACTION_SETTINGS_MANAGE_WALLETS_MANAGE_WALLET_TAB_AA_NAME_EDIT)
            else
                FireBaseUtil.logFireBase(FireBaseEvent.ACTION_SETTINGS_MANAGE_WALLETS_MANAGE_WALLET_TAB_PRIVATE_KEY_NAME_EDIT)
        }

        bind.llDelete.setOnClickListener {
            onDeleteWallet?.invoke(item)
        }

        bind.llContent.setOnClickListener {
            onSelectWallet?.invoke(item)
        }

    }

    private fun checkColor(bind: ItemPrivateKeyWalletBinding, item: Wallet) {
        val checkAddress = if (ioWallet) ioAddress else UserStore.getWalletAddress()
        if (item.address == checkAddress) {
            bind.llContent.setBackgroundColor(ContextCompat.getColor(bind.llContent.context, R.color.color_card_back))
        } else {
            bind.llContent.setBackgroundColor(ContextCompat.getColor(bind.llContent.context, R.color.transparent))
        }
    }

}