package io.iotex.iopay.wallet.list.item

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.LinearLayoutManager
import com.drakeet.multitype.ItemViewBinder
import com.drakeet.multitype.MultiTypeAdapter
import io.iotex.base.bindbase.BaseBindVH
import io.iotex.iopay.R
import io.iotex.iopay.data.UserStore
import io.iotex.iopay.data.db.Mnemonic
import io.iotex.iopay.data.db.Wallet
import io.iotex.iopay.databinding.ItemRecoveryBinding
import io.iotex.iopay.util.FireBaseEvent
import io.iotex.iopay.util.FireBaseUtil
import io.iotex.iopay.util.IoPayConstant
import io.iotex.iopay.util.TokenUtil
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.util.extension.fromSatoshis
import io.iotex.iopay.util.extension.setGone
import io.iotex.iopay.util.extension.setVisible
import io.iotex.iopay.wallet.add.NameWalletActivity
import java.math.BigDecimal
import java.math.BigInteger

class RecoveryItemBinder(private val manager: Boolean, private val ioWallet:Boolean,private val ioAddress:String?) :
    ItemViewBinder<Mnemonic, BaseBindVH<ItemRecoveryBinding>>() {

    var onSelectRecoveryWallet: ((Wallet) -> Unit)? = null
    var onDeleteRecoveryWallet: ((Wallet) -> Unit)? = null
    var showAll:Boolean = false

    override fun onCreateViewHolder(
        inflater: LayoutInflater,
        parent: ViewGroup
    ): BaseBindVH<ItemRecoveryBinding> {
        val bind = ItemRecoveryBinding.inflate(inflater, parent, false)
        return BaseBindVH(bind)
    }

    override fun onBindViewHolder(holder: BaseBindVH<ItemRecoveryBinding>, item: Mnemonic) {
        val bind = holder.bind
        bind.walletName.text = item.name

        val nativeCurrency = if (ioWallet) IoPayConstant.IOTX else UserStore.getNetworkSymbol()
        if (ioWallet || showAll) showWallets(bind) else hideWallets(bind)

        if (UserStore.getAllNetwork()) {
            var totalBalance = BigDecimal.ZERO
            item.walletList?.forEach {
                if (it.address == UserStore.getWalletAddress()) {
                    showWallets(bind)
                }
                totalBalance = totalBalance.add(it.totalBalance.toBigDecimalOrNull() ?: BigDecimal.ZERO)
            }
            bind.walletAmount.text = "$${totalBalance}"
        } else {
            var totalBalance = BigInteger.ZERO
            item.walletList?.forEach {
                if (it.address == UserStore.getWalletAddress()) {
                    showWallets(bind)
                }
                totalBalance = totalBalance.add(it.curBalance.toBigIntegerOrNull() ?: BigInteger.ZERO)
            }
            val value = if (WalletHelper.isBitcoinNetwork()) {
                totalBalance.fromSatoshis()
            } else {
                TokenUtil.weiToTokenBN(totalBalance.toString(), UserStore.getNetworkDecimals().toLong())
            }
            val balance = TokenUtil.displayBalance(value)
            bind.walletAmount.text = "$balance $nativeCurrency"
        }

        bind.llContent.setOnClickListener {
            if (bind.recyclerView.visibility == View.GONE) {
                showWallets(bind)
            } else {
                hideWallets(bind)
            }
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_WALLET_RECOVERY_PHRASE_EXPAND)
        }

        val mAdapter = MultiTypeAdapter()
        mAdapter.register(
            Wallet::class.java, RecoveryWalletItemBinder(manager, ioWallet, ioAddress)
                .apply {
                    onSelectWallet = {
                        onSelectRecoveryWallet?.invoke(it)
                    }
                    onDeleteWallet = {
                        onDeleteRecoveryWallet?.invoke(it)
                    }
                })
        bind.recyclerView.layoutManager = LinearLayoutManager(bind.recyclerView.context)
        bind.recyclerView.adapter = mAdapter
        item.walletList?.let {
            mAdapter.items = it
            mAdapter.notifyDataSetChanged()
        }


        bind.llAdd.setOnClickListener {
            NameWalletActivity.start(
                bind.llAdd.context,
                NameWalletActivity.CREATE_VIA_MNEMONIC,
                item.encryptedMnemonic
            )
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_WALLET_RECOVERY_PHRASE_NEW_WALLET)
        }
    }

    private fun showWallets(bind: ItemRecoveryBinding) {
        bind.recyclerView.setVisible()
        if (ioWallet){
            bind.llAdd.setGone()
        } else {
            bind.llAdd.setVisible()
        }
        bind.ivArrow.setImageResource(R.drawable.icon_recovery_up)
    }

    private fun hideWallets(bind: ItemRecoveryBinding) {
        bind.recyclerView.setGone()
        bind.llAdd.setGone()
        bind.ivArrow.setImageResource(R.drawable.icon_recovery_down)
    }

}