package io.iotex.iopay.wallet.list.item

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import com.blankj.utilcode.util.ClipboardUtils
import com.blankj.utilcode.util.ToastUtils
import com.blankj.utilcode.util.Utils
import com.daimajia.swipe.SimpleSwipeListener
import com.daimajia.swipe.SwipeLayout
import com.drakeet.multitype.ItemViewBinder
import io.iotex.base.bindbase.BaseBindVH
import io.iotex.iopay.R
import io.iotex.iopay.data.UserStore
import io.iotex.iopay.data.db.Wallet
import io.iotex.iopay.databinding.ItemRecoveryWalletBinding
import io.iotex.iopay.util.FireBaseEvent
import io.iotex.iopay.util.FireBaseUtil
import io.iotex.iopay.util.IoPayConstant
import io.iotex.iopay.util.TokenUtil
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.util.extension.fromSatoshis
import io.iotex.iopay.util.extension.loadSvgOrImage
import io.iotex.iopay.util.extension.toIoAddress
import io.iotex.iopay.wallet.aawallet.dialog.AAReceiveDialog.showAAReceiveCopy
import io.iotex.iopay.wallet.add.NameWalletActivity

class RecoveryWalletItemBinder(val manager: Boolean, private val ioWallet:Boolean, private val ioAddress:String?) :
    ItemViewBinder<Wallet, BaseBindVH<ItemRecoveryWalletBinding>>() {

    var onSelectWallet: ((Wallet) -> Unit)? = null
    var onDeleteWallet: ((Wallet) -> Unit)? = null

    private var lastSwipeLayout:SwipeLayout? = null
    override fun onCreateViewHolder(
        inflater: LayoutInflater,
        parent: ViewGroup
    ): BaseBindVH<ItemRecoveryWalletBinding> {
        val bind = ItemRecoveryWalletBinding.inflate(inflater, parent, false)
        return BaseBindVH(bind)
    }

    override fun onBindViewHolder(holder: BaseBindVH<ItemRecoveryWalletBinding>, item: Wallet) {
        val bind = holder.bind
        checkColor(bind, item)
        bind.swipeLayout.addSwipeListener(object : SimpleSwipeListener() {
            override fun onOpen(layout: SwipeLayout?) {
                if (lastSwipeLayout != holder.bind.swipeLayout) {
                    lastSwipeLayout?.close(true)
                    lastSwipeLayout = holder.bind.swipeLayout
                }
            }
        })
        bind.swipeLayout.isSwipeEnabled = manager
        bind.ivEdit.isVisible = manager
        bind.walletAvatar.loadSvgOrImage(item.avatar, R.drawable.icon_wallet_default)
        bind.walletName.text = item.alias

        val address =
            if (ioWallet) item.address.toIoAddress() else WalletHelper.formatWalletAddress(item.getCurNetworkAddress())
        val balance: String = if (ioWallet) {
            val value = TokenUtil.weiToTokenBN(item.curBalance)
            TokenUtil.displayBalance(value)
        } else if (WalletHelper.isBitcoinNetwork()) {
            item.curBalance.toBigIntegerOrNull()?.fromSatoshis() ?: "0"
        } else {
            val value = TokenUtil.weiToTokenBN(item.curBalance, UserStore.getNetworkDecimals().toLong())
            TokenUtil.displayBalance(value)
        }
        bind.walletAddress.text = TokenUtil.textEllipsis(address, 6, 8)
        val nativeCurrency = if (ioWallet) IoPayConstant.IOTX else UserStore.getNetworkSymbol()
        if (UserStore.getAllNetwork()) {
            bind.walletAmount.text = "$"+item.totalBalance
        } else {
            bind.walletAmount.text = "${TokenUtil.displayBalance(balance)} $nativeCurrency"
        }

        bind.ivCopy.setOnClickListener {
            ClipboardUtils.copyText(address)
            if (item.isAAWallet()) {
                showAAReceiveCopy()
            } else {
                ToastUtils.showShort(R.string.copy_success)
            }
        }

        bind.ivEdit.setOnClickListener {
            NameWalletActivity.start(
                bind.root.context,
                NameWalletActivity.ACTION_RENAME,
                item.alias,
                title = Utils.getApp().getString(R.string.edit_wallet_name)
            )
            FireBaseUtil.logFireBase(FireBaseEvent.NAVIGATION_TO_EDIT_WALLET)
        }

        bind.llDelete.setOnClickListener {
            onDeleteWallet?.invoke(item)
        }

        bind.llContent.setOnClickListener {
            onSelectWallet?.invoke(item)
        }

    }

    private fun checkColor(bind: ItemRecoveryWalletBinding, item: Wallet) {
        val checkAddress = if (ioWallet) ioAddress else UserStore.getWalletAddress()
        if (item.address == checkAddress) {
            bind.llContent.setBackgroundColor(ContextCompat.getColor(bind.llContent.context, R.color.color_card_back))
        } else {
            bind.llContent.setBackgroundColor(ContextCompat.getColor(bind.llContent.context, R.color.transparent))
        }
    }

}