package io.iotex.iopay.wallet.list.item

import android.view.LayoutInflater
import android.view.ViewGroup
import com.blankj.utilcode.util.ClipboardUtils
import com.blankj.utilcode.util.ToastUtils
import com.drakeet.multitype.ItemViewBinder
import io.iotex.base.bindbase.BaseBindVH
import io.iotex.iopay.R
import io.iotex.iopay.data.UserStore
import io.iotex.iopay.data.db.Wallet
import io.iotex.iopay.databinding.ItemSwitchWalletBinding
import io.iotex.iopay.util.TokenUtil
import io.iotex.iopay.util.extension.loadSvgOrImage
import io.iotex.iopay.util.extension.setGone
import io.iotex.iopay.util.extension.setVisible
import io.iotex.iopay.wallet.aawallet.dialog.AAReceiveDialog.showAAReceiveCopy

class SwitchWalletBinder:
    ItemViewBinder<Wallet, BaseBindVH<ItemSwitchWalletBinding>>() {
    var onSelectWallet: ((Wallet) -> Unit)? = null

    override fun onCreateViewHolder(
        inflater: LayoutInflater,
        parent: ViewGroup
    ): BaseBindVH<ItemSwitchWalletBinding> {
        val bind = ItemSwitchWalletBinding.inflate(inflater, parent, false)
        return BaseBindVH(bind)
    }

    override fun onBindViewHolder(holder: BaseBindVH<ItemSwitchWalletBinding>, item: Wallet) {
        val bind = holder.bind
        checkColor(bind, item)
        bind.walletAvatar.loadSvgOrImage(item.avatar, R.drawable.icon_wallet_default)
        bind.walletName.text = item.alias
        val address = item.getCurNetworkAddress()
        bind.walletAddress.text = TokenUtil.textEllipsis(address, 6, 8)
        if (item.isWatch) {
            bind.ivWatch.setVisible()
        } else {
            bind.ivWatch.setGone()
        }

        val nativeCurrency = UserStore.getNetworkSymbol()
        val value = TokenUtil.weiToTokenBN(item.curBalance, UserStore.getNetworkDecimals().toLong())
        val balance = TokenUtil.displayBalance(value)
        bind.walletAmount.text = "$balance $nativeCurrency"

        bind.ivCopy.setOnClickListener {
            ClipboardUtils.copyText(address)
            if (item.isAAWallet()) {
                showAAReceiveCopy()
            } else {
                ToastUtils.showShort(R.string.copy_success)
            }
        }

        bind.llContent.setOnClickListener {
            onSelectWallet?.invoke(item)
        }

    }

    private fun checkColor(bind: ItemSwitchWalletBinding, item: Wallet) {
        if (item.address == UserStore.getWalletAddress()) {
            bind.llContent.setBackgroundResource(R.drawable.shape_dialog_card_back_stroke_gradient)
        } else {
            bind.llContent.setBackgroundResource(R.drawable.shape_dialog_card_back)
        }
    }
}