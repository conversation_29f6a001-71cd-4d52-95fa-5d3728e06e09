package io.iotex.iopay.wallet.qrcode

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.provider.MediaStore
import android.view.KeyEvent
import android.view.View
import android.webkit.URLUtil
import com.blankj.utilcode.constant.PermissionConstants
import com.blankj.utilcode.util.PermissionUtils
import com.blankj.utilcode.util.ToastUtils
import com.google.zxing.BarcodeFormat
import com.google.zxing.ResultPoint
import com.google.zxing.client.android.BeepManager
import com.journeyapps.barcodescanner.BarcodeCallback
import com.journeyapps.barcodescanner.BarcodeResult
import com.journeyapps.barcodescanner.DecoratedBarcodeView
import com.journeyapps.barcodescanner.DefaultDecoderFactory
import com.machinefi.walletconnect2.WC2Helper
import io.iotex.base.bindbase.BaseBindActivity
import io.iotex.iopay.R
import io.iotex.iopay.base.BaseLaunchVM
import io.iotex.iopay.databinding.ActivityIoScanQrCodeBinding
import io.iotex.iopay.util.Constant
import io.iotex.iopay.util.IoPayConstant
import io.iotex.iopay.util.MediaUtil
import io.iotex.iopay.util.QRCodeUtil
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.wallet.TransferActivity
import io.iotex.iopay.xapp.XAppsActivity
import org.jetbrains.anko.doAsync
import org.jetbrains.anko.uiThread

class IoScanQRCodeActivity :
    BaseBindActivity<BaseLaunchVM, ActivityIoScanQrCodeBinding>(R.layout.activity_io_scan_qr_code) {

    private val beepManager by lazy {
        BeepManager(this)
    }

    private var lastContent = ""

    companion object {
        private const val CHOOSE_PHOTO = 100
        var onSuccess: ((String) -> Unit)? = null
        fun startActivity(context: Context, onScanSuccess: ((String) -> Unit)? = null) {
            PermissionUtils.permission(PermissionConstants.CAMERA)
                .callback(object : PermissionUtils.SimpleCallback {
                    override fun onGranted() {
                        onSuccess = onScanSuccess
                        context.startActivity(Intent(context, IoScanQRCodeActivity::class.java))
                    }

                    override fun onDenied() {
                        ToastUtils.showShort(R.string.need_permission)
                    }
                }).request()

        }
    }

    override fun initView() {
        val formats = listOf(BarcodeFormat.QR_CODE, BarcodeFormat.CODE_39)
        mBinding.barcodeView.barcodeView.decoderFactory = DefaultDecoderFactory(formats)
        mBinding.barcodeView.initializeFromIntent(intent)
        mBinding.barcodeView.decodeContinuous(object : BarcodeCallback {
            override fun barcodeResult(result: BarcodeResult?) {
                scanSuccess(result?.text)
            }

            override fun possibleResultPoints(resultPoints: MutableList<ResultPoint>?) {
                //nothing.
            }

        })

        mBinding.barcodeView.setStatusText(getString(R.string.prompt_point_at_a_barcode))
        mBinding.barcodeView.setTorchListener(object : DecoratedBarcodeView.TorchListener {
            override fun onTorchOn() {
                mBinding.ivFlash.setImageResource(R.drawable.ic_flash_on_vd_white_24)
                mBinding.ivFlash.tag = true
            }

            override fun onTorchOff() {
                mBinding.ivFlash.setImageResource(R.drawable.ic_flash_off_vd_white_24)
                mBinding.ivFlash.tag = false
            }

        })

        mBinding.ivClose.setOnClickListener {
            finish()
        }

        if (!hasFlash()) {
            mBinding.ivFlash.visibility = View.GONE
        }

        mBinding.ivFlash.setOnClickListener {
            switchFlashlight()
        }

        mBinding.ivImage.setOnClickListener {
            val intent = Intent(Intent.ACTION_PICK)
            intent.setDataAndType(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, "image/*")
            startActivityForResult(intent, CHOOSE_PHOTO)
        }

        mBinding.ivSelect.setOnClickListener {
            val content = mBinding.tvValue.text.toString()
            finish()
            if (onSuccess != null) {
                onSuccess?.invoke(content)
            } else {
                if (WalletHelper.isValidAddress(content)) {
                    Constant.currentWallet?.let {
                        TransferActivity.start(this, address = content)
                    }
                }

                if (URLUtil.isValidUrl(content)) {
                    val intent = Intent(this, XAppsActivity::class.java)
                    intent.putExtra(IoPayConstant.BROWSER_URL, content)
                    startActivity(intent)
                }

                if (WC2Helper.isValidWc2Bridge(content)) {
                    WC2Helper.pair(content,"","")
                }

                if (WalletHelper.isEvmTransferLink(content)) {
                    TransferActivity.startByLink(this, content)
                }
            }
        }
    }

    override fun onResume() {
        super.onResume()
        mBinding.barcodeView.resume()
    }

    override fun onPause() {
        super.onPause()
        mBinding.barcodeView.pause()
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent): Boolean {
        return mBinding.barcodeView.onKeyDown(keyCode, event) || super.onKeyDown(keyCode, event)
    }

    private fun hasFlash(): Boolean {
        return applicationContext.packageManager
            .hasSystemFeature(PackageManager.FEATURE_CAMERA_FLASH)
    }

    private fun switchFlashlight() {
        if (mBinding.ivFlash.tag == true) {
            mBinding.barcodeView.setTorchOff()
        } else {
            mBinding.barcodeView.setTorchOn()
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == CHOOSE_PHOTO && resultCode == Activity.RESULT_OK) {
            data?.data?.let {
                val bitmap = MediaUtil.getImageBitMap(this, it)
                bitmap?.let {
                    doAsync {
                        val content = QRCodeUtil.syncDecodeQRCode(it)
                        uiThread {
                            scanSuccess(content)
                        }
                    }
                }
            }
        }
    }

    private fun scanSuccess(content: String?) {
        if (!content.isNullOrEmpty()) {
            if (content.contains(":")
                && content.split(":").size > 1
                && WalletHelper.isValidAddress(content.split(":")[1])
            ) {
                mBinding.tvValue.text = content.split(":")[1]
            } else {
                mBinding.tvValue.text = content
            }
            mBinding.barcodeView.setStatusText(content)
            mBinding.tvLabel.text = MediaUtil.getScanType(content)
            mBinding.flContainer.visibility = View.VISIBLE
            if(lastContent !=content){
                beepManager.playBeepSoundAndVibrate()
                lastContent = content
            }
        }
    }
}