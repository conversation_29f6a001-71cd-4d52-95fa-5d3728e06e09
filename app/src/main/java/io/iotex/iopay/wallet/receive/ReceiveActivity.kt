package io.iotex.iopay.wallet.receive

import android.content.Context
import android.content.Intent
import androidx.core.view.isVisible
import androidx.recyclerview.widget.LinearLayoutManager
import com.blankj.utilcode.util.ClipboardUtils
import com.blankj.utilcode.util.Utils
import com.drakeet.multitype.MultiTypeAdapter
import com.machinefi.w3bstream.utils.extension.ellipsis
import io.iotex.base.BaseViewModel
import io.iotex.iopay.R
import io.iotex.iopay.base.BaseBindToolbarActivity
import io.iotex.iopay.data.db.AddressType_Legacy
import io.iotex.iopay.data.db.AddressType_NativeSegwit
import io.iotex.iopay.data.db.AddressType_NestedSegwit
import io.iotex.iopay.data.db.AddressType_Taproot
import io.iotex.iopay.databinding.ActivityWalletReceiveBinding
import io.iotex.iopay.util.Config
import io.iotex.iopay.util.Constant
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.util.extension.setGone
import io.iotex.iopay.util.extension.setVisible
import io.iotex.iopay.util.extension.toast
import io.iotex.iopay.wallet.receive.item.ReceiveAddressBean
import io.iotex.iopay.wallet.receive.item.ReceiveAddressBinder

class ReceiveActivity :
    BaseBindToolbarActivity<BaseViewModel, ActivityWalletReceiveBinding>(R.layout.activity_wallet_receive) {

    private val mAdapter = MultiTypeAdapter()
    private val mAdapterB = MultiTypeAdapter()

    companion object {
        private const val KEY_BITCOIN = "Key_bitcoin"
        private const val KEY_TITLE = "Key_title"
        fun startActivity(context: Context, isBitCoin: Boolean = false, title:String = Utils.getApp().getString(R.string.receive)) {
            val intent = Intent(context, ReceiveActivity::class.java)
            intent.putExtra(KEY_BITCOIN, isBitCoin)
            intent.putExtra(KEY_TITLE, title)
            context.startActivity(intent)
        }
    }

    private val isBitCoin by lazy {
        intent.getBooleanExtra(KEY_BITCOIN, false)
    }

    private val title by lazy {
        intent.getStringExtra(KEY_TITLE) ?: ""
    }

    override fun initView() {
        setToolbarTitle(title)

        mBinding.recyclerView.isVisible = !isBitCoin
        mBinding.llBtcLayout.isVisible =
            Constant.currentWallet?.isMnemonicWallet() == true || Constant.currentWallet?.isEvmPrivateWallet() == true

        mBinding.recyclerView.layoutManager = LinearLayoutManager(this)
        mBinding.recyclerViewB.layoutManager = LinearLayoutManager(this)
        if (Constant.currentWallet?.isSolanaWatchWallet() == true || Constant.currentWallet?.isSolanaPrivateWallet() == true) {
            mAdapter.items = arrayListOf(
                ReceiveAddressBean(
                    Config.SOLANA_MAIN_CHAIN_ID,
                    R.drawable.icon_solana_logo,
                    "Solana",
                    getAddress(Config.SOLANA_MAIN_CHAIN_ID)
                ),
            )
        } else {
            mAdapter.items = arrayListOf(
                ReceiveAddressBean(
                    Config.IOTEX_CHAIN_ID,
                    R.drawable.icon_iotex_network_logo,
                    "IoTeX",
                    getAddress(Config.IOTEX_CHAIN_ID)
                ),
                ReceiveAddressBean(
                    Config.ETH_CHAIN_ID,
                    R.drawable.icon_eth_network_logo,
                    "Ethereum",
                    getAddress(Config.ETH_CHAIN_ID)
                ),
            ).apply {
                if (Constant.currentWallet?.isMnemonicWallet() == true || Constant.currentWallet?.isSolanaWatchWallet() == true || Constant.currentWallet?.isSolanaPrivateWallet() == true) {
                    add(ReceiveAddressBean(
                        Config.SOLANA_MAIN_CHAIN_ID,
                        R.drawable.icon_solana_logo,
                        "Solana",
                        getAddress(Config.SOLANA_MAIN_CHAIN_ID)
                    ))
                }
            }
            mAdapterB.items = arrayListOf(
                ReceiveAddressBean(
                    Config.BSC_CHAIN_ID,
                    R.drawable.icon_bsc_logo,
                    "BNB Chain",
                    getAddress(Config.ETH_CHAIN_ID)
                ),
                ReceiveAddressBean(
                    Config.POLYGON_CHAIN_ID,
                    R.drawable.icon_polygon_logo,
                    "Polygon",
                    getAddress(Config.ETH_CHAIN_ID)
                ),
                ReceiveAddressBean(
                    Config.ARB_CHAIN_ID,
                    R.drawable.icon_arb_network_logo,
                    "Arbitrum",
                    getAddress(Config.ETH_CHAIN_ID)
                ),
                ReceiveAddressBean(
                    Config.AVAX_CHAIN_ID,
                    R.drawable.icon_ava_network_logo,
                    "Avalanche",
                    getAddress(Config.ETH_CHAIN_ID)
                ),
                ReceiveAddressBean(
                    Config.FTM_CHAIN_ID,
                    R.drawable.icon_ftm_network_logo,
                    "Fantom",
                    getAddress(Config.ETH_CHAIN_ID)
                ),
                ReceiveAddressBean(
                    Config.BASE_CHAIN_ID,
                    R.drawable.icon_base_network_logo,
                    "Base",
                    getAddress(Config.ETH_CHAIN_ID)
                ),
            )
        }
        mBinding.recyclerView.adapter = mAdapter
        mBinding.recyclerViewB.adapter = mAdapterB
        mAdapter.register(ReceiveAddressBean::class.java, ReceiveAddressBinder())
        mAdapterB.register(ReceiveAddressBean::class.java, ReceiveAddressBinder())

        mBinding.tvLegacyName.text = getBitcoinAddressTypeName(AddressType_Legacy)
        mBinding.tvLegacyAddress.text =
            getAddress(Config.BITCOIN_MAIN_CHAIN_ID, AddressType_Legacy).ellipsis()
        mBinding.ivCopyLegacy.setOnClickListener {
            ClipboardUtils.copyText(getAddress(Config.BITCOIN_MAIN_CHAIN_ID, AddressType_Legacy))
            Utils.getApp().getString(R.string.copy_success).toast()
        }
        mBinding.llLegacy.setOnClickListener {
            ReceiveDetailActivity.startActivity(
                this,
                Config.BITCOIN_MAIN_CHAIN_ID,
                AddressType_Legacy
            )
        }

        mBinding.tvNativeName.text = getBitcoinAddressTypeName(AddressType_NativeSegwit)
        mBinding.tvNativeAddress.text =
            getAddress(Config.BITCOIN_MAIN_CHAIN_ID, AddressType_NativeSegwit).ellipsis()
        mBinding.ivCopyNative.setOnClickListener {
            ClipboardUtils.copyText(
                getAddress(
                    Config.BITCOIN_MAIN_CHAIN_ID,
                    AddressType_NativeSegwit
                )
            )
            Utils.getApp().getString(R.string.copy_success).toast()
        }
        mBinding.llNativeSegwit.setOnClickListener {
            ReceiveDetailActivity.startActivity(
                this,
                Config.BITCOIN_MAIN_CHAIN_ID,
                AddressType_NativeSegwit
            )
        }

        mBinding.tvNestedName.text = getBitcoinAddressTypeName(AddressType_NestedSegwit)
        mBinding.tvNestedAddress.text =
            getAddress(Config.BITCOIN_MAIN_CHAIN_ID, AddressType_NestedSegwit).ellipsis()
        mBinding.ivCopyNested.setOnClickListener {
            ClipboardUtils.copyText(
                getAddress(
                    Config.BITCOIN_MAIN_CHAIN_ID,
                    AddressType_NestedSegwit
                )
            )
            Utils.getApp().getString(R.string.copy_success).toast()
        }
        mBinding.llNestedSegwit.setOnClickListener {
            ReceiveDetailActivity.startActivity(
                this,
                Config.BITCOIN_MAIN_CHAIN_ID,
                AddressType_NestedSegwit
            )
        }

        mBinding.tvTaprootName.text = getBitcoinAddressTypeName(AddressType_Taproot)
        mBinding.tvTaprootAddress.text =
            getAddress(Config.BITCOIN_MAIN_CHAIN_ID, AddressType_Taproot).ellipsis()
        mBinding.ivCopyTaproot.setOnClickListener {
            ClipboardUtils.copyText(getAddress(Config.BITCOIN_MAIN_CHAIN_ID, AddressType_Taproot))
            Utils.getApp().getString(R.string.copy_success).toast()
        }
        mBinding.llTaproot.setOnClickListener {
            ReceiveDetailActivity.startActivity(
                this,
                Config.BITCOIN_MAIN_CHAIN_ID,
                AddressType_Taproot
            )
        }

        mBinding.llBtc.setOnClickListener {
            if (mBinding.llBtcContent.isVisible) {
                mBinding.llBtcContent.setGone()
                mBinding.ivArrow.rotation = 90f
            } else {
                mBinding.llBtcContent.setVisible()
                mBinding.ivArrow.rotation = 270f
            }
        }

    }

    private fun getAddress(chainId: Int, addressType: Int = AddressType_Legacy): String {
        val wallet = Constant.currentWallet ?: return ""
        return if (chainId == Config.IOTEX_CHAIN_ID) {
            WalletHelper.formatWalletAddress(wallet.address)
        } else if (WalletHelper.isBitcoinNetwork(chainId)) {
            wallet.bitcoinWallets.find { it.addressType == addressType }?.bitcoinAddress
                ?: wallet.address
        } else if (WalletHelper.isSolanaNetwork(chainId)) {
            wallet.solanaWallet?.publicKeyBase58 ?: wallet.address
        } else {
            WalletHelper.convertWeb3Address(wallet.address)
        }
    }

    private fun getBitcoinAddressTypeName(addressType: Int = AddressType_Legacy): String {
        val wallet = Constant.currentWallet ?: return ""
        return wallet.bitcoinWallets.find { it.addressType == addressType }?.typeName?.split("(")
            ?.get(0) ?: ""
    }
}