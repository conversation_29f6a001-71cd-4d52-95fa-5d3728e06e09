package io.iotex.iopay.wallet.receive

import android.content.Context
import android.content.Intent
import android.view.View
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import com.blankj.utilcode.util.ClipboardUtils
import com.blankj.utilcode.util.ConvertUtils
import com.blankj.utilcode.util.ToastUtils
import io.iotex.base.BaseViewModel
import io.iotex.iopay.R
import io.iotex.iopay.base.BaseBindToolbarActivity
import io.iotex.iopay.data.db.AddressType_Legacy
import io.iotex.iopay.data.db.Wallet
import io.iotex.iopay.databinding.ActivityWalletReceiveDetailBinding
import io.iotex.iopay.util.Config
import io.iotex.iopay.util.Constant
import io.iotex.iopay.util.QRCodeUtil
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.util.extension.loadSvgOrImage
import io.iotex.iopay.wallet.aawallet.dialog.AAReceiveDialog.showAAReceiveCopy
import io.iotex.iopay.wallet.bitcoin.BitcoinHelper
import io.iotex.iopay.wallet.receive.dialog.ReceiveExchangeDialog
import io.iotex.iopay.wallet.receive.dialog.ReceiveShareDialog
import io.iotex.iopay.wallet.receive.dialog.ReceiveWalletDialog

class ReceiveDetailActivity :
    BaseBindToolbarActivity<BaseViewModel, ActivityWalletReceiveDetailBinding>(R.layout.activity_wallet_receive_detail) {

    private val chainId by lazy {
        intent.getIntExtra("chainId", Config.IOTEX_CHAIN_ID)
    }

    private val addressType by lazy {
        intent.getIntExtra("addressType", AddressType_Legacy)
    }

    private var type = 0

    companion object {
        const val TYPE_IOTEX = 0
        const val TYPE_ETH = 1
        fun startActivity(context: Context, chainId: Int, addressType: Int = AddressType_Legacy) {
            val intent = Intent(context, ReceiveDetailActivity::class.java)
            intent.putExtra("chainId", chainId)
            intent.putExtra("addressType", addressType)
            context.startActivity(intent)
        }
    }


    override fun initView() {
        setToolbarTitle(getString(R.string.receive))

        mBinding.llTab.isVisible = chainId == Config.IOTEX_CHAIN_ID
        mBinding.llHowTo.isVisible = chainId == Config.IOTEX_CHAIN_ID
        type = TYPE_ETH

        setData()

        mBinding.tabIotex.setOnClickListener {
            type = TYPE_IOTEX
            setData()
        }

        mBinding.tabEth.setOnClickListener {
            type = TYPE_ETH
            setData()
        }

        mBinding.ivSave.setOnClickListener {
            ReceiveShareDialog(chainId, type, addressType).show(
                supportFragmentManager,
                System.currentTimeMillis().toString()
            )
        }

        mBinding.llOtherWallet.setOnClickListener {
            ReceiveWalletDialog(chainId, type, addressType).show(
                supportFragmentManager,
                System.currentTimeMillis().toString()
            )
        }

        mBinding.llExchange.setOnClickListener {
            ReceiveExchangeDialog(chainId, type, addressType).show(
                supportFragmentManager,
                System.currentTimeMillis().toString()
            )
        }
    }

    private fun getAddress(wallet: Wallet?): String {
        if (wallet == null) return ""
        return if (chainId == Config.IOTEX_CHAIN_ID && type == TYPE_IOTEX) {
            WalletHelper.convertIoAddress(wallet.address)
        } else if (WalletHelper.isBitcoinNetwork(chainId)) {
            wallet.bitcoinWallets.find { it.addressType == addressType }?.bitcoinAddress
                ?: wallet.address
        } else if (WalletHelper.isSolanaNetwork(chainId)) {
            wallet.solanaWallet?.publicKeyBase58 ?: wallet.address
        } else {
            WalletHelper.convertWeb3Address(wallet.address)
        }
    }

    private fun getBitcoinAddressTypeName(): String {
        return BitcoinHelper.getTypeNameByType(addressType)
    }


    private fun setData() {
        val isAA = Constant.currentWallet?.isAAWallet() == true
        mBinding.ivAA.isVisible = isAA
        val address = getAddress(Constant.currentWallet)
        mBinding.ivAvatar.loadSvgOrImage(
            WalletHelper.getAddressAvatar(address, ConvertUtils.dp2px(70f)),
            R.drawable.icon_wallet_default
        )
        val mBitmap = QRCodeUtil.createQRCodeBitmap(address, 480, 480)
        mBinding.ivQrCode.setImageBitmap(mBitmap)
        mBinding.tvAddressType.isVisible = WalletHelper.isBitcoinNetwork(chainId)
        mBinding.tvAddressType.text = getBitcoinAddressTypeName()
        mBinding.tvAddress.text = address

        mBinding.copyAddress.setOnClickListener {
            ClipboardUtils.copyText(address)
            if (Constant.currentWallet?.isAAWallet() == true) {
                showAAReceiveCopy()
            } else {
                ToastUtils.showShort(R.string.copy_success)
            }
        }

        mBinding.llInfo.visibility = View.VISIBLE
        mBinding.tvTips01.visibility = View.VISIBLE
        mBinding.tvTips02.visibility = View.VISIBLE

        if (type == TYPE_IOTEX) {
            mBinding.tabIotex.setTextColor(ContextCompat.getColor(this, R.color.white))
            mBinding.tabEth.setTextColor(ContextCompat.getColor(this, R.color.color_title))
            mBinding.tabIotex.setBackgroundResource(R.drawable.btn_shape_gradient_common)
            mBinding.tabEth.background = null
            mBinding.tvTips01.text = getString(R.string.this_address_only_accepts_native)
            if (isAA) {
                mBinding.tvTips02.text = getString(R.string.aa_wallet_operates_on_smart_contracts)
            } else {
                mBinding.tvTips02.visibility = View.GONE
            }
        }
        if (type == TYPE_ETH) {
            mBinding.tabEth.setTextColor(ContextCompat.getColor(this, R.color.white))
            mBinding.tabIotex.setTextColor(ContextCompat.getColor(this, R.color.color_title))
            mBinding.tabEth.setBackgroundResource(R.drawable.btn_shape_gradient_common)
            mBinding.tabIotex.background = null
            if (WalletHelper.isBitcoinNetwork(chainId)) {
                mBinding.tvTips01.text = getString(R.string.btc_wallet_tips_1)
                mBinding.tvTips02.text = getString(R.string.btc_wallet_tips_2)
            }else if (WalletHelper.isSolanaNetwork(chainId)) {
                mBinding.tvTips01.text = getString(R.string.receive_sol_on_solana)
                mBinding.tvTips02.visibility = View.GONE
            } else if (isAA) {
                mBinding.tvTips01.text = getString(R.string.aa_wallet_operates_on_smart_contracts)
                mBinding.tvTips02.visibility = View.GONE
            } else {
                mBinding.llInfo.visibility = View.GONE
            }
        }
    }
}