package io.iotex.iopay.wallet.receive.dialog

import android.content.Intent
import android.view.Gravity
import android.view.WindowManager
import com.blankj.utilcode.util.ClipboardUtils
import com.blankj.utilcode.util.ToastUtils
import com.machinefi.w3bstream.utils.extension.ellipsis
import io.iotex.base.BaseViewModel
import io.iotex.base.bindbase.BaseBindDialog
import io.iotex.iopay.R
import io.iotex.iopay.data.db.AddressType_Legacy
import io.iotex.iopay.data.db.Wallet
import io.iotex.iopay.databinding.DialogReceiveExchangeBinding
import io.iotex.iopay.util.Config
import io.iotex.iopay.util.Constant
import io.iotex.iopay.util.IoPayConstant
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.wallet.aawallet.dialog.AAReceiveDialog.showAAReceiveCopy
import io.iotex.iopay.xapp.XAppsActivity
import io.iotex.iopay.wallet.receive.ReceiveDetailActivity.Companion.TYPE_IOTEX

class ReceiveExchangeDialog(
    val chainId: Int,
    val type: Int = TYPE_IOTEX,
    val addressType: Int = AddressType_Legacy
) : BaseBindDialog<BaseViewModel, DialogReceiveExchangeBinding>(R.layout.dialog_receive_exchange) {

    companion object {
        const val KUCOIN_PACKAGE = "com.kubi.kucoin"
        const val KUCOIN_URL = "https://www.kucoin.com/trade/IOTX-USDT"
        const val BINANCE_PACKAGE = "com.binance.dev"
        const val BINANCE_URL = "https://www.binance.com/trade/IOTX-USDT"
        const val GATE_PACKAGE = "com.gateio.gateio"
        const val GATE_URL = "https://www.gate.io/trade/IOTX_USDT"
        const val UPBIT_PACKAGE = "com.dunamu.exchange"
        const val UPBIT_URL = "https://upbit.com/exchange?code=CRIX.UPBIT.BTC-IOTX"
        const val BITGET_PACKAGE = "com.bitget.exchange"
        const val BITGET_URL = "https://www.bitget.fit/spot/IOTXUSDT"
        const val HTX_PACKAGE = "pro.huobi"
        const val HTX_URL = "https://www.htx.com/trade/iotx_usdt"
    }

    override fun initView() {
        val address = getAddress(Constant.currentWallet)
        mBinding.tvAddress.text = address.ellipsis()

        mBinding.ivCopy.setOnClickListener {
            ClipboardUtils.copyText(address)
            if (Constant.currentWallet?.isAAWallet() == true) {
                showAAReceiveCopy()
            } else {
                ToastUtils.showShort(R.string.copy_success)
            }
        }
        mBinding.ivClose.setOnClickListener {
            dismiss()
        }

        mBinding.llKuCoin.setOnClickListener {
            openExchange(KUCOIN_PACKAGE, KUCOIN_URL)
        }

        mBinding.llBinance.setOnClickListener {
            openExchange(BINANCE_PACKAGE, BINANCE_URL)
        }

        mBinding.llGate.setOnClickListener {
            openExchange(GATE_PACKAGE, GATE_URL)
        }

        mBinding.llUpbit.setOnClickListener {
            openExchange(UPBIT_PACKAGE, UPBIT_URL)
        }

        mBinding.llBitget.setOnClickListener {
            openExchange(BITGET_PACKAGE, BITGET_URL)
        }

        mBinding.llhtx.setOnClickListener {
            openExchange(HTX_PACKAGE, HTX_URL)
        }
    }

    private fun openExchange(pageName: String, url: String) {
        val intent = requireActivity().packageManager.getLaunchIntentForPackage(pageName)
        if (intent != null) {
            startActivity(intent)
        } else {
            val intentUrl = Intent(requireContext(), XAppsActivity::class.java)
            intentUrl.putExtra(IoPayConstant.BROWSER_URL, url)
            startActivity(intentUrl)
        }
    }

    private fun getAddress(wallet: Wallet?): String {
        if (wallet == null) return ""
        return if (chainId == Config.IOTEX_CHAIN_ID && type == TYPE_IOTEX) {
            WalletHelper.convertIoAddress(wallet.address)
        } else if (WalletHelper.isBitcoinNetwork(chainId)) {
            wallet.bitcoinWallets.find { it.addressType == addressType }?.bitcoinAddress
                ?: wallet.address
        } else if (WalletHelper.isSolanaNetwork(chainId)) {
            wallet.solanaWallet?.publicKeyBase58 ?: wallet.address
        } else {
            WalletHelper.convertWeb3Address(wallet.address)
        }
    }

    override fun onStart() {
        super.onStart()
        val attributes = dialog?.window?.attributes
        attributes?.width = WindowManager.LayoutParams.MATCH_PARENT
        attributes?.gravity = Gravity.BOTTOM
        dialog?.window?.attributes = attributes
    }
}