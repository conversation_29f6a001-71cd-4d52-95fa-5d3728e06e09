package io.iotex.iopay.wallet.receive.dialog

import com.blankj.utilcode.util.IntentUtils
import com.blankj.utilcode.util.ToastUtils
import com.blankj.utilcode.util.Utils
import io.iotex.base.BaseViewModel
import io.iotex.base.bindbase.BaseBindDialog
import io.iotex.iopay.R
import io.iotex.iopay.data.db.AddressType_Legacy
import io.iotex.iopay.data.db.Wallet
import io.iotex.iopay.databinding.DialogReceiveShareBinding
import io.iotex.iopay.util.Config
import io.iotex.iopay.util.Constant
import io.iotex.iopay.util.FireBaseEvent
import io.iotex.iopay.util.FireBaseUtil
import io.iotex.iopay.util.QRCodeUtil
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.util.extension.toBitmap
import io.iotex.iopay.wallet.receive.ReceiveDetailActivity.Companion.TYPE_IOTEX

class ReceiveShareDialog(
    val chainId: Int,
    val type: Int = TYPE_IOTEX,
    val addressType: Int = AddressType_Legacy
) : BaseBindDialog<BaseViewModel, DialogReceiveShareBinding>(R.layout.dialog_receive_share) {

    override fun initView() {
        val address = getAddress(Constant.currentWallet)
        val mBitmap = QRCodeUtil.createQRCodeBitmap(address, 480, 480)
        mBinding.ivQrCode.setImageBitmap(mBitmap)
        mBinding.tvAddress.text = address
        mBinding.mIvDownload.setOnClickListener {
            val drawingCache = mBinding.llContent.toBitmap()
            if (QRCodeUtil.savePhoto(
                    drawingCache,
                    "receive_address_${System.currentTimeMillis()}.jpg"
                ) != null
            ) {
                ToastUtils.showShort(R.string.saved_successfully)
            } else {
                ToastUtils.showShort(R.string.saved_fail)
            }
        }
        mBinding.mIvShare.setOnClickListener {
            val drawingCache = mBinding.llContent.toBitmap()
            val uri = QRCodeUtil.savePhoto(
                drawingCache,
                "receive_address_${System.currentTimeMillis()}.jpg"
            )
            if (uri != null) {
                val i = IntentUtils.getShareImageIntent(uri)
                Utils.getApp().startActivity(i)
                FireBaseUtil.logFireBase(FireBaseEvent.ACTION_ACTION_TXN_SHARE)
            }
        }
    }

    private fun getAddress(wallet: Wallet?): String {
        if (wallet == null) return ""
        return if (chainId == Config.IOTEX_CHAIN_ID && type == TYPE_IOTEX) {
            WalletHelper.convertIoAddress(wallet.address)
        } else if (WalletHelper.isBitcoinNetwork(chainId)) {
            wallet.bitcoinWallets.find { it.addressType == addressType }?.bitcoinAddress
                ?: wallet.address
        } else if (WalletHelper.isSolanaNetwork(chainId)) {
            wallet.solanaWallet?.publicKeyBase58 ?: wallet.address
        } else {
            WalletHelper.convertWeb3Address(wallet.address)
        }
    }
}