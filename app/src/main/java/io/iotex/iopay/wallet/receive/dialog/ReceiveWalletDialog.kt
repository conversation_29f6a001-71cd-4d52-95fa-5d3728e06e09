package io.iotex.iopay.wallet.receive.dialog

import android.view.Gravity
import android.view.WindowManager
import com.blankj.utilcode.util.ClipboardUtils
import com.blankj.utilcode.util.ToastUtils
import com.machinefi.w3bstream.utils.extension.ellipsis
import io.iotex.base.BaseViewModel
import io.iotex.base.bindbase.BaseBindDialog
import io.iotex.iopay.R
import io.iotex.iopay.data.db.AddressType_Legacy
import io.iotex.iopay.data.db.Wallet
import io.iotex.iopay.databinding.DialogReceiveWalletBinding
import io.iotex.iopay.util.Config
import io.iotex.iopay.util.Constant
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.wallet.aawallet.dialog.AAReceiveDialog.showAAReceiveCopy
import io.iotex.iopay.wallet.receive.ReceiveDetailActivity.Companion.TYPE_IOTEX

class ReceiveWalletDialog(
    val chainId: Int,
    val type: Int = TYPE_IOTEX,
    val addressType: Int = AddressType_Legacy
) : BaseBindDialog<BaseViewModel, DialogReceiveWalletBinding>(R.layout.dialog_receive_wallet) {

    override fun initView() {
        val address = getAddress(Constant.currentWallet)
        mBinding.tvAddress.text = address.ellipsis()

        mBinding.ivCopy.setOnClickListener {
            ClipboardUtils.copyText(address)
            if (Constant.currentWallet?.isAAWallet() == true) {
                showAAReceiveCopy()
            } else {
                ToastUtils.showShort(R.string.copy_success)
            }
        }
        mBinding.ivClose.setOnClickListener {
            dismiss()
        }
    }

    private fun getAddress(wallet: Wallet?): String {
        if (wallet == null) return ""
        return if (chainId == Config.IOTEX_CHAIN_ID && type == TYPE_IOTEX) {
            WalletHelper.convertIoAddress(wallet.address)
        } else if (WalletHelper.isBitcoinNetwork(chainId)) {
            wallet.bitcoinWallets.find { it.addressType == addressType }?.bitcoinAddress
                ?: wallet.address
        } else if (WalletHelper.isSolanaNetwork(chainId)) {
            wallet.solanaWallet?.publicKeyBase58 ?: wallet.address
        } else {
            WalletHelper.convertWeb3Address(wallet.address)
        }
    }

    override fun onStart() {
        super.onStart()
        val attributes = dialog?.window?.attributes
        attributes?.width = WindowManager.LayoutParams.MATCH_PARENT
        attributes?.gravity = Gravity.BOTTOM
        dialog?.window?.attributes = attributes
    }
}