package io.iotex.iopay.wallet.receive.item

import android.view.LayoutInflater
import android.view.ViewGroup
import com.blankj.utilcode.util.ClipboardUtils
import com.blankj.utilcode.util.Utils
import com.drakeet.multitype.ItemViewBinder
import com.machinefi.w3bstream.utils.extension.ellipsis
import io.iotex.base.bindbase.BaseBindVH
import io.iotex.iopay.R
import io.iotex.iopay.databinding.ItemWalletReceiveBinding
import io.iotex.iopay.util.extension.loadImage
import io.iotex.iopay.util.extension.toast
import io.iotex.iopay.wallet.receive.ReceiveDetailActivity

class ReceiveAddressBinder :
    ItemViewBinder<ReceiveAddressBean, BaseBindVH<ItemWalletReceiveBinding>>() {
    override fun onBindViewHolder(
        holder: BaseBindVH<ItemWalletReceiveBinding>,
        item: ReceiveAddressBean
    ) {
        holder.bind.apply {
            ivLogo.loadImage(item.logo, R.drawable.ic_network_default)
            tvName.text = item.name
            tvAddress.text = item.address.ellipsis()
            ivCopyAddress.setOnClickListener {
                ClipboardUtils.copyText(item.address)
                Utils.getApp().getString(R.string.copy_success).toast()
            }
            root.setOnClickListener {
                ReceiveDetailActivity.startActivity(
                    root.context,
                    item.chainId
                )
            }
        }
    }

    override fun onCreateViewHolder(
        inflater: LayoutInflater,
        parent: ViewGroup
    ): BaseBindVH<ItemWalletReceiveBinding> {
        val bind = ItemWalletReceiveBinding.inflate(inflater, parent, false)
        return BaseBindVH(bind)
    }

}