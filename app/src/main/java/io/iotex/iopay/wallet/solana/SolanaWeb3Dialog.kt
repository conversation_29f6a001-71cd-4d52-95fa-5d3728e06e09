package io.iotex.iopay.wallet.solana

import androidx.appcompat.app.AppCompatActivity
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.TimeUtils
import com.solana.core.Transaction
import com.solana.core.versioned.VersionedTransaction
import io.iotex.iopay.data.db.ACTION_TYPE_DAPP
import io.iotex.iopay.transaction.TransactionDialog
import io.iotex.iopay.transaction.bean.OptionEntry
import io.iotex.iopay.transaction.sign.SignMessageDialog
import io.iotex.iopay.util.extension.toHexByteArray
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import org.bouncycastle.util.encoders.Base64
import java.math.BigInteger


object SolanaWeb3Dialog {
    private var signMessageDialog: SignMessageDialog? = null
    private var transactionDialog: TransactionDialog? = null
    fun signMessage(
        logo: String,
        url: String,
        message: String,
        callBack: (signedMessage: String?) -> Unit
    ) {
        signMessageDialog = SignMessageDialog(logo, url, null, message)
            .apply {
                onResult = { confirm ->
                    signMessageDialog = null
                    if (confirm) {
                        CoroutineScope(Dispatchers.IO).launch {
                            val signedMessage = kotlin.runCatching {
                                SolanaWeb3.signMessage(message.toHexByteArray())
                            }.getOrNull()
                            callBack.invoke(signedMessage)
                        }
                    } else {
                        callBack.invoke(null)
                    }
                }
            }
        (ActivityUtils.getTopActivity() as? AppCompatActivity)?.let {
            signMessageDialog?.show(
                it.supportFragmentManager,
                System.currentTimeMillis().toString()
            )
        }
    }

    fun signAllTransactions(
        logo: String,
        url: String,
        transactions: String,
        callBack: (signedMessage: String?) -> Unit
    ) {
        signMessageDialog = SignMessageDialog(logo, url, null, transactions)
            .apply {
                onResult = { confirm ->
                    signMessageDialog = null
                    if (confirm) {
                        CoroutineScope(Dispatchers.IO).launch {
                            val signedMessage = kotlin.runCatching {
                                SolanaWeb3.signAllTransactions(transactions)
                            }.getOrNull()
                            callBack.invoke(signedMessage)
                        }
                    } else {
                        callBack.invoke(null)
                    }
                }
            }
        (ActivityUtils.getTopActivity() as? AppCompatActivity)?.let {
            signMessageDialog?.show(
                it.supportFragmentManager,
                System.currentTimeMillis().toString()
            )
        }
    }

    fun sendRawTransaction(
        option: ArrayList<OptionEntry>,
        recentBlockhash: String,
        transaction: Transaction?,
        versionedTransaction: VersionedTransaction?,
        souse: String,
        method: String,
        type: Int = ACTION_TYPE_DAPP,
        value: String = "",
        symbol: String = "",
        confirm: (() -> Unit)? = null,
        callBack: ((signature: String?) -> Unit)? = null
    ) {
        val data = Base64.toBase64String(versionedTransaction?.message?.serialize())
        transactionDialog =
            TransactionDialog("0x", BigInteger.ZERO, data, option, "", "", null, null).apply {
                onCancel = {
                    transactionDialog?.dismiss()
                    transactionDialog = null
                    callBack?.invoke(null)
                }
                onTransactionConfirm =
                    { _: Long, _: String, _, _ ->
                        CoroutineScope(Dispatchers.IO).launch {
                            val timestamp = TimeUtils.getNowMills().toString()
                            val signature = SolanaWeb3.sendRawTransaction(
                                timestamp,
                                recentBlockhash,
                                transaction,
                                versionedTransaction,
                                souse,
                                method,
                                type,
                                value,
                                symbol,
                                confirm = {
                                    MainScope().launch {
                                        confirm?.invoke()
                                        transactionDialog?.dismiss()
                                        transactionDialog = null
                                    }
                                }
                            )
                            if (signature != null) {
                                MainScope().launch {
                                    callBack?.invoke(signature)
                                    transactionDialog?.dismiss()
                                    transactionDialog = null
                                }
                            } else {
                                MainScope().launch {
                                    transactionDialog?.dismiss()
                                    callBack?.invoke(null)
                                    transactionDialog = null
                                }
                            }
                        }
                    }
            }
        (ActivityUtils.getTopActivity() as? AppCompatActivity)?.let {
            transactionDialog?.show(
                it.supportFragmentManager,
                System.currentTimeMillis().toString()
            )
        }
    }
}