package io.iotex.iopay.wallet.viewmodel

import android.app.Application
import androidx.lifecycle.MutableLiveData
import com.apollographql.apollo.ApolloClient
import com.apollographql.apollo.coroutines.await
import com.blankj.utilcode.util.TimeUtils
import com.blankj.utilcode.util.Utils
import io.iotex.base.okHttpClient
import io.iotex.iopay.R
import io.iotex.iopay.base.BaseLaunchVM
import io.iotex.iopay.data.BucketInfoBean
import io.iotex.iopay.service.HttpRequestManager
import io.iotex.iopay.util.Config
import io.iotex.iopay.util.TokenUtil
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.wallet.web3.Web3Delegate
import io.iotex.stake.DelegateQuery
import io.iotex.stake.type.Order_by
import java.math.BigInteger

class NftTokenDetailViewModel(application: Application) : BaseLaunchVM(application) {
    private val apolloClient by lazy {
        ApolloClient.builder()
            .serverUrl(Config.stakeGatewayUrl)
            .okHttpClient(okHttpClient)
            .build()
    }

    val bucketInfoLiveData = MutableLiveData<BucketInfoBean>()

    fun getBucketInfo(contract: String, tokenId: String) {
        addLaunch {
            val s = Web3Delegate.bucketOf(contract, tokenId.toBigInteger())
            val amount = TokenUtil.weiToTokenBN(s[0]) + " " + TokenUtil.getNativeCurrencySymbol()
            val dayNum = (s[1].toIntOrNull() ?: 0) / 86400
            val address = WalletHelper.convertIoAddress(s[4])
            val lock = if (BigInteger(s[2]).toString(16) == Config.MAX_AMOUNT) {
                "ON"
            } else {
                "OFF"
            }
            val status = if (BigInteger(s[3]).toString(16) == Config.MAX_AMOUNT) {
                Utils.getApp().getString(R.string.stake_status_ongoing)
            } else {
                ""
            }
            val duration =
                Utils.getApp().resources.getQuantityString(R.plurals.day_num, dayNum, dayNum)
            val bucketInfoBean = BucketInfoBean(amount, duration, lock, status, "")
            if (status.isNotEmpty()) {
                voteName(address, bucketInfoBean)
            } else {
                rpcTime(s[3], address, bucketInfoBean)
            }
        }
    }

    private fun rpcTime(block: String, address: String, bucketInfoBean: BucketInfoBean) {
        addLaunch {
            val network = WalletHelper.getCurNetwork()
            network?.let {
                HttpRequestManager.getRpcBlockTime(
                    it.rpc,
                    block,
                    object : HttpRequestManager.HttpResultCallBack {
                        override fun failure(error: String) {

                        }

                        override fun success(result: String) {
                            val now = TimeUtils.getNowMills() / 1000
                            val status =
                                if (now - (result.toLongOrNull() ?: 0) > 3 * 24 * 60 * 60) {
                                    Utils.getApp().getString(R.string.stake_status_withdrawble)
                                } else {
                                    Utils.getApp().getString(R.string.stake_status_unstaking)
                                }
                            bucketInfoBean.status = status
                            voteName(address, bucketInfoBean)
                        }

                    })
            }
        }
    }

    private fun voteName(address: String, bucketInfoBean: BucketInfoBean) {
        addLaunch(true) {
            val contractActionsQuery = DelegateQuery.builder()
                .order_by(Order_by.DESC)
                .build()

            apolloClient.query(contractActionsQuery).await().data?.iopay_delegate()?.forEach {
                var name = ""
                if (it.owner_address().equals(address, true)) {
                    name = it.name() ?: ""
                }
                if (name.isNotEmpty()) {
                    bucketInfoBean.vote = name
                }
                bucketInfoLiveData.postValue(bucketInfoBean)
            }
        }
    }
}