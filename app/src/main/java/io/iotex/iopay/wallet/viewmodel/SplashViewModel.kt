package io.iotex.iopay.wallet.viewmodel

import android.app.Application
import com.apollographql.apollo.ApolloClient
import com.apollographql.apollo.coroutines.await
import com.blankj.utilcode.util.AppUtils
import com.blankj.utilcode.util.Utils
import io.iotex.api.AppSwitchQuery
import io.iotex.base.okHttpClient
import io.iotex.iopay.base.BaseLaunchVM
import io.iotex.iopay.data.UserStore
import io.iotex.iopay.data.db.AppDatabase
import io.iotex.iopay.util.Config
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.util.extension.toEvmAddress

class SplashViewModel(application: Application) : BaseLaunchVM(application) {

    private val apolloClient by lazy {
        ApolloClient.builder()
            .serverUrl(Config.IoPayUrl)
            .okHttpClient(okHttpClient)
            .build()
    }

    fun oldAddressToEvm() {
        addLaunchNoCancel("oldAddressToEvm") {
            val walletList = AppDatabase.getInstance(Utils.getApp()).walletDao().queryAllWallets()
            walletList.forEach {
                if (it.address != it.address.toEvmAddress()) {
                    AppDatabase.getInstance(Utils.getApp()).walletDao().deleteWallet(it)
                    it.address = it.address.toEvmAddress()
                    AppDatabase.getInstance(Utils.getApp()).walletDao().insertIfNotExists(it)
                }
                WalletHelper.generateBitcoinWallet(it)
                WalletHelper.generateSolanaWallet(it)
            }
            //change address for next version.
            val wallet = WalletHelper.getCurWallet()
            if (wallet != null) {
                UserStore.setWalletAddress(wallet.address)
                if (WalletHelper.checkWalletNetwork(wallet, UserStore.getChainId())) {
                    WalletHelper.switchWallet(wallet)
                }
            }
            val network = WalletHelper.getCurNetwork()
            if (network != null) {
                UserStore.saveNetwork(network)
            }
        }
    }

    fun loadAppSwitch() {
        addLaunchNoCancel("loadAppSwitch") {
            val appSwitchQuery = AppSwitchQuery.builder().build()
            apolloClient.query(appSwitchQuery).await().data?.app_function_switch()?.forEach {
                when (it.function_name()) {
                    Config.SWITCH_GIFT_CENTER -> {
                        UserStore.setSwitchGiftCenter(it.enabled())
                        UserStore.setGiftCenterUrl(it.link() ?: "")
                    }
                    Config.SWITCH_RN_FAKE_DEPIN -> {
                        UserStore.setSwitchRNFakeDepin(it.enabled())
                    }
                    Config.SWITCH_BINO_AI -> {
                        UserStore.setSwitchBinoAi(it.enabled())
                        UserStore.setBinoAiUrl(it.link() ?: "")
                    }
                    Config.SWITCH_BUCKET_V3 -> {
                        if(AppUtils.getAppVersionCode() < (it.min_android_version()?.toIntOrNull()?:0)){
                            UserStore.setBucketDialog(it.enabled())
                        } else {
                            UserStore.setBucketDialog(false)
                        }
                    }
                }
            }
        }
    }
}