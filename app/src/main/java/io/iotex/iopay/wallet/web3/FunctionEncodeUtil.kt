package io.iotex.iopay.wallet.web3

import io.iotex.iopay.util.extension.toHexByteArray
import org.web3j.abi.FunctionEncoder
import org.web3j.abi.TypeReference
import org.web3j.abi.datatypes.Address
import org.web3j.abi.datatypes.DynamicBytes
import org.web3j.abi.datatypes.Function
import org.web3j.abi.datatypes.generated.Bytes32
import org.web3j.abi.datatypes.generated.Uint256
import org.web3j.crypto.Hash
import java.math.BigInteger

object FunctionEncodeUtil {
    const val FUN_ADD_EMAIL_GUARDIAN = "addEmailGuardian"
    const val FUN_STOP_RECOVERY = "stopRecovery"
    private const val FUNC_APPROVE = "approve"

    fun addEmailGuardianData(email: String, signature: String): String {
        return FunctionEncoder.encode(
            Function(
                FUN_ADD_EMAIL_GUARDIAN,
                listOf(
                    Bytes32(Hash.sha3(email.toByteArray())),
                    DynamicBytes(signature.toHexByteArray())
                ),
                listOf()
            )
        )
    }

    fun stopRecoveryData(): String {
        return FunctionEncoder.encode(
            Function(
                FUN_STOP_RECOVERY,
                listOf(),
                listOf()
            )
        )
    }

    fun approveData(_spender: String?,_value: BigInteger):String{
        return FunctionEncoder.encode(
            Function(
                FUNC_APPROVE,
                listOf(Address(160, _spender), Uint256(_value),),
                listOf<TypeReference<*>>(object : TypeReference<Uint256>() {})
            )
        )
    }

}