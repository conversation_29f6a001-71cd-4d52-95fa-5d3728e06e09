package io.iotex.iopay.wallet.web3

import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.util.extension.toHexByteArray
import org.web3j.abi.FunctionEncoder
import org.web3j.abi.TypeReference
import org.web3j.abi.datatypes.Address
import org.web3j.abi.datatypes.Bool
import org.web3j.abi.datatypes.DynamicBytes
import org.web3j.abi.datatypes.DynamicStruct
import org.web3j.abi.datatypes.Function
import org.web3j.abi.datatypes.Type
import org.web3j.abi.datatypes.Utf8String
import org.web3j.abi.datatypes.generated.Bytes32
import org.web3j.abi.datatypes.generated.Uint256
import org.web3j.abi.datatypes.generated.Uint48
import org.web3j.crypto.Hash
import java.math.BigInteger

object FunctionSignData {

    fun getTransferSignData(toAddress: String, value: BigInteger): String {
        val methodName = "transfer"
        val inputParameters: MutableList<Type<*>> = ArrayList()
        val outputParameters: MutableList<TypeReference<*>> = ArrayList()
        val tAddress = Address(WalletHelper.convertWeb3Address(toAddress))
        inputParameters.add(tAddress)
        inputParameters.add(Uint256(value))
        val typeReference: TypeReference<Bool> = object : TypeReference<Bool>() {}
        outputParameters.add(typeReference)
        val function = Function(methodName, inputParameters, outputParameters)
        return FunctionEncoder.encode(function)
    }

    fun getTokenOwnerOfFunction(tokenId: String): Function {
        val methodName = "ownerOf"
        val inputParameters: MutableList<Type<*>> = java.util.ArrayList()
        val outputParameters: MutableList<TypeReference<*>> = java.util.ArrayList()

        val tokenIdParam = Uint256(tokenId.toBigInteger())
        inputParameters.add(tokenIdParam)
        val typeReference: TypeReference<Address> = object : TypeReference<Address>() {}
        outputParameters.add(typeReference)

        return Function(methodName, inputParameters, outputParameters)
    }

    fun getToken1155BalanceOfData(address: String, tokenId: String): String {
        val function = getToken1155BalanceOfFunction(address, tokenId)
        return FunctionEncoder.encode(function)
    }

    fun getToken1155BalanceOfFunction(address: String, tokenId: String): Function {
        val methodName = "balanceOf"
        val inputParameters: MutableList<Type<*>> = java.util.ArrayList()
        val outputParameters: MutableList<TypeReference<*>> = java.util.ArrayList()

        val addressParam = Address(WalletHelper.convertWeb3Address(address))
        val tokenIdParam = Uint256(tokenId.toBigInteger())
        inputParameters.add(addressParam)
        inputParameters.add(tokenIdParam)
        val typeReference: TypeReference<Uint256> = object : TypeReference<Uint256>() {}
        outputParameters.add(typeReference)

        return Function(methodName, inputParameters, outputParameters)
    }

    fun getTokenBalanceData(from: String): String {
        val function = getTokenBalanceFunction(from)
        return FunctionEncoder.encode(function)
    }

    fun getTokenBalanceFunction(from: String): Function {
        val ethAddress = WalletHelper.convertWeb3Address(from)
        val methodName = "balanceOf"
        val inputParameters: MutableList<Type<*>> = java.util.ArrayList()
        val outputParameters: MutableList<TypeReference<*>> = java.util.ArrayList()
        val address = Address(ethAddress)
        inputParameters.add(address)
        val typeReference: TypeReference<Uint256> = object : TypeReference<Uint256>() {}
        outputParameters.add(typeReference)
        return Function(methodName, inputParameters, outputParameters)
    }

    fun getNftTransferSignData(from: String, to: String, tokenId: String, amount: Int?): String {
        val methodName = "safeTransferFrom"
        val inputParameters: MutableList<Type<*>> = ArrayList()
        val outputParameters: MutableList<TypeReference<*>> = ArrayList()
        val fromParam = Address(WalletHelper.convertWeb3Address(from))
        val toParam = Address(WalletHelper.convertWeb3Address(to))
        val tokenIdParam = Uint256(tokenId.toBigInteger())
        inputParameters.add(fromParam)
        inputParameters.add(toParam)
        inputParameters.add(tokenIdParam)
        if (amount != null) {
            val amountParam = Uint256(amount.toBigInteger())
            val bytesParam = DynamicBytes("0x".toByteArray())
            inputParameters.add(amountParam)
            inputParameters.add(bytesParam)
        }
        val function = Function(methodName, inputParameters, outputParameters)
        return FunctionEncoder.encode(function)
    }

    fun getBidSignData(): String {
        val methodName = "bid"
        val inputParameters: MutableList<Type<*>> = ArrayList()
        val outputParameters: MutableList<TypeReference<*>> = ArrayList()
        val function = Function(methodName, inputParameters, outputParameters)
        return FunctionEncoder.encode(function)
    }

    fun getClaimSignData(): String {
        val methodName = "claim"
        val inputParameters: MutableList<Type<*>> = ArrayList()
        val outputParameters: MutableList<TypeReference<*>> = ArrayList()
        val function = Function(methodName, inputParameters, outputParameters)
        return FunctionEncoder.encode(function)
    }

    fun getApproveSignData(address: String?,amount: BigInteger?): String {
        val methodName = "approve"
        val value = Uint256(amount)
        val inputParameters: MutableList<Type<*>> = ArrayList()
        val outputParameters: MutableList<TypeReference<*>> = ArrayList()
        inputParameters.add(Address(address))
        inputParameters.add(value)
        val function = Function(methodName, inputParameters, outputParameters)
        return FunctionEncoder.encode(function)
    }

    fun getRegistrationData(imei: String, pubkey: String, sn: String,
                            timestamp: String, signature: String, authentication: String): String {
        val methodName = "register"
        val inputParameters: MutableList<Type<*>> = ArrayList()
        val outputParameters: MutableList<TypeReference<*>> = ArrayList()
        val imeiParam = Utf8String(imei)
        val pubkeyParam = DynamicBytes(pubkey.toHexByteArray())
        val snParam = Bytes32(sn.toHexByteArray())
        val timestampParam = Uint256(timestamp.toBigInteger())
        val signatureParam = DynamicBytes(signature.toHexByteArray())
        val authenticationParam = DynamicBytes(authentication.toHexByteArray())
        inputParameters.add(imeiParam)
        inputParameters.add(pubkeyParam)
        inputParameters.add(snParam)
        inputParameters.add(timestampParam)
        inputParameters.add(signatureParam)
        inputParameters.add(authenticationParam)
        val function = Function(methodName, inputParameters, outputParameters)
        return FunctionEncoder.encode(function)
    }

    fun createAccountData(pubKey: String, salt: BigInteger): Function {
        val methodName = "createAccount"
        val inputParameters: MutableList<Type<*>> = ArrayList()
        val outputParameters: MutableList<TypeReference<*>> = ArrayList()
        val pubKeyParam = DynamicBytes(pubKey.toHexByteArray())
        val saltParam = Uint256(salt)
        inputParameters.add(pubKeyParam)
        inputParameters.add(saltParam)
        val typeReference: TypeReference<Address> = object : TypeReference<Address>() {}
        outputParameters.add(typeReference)
        return Function(methodName, inputParameters, outputParameters)
    }

    fun getUserOpHash(address: String, nonce: BigInteger, initCode: String, callData: String,
                      callGasLimit: BigInteger, verificationGasLimit: BigInteger,
                      preVerificationGas: BigInteger, maxFeePerGas: BigInteger,
                      maxPriorityFeePerGas: BigInteger, paymasterAndData: String,
                      signature: String, validUntil: BigInteger, validAfter: BigInteger
    ): Function {
        val methodName = "getHash"
        val inputParameters: MutableList<Type<*>> = ArrayList()
        val outputParameters: MutableList<TypeReference<*>> = ArrayList()
        val struct = DynamicStruct(Address(address), Uint256(nonce), DynamicBytes(initCode.toHexByteArray()),
            DynamicBytes(callData.toHexByteArray()), Uint256(callGasLimit),
            Uint256(verificationGasLimit), Uint256(preVerificationGas), Uint256(maxFeePerGas),
            Uint256(maxPriorityFeePerGas), DynamicBytes(paymasterAndData.toHexByteArray()),
            DynamicBytes(signature.toHexByteArray())
        )

        inputParameters.add(struct)
        inputParameters.add(Uint48(validUntil))
        inputParameters.add(Uint48(validAfter))
        val typeReference: TypeReference<Bytes32> = object : TypeReference<Bytes32>() {}
        outputParameters.add(typeReference)
        return Function(methodName, inputParameters, outputParameters)
    }

    fun transferData(to: String, amount: BigInteger, callData: String): String {
        val methodName = "execute"
        val inputParameters: MutableList<Type<*>> = ArrayList()
        val outputParameters: MutableList<TypeReference<*>> = ArrayList()
        val toAddressParam = Address(to)
        val amountParam = Uint256(amount)
        val callDataParam = DynamicBytes(callData.toHexByteArray())
        inputParameters.add(toAddressParam)
        inputParameters.add(amountParam)
        inputParameters.add(callDataParam)
        return FunctionEncoder.encode(Function(methodName, inputParameters, outputParameters))
    }

    fun executeFunctionData(address: String, value: BigInteger, callData: String): String {
        val methodName = "execute"
        val inputParameters: MutableList<Type<*>> = ArrayList()
        val outputParameters: MutableList<TypeReference<*>> = ArrayList()
        inputParameters.add(Address(WalletHelper.convertWeb3Address(address)))
        inputParameters.add(Uint256(value))
        inputParameters.add(DynamicBytes(callData.toHexByteArray()))
        return FunctionEncoder.encode(Function(methodName, inputParameters, outputParameters))
    }

    fun balanceOfData(address: String): Function {
        val methodName = "balanceOf"
        val inputParameters: MutableList<Type<*>> = ArrayList()
        val outputParameters: MutableList<TypeReference<*>> = ArrayList()
        inputParameters.add(Address(address))
        outputParameters.add(object : TypeReference<Uint256>() {})
        return Function(methodName, inputParameters, outputParameters)
    }

    fun depositToData(address: String): String {
        val methodName = "depositTo"
        val inputParameters: MutableList<Type<*>> = ArrayList()
        val outputParameters: MutableList<TypeReference<*>> = ArrayList()
        inputParameters.add(Address(address))
        return FunctionEncoder.encode(Function(methodName, inputParameters, outputParameters))
    }

    fun nonceFunction(address: String): Function {
        val methodName = "getNonce"
        val inputParameters: MutableList<Type<*>> = ArrayList()
        val outputParameters: MutableList<TypeReference<*>> = ArrayList()
//        inputParameters.add(Address(address))
        outputParameters.add(object : TypeReference<Uint256>() {})
        return Function(methodName, inputParameters, outputParameters)
    }

    fun addEmailGuardianData(email: String, signature: String): String {
        val methodName = "addEmailGuardian"
        val inputParameters: MutableList<Type<*>> = ArrayList()
        val outputParameters: MutableList<TypeReference<*>> = ArrayList()
        inputParameters.add(Bytes32(Hash.sha3(email.toByteArray())))
        inputParameters.add(DynamicBytes(signature.toHexByteArray()))
        return FunctionEncoder.encode(Function(methodName, inputParameters, outputParameters))
    }

}