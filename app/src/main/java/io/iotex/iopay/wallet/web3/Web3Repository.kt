package io.iotex.iopay.wallet.web3

import androidx.appcompat.app.AppCompatActivity
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.TimeUtils
import com.blankj.utilcode.util.ToastUtils
import com.blankj.utilcode.util.Utils
import io.iotex.iopay.R
import io.iotex.iopay.data.UserStore
import io.iotex.iopay.data.db.ACTION_TYPE_APPROVE
import io.iotex.iopay.data.db.ACTION_TYPE_DAPP
import io.iotex.iopay.data.db.AppDatabase
import io.iotex.iopay.decoder.createContractDecoder
import io.iotex.iopay.decoder.decodeContract
import io.iotex.iopay.repo.TransferEntryMapRepo
import io.iotex.iopay.transaction.TransactionDialog
import io.iotex.iopay.transaction.approve.ApproveDialog
import io.iotex.iopay.transaction.bean.ExtraExpend
import io.iotex.iopay.transaction.bean.ExtraHead
import io.iotex.iopay.transaction.bean.OptionEntry
import io.iotex.iopay.transaction.sign.SignMessageDialog
import io.iotex.iopay.util.Constant
import io.iotex.iopay.util.TokenUtil
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.util.extension.asBigDecimal
import io.iotex.iopay.util.extension.cleanHexPrefix
import io.iotex.iopay.util.extension.i
import io.iotex.iopay.util.extension.toast
import io.iotex.iopay.util.extension.trimDecimal
import io.iotex.iopay.wallet.web3.aawallet.P256AccountManager
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.jetbrains.anko.doAsync
import org.jetbrains.anko.uiThread
import org.web3j.protocol.core.Response
import org.web3j.protocol.core.methods.request.Transaction
import org.web3j.tx.gas.DefaultGasProvider
import org.web3j.utils.Convert
import java.math.BigDecimal
import java.math.BigInteger

object Web3Repository {

    private const val APPROVE_CONTRACT_CODE = "0x095ea7b3"

    private var approveDialog: ApproveDialog? = null
    private var transactionDialog: TransactionDialog? = null
    private var signMessageDialog: SignMessageDialog? = null

    fun signTransaction(
        id: Long,
        from: String,
        to: String,
        data: String?,
        value: String?,
        dAppName: String,
        dAppLogo: String,
        dAppUrl: String,
        optionEntries: List<OptionEntry>? = null,
        gasLimit: String? = null,
        gasPrice: String? = null,
        confirm: (() -> Unit)?,
        response: (Long, String?, Response.Error?) -> Unit
    ) {
        if (WalletHelper.checkWalletExpired()) return
        if (WalletHelper.checkWatchModel()) return
        if (APPROVE_CONTRACT_CODE == Web3Delegate.resolveMethodCode(data)) {
            MainScope().launch {
                showApproveDialog(id, to, data, dAppName, gasLimit, gasPrice, confirm,response)
            }
            return
        } else {
            doAsync {
                val valueBig = if (!value.isNullOrBlank()) {
                    BigInteger(value.cleanHexPrefix(), 16)
                } else {
                    BigInteger.ZERO
                }
                val transaction = Transaction.createFunctionCallTransaction(
                    from.lowercase(),
                    null,
                    null,
                    null,
                    to.lowercase(),
                    valueBig,
                    data
                )
                "signTransaction ==>".i()
                var decodedData =
                    kotlin.runCatching { createContractDecoder(transaction)?.decode() }
                        .onFailure {
                            it.printStackTrace()
                        }
                        .getOrNull()
                if (decodedData == null) {
                    decodedData =
                        kotlin.runCatching { decodeContract(from, to, data, value) }.getOrNull()
                }
                val options = ArrayList<OptionEntry>()
                if (decodedData == null) {
                    val signature = Web3Delegate.resolveMethodCode(data)
                    val method = TransferEntryMapRepo().querySignatures(signature)
                    options.add(
                        OptionEntry(Utils.getApp().getString(R.string.method), method),
                    )
                    options.add(
                        OptionEntry(Utils.getApp().getString(R.string.from), from),
                    )
                    if (Web3Delegate.isContract(to)) {
                        options.add(
                            OptionEntry(
                                Utils.getApp().getString(R.string.contract_addr),
                                to
                            )
                        )
                    } else {
                        options.add(
                            OptionEntry(
                                Utils.getApp().getString(R.string.to),
                                to
                            )
                        )
                    }
                    if (valueBig != null && valueBig != BigInteger.ZERO) {
                        options.add(
                            OptionEntry(
                                Utils.getApp().getString(R.string.amount),
                                "${
                                    Convert.fromWei(
                                        BigDecimal(valueBig),
                                        Convert.Unit.ETHER
                                    )
                                } ${UserStore.getNetworkSymbol()}"
                            )
                        )
                    }
                } else {
                    if (optionEntries.isNullOrEmpty()) {
                        options.addAll(decodedData.options)
                    } else {
                        options.addAll(optionEntries)
                    }
                }

                var extraHead: ExtraHead? = null
                if (dAppUrl.isNotBlank()) {
                    extraHead = ExtraHead(dAppLogo, dAppUrl, false)
                }

                val token = AppDatabase.getInstance(Utils.getApp()).tokenDao().queryByAddress(WalletHelper.getCurChainId(),decodedData?.tokenAddress?:"")
                val extraExpend = if (valueBig > BigInteger.ZERO) {
                    ExtraExpend(
                        TokenUtil.weiToTokenBN(valueBig.toString()).asBigDecimal(),
                        WalletHelper.getCurNetwork()?.currencyPrice?.asBigDecimal()
                            ?: BigDecimal.ZERO,
                        TokenUtil.getNativeCurrencySymbol(),
                        18
                    )
                } else if (token != null) {
                    ExtraExpend(
                        decodedData?.value.asBigDecimal(),
                        token.price.asBigDecimal(),
                        token.symbol,
                        token.decimals
                    )
                } else null
                if (transactionDialog != null) {
                    transactionDialog = null
                    return@doAsync
                }
                uiThread {
                    transactionDialog =
                        TransactionDialog(
                            to,
                            valueBig,
                            data ?: "0x",
                            options,
                            gasLimit ?: "",
                            gasPrice ?: "",
                            extraHead,
                            extraExpend
                        ).apply {
                            onCancel = {
                                transactionDialog = null
                                response.invoke(id, null, Response.Error(0, "cancel"))
                            }
                            onTransactionConfirm = { gasLimit, gasPrice, _, _ ->
                                doTransaction(
                                    id,
                                    to,
                                    gasPrice.trimDecimal().toBigInteger(),
                                    gasLimit.toBigInteger(),
                                    data,
                                    valueBig,
                                    decodedData?.type ?: ACTION_TYPE_DAPP,
                                    decodedData?.to,
                                    decodedData?.tokenAddress,
                                    decodedData?.value,
                                    dAppName,
                                    {
                                        hideLoading()
                                        if (transactionDialog?.isAdded == true)
                                            transactionDialog?.dismissAllowingStateLoss()
                                        transactionDialog = null
                                        confirm?.invoke()
                                    },
                                    response
                                )
                            }
                        }
                    val activity = ActivityUtils.getTopActivity() as? AppCompatActivity ?:return@uiThread
                    if (ActivityUtils.isActivityAlive(activity)) {
                        transactionDialog?.show(
                            activity.supportFragmentManager,
                            System.currentTimeMillis().toString()
                        )
                    }
                }
            }
        }
    }

    fun doTransaction(
        id: Long,
        to: String,
        gasPrice: BigInteger?,
        gasLimit: BigInteger?,
        data: String?,
        value: BigInteger?,
        type: Int,
        decodeTo: String?,
        decodeContract: String?,
        decodeValue: String?,
        extra: String,
        confirm: () -> Unit,
        response: ((Long, String?, Response.Error?) -> Unit)
    ) {
        val exceptionHandler = CoroutineExceptionHandler { _, e ->
            MainScope().launch {
                Utils.getApp().getString(R.string.network_error).toast()
                confirm.invoke()
                response.invoke(id, null, Response.Error(200, e.message))
            }
        }
        MainScope().launch(Dispatchers.IO + exceptionHandler) {
            val wallet = WalletHelper.getCurWallet()
            val timestamp = TimeUtils.getNowMills().toString()
            if (wallet?.isAAWallet() == true) {
                P256AccountManager.executeTransaction(
                    timestamp,
                    to,
                    value ?: BigInteger.ZERO,
                    data ?: "0x",
                    type,
                    decodeTo,
                    decodeContract,
                    decodeValue,
                    extra,
                    {
                        MainScope().launch {
                            confirm.invoke()
                        }
                    }
                ) { res, error ->
                    MainScope().launch {
                        response.invoke(id, res, error)
                    }
                }
            } else {
                Web3Delegate.executeRawTransactionWithAction(
                    timestamp,
                    to,
                    value ?: BigInteger.ZERO,
                    gasPrice ?: DefaultGasProvider.GAS_PRICE,
                    gasLimit ?: DefaultGasProvider.GAS_LIMIT,
                    data ?: "0x",
                    type,
                    decodeTo,
                    decodeContract,
                    decodeValue,
                    extra,
                    {
                        MainScope().launch {
                            confirm.invoke()
                        }
                    },
                ) { res, error ->
                    MainScope().launch {
                        response.invoke(id, res, error)
                    }
                }
            }
        }
    }

    fun handleSignMessage(
        id: Long, data: ByteArray, addPrefix: Boolean, raw: String? = null, appUrl: String,
        appLogo: String, response: (Long, String?, Response.Error?) -> Unit
    ) {
        if (notSupportedAction()) {
            return
        }
        if(signMessageDialog != null){
            signMessageDialog = null
            return
        }
        signMessageDialog = SignMessageDialog(appLogo, appUrl, data, raw)
            .apply {
                onResult = { confirm ->
                    confirmSignMessage(confirm, id, data, addPrefix, response)
                    signMessageDialog = null
                }
            }
        val activity = ActivityUtils.getTopActivity() as? AppCompatActivity ?: return
        if (ActivityUtils.isActivityAlive(activity)) {
            signMessageDialog?.show(
                activity.supportFragmentManager,
                System.currentTimeMillis().toString()
            )
        }
    }

    fun handleSignTypedData(
        id: Long,
        data: String,
        appUrl: String,
        appLogo: String,
        response: (Long, String?, Response.Error?) -> Unit
    ) {
        if (notSupportedAction()) {
            return
        }
        if(signMessageDialog != null){
            signMessageDialog = null
            return
        }
        signMessageDialog = SignMessageDialog(appLogo, appUrl, ByteArray(64), data)
            .apply {
                onResult = { confirm ->
                    confirmSignTypeData(confirm, id, data, response)
                    signMessageDialog = null
                }
            }
        val activity = ActivityUtils.getTopActivity() as? AppCompatActivity ?: return
        signMessageDialog?.show(
            activity.supportFragmentManager,
            System.currentTimeMillis().toString()
        )
    }

    private fun showApproveDialog(
        id: Long, to: String, data: String?, appName: String,
        gasLimit: String? = null,
        gasPrice: String? = null,
        confirm: (() -> Unit)?,
        response: (Long, String?, Response.Error?) -> Unit
    ) {
        if (approveDialog != null) {
            approveDialog = null
            return
        }
        approveDialog = ApproveDialog(to, data ?: "", gasLimit ?: "", gasPrice ?: "")
            .apply {
                onCancel = {
                    approveDialog = null
                    response.invoke(id, null, Response.Error(0, "cancel"))
                }
                onApproveConfirm = { to: String,
                                     gasLimit: Long,
                                     gasPrice: String,
                                     data: String ->
                    doTransaction(
                        id,
                        to,
                        gasPrice.asBigDecimal().toBigInteger(),
                        gasLimit.toBigInteger(),
                        data,
                        BigInteger.ZERO,
                        ACTION_TYPE_APPROVE,
                        "",
                        to,
                        "",
                        appName,
                        {
                            hideLoading()
                            approveDialog?.dismissAllowingStateLoss()
                            approveDialog = null
                            confirm?.invoke()
                        },
                        response
                    )
                }
            }
        val activity = ActivityUtils.getTopActivity() as? AppCompatActivity ?: return
        if (ActivityUtils.isActivityAlive(activity)) {
            approveDialog?.show(
                activity.supportFragmentManager,
                System.currentTimeMillis().toString()
            )
        }
    }

    private fun confirmSignMessage(
        confirm: Boolean,
        id: Long,
        data: ByteArray,
        addPrefix: Boolean,
        response: (Long, String?, Response.Error?) -> Unit
    ) {
        MainScope().launch {
            if (confirm) {
                val signData = withContext(Dispatchers.IO) {
                    Web3Delegate.signMessage(data, addPrefix)
                }
                response.invoke(id, signData, null)
            } else {
                response.invoke(id, null, Response.Error(100, "cancel"))
            }
            if (signMessageDialog?.isAdded == true) signMessageDialog?.dismiss()
        }
    }

    private fun confirmSignTypeData(confirm: Boolean, id: Long, data: String, response: (Long, String?, Response.Error?) -> Unit) {
        MainScope().launch {
            if (confirm) {
                val signData = withContext(Dispatchers.IO) {
                    Web3Delegate.signTypedData(data)
                }
                response.invoke(id, signData, null)
            } else {
                response.invoke(id, null, Response.Error(100, "cancel"))
            }
            if (signMessageDialog?.isAdded == true) signMessageDialog?.dismiss()
        }
    }

    private fun notSupportedAction(): Boolean {
        if (Constant.currentWallet?.isAAWallet() == true) {
            ToastUtils.showShort(R.string.not_supporting_action)
            return true
        }
        if (Constant.currentWallet?.isWatch == true) {
            ToastUtils.showShort(R.string.watch_wallet_warning)
            return true
        }
        return false
    }
}