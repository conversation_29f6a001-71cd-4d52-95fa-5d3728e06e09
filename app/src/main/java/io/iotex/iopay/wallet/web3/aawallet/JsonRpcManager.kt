package io.iotex.iopay.wallet.web3.aawallet

import com.blankj.utilcode.util.Utils
import io.iotex.iopay.data.db.AppDatabase
import io.iotex.iopay.util.Config
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.viewmodel.showContractError
import io.iotex.userop.provider.JsonRpcProvider
import org.web3j.abi.datatypes.generated.Uint256
import org.web3j.protocol.core.Response
import java.math.BigInteger

object JsonRpcManager {

    private val config by lazy {
        val chainId = WalletHelper.getCurChainId()
        AppDatabase.getInstance(Utils.getApp()).networkAAConfigDao()
            .queryByChainId(chainId)
    }
    private val emailService by lazy {
        config?.emailService ?: Config.EMAIL_SERVICE
    }
    private val paymasterService by lazy {
        config?.paymasterService?.let {
            "${it}/${Config.paymasterService()}".replace("/+", "/").replace(":/+", "://")
        } ?: Config.PAYMASTER_SERVICE
    }

    fun sendEmailCode(address: String, email: String): String? {
        val response = JsonRpcProvider(emailService).send(
            "send_code",
            listOf(address, email),
            Response<String>()::class.java
        )
        showContractError(response.error?.message)
        return response.result
    }

    fun verifyCode(address: String, email: String, code: String): String? {
        val response = JsonRpcProvider(emailService).send(
            "verify_code",
            listOf(address, email, code),
            Response<String>()::class.java
        )
        showContractError(response.error?.message)
        return response.result
    }

    fun getGasRemain(address: String): RemainGas? {
        val response = JsonRpcProvider(paymasterService).send(
            "pm_gasRemain",
            listOf(address),
            RemainGasResponse::class.java
        )
        showContractError(response.error?.message)
        return response.result
    }

    fun requestGas(address: String): Boolean {
        val response = JsonRpcProvider(paymasterService).send(
            "pm_requestGas",
            listOf(address),
            Response<Boolean>()::class.java
        )
        showContractError(response.error?.message)
        return response.result ?: false
    }

    fun depositBalance(address: String): BigInteger {
        val response = JsonRpcProvider(Config.BUNDLER_SERVICE).send(
            "balanceOf",
            listOf(address),
            Response<Uint256>()::class.java
        )
        showContractError(response.error?.message)
        return response.result?.value ?: BigInteger.ZERO
    }
}

class RemainGas {
    val remain: String = "0x0"
    val last_request: Int = 0
    val total_used: String = "0"
}

class RemainGasResponse : Response<RemainGas>()