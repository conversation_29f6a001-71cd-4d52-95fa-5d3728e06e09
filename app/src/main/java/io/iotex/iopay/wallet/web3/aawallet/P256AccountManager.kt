package io.iotex.iopay.wallet.web3.aawallet

import androidx.appcompat.app.AppCompatActivity
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.TimeUtils
import com.blankj.utilcode.util.Utils
import com.github.iotexproject.antenna.crypto.SecureRandomUtils
import com.machinefi.lockscreen.LockAuthHelper
import io.iotex.iopay.data.UserStore
import io.iotex.iopay.data.db.ACTION_TYPE_BOUND_EMAIL
import io.iotex.iopay.data.db.ACTION_TYPE_NFT
import io.iotex.iopay.data.db.ACTION_TYPE_TRANSFER
import io.iotex.iopay.data.db.AppDatabase
import io.iotex.iopay.data.db.NFT_TYPE_1155
import io.iotex.iopay.data.db.NFT_TYPE_721
import io.iotex.iopay.data.db.STATUS_FAILED
import io.iotex.iopay.data.db.STATUS_WAITING
import io.iotex.iopay.data.db.Wallet
import io.iotex.iopay.util.Config
import io.iotex.iopay.util.FireBaseEvent
import io.iotex.iopay.util.FireBaseUtil
import io.iotex.iopay.util.TokenUtil
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.util.extension.cleanHexPrefix
import io.iotex.iopay.util.extension.isSupportedEmail
import io.iotex.iopay.util.extension.prependHexPrefix
import io.iotex.iopay.util.extension.toEvmAddress
import io.iotex.iopay.util.extension.toHexString
import io.iotex.iopay.viewmodel.showContractError
import io.iotex.iopay.wallet.aawallet.dialog.WalletExpiredDialog
import io.iotex.iopay.wallet.web3.FunctionEncodeUtil
import io.iotex.iopay.wallet.web3.Web3Delegate
import io.iotex.userop.Client
import io.iotex.userop.PresetBuilderOpts
import io.iotex.userop.UserOperation
import io.iotex.userop.api.SendUserOperationResponse
import io.iotex.userop.preset.builder.P256AccountBuilder
import io.iotex.userop.preset.middleware.PaymasterMiddleware
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext
import org.web3j.abi.FunctionEncoder
import org.web3j.abi.TypeReference
import org.web3j.abi.datatypes.Address
import org.web3j.abi.datatypes.Bool
import org.web3j.abi.datatypes.DynamicBytes
import org.web3j.abi.datatypes.Function
import org.web3j.abi.datatypes.Type
import org.web3j.abi.datatypes.generated.Bytes32
import org.web3j.abi.datatypes.generated.Uint256
import org.web3j.protocol.core.Response
import org.web3j.tx.gas.DefaultGasProvider
import java.math.BigInteger
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine

object P256AccountManager {

    private lateinit var instance: P256AccountBuilder
    private var sender: String? = null

    private lateinit var client: Client

    private val config by lazy {
        val chainId = WalletHelper.getCurChainId()
        AppDatabase.getInstance(Utils.getApp()).networkAAConfigDao()
            .queryByChainId(chainId)
    }

    private val factory by lazy {
        config?.factory ?: Config.CONTRACT_FACTORY
    }
    private val entryPoint by lazy {
        config?.entryPoint ?: Config.CONTRACT_ENTRY_POINT
    }
    private val emailContract by lazy {
        config?.boundEmail ?: Config.CONTRACT_BOUND_EMAIL
    }
    private val bundlerService by lazy {
        config?.bundlerService ?: Config.BUNDLER_SERVICE
    }
    private val paymasterService by lazy {
        config?.paymasterService?.let {
            "${it}/${Config.paymasterService()}".replace("/+", "/").replace(":/+", "://")
        } ?: Config.PAYMASTER_SERVICE
    }

    private var userOp: UserOperation? = null

    private val p256AccountBuilder: P256AccountBuilder
        get() = runBlocking {
            withContext(Dispatchers.IO) {
                return@withContext createP256AccountBuilder()
            }
        }

    private suspend fun createP256AccountBuilder(): P256AccountBuilder {
        val wallet = WalletHelper.getCurWallet()
        val rpc = WalletHelper.getCurNetwork()?.rpc ?: Config.IOTEX_RPC_URL
        val salt = wallet?.aaSalt?.cleanHexPrefix()?.toBigIntegerOrNull(16)
            ?: computeAASalt().cleanHexPrefix().toBigInteger(16)
        val opts = PresetBuilderOpts(
            entryPoint,
            factory,
            salt,
            bundlerService,
            PaymasterMiddleware(paymasterService)
        )

        val address = if (wallet?.isAAWallet() == true) wallet.address else this.sender
        if (!this::instance.isInitialized || address != instance.sender || rpc != instance.rpcUrl) {
            client = Client.init(rpc, entryPoint, bundlerService)
            instance = P256AccountBuilder.init(address, rpc, P256AccountSigner(), opts)
        }
        return instance
    }

    fun createWallet(alias: String): Wallet {
        this.sender = null
        val address = p256AccountBuilder.sender
        sender = address
        val salt = p256AccountBuilder.opts?.salt?.toString(16)?.prependHexPrefix() ?: computeAASalt()
        return Wallet(
            address.toEvmAddress(),
            alias,
            "",
            "",
            false,
            timestamp = TimeUtils.getNowMills().toString(),
            aaSalt = salt,
            effective = false
        )
    }

    suspend fun bindEmailWithoutAuth(email: String, signature: String, usePaymaster: Boolean): SendUserOperationResponse {
        val data = FunctionEncodeUtil.addEmailGuardianData(email, signature)
        val callData = p256AccountBuilder.execute(
            p256AccountBuilder.sender,
            BigInteger.ZERO,
            data
        )
        if (userOp == null || p256AccountBuilder.callData != callData) {
            p256AccountBuilder.callData = callData
        }
        p256AccountBuilder.setupGasMiddleware(usePaymaster)
        val response = client.sendUserOperation(p256AccountBuilder, userOp)
        showContractError(response.error)
        userOp = null
        return response
    }

    suspend fun bindEmail(timestamp: String, email: String, signature: String, confirm: () -> Unit) {
        if (!authTransaction()) return
        confirm.invoke()
        Web3Delegate.insertTransaction(
            timestamp,
            from = p256AccountBuilder.sender,
            type = ACTION_TYPE_BOUND_EMAIL
        )
        val response = bindEmailWithoutAuth(email, signature, false)
        val hash = response.wait.invoke()?.transactionHash
        val status = if (hash.isNullOrBlank()) {
            showContractError(response.error)
            STATUS_FAILED
        } else {
            val wallet = WalletHelper.getCurWallet()
            wallet?.email = email
            wallet?.let {
                AppDatabase.getInstance(Utils.getApp()).walletDao().updateWallet(wallet)
            }
            STATUS_WAITING
        }
        userOp = null
        Web3Delegate.updateAction(
            timestamp,
            hash,
            status = status,
        )
    }

    suspend fun transfer(
        timestamp: String,
        to: String,
        value: BigInteger,
        data: String,
        confirm: () -> Unit
    ) {
        if (!authTransaction()) return
        confirm.invoke()
        Web3Delegate.insertTransaction(
            timestamp,
            "",
            "",
            p256AccountBuilder.sender,
            to,
            TokenUtil.weiToTokenBN(value.toString()),
            data,
            "",
            "",
            "",
            "",
            STATUS_WAITING,
            ACTION_TYPE_TRANSFER,
            symbol = UserStore.getNetworkSymbol(),
        )
        val callData = p256AccountBuilder.execute(
            to,
            value,
            data
        )
        if (userOp == null || p256AccountBuilder.callData != callData) {
            p256AccountBuilder.callData = callData
        }
        val response = client.sendUserOperation(p256AccountBuilder, userOp)
        val hash = response.wait.invoke()?.transactionHash
        val status = if (hash.isNullOrBlank()) {
            showContractError(response.error)
            STATUS_FAILED
        } else {
            STATUS_WAITING
        }
        userOp = null
        Web3Delegate.updateAction(
            timestamp,
            hash,
            status,
        )
    }

    suspend fun transferErc20(
        timestamp: String,
        to: String,
        contract: String,
        amount: String,
        decimals: Long,
        symbol: String,
        confirm: () -> Unit
    ) {
        if (!authTransaction()) return
        confirm.invoke()
        val value = TokenUtil.toWei(amount, decimals.toInt())
        val data = FunctionEncoder.encode(
            Function(
                "transfer",
                listOf(Address(to.toEvmAddress()), Uint256(value)),
                listOf(object : TypeReference<Bool>() {})
            )
        )
        Web3Delegate.insertTransaction(
            timestamp,
            "",
            "",
            p256AccountBuilder.sender,
            to,
            TokenUtil.weiToTokenBN(value.toString(), decimals),
            data,
            "",
            "",
            "",
            "",
            STATUS_WAITING,
            ACTION_TYPE_TRANSFER,
            contract,
            symbol = symbol,
        )
        val callData = p256AccountBuilder.execute(
            contract,
            BigInteger.ZERO,
            data
        )
        if (userOp == null || p256AccountBuilder.callData != callData) {
            p256AccountBuilder.callData = callData
        }
        val response = client.sendUserOperation(p256AccountBuilder, userOp)
        val hash = response.wait.invoke()?.transactionHash
        val status = if (hash.isNullOrBlank()) {
            showContractError(response.error)
            STATUS_FAILED
        } else {
            STATUS_WAITING
        }
        userOp = null
        Web3Delegate.updateAction(
            timestamp,
            hash,
            status,
        )
    }

    suspend fun transferERC721(
        timestamp: String,
        to: String,
        contract: String,
        name: String,
        symbol: String,
        tokenId: BigInteger,
        confirm: () -> Unit
    ) {
        if (!authTransaction()) return
        confirm.invoke()
        val data = FunctionEncoder.encode(
            Function(
                "safeTransferFrom",
                listOf<Type<*>>(
                    Address(160, p256AccountBuilder.sender),
                    Address(160, to),
                    Uint256(tokenId)
                ), emptyList()
            )
        )
        Web3Delegate.insertTransaction(
            timestamp,
            "",
            "",
            p256AccountBuilder.sender,
            to,
            "1",
            data,
            "",
            "",
            "",
            "",
            STATUS_WAITING,
            ACTION_TYPE_NFT,
            contract,
            "",
            name,
            tokenId.toString(),
            NFT_TYPE_721,
            symbol,
        )
        val callData = p256AccountBuilder.execute(
            contract,
            BigInteger.ZERO,
            data
        )
        if (userOp == null || p256AccountBuilder.callData != callData) {
            p256AccountBuilder.callData = callData
        }
        val response = client.sendUserOperation(p256AccountBuilder, userOp)
        val hash = response.wait.invoke()?.transactionHash
        val status = if (hash.isNullOrBlank()) {
            showContractError(response.error)
            STATUS_FAILED
        } else {
            STATUS_WAITING
        }
        userOp = null
        Web3Delegate.updateAction(
            timestamp,
            hash,
            status,
        )
    }

    suspend fun transferERC1155(
        timestamp: String,
        to: String,
        contract: String,
        name: String,
        symbol: String,
        tokenId: BigInteger,
        value: BigInteger,
        confirm: () -> Unit
    ){
        if (!authTransaction()) return
        confirm.invoke()
        val data = FunctionEncoder.encode(
            Function(
                "safeTransferFrom",
                listOf<Type<*>>(
                    Address(160, p256AccountBuilder.sender),
                    Address(160, to),
                    Uint256(tokenId),
                    Uint256(value),
                    DynamicBytes("0x".toByteArray())
                ), emptyList()
            )
        )
        Web3Delegate.insertTransaction(
            timestamp,
            "",
            "",
            p256AccountBuilder.sender,
            to,
            value.toString(),
            data,
            "",
            "",
            "",
            "",
            STATUS_WAITING,
            ACTION_TYPE_NFT,
            contract,
            "",
            name,
            tokenId.toString(),
            NFT_TYPE_1155,
            symbol,
        )
        val callData = p256AccountBuilder.execute(
            contract,
            BigInteger.ZERO,
            data
        )
        if (userOp == null || p256AccountBuilder.callData != callData) {
            p256AccountBuilder.callData = callData
        }
        val response = client.sendUserOperation(p256AccountBuilder, userOp)
        val hash = response.wait.invoke()?.transactionHash
        val status = if (hash.isNullOrBlank()) {
            showContractError(response.error)
            STATUS_FAILED
        } else {
            STATUS_WAITING
        }
        userOp = null
        Web3Delegate.updateAction(
            timestamp,
            hash,
            status,
        )
    }

    suspend fun executeTransaction(
        timestamp: String,
        to: String,
        value: BigInteger,
        data: String,
        type: Int,
        decodeTo: String?,
        decodeContract: String?,
        decodeValue: String?,
        origin: String,
        confirm: ()->Unit,
        response: ((String?, Response.Error?) -> Unit)
    ) {
        if (!authTransaction()) return
        confirm.invoke()
        val methodCode = Web3Delegate.resolveMethodCode(`data`)
        val displayValue =
            if (decodeValue.isNullOrEmpty())
                TokenUtil.weiToTokenBN(value.toString())
            else decodeValue
        var symbol = UserStore.getNetworkSymbol()
        if(!decodeContract.isNullOrBlank()){
            val erc20Entry = AppDatabase.getInstance(Utils.getApp()).tokenDao().queryByAddress(WalletHelper.getCurChainId(),
                decodeContract)
            symbol = erc20Entry?.symbol?:""
        }
        Web3Delegate.insertTransaction(
            timestamp,
            "",
            "",
            WalletHelper.getCurWallet()?.address ?: "",
            decodeTo?:to,
            displayValue,
            data,
            "",
            "",
            "",
            "",
            STATUS_WAITING,
            type,
            decodeContract?:"",
            methodCode,
            "",
            "",
            "",
            symbol,
            "",
            origin
        )
        val callData = p256AccountBuilder.execute(
            to,
            value,
            data
        )
        if (userOp == null || p256AccountBuilder.callData != callData) {
            p256AccountBuilder.callData = callData
        }
        val sendUserOperationResponse = client.sendUserOperation(p256AccountBuilder, userOp)
        val hash = sendUserOperationResponse.wait.invoke()?.transactionHash
        if (hash.isNullOrBlank()) {
            response.invoke(hash, Response.Error(100, sendUserOperationResponse.error))
        } else {
            response.invoke(hash, null)
        }
        val status = if (hash.isNullOrBlank()) {
            showContractError(sendUserOperationResponse.error)
            STATUS_FAILED
        } else {
            STATUS_WAITING
        }
        userOp = null

        Web3Delegate.updateAction(
            timestamp,
            hash,
            status,
        )
        FireBaseUtil.logFireBase(FireBaseEvent.ACTION_AA_WALLET_TRANSFER)
    }

    fun hasBoundEmail(address: String): Boolean {
        val wallet = AppDatabase.getInstance(Utils.getApp()).walletDao()
            .queryWalletByAddress(address)
        if (wallet?.email?.isSupportedEmail() == true) {
            return true
        }
        val function = Function(
            "emails",
            listOf(Address(address)),
            listOf<TypeReference<*>>(object : TypeReference<Bytes32>() {})
        )
        val result = Web3Delegate.executeCall(emailContract, function, Bytes32::class.java)
            ?: return false
        return result.value.toHexString().cleanHexPrefix().toBigInteger(16) != BigInteger.ZERO
    }

    fun isEffective(address: String): Boolean {
        val function = Function(
            "publicKey",
            listOf(),
            listOf<TypeReference<*>>(object : TypeReference<DynamicBytes>() {})
        )
        val pubKey = kotlin.runCatching {
            Web3Delegate.executeCall(
                address,
                function,
                DynamicBytes::class.java
            )?.value?.toHexString()
        }.getOrNull() ?: return true
        return pubKey == P256KeyManager.getPubKey()
    }

    fun getRecoveryTimestamp(address: String): List<String> {
        return kotlin.runCatching {
            val function = Function(
                "pendingPublicKey",
                listOf(),
                listOf<TypeReference<*>>(
                    object : TypeReference<Uint256>() {},
                    object : TypeReference<DynamicBytes>() {}
                )
            )
            val result = Web3Delegate.executeCallForMultipleValue(address, function)
            return if (result.isNotEmpty()) {
                val timestamp = (result[0] as Uint256).value?.toString() ?: "0"
                val pubKey = (result[1] as DynamicBytes).value?.toHexString() ?: ""
                listOf(timestamp, pubKey)
            } else emptyList()
        }.getOrNull() ?: emptyList()
    }

    suspend fun stopRecovery(timestamp: String, address: String, confirm: ()->Unit) {
        if (!authTransaction()) return
        confirm.invoke()
        val data = FunctionEncodeUtil.stopRecoveryData()
        val callData = p256AccountBuilder.execute(address, BigInteger.ZERO, data)
        if (userOp == null || p256AccountBuilder.callData != callData) {
            p256AccountBuilder.callData = callData
        }
        val response = client.sendUserOperation(p256AccountBuilder, userOp)
        val hash = response.wait.invoke()?.transactionHash
        val status = if (hash.isNullOrBlank()) {
            showContractError(response.error)
            STATUS_FAILED
        } else {
            STATUS_WAITING
        }
        userOp = null
        Web3Delegate.insertTransaction(
            timestamp,
            hash,
            "",
            p256AccountBuilder.sender,
            address,
            "",
            data,
            "",
            "",
            "",
            "",
            status,
            ACTION_TYPE_BOUND_EMAIL,
        )
    }

    private fun buildUserOp(to: String, value: BigInteger, data: String, usePaymaster: Boolean = false): UserOperation {
        p256AccountBuilder.resetOp()
        val callData = p256AccountBuilder.execute(to, value, data)
        p256AccountBuilder.callData = callData
        p256AccountBuilder.setupGasMiddleware(usePaymaster)
        return client.buildUserOperation(p256AccountBuilder)
    }

    fun estimateGas(to: String, value: BigInteger, data: String): GasBean {
        return kotlin.runCatching {
            val wallet = WalletHelper.getCurWallet()
            val chainId = WalletHelper.getCurChainId()
            val config = AppDatabase.getInstance(Utils.getApp()).networkAAConfigDao().queryByChainId(chainId)
            var useFreeGas = config?.forceUsePaymaster ?: false
            var op = this.buildUserOp(to, value, data, useFreeGas)
            var list = calculateGas(op)
            val gasLimit = list[0]
            val gasPrice = list[1]
            val gasFee = gasLimit.multiply(gasPrice)
            val walletCache = AppDatabase.getInstance(Utils.getApp()).walletCacheDao().queryWalletCache(wallet?.address?:"", chainId)
            val balance = BigInteger( walletCache?.balance ?: "0")
            if (gasFee == BigInteger.ZERO && (useFreeGas.not() && gasFee >= balance || useFreeGas && balance > BigInteger.ZERO)) {
                op = this.buildUserOp(to, value, data, useFreeGas.not())
                list = calculateGas(op)
                useFreeGas = useFreeGas.not()
            }
            this.userOp = op
            return GasBean(list[0], list[1], BigInteger.ZERO, BigInteger.ZERO, useFreeGas,gasLimit != DefaultGasProvider.GAS_LIMIT)
        }.getOrNull() ?: GasBean(BigInteger.ZERO, BigInteger.ZERO, BigInteger.ZERO, BigInteger.ZERO, false,false)
    }

    private fun calculateGas(op: UserOperation): List<BigInteger> {
        val preVerificationGas = BigInteger(op.preVerificationGas.cleanHexPrefix(), 16)
        val verificationGasLimit = BigInteger(op.verificationGasLimit.cleanHexPrefix(), 16)
        val callGasLimit = BigInteger(op.callGasLimit.cleanHexPrefix(), 16)
        val gasLimit =  preVerificationGas + verificationGasLimit + callGasLimit

        val maxFeePerGas = BigInteger(op.maxFeePerGas.cleanHexPrefix(), 16)
        val maxPriorityFeePerGas = BigInteger(op.maxPriorityFeePerGas.cleanHexPrefix(), 16)
        val gasPrice = maxFeePerGas.min(maxPriorityFeePerGas)

        return listOf(gasLimit, gasPrice)
    }

    fun computeAASalt(): String {
        val initialEntropy = ByteArray(16)
        SecureRandomUtils.secureRandom().nextBytes(initialEntropy)
        val salt = initialEntropy.toHexString()
        val saltList = AppDatabase.getInstance(Utils.getApp()).walletDao()
            .queryAAWallets()
            .map { it.aaSalt }
        val index = saltList.indexOf(salt)
        return if (index == -1) salt else computeAASalt()
    }

    private suspend fun authTransaction(): Boolean {
        val context = ActivityUtils.getTopActivity() ?: return false
        val wallet = WalletHelper.getCurWallet() ?: return false
        if (!wallet.isAAWallet()) return false
        if (!wallet.effective) {
            WalletExpiredDialog().show((context as AppCompatActivity).supportFragmentManager, "wallet_expired_dialog")
            return false
        }
        return suspendCoroutine { continuation ->
            LockAuthHelper.showTransferAuth(
                context,
                onSuccess = {
                    continuation.resume(true)
                },
                onCancel = {
                    continuation.resume(false)
                }
            )
        }
    }
}
data class GasBean(
    var gasLimit: BigInteger,
    var gasPrice: BigInteger,
    var maxPriorityFeePerGas: BigInteger,
    var maxFeePerGas: BigInteger,
    val freeGas: Boolean,
    val success: Boolean,
)
