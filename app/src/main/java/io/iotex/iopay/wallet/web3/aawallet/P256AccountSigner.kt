package io.iotex.iopay.wallet.web3.aawallet

import io.iotex.iopay.util.extension.toHexByteArray
import io.iotex.userop.api.ISigner

class P256AccountSigner: ISigner {

    override val publicKey: ByteArray
        get() = P256KeyManager.getPubKey()?.toHexByteArray() ?: ByteArray(64)

    override fun sign(data: ByteArray): ByteArray {
        return P256KeyManager.signData(data).toHexByteArray()
    }
}