package io.iotex.iopay.widget

import android.content.Context
import android.graphics.Typeface
import android.util.AttributeSet
import androidx.appcompat.widget.AppCompatTextView
import io.iotex.iopay.R
import io.iotex.iopay.util.FontCacheHelper

/**
 * <com.chuangzhen.baseframe.widgets.view.DINTextView
 * android:id="@+id/tv_year"
 * android:layout_width="wrap_content"
 * android:layout_height="wrap_content"
 * android:layout_gravity="center"
 * android:layout_marginBottom="4dp"
 * android:gravity="center"
 * android:text="- "
 * android:textColor="@color/tv_color_66"
 * android:textSize="20sp"
 * app:textType="BOLD" />
 */
class DINTextView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyle: Int = 0
) : AppCompatTextView(context, attrs, defStyle) {

    init {
        val mTypedArray = context.obtainStyledAttributes(attrs, R.styleable.DINTextView)

        // 获取自定义属性和默认值
        val textType = mTypedArray.getInt(R.styleable.DINTextView_textType, 0)
        mTypedArray.recycle()
        applyCustomFont(context, textType)
    }

    private fun applyCustomFont(context: Context, textType: Int) {
        val customFont: Typeface? = if (1 == textType) {
            FontCacheHelper.getTypeface("fonts/DIN_Alternate_Bold.ttf", context)
        } else {
            FontCacheHelper.getTypeface("fonts/DIN-Regular.ttf", context)
        }
        if (null != customFont) {
            typeface = customFont
        }
    }
}