package io.iotex.iopay.xapp

import android.webkit.JavascriptInterface
import android.webkit.WebView
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.FragmentActivity
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.TimeUtils
import io.iotex.iopay.dapp.dialog.DAppNewWalletTipsDialog
import io.iotex.iopay.data.db.AppDatabase
import io.iotex.iopay.data.db.TrustDapp
import io.iotex.iopay.transaction.connect.ConnectWalletDialog
import io.iotex.iopay.util.Constant
import io.iotex.iopay.wallet.aawallet.dialog.NotSupportAAWalletDialog
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

abstract class DappBridge(
    private val webview: WebView,
    private val context: FragmentActivity,
    private val dappName: String,
    private val dappLogo: String
) {

    protected var dAppNewWalletTipsDialog: DAppNewWalletTipsDialog? = null
    protected var connectWalletDialog: ConnectWalletDialog? = null

    var connectAddress = ""

    @JavascriptInterface
    open fun postMessage(json: String) {

    }

    protected fun saveTrustDApp(address: String, host: String) {
        CoroutineScope(Dispatchers.IO).launch {
            AppDatabase.getInstance(context)
                .trustDapp().insertOrReplace(
                    TrustDapp(
                        address, host,
                        TimeUtils.getNowMills(), dappName, dappLogo
                    )
                )
        }
    }

    protected fun notSupportedAction(): Boolean {
        if (Constant.currentWallet?.isAAWallet() != false) {
            ActivityUtils.getTopActivity()?.let {
                it.runOnUiThread {
                    if (it is AppCompatActivity) {
                        NotSupportAAWalletDialog()
                            .show(
                                it.supportFragmentManager,
                                NotSupportAAWalletDialog::class.java.name
                            )
                    }
                }
            }
            return true
        }
        return false
    }

}