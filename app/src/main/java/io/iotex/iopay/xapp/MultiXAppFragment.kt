package io.iotex.iopay.xapp

import android.content.res.Configuration
import android.os.Bundle
import android.view.inputmethod.EditorInfo
import android.widget.EditText
import androidx.recyclerview.widget.GridLayoutManager
import com.drakeet.multitype.MultiTypeAdapter
import io.iotex.base.BaseViewModel
import io.iotex.base.bindbase.BaseBindFragment
import io.iotex.iopay.R
import io.iotex.iopay.databinding.FragmentMultiXappBinding
import io.iotex.iopay.util.extension.setGone
import io.iotex.iopay.util.extension.setVisible
import io.iotex.iopay.xapp.entity.MultiWebData
import io.iotex.iopay.xapp.item.MultiWebViewBinder
import java.util.concurrent.CopyOnWriteArrayList

/**
 * multi window item
 */
class MultiXAppFragment :
    BaseBindFragment<BaseViewModel, FragmentMultiXappBinding>(R.layout.fragment_multi_xapp) {

    private val mAdapter = MultiTypeAdapter()

    companion object {
        fun newInstance(): MultiXAppFragment {
            val args = Bundle()

            val fragment = MultiXAppFragment()
            fragment.arguments = args
            return fragment
        }
    }

    var currentUrl: String = "" //webView url

    //done button, add button,after click invoke back
    var done: (() -> Unit)? = null
    var add: (() -> Unit)? = null
    var clear: (() -> Unit)? = null
    var etUrl: ((et:EditText) -> Unit)? = null
    var close: ((Int) -> Unit)? = null
    var item: ((Int, MultiWebData) -> Unit)? = null

    override fun onResume() {
        super.onResume()
        mBinding.etUrl.setText(currentUrl)
        if (currentUrl.startsWith("https")) {
            mBinding.ivWebSafe.setImageResource(R.drawable.web_safe_https)
        } else {
            mBinding.ivWebSafe.setImageResource(R.drawable.web_safe_http)
        }
    }

    override fun initView() {
        mBinding.tvDone.setOnClickListener {
            done?.invoke()
        }
        mBinding.tvDoneLand.setOnClickListener {
            done?.invoke()
        }

        mBinding.ivAdd.setOnClickListener {
            add?.invoke()
        }
        mBinding.ivAddLand.setOnClickListener {
            add?.invoke()
        }

        mBinding.tvClear.setOnClickListener {
            clear?.invoke()
        }
        mBinding.tvClearLand.setOnClickListener {
            clear?.invoke()
        }

        mBinding.llRoot.setOnClickListener {
            //no thing, just cover click
        }

        mBinding.etUrl.setOnEditorActionListener { _, actionId, _ ->
            if (actionId == EditorInfo.IME_ACTION_DONE) {
                etUrl?.invoke(mBinding.etUrl)
            }
            false
        }

        val binder = MultiWebViewBinder().apply {
            closeClick = {
                close?.invoke(it)
            }
            itemClick = { position: Int, multiWebData: MultiWebData ->
                item?.invoke(position, multiWebData)
            }
        }
        mAdapter.register(MultiWebData::class.java, binder)
        mBinding.recyclerView.layoutManager = GridLayoutManager(requireActivity(), 2)
        mBinding.recyclerView.adapter = mAdapter

        mBinding.recyclerViewLand.layoutManager = GridLayoutManager(requireActivity(), 4)
        mBinding.recyclerViewLand.adapter = mAdapter
    }

    /**
     * after add remove, upData item
     */
    fun setNewData(data: CopyOnWriteArrayList<MultiWebData>) {
        mAdapter.items = data
        mAdapter.notifyDataSetChanged()
    }

    fun setOrientation(orientation: Int) {
        if (orientation == Configuration.ORIENTATION_LANDSCAPE) {
            mBinding.recyclerViewLand.setVisible()
            mBinding.recyclerView.setGone()
            mBinding.llLand.setVisible()
            mBinding.llVertical.setGone()
            mBinding.btnClose.setGone()
        } else if (orientation == Configuration.ORIENTATION_PORTRAIT) {
            mBinding.recyclerViewLand.setGone()
            mBinding.recyclerView.setVisible()
            mBinding.llVertical.setVisible()
            mBinding.llLand.setGone()
            mBinding.btnClose.setVisible()
        }
    }

    fun setUrl(url:String){
        currentUrl = url
        if(isResumed){
            mBinding.etUrl.setText(currentUrl)
            if (url.startsWith("https")) {
                mBinding.ivWebSafe.setImageResource(R.drawable.web_safe_https)
            } else {
                mBinding.ivWebSafe.setImageResource(R.drawable.web_safe_http)
            }
        }
    }
}