package io.iotex.iopay.xapp

import android.annotation.SuppressLint
import android.content.Intent
import android.text.TextUtils
import android.view.View
import android.view.inputmethod.EditorInfo
import com.blankj.utilcode.util.KeyboardUtils
import com.blankj.utilcode.util.SPUtils
import com.blankj.utilcode.util.ToastUtils
import com.drakeet.multitype.MultiTypeAdapter
import io.iotex.base.bindbase.BaseBindActivity
import io.iotex.iopay.R
import io.iotex.iopay.dapp.item.DAppCategoryItemBinder
import io.iotex.iopay.dapp.item.DAppRecordItemBinder
import io.iotex.iopay.dapp.viewmodel.SearchDAppViewModel
import io.iotex.iopay.databinding.ActivitySearchXappsBinding
import io.iotex.iopay.meta.SP_KEY_W3B_STREAM_MENU_LOCAL
import io.iotex.iopay.ui.RemoveWarningDialog
import io.iotex.iopay.util.Constant
import io.iotex.iopay.util.Constant.IOPAY_W3BSTREAM_ENABLE
import io.iotex.iopay.util.IoPayConstant
import io.iotex.iopay.util.*
import org.jetbrains.anko.toast
import java.util.concurrent.TimeUnit

class SearchXAppsActivity : BaseBindActivity<SearchDAppViewModel,ActivitySearchXappsBinding>(R.layout.activity_search_xapps) {
    private val adapterSearch = MultiTypeAdapter()
    private val adapterRecord = MultiTypeAdapter()

    private val content by lazy {
        intent.getStringExtra("content") ?: ""
    }

    @SuppressLint("CheckResult")
    override fun initView() {
        adapterSearch.register(DAppCategoryItemBinder(supportFragmentManager))
        mBinding.appsList.adapter = adapterSearch
        adapterRecord.register(DAppRecordItemBinder())
        mBinding.recyclerView.adapter = adapterRecord
        mBinding.tvSearch.setOnClickListener {
            val content = mBinding.etSearch.text.trim().toString()
            if (mBinding.tvSearch.text != getString(R.string.cancel)) {
                KeyboardUtils.hideSoftInput(mBinding.tvSearch)
                search(content)
                FireBaseUtil.logFireBase(FireBaseEvent.ACTION_BROWSER_SEARCH_PAGE_CLICK_SEARCH)
            } else {
                KeyboardUtils.hideSoftInput(mBinding.tvSearch)
                finish()
            }
        }
        if (content.isNotEmpty()) mBinding.etSearch.setText(content)
        mBinding.etSearch.setOnEditorActionListener { _, actionId, _ ->
            if (actionId == EditorInfo.IME_ACTION_SEARCH) {
                val content = mBinding.etSearch.text.trim().toString()
                search(content)
            }
            false
        }

        RxUtil.textChange(mBinding.etSearch)
            .debounce(100, TimeUnit.MILLISECONDS)
            .compose(RxUtil.applySchedulers())
            .subscribe {
                if (!TextUtils.isEmpty(it)) {
                    mBinding.ivDelete.visibility = View.VISIBLE
                    mBinding.appsList.visibility = View.VISIBLE
                    mBinding.tvSearch.setText(R.string.Search)
                    val content = it.trim()
                    mViewModel.searchDAppRecord(content)
                    mViewModel.searchDApp(content)
                } else {
                    mBinding.ivDelete.visibility = View.GONE
                    mBinding.tvDapps.visibility = View.GONE
                    mBinding.appsList.visibility = View.GONE
                    mBinding.tvSearch.setText(R.string.cancel)
                    mViewModel.searchAllDAppRecord()
                }
            }
        mBinding.ivHistoryDelete.setOnClickListener {
            RemoveWarningDialog(getString(R.string.are_you_sure_you_want_to_delete))
                .apply {
                    onConfirmClick = {
                        dismiss()
                        mViewModel.deleteAllDAppRecord()
                        adapterRecord.items = ArrayList()
                        adapterRecord.notifyDataSetChanged()
                        mBinding.relHistory.visibility = View.GONE
                    }
                }.show(supportFragmentManager,System.currentTimeMillis().toString())
        }
        mBinding.ivDelete.setOnClickListener {
            mBinding.etSearch.text.clear()
        }

        mBinding.etSearch.requestFocus()
        KeyboardUtils.showSoftInput(mBinding.etSearch)
    }

    fun search(content: String) {
        if (content.isNotEmpty()) {
            KeyboardUtils.hideSoftInput(mBinding.etSearch)
            if(content == IOPAY_W3BSTREAM_ENABLE){
                SPUtils.getInstance().put(SP_KEY_W3B_STREAM_MENU_LOCAL,true)
                ToastUtils.showShort("w3bstream enable")
                finish()
                return
            }
            if(content == Constant.IOPAY_W3BSTREAM_DISABLE){
                SPUtils.getInstance().put(SP_KEY_W3B_STREAM_MENU_LOCAL, false)
                ToastUtils.showShort("w3bstream disable")
                finish()
                return
            }
            mViewModel.recordDApp(content)
            val intent = Intent(this@SearchXAppsActivity, XAppsActivity::class.java)
            intent.putExtra(IoPayConstant.BROWSER_URL, content)
            startActivity(intent)
            finish()
        } else {
            toast(R.string.must_not_empty)
        }
    }

    override fun finish() {
        super.finish()
        overridePendingTransition(android.R.anim.fade_in, android.R.anim.fade_out)
    }

    override fun onResume() {
        super.onResume()
        mViewModel.searchAllDAppRecord()
    }

    override fun initEvent() {
        mViewModel.dAppRecordLiveData.observe(this){
            if(it!=null){
                adapterRecord.items = it
                adapterRecord.notifyDataSetChanged()
                if (it.isNotEmpty()) {
                    mBinding.relHistory.visibility = View.VISIBLE
                } else {
                    mBinding.relHistory.visibility = View.GONE
                }
            }
        }

        mViewModel.dAppLiveData.observe(this){
            if(it!=null){
                adapterSearch.items = it
                adapterSearch.notifyDataSetChanged()
                if (it.isEmpty()) {
                    mBinding.tvDapps.visibility = View.GONE
                } else {
                    mBinding.tvDapps.visibility = View.VISIBLE
                }
            }
        }
    }
}