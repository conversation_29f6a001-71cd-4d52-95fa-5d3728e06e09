package io.iotex.iopay.xapp

import android.content.Context
import android.content.Intent
import com.blankj.utilcode.util.LogUtils
import io.iotex.base.BaseViewModel
import io.iotex.iopay.R
import io.iotex.iopay.base.BaseBindToolbarActivity
import io.iotex.iopay.databinding.ActivityWebBinding

class WebActivity : BaseBindToolbarActivity<BaseViewModel,ActivityWebBinding>(R.layout.activity_web) {
    companion object{
        fun startActivity(context: Context,url:String){
            val intent = Intent(context, WebActivity::class.java)
            intent.putExtra("url",url)
            context.startActivity(intent)
        }
    }

    val url by lazy {
        intent.getStringExtra("url")?:""
    }

    override fun initView() {
        LogUtils.i(url)
        val webViewFrag = XAppFragment.newInstance(url, "", "")
        supportFragmentManager.beginTransaction().add(R.id.flWeb,webViewFrag).commit()
        webViewFrag.apply {
            titleBack = {
                setToolbarTitle(it)
            }
        }
    }
}
