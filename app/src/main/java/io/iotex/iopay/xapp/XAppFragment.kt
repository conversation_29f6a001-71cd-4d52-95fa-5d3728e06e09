package io.iotex.iopay.xapp

import android.content.Intent
import android.graphics.Bitmap
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.view.View
import android.webkit.GeolocationPermissions
import android.webkit.WebChromeClient
import android.webkit.WebResourceError
import android.webkit.WebResourceRequest
import android.webkit.WebSettings
import android.webkit.WebView
import android.webkit.WebViewClient
import com.blankj.utilcode.constant.PermissionConstants
import com.blankj.utilcode.util.PermissionUtils
import com.machinefi.walletconnect2.WC2Helper
import io.iotex.base.BaseViewModel
import io.iotex.base.bindbase.BaseBindFragment
import io.iotex.iopay.MainActivity
import io.iotex.iopay.R
import io.iotex.iopay.SchemeUtil
import io.iotex.iopay.data.UserStore
import io.iotex.iopay.databinding.FragmentXappBinding
import io.iotex.iopay.meta.ui.GeoHomeActivity
import io.iotex.iopay.support.eventbus.NetworkSwitchEvent
import io.iotex.iopay.support.eventbus.SwitchWalletEvent
import io.iotex.iopay.support.eventbus.WebViewBinoAiRefreshErrorEvent
import io.iotex.iopay.support.eventbus.WebViewDePinErrorEvent
import io.iotex.iopay.support.eventbus.WebViewDePinRefreshErrorEvent
import io.iotex.iopay.util.Config
import io.iotex.iopay.util.Constant
import io.iotex.iopay.util.Constant.IOPAY_HOST_FIOBACK
import io.iotex.iopay.util.Constant.IOPAY_IOTEX_2
import io.iotex.iopay.util.Constant.IOPAY_OPEN_W3BSTREAM_PAGE
import io.iotex.iopay.util.FireBaseEvent
import io.iotex.iopay.util.FireBaseUtil
import io.iotex.iopay.util.IoPayConstant
import io.iotex.iopay.util.RegexUtils
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.util.extension.i
import io.iotex.iopay.wallet.TransferActivity
import io.iotex.iopay.xapp.bitcoin.BitcoinDappBridge
import io.iotex.iopay.xapp.bitcoin.BitcoinScriptConfig
import io.iotex.iopay.xapp.solana.SolanaDappBridge
import io.iotex.iopay.xapp.solana.SolanaScriptConfig
import io.iotex.iopay.xapp.trust.Web3DappBridge
import io.iotex.iopay.xapp.trust.Web3ScriptConfig
import io.iotex.iopay.xapp.trust.presetAddress
import io.iotex.iopay.xapp.trust.sendBitcoinNetworkChanged
import io.iotex.iopay.xapp.trust.sendChainChanged
import io.iotex.iopay.xapp.trust.sendMessage
import io.iotex.iopay.xapp.trust.setConfig
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import java.net.URLEncoder

/**
 * for multi window, webView Fragment
 */
class XAppFragment : BaseBindFragment<BaseViewModel, FragmentXappBinding>(R.layout.fragment_xapp) {

    companion object {
        fun newInstance(url: String, logo: String, name: String, loadingBar:Boolean = true): XAppFragment {
            val args = Bundle()
            args.putString("url", url)
            args.putString("dAppLogo", logo)
            args.putString("dAppName", name)
            args.putBoolean("loadingBar", loadingBar)
            val fragment = XAppFragment()
            fragment.arguments = args
            return fragment
        }
    }

    private var hasInjectedJS = false
    var firstPageFinished = false
    private var pageProgress = 0
    private var injectJsThreshold = 20
    private var web3DappBridge: Web3DappBridge? = null
    private var solanaDappBridge: SolanaDappBridge? = null
    private var bitcoinDappBridge: BitcoinDappBridge? = null

    private var sendMessage: String? = null
    private var presetAddress: String? = null

    private val url by lazy {
        val url = arguments?.getString("url") ?: ""
        if (!RegexUtils.isXAppsURl(url)) {
            "https://www.google.com/search?q=${URLEncoder.encode(url, "UTF-8")}"
        } else if (RegexUtils.isLinkURl(url) && !url.startsWith("http")) {
            "https://$url"
        } else {
            url
        }
    }

    private val dAppLogo by lazy {
        arguments?.getString("dAppLogo") ?: ""
    }

    private val dAppName by lazy {
        arguments?.getString("dAppName") ?: ""
    }

    private val loadingBar by lazy {
        arguments?.getBoolean("loadingBar", true) ?: true
    }

    var title: String = "" //webView title
    var currentUrl: String = "" //webView url

    //title back foreword upData invoke
    var urlBack: ((String) -> Unit)? = null
    var titleBack: ((String) -> Unit)? = null
    var backAndForeCallback: ((back: Boolean, fore: Boolean) -> Unit)? = null
    var scrollCallback: ((scroll: Boolean) -> Unit)? = null

    val webView by lazy {
        mBinding.webView
    }

    override fun initView() {
        initWebSetting()
        initWebViewClient()
        initWebChromeClient()
        initDownload()
        currentUrl = url
        webView.loadUrl(url)
        webView.setOnScrollChangeListener { _, _, _, _, _ ->
            scrollCallback?.invoke(true)
        }
    }

    override fun initEvent() {
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this)
        }
    }

    private fun initWebSetting() {
        val webSettings = webView.settings
        webSettings.javaScriptEnabled = true
        webSettings.domStorageEnabled = true
        WebView.setWebContentsDebuggingEnabled(true)
        webSettings.allowFileAccess = true
        webSettings.cacheMode = WebSettings.LOAD_DEFAULT
        webSettings.userAgentString = "Mozilla/5.0 (Linux; Android ${Build.VERSION.RELEASE}; ${Build.MODEL}) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/85.0.4183.81 Mobile Safari/537.36"
        if (!webSettings.userAgentString.contains("IoPayAndroid"))
            webSettings.userAgentString = "${webSettings.userAgentString} IoPayAndroid"

        web3DappBridge = Web3DappBridge(webView, requireActivity(), dAppName, dAppLogo).apply {
            webView.addJavascriptInterface(
                this, "_tw_"
            )
        }
        solanaDappBridge = SolanaDappBridge(webView, requireActivity(), dAppName, dAppLogo).apply {
            webView.addJavascriptInterface(
                this, "_phantom_"
            )
        }
        bitcoinDappBridge = BitcoinDappBridge(webView, requireActivity(), dAppName, dAppLogo).apply {
            webView.addJavascriptInterface(
                this, "_iopay_"
            )
        }
    }

    /**
     * WebChromeClient
     */
    private fun initWebChromeClient() {
        webView.webChromeClient = object : WebChromeClient() {

            override fun onReceivedTitle(view: WebView?, title: String?) {
                super.onReceivedTitle(view, title)
                if (title?.startsWith("http") == false) {
                    <EMAIL> = title
                    titleBack?.invoke(title)
                }
            }

            override fun onProgressChanged(view: WebView, newProgress: Int) {
                pageProgress = newProgress
                if (newProgress == 100) {
                    mBinding.progressBar.visibility = View.GONE
                } else {
                    if (loadingBar) mBinding.progressBar.visibility = View.VISIBLE
                    mBinding.progressBar.progress = newProgress
                }

                if (!hasInjectedJS && newProgress >= injectJsThreshold) {
                    "onProgressChanged".i()
                    hasInjectedJS = true
                    BitcoinScriptConfig.injectProvider(webView)
                    SolanaScriptConfig.injectProvider(webView)
                    Web3ScriptConfig.injectProvider(webView)
                }
            }

            override fun onGeolocationPermissionsShowPrompt(
                origin: String,
                callback: GeolocationPermissions.Callback
            ) {
                PermissionUtils.permission(PermissionConstants.LOCATION)
                    .callback(object : PermissionUtils.SimpleCallback {
                        override fun onGranted() {
                            callback.invoke(origin, true, false)
                        }

                        override fun onDenied() {
                            callback.invoke(origin, false, false)
                        }
                    }).request()
                super.onGeolocationPermissionsShowPrompt(origin, callback)
            }
        }
    }

    /**
     * WebViewClient
     */
    private fun initWebViewClient() {
        webView.webViewClient = object : WebViewClient() {

            override fun onReceivedError(
                view: WebView?,
                request: WebResourceRequest?,
                error: WebResourceError?
            ) {
                if (url.contains(Config.DEPIN_SCAN_WEB_URL) && !firstPageFinished) {
                    EventBus.getDefault().post(WebViewDePinErrorEvent())
                }
                if (url.contains(Config.DEPIN_SCAN_WEB_URL)) {
                    EventBus.getDefault().post(WebViewDePinRefreshErrorEvent())
                }
                if (url.contains(Config.BINO_AI_WEB_URL)) {
                    EventBus.getDefault().post(WebViewBinoAiRefreshErrorEvent())
                }
                super.onReceivedError(view, request, error)
            }

            override fun onPageFinished(view: WebView?, url: String?) {
                super.onPageFinished(view, url)
                mBinding.progressBar.visibility = View.GONE
                scrollCallback?.invoke(false)
                "onPageFinished".i()
                if (!hasInjectedJS) {
                    hasInjectedJS = true
                    BitcoinScriptConfig.injectProvider(webView)
                    SolanaScriptConfig.injectProvider(webView)
                    Web3ScriptConfig.injectProvider(webView)
                }
                firstPageFinished = true
                sendMessage?.let {
                    webView.sendMessage(it,UserStore.getWalletAddress())
                    sendMessage = null
                }
                presetAddress?.let {
                    webView.presetAddress(it)
                    presetAddress = null
                }
                backAndForeCallback?.invoke(webView.canGoBack(), webView.canGoForward())
            }

            override fun onPageStarted(view: WebView?, url: String?, favicon: Bitmap?) {
                super.onPageStarted(view, url, favicon)
                if (loadingBar) mBinding.progressBar.visibility = View.VISIBLE
                hasInjectedJS = false
                "onPageStarted".i()

                backAndForeCallback?.invoke(webView.canGoBack(), webView.canGoForward())
            }

            override fun onLoadResource(view: WebView?, url: String?) {
                view?.url?.let {
                    if(currentUrl != it){
                        currentUrl = it
                        urlBack?.invoke(it)
                    }
                }
                backAndForeCallback?.invoke(webView.canGoBack(), webView.canGoForward())
                super.onLoadResource(view, url)
            }

            override fun shouldOverrideUrlLoading(
                view: WebView?,
                request: WebResourceRequest?
            ): Boolean {
                val url = request?.url?.toString()
                //iopay://io.iotex.iopay/fioback
                if (url == IOPAY_HOST_FIOBACK) {
                    MainActivity.startActivity(requireContext())
                }
                val binoUrl = UserStore.getBinoAiUrl().ifEmpty {
                    Config.BINO_AI_WEB_URL
                }
                if (url?.startsWith("http") == true
                    && (url == IOPAY_IOTEX_2 || <EMAIL>(binoUrl))) {
                    val intent = Intent(requireActivity(), XAppsActivity::class.java)
                    intent.putExtra(IoPayConstant.BROWSER_URL, url)
                    startActivity(intent)
                    return true
                }
                //iopay://open_w3bstream_page
                if (url == IOPAY_OPEN_W3BSTREAM_PAGE) {
                    val intent = Intent(requireActivity(), GeoHomeActivity::class.java)
                    startActivity(intent)
                }
                //wc2
                if (WC2Helper.isValidWc2Bridge(url)) {
                    WC2Helper.pair(url, dAppName, dAppLogo)
                    return true
                }
                if (url?.startsWith("iopay", true) == true) {
                    SchemeUtil.goto(requireContext(), url)
                    return true
                }

                if (WalletHelper.isEvmTransferLink(url?:"")) {
                    TransferActivity.startByLink(requireContext(), url?:"")
                    return true
                }

                if (url?.startsWith("http") == false) {
                    try {
                        val intent = Intent(Intent.ACTION_VIEW, Uri.parse(url))
                        startActivity(intent)
                    } catch (e: Exception) {
                        return super.shouldOverrideUrlLoading(view, request)
                    }
                    return true
                }
                return super.shouldOverrideUrlLoading(view, request)
            }
        }
    }

    private fun initDownload() {
        webView.setDownloadListener { url, _, _, _, _ ->
            if (url.isEmpty()) return@setDownloadListener
            kotlin.runCatching {
                val intent = Intent(Intent.ACTION_VIEW)
                intent.addCategory(Intent.CATEGORY_BROWSABLE)
                intent.data = Uri.parse(url)
                startActivity(intent)
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onNetworkSwitchEvent(event: NetworkSwitchEvent) {
        val isFromBitcoinNet = WalletHelper.isBitcoinNetwork(event.from.chainId)
        val isFromSolanaNetwork = WalletHelper.isSolanaNetwork(event.from.chainId)
        val isToBitcoinNet = WalletHelper.isBitcoinNetwork(event.to.chainId)
        val isSolanaNetwork = WalletHelper.isSolanaNetwork(event.to.chainId)
        if (isFromBitcoinNet != isToBitcoinNet || isFromSolanaNetwork != isSolanaNetwork) {
            webView.reload()
        } else {
            webView.setConfig(WalletHelper.getCurChainId(), WalletHelper.getCurRpcUrl())
            webView.sendChainChanged(WalletHelper.getCurChainId())
            webView.sendBitcoinNetworkChanged(if (isToBitcoinNet) Config.BITCOIN_TEST_NETWORK else Config.BITCOIN_MAIN_NETWORK)
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onSwitchWalletEvent(event: SwitchWalletEvent) {
        if (!web3DappBridge?.connectAddress.isNullOrEmpty()
            && web3DappBridge?.connectAddress != Constant.currentWallet?.address ||
            !bitcoinDappBridge?.connectAddress.isNullOrEmpty() &&
            bitcoinDappBridge?.connectAddress != Constant.currentWallet?.getBitcoinWallet()?.bitcoinAddress
        ) {
            webView.reload()
        }
    }

    fun hide() {
        if (isResumed) {
            webView.onPause()
        }
    }

    fun show() {
        if (isResumed) {
            webView.onResume()
        }
    }

    fun goBack() {
        if (webView.canGoBack()) {
            webView.goBack()
        }
    }

    fun evaluateMessage(message: String) {
        if (firstPageFinished) {
            webView.sendMessage(message, UserStore.getWalletAddress())
        } else {
            sendMessage = message
        }
    }

    fun presetAddress(address: String) {
        if (firstPageFinished) {
            webView.presetAddress(address)
        } else {
            presetAddress = address
        }
    }

    override fun onResume() {
        super.onResume()
        webView.onResume()
        webView.resumeTimers()
    }

    override fun onPause() {
        super.onPause()
        webView.onPause()
        webView.pauseTimers()
    }

    override fun onDestroy() {
        webView.removeAllViews()
        webView.destroy()

        val bundleEvent = Bundle()
        bundleEvent.putString(FireBaseUtil.DAPP_NAME, dAppName.lowercase())
        bundleEvent.putLong(FireBaseUtil.END_TIME, System.currentTimeMillis())
        bundleEvent.putString(FireBaseUtil.DAPP_URL, url)
        FireBaseUtil.logFireBase(FireBaseEvent.DAPP_QUIT, bundleEvent)

        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this)
        }
        super.onDestroy()
    }
}