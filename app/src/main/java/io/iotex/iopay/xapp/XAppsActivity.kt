package io.iotex.iopay.xapp

import android.annotation.SuppressLint
import android.content.Intent
import android.content.pm.ActivityInfo
import android.content.res.Configuration
import android.os.Build
import android.os.Bundle
import android.view.GestureDetector
import android.view.MotionEvent
import android.view.View
import android.view.WindowManager
import android.view.inputmethod.EditorInfo
import android.webkit.WebView
import android.widget.EditText
import android.widget.TextView
import androidx.appcompat.widget.AppCompatImageView
import androidx.core.view.GestureDetectorCompat
import androidx.lifecycle.MutableLiveData
import com.blankj.utilcode.util.BarUtils
import com.blankj.utilcode.util.ConvertUtils
import com.blankj.utilcode.util.DeviceUtils
import com.blankj.utilcode.util.KeyboardUtils
import com.blankj.utilcode.util.Utils
import com.google.firebase.analytics.FirebaseAnalytics
import com.machinefi.walletconnect2.WC2Helper
import io.iotex.base.bindbase.BaseBindActivity
import io.iotex.iopay.R
import io.iotex.iopay.data.UserStore
import io.iotex.iopay.data.db.AppDatabase
import io.iotex.iopay.databinding.ActivityXappsBinding
import io.iotex.iopay.ui.OptionsMenu
import io.iotex.iopay.util.AndroidBug5497Workaround
import io.iotex.iopay.util.Constant
import io.iotex.iopay.util.FireBaseEvent
import io.iotex.iopay.util.FireBaseUtil
import io.iotex.iopay.util.IoPayConstant
import io.iotex.iopay.util.IoPayConstant.Companion.BROWSER_DAPP_LOGO
import io.iotex.iopay.util.IoPayConstant.Companion.BROWSER_DAPP_NAME
import io.iotex.iopay.util.IoPayConstant.Companion.BROWSER_URL
import io.iotex.iopay.util.RegexUtils
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.util.extension.setGone
import io.iotex.iopay.util.extension.setVisible
import io.iotex.iopay.util.extension.toBitmap
import io.iotex.iopay.wallet.TransferActivity
import io.iotex.iopay.wallet.dialog.ScanLoadingDialog
import io.iotex.iopay.xapp.entity.MultiWebData
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.jetbrains.anko.toast
import java.net.URLEncoder
import java.util.concurrent.CopyOnWriteArrayList
import kotlin.math.abs

class XAppsActivity : BaseBindActivity<XAppsViewModel, ActivityXappsBinding>(R.layout.activity_xapps) {

    private val walletEmptyLiveData = MutableLiveData<Boolean>()

    //dAppLogo
    private val dAppLogo by lazy {
        intent?.getStringExtra(BROWSER_DAPP_LOGO) ?: ""
    }

    //dAppName
    private val dAppName by lazy {
        intent?.getStringExtra(BROWSER_DAPP_NAME)?:""
    }

    //url
    private val url: String
        get() {
            val url = intent?.getStringExtra(BROWSER_URL) ?: ""
            return if (!RegexUtils.isXAppsURl(url)) {
                "https://www.google.com/search?q=${URLEncoder.encode(url, "UTF-8")}"
            } else if (RegexUtils.isLinkURl(url) && !url.startsWith("http")) {
                "https://$url"
            } else {
                url
            }
        }

    //scan code loading
    private val mLoadingDialog by lazy {
        ScanLoadingDialog.Builder(this)
            .setCancelable(true)
            .setCancelOutside(true).create()
    }

    private var optionsMenu: OptionsMenu? = null
    private var currentPosition = 0

    //multi item frag
    private val multiFrag by lazy {
        MultiXAppFragment.newInstance()
    }

    //webView frag list
    private val webViewFragList = CopyOnWriteArrayList<MultiWebData>()

    private var scroll: Boolean = false

    //new webView window
    @SuppressLint("MissingSuperCall")
    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        this.intent = intent
        addWebViewFrag()
    }

    override fun onResume() {
        super.onResume()
        changeOrientation()
        intent?.getStringExtra(IoPayConstant.BROWSER_SCREEN_NAME)?.let {
            val bundle = Bundle()
            bundle.putString(FirebaseAnalytics.Param.SCREEN_NAME, it)
            bundle.putString(FirebaseAnalytics.Param.SCREEN_CLASS, "XAppsActivity")
            FireBaseUtil.logFireBase(FirebaseAnalytics.Event.SCREEN_VIEW, bundle)
        }
    }

    private val mDetectorCompat by lazy {
        GestureDetectorCompat(this, object :
            GestureDetector.SimpleOnGestureListener() {
            override fun onScroll(
                e1: MotionEvent?,
                e2: MotionEvent,
                distanceX: Float,
                distanceY: Float
            ): Boolean {
                if (abs(distanceY) > abs(distanceX) && abs(distanceY) > 50) { //判断是否竖直滑动
                    handlerMoveEvent(distanceY)
                }
                return super.onScroll(e1, e2, distanceX, distanceY)
            }
        })
    }

    override fun dispatchTouchEvent(event: MotionEvent?): Boolean {
        when (event?.actionMasked) {
            MotionEvent.ACTION_UP -> {
                if (mBinding.llBottom.layoutParams.height > ConvertUtils.dp2px(25f)) {
                    handlerMoveEvent(-ConvertUtils.dp2px(25f).toFloat())
                } else {
                    handlerMoveEvent(ConvertUtils.dp2px(25f).toFloat())
                }
            }
        }
        event?.let {
            mDetectorCompat.onTouchEvent(it)
        }
        return super.dispatchTouchEvent(event)
    }

    private fun handlerMoveEvent(dy: Float) {
        if (!scroll && isMenuVisible()) return
        setViewScroll(mBinding.llBottom, ConvertUtils.dp2px(50f), dy.toInt())
        setViewScroll(mBinding.llToolBarLand, ConvertUtils.dp2px(50f), dy.toInt())
        setViewScroll(mBinding.llToolBarContent, ConvertUtils.dp2px(50f), dy.toInt())
    }

    private fun isMenuVisible(): Boolean {
        val orientation = resources.configuration.orientation
        return if (orientation == Configuration.ORIENTATION_LANDSCAPE) {
            mBinding.llToolBarLand.layoutParams.height == ConvertUtils.dp2px(50f)
        } else {
            mBinding.llToolBarContent.layoutParams.height == ConvertUtils.dp2px(50f)
        }
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        changeOrientation()
    }

    private fun changeOrientation() {
        optionsMenu?.dismiss()
        val orientation = resources.configuration.orientation
        if (orientation == Configuration.ORIENTATION_LANDSCAPE) {
            mBinding.llBottom.visibility = View.GONE
            mBinding.llToolBar.visibility = View.GONE
            mBinding.llToolBarLand.visibility = View.VISIBLE
            window.decorView.systemUiVisibility = (View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                    or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                    or View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                    or View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                    or View.SYSTEM_UI_FLAG_FULLSCREEN
                    or View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY)
        } else if (orientation == Configuration.ORIENTATION_PORTRAIT) {
            mBinding.llBottom.visibility = View.VISIBLE
            mBinding.llToolBar.visibility = View.VISIBLE
            mBinding.llToolBarLand.visibility = View.GONE
            window.decorView.systemUiVisibility = View.SYSTEM_UI_FLAG_VISIBLE
        }
        multiFrag.setOrientation(orientation)
        BarUtils.setStatusBarLightMode(this, !UserStore.isDarkTheme())
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        //content to full screen
        AndroidBug5497Workaround.assistActivity(this)
        val lp = window.attributes
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
            lp.layoutInDisplayCutoutMode =
                WindowManager.LayoutParams.LAYOUT_IN_DISPLAY_CUTOUT_MODE_SHORT_EDGES
        }
        window.attributes = lp
        setContentView(mBinding.root)

        changeUrl(url)

        //close window
        mBinding.btnClose.setOnClickListener {
            KeyboardUtils.hideSoftInput(mBinding.btnClose)
            finish()
        }

        mBinding.btnCloseLand.setOnClickListener {
            KeyboardUtils.hideSoftInput(mBinding.btnClose)
            finish()
        }

        //webView goBack
        mBinding.ivBack.setOnClickListener {
            if (getCurrentWebView().canGoBack()) {
                getCurrentWebView().goBack()
            }
        }
        mBinding.ivBackLand.setOnClickListener {
            if (getCurrentWebView().canGoBack()) {
                getCurrentWebView().goBack()
            }
        }

        //webView goForward
        mBinding.ivFore.setOnClickListener {
            if (getCurrentWebView().canGoForward()) {
                getCurrentWebView().goForward()
            }
        }

        mBinding.ivForeLand.setOnClickListener {
            if (getCurrentWebView().canGoForward()) {
                getCurrentWebView().goForward()
            }
        }

        mBinding.etUrlLand.setOnEditorActionListener { _, actionId, _ ->
            if (actionId == EditorInfo.IME_ACTION_DONE) {
                etUrl(mBinding.etUrlLand)
                FireBaseUtil.logFireBase(
                    FireBaseEvent.ACTION_DAPP_WEB_CLICK_SEARCH
                )
            }
            false
        }

        mBinding.llTitle.setOnClickListener {
            val intent = Intent(this@XAppsActivity, SearchXAppsActivity::class.java)
            intent.putExtra("content", getCurrentWebFrag()?.currentUrl)
            startActivity(intent)
            overridePendingTransition(android.R.anim.fade_in, android.R.anim.fade_out)
        }

        mBinding.ivLoad.setOnClickListener {
            getCurrentWebView().reload()
        }

        mBinding.ivVertical.setOnClickListener {
            requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE
        }

        mBinding.ivLand.setOnClickListener {
            requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
        }

        //add a webViewFrag
        addWebViewFrag()

        //add a multi item frag
        supportFragmentManager.beginTransaction().add(R.id.flMulti, multiFrag).commit()
        multiFrag.apply {
            done = {
                //close multi item
                if (webViewFragList.size == 0) {
                    finish()
                } else {
                    mBinding.flMulti.setGone()
                    getCurrentWebFrag()?.let { fragment ->
                        changeUrl(fragment.currentUrl)
                        loadWebViewFrag(fragment)
                    }
                }
            }
            add = {
                //close multi item and to search
                mBinding.flMulti.setGone()
                startActivity(Intent(this@XAppsActivity, SearchXAppsActivity::class.java))
            }
            clear = {
                finish()
            }
            etUrl = {
                etUrl(it)
                mBinding.flMulti.setGone()
            }
            close = {
                supportFragmentManager.beginTransaction().remove(webViewFragList[it].webViewFrag)
                    .commit()
                webViewFragList.removeAt(it)
                if (it <= currentPosition) currentPosition -= 1
                if (currentPosition < 0) currentPosition = 0
                mBinding.tvMulti.text = webViewFragList.size.toString()
                mBinding.tvMultiLand.text = webViewFragList.size.toString()
            }
            item = { position: Int, multiWebData: MultiWebData ->
                mBinding.flMulti.setGone()
                intent = Intent()
                intent.putExtra(BROWSER_URL, multiWebData.url)
                intent.putExtra(BROWSER_DAPP_NAME, multiWebData.dAppName)
                intent.putExtra(BROWSER_DAPP_LOGO, multiWebData.dAppLogo)
                currentPosition = position
                changeUrl(multiWebData.webViewFrag.currentUrl)
                loadWebViewFrag(multiWebData.webViewFrag)
            }
        }

        //show multi item
        mBinding.tvMulti.setOnClickListener {
            webViewFragList[currentPosition].bitmap = getCurrentWebView().toBitmap()
            multiFrag.setNewData(webViewFragList)
            mBinding.flMulti.setVisible()
            getCurrentWebFrag()?.hide()
        }

        mBinding.tvMultiLand.setOnClickListener {
            webViewFragList[currentPosition].bitmap = getCurrentWebView().toBitmap()
            multiFrag.setNewData(webViewFragList)
            mBinding.flMulti.setVisible()
            getCurrentWebFrag()?.hide()
        }

        mBinding.llBottom.setOnClickListener {
            //no thing,just cover click
        }

        //option menu
        mBinding.btnOpts.setOnClickListener {
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_BROWSER_CLICK_MORE)
            optionsMenu = OptionsMenu(
                this@XAppsActivity,
                getCurrentWebFrag()?.currentUrl ?: "",
                webView = getCurrentWebView(),
                refreshCallback = {
                    getCurrentWebView().reload()
                },
                scanResult = { lastText ->
                    if (lastText.startsWith("http")) {
                        getCurrentWebView().loadUrl(lastText)
                    } else {
                        findViewById<TextView>(R.id.barcode_field_value).text = lastText
                        findViewById<TextView>(R.id.barcode_field_label).setText(getTextResource(lastText))
                        mBinding.llCode.visibility = View.VISIBLE
                    }
                }, visitor = walletEmptyLiveData.value?:false)
            optionsMenu?.show(mBinding.btnOpts)
        }
        mBinding.btnOptsLand.setOnClickListener {
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_BROWSER_CLICK_MORE)
            optionsMenu = OptionsMenu(
                this@XAppsActivity,
                getCurrentWebFrag()?.currentUrl ?: "",
                webView = getCurrentWebView(),
                refreshCallback = {
                    getCurrentWebView().reload()
                },
                scanResult = { lastText ->
                    if (lastText.startsWith("http")) {
                        getCurrentWebView().loadUrl(lastText)
                    } else {
                        findViewById<TextView>(R.id.barcode_field_value).text = lastText
                        findViewById<TextView>(R.id.barcode_field_label).setText(getTextResource(lastText))
                        mBinding.llCode.visibility = View.VISIBLE
                    }
                },
                showRefresh = false,
                visitor = walletEmptyLiveData.value?:false
            )
            optionsMenu?.show(mBinding.btnOptsLand)//land but btnOpts onChange orientation show
        }

        mBinding.flCode.setOnClickListener {
            mBinding.llCode.visibility = View.GONE
        }

        //Contact address
        findViewById<AppCompatImageView>(R.id.select_button).setOnClickListener {
            if (!mLoadingDialog.isShowing) {
                mLoadingDialog.show()
            }
            val address: String = findViewById<TextView>(R.id.barcode_field_value).text.toString()
            if (WalletHelper.isValidAddress(address)) {
                if (WalletHelper.checkWalletExpired()) return@setOnClickListener
                TransferActivity.start(this, address = address)
                mBinding.llCode.visibility = View.GONE
                if (mLoadingDialog.isShowing) {
                    mLoadingDialog.dismiss()
                }
                return@setOnClickListener
            }
            if (WC2Helper.isValidWc2Bridge(address)) {
                val webData = getCurrentMultiWebData()
                WC2Helper.pair(address, webData?.dAppName ?: "", webData?.dAppLogo ?: "")
                mBinding.llCode.visibility = View.GONE
                if (mLoadingDialog.isShowing) {
                    mLoadingDialog.dismiss()
                }
                return@setOnClickListener
            }
            if (mLoadingDialog.isShowing) {
                mLoadingDialog.dismiss()
            }
            mBinding.llCode.visibility = View.GONE
            toast(R.string.address_invalid)
        }

        CoroutineScope(Dispatchers.IO).launch {
            val wallet = AppDatabase.getInstance(Utils.getApp()).walletDao().first()
            walletEmptyLiveData.postValue(wallet == null)
        }
    }

    private fun etUrl(et: EditText) {
        val content = et.text.trim().toString()
        KeyboardUtils.hideSoftInput(et)
        intent = Intent()
        intent.putExtra(BROWSER_URL, content)
        addWebViewFrag()
    }

    /**
     * add new webView
     */
    private fun addWebViewFrag() {
        val webViewFrag = XAppFragment.newInstance(url, dAppLogo, dAppName)
        webViewFragList.add(MultiWebData("", url, dAppName, dAppLogo, null, webViewFrag))
        mBinding.tvMulti.text = webViewFragList.size.toString()
        mBinding.tvMultiLand.text = webViewFragList.size.toString()

        currentPosition = webViewFragList.size - 1
        if (currentPosition < 0) currentPosition = 0
        changeUrl(url)
        loadWebViewFrag(webViewFrag)
    }

    /**
     * show webView frag
     */
    private fun loadWebViewFrag(webViewFrag: XAppFragment) {
        webViewFragList.forEach { data ->
            //hide other frag
            data.webViewFrag.hide()
            supportFragmentManager.beginTransaction().hide(data.webViewFrag).commit()
        }
        if (webViewFrag.isAdded) {
            mBinding.tvTitle.text = webViewFrag.title
            setBackAndFore(getCurrentWebView().canGoBack(), getCurrentWebView().canGoForward())
            supportFragmentManager.beginTransaction().show(webViewFrag).commit()
            webViewFrag.show()
        } else {
            mBinding.tvTitle.text = ""
            setBackAndFore(back = false, fore = false)
            supportFragmentManager.beginTransaction().add(R.id.flWebView, webViewFrag).commit()
            supportFragmentManager.beginTransaction().show(webViewFrag).commit()
            webViewFrag.show()
        }

        webViewFrag.apply {
            urlBack = {
                changeUrl(it)
            }
            titleBack = {
                mBinding.tvTitle.text = it
                webViewFragList[currentPosition].title = it
            }
            backAndForeCallback = { back, fore ->
                setBackAndFore(back, fore)
            }
            scrollCallback = { scroll ->
                <EMAIL> = scroll
            }
        }
    }

    private fun setViewScroll(view: View, originHeight: Int, change: Int) {
        if (change < 0) {
            val layoutParams = view.layoutParams
            val height = layoutParams.height
            layoutParams.height =
                if (height - change > originHeight) originHeight else height - change
            view.layoutParams = layoutParams
        } else {
            val layoutParams = view.layoutParams
            val height = layoutParams.height
            layoutParams.height = if (height - change < 0) 0 else height - change
            view.layoutParams = layoutParams
        }
    }

    /**
     * back fore button alpha
     */
    private fun setBackAndFore(back: Boolean, fore: Boolean) {
        if (back) {
            mBinding.ivBack.alpha = 1f
            mBinding.ivBackLand.alpha = 1f
        } else {
            mBinding.ivBack.alpha = 0.2f
            mBinding.ivBackLand.alpha = 0.2f
        }
        if (fore) {
            mBinding.ivFore.alpha = 1f
            mBinding.ivForeLand.alpha = 1f
        } else {
            mBinding.ivFore.alpha = 0.2f
            mBinding.ivForeLand.alpha = 0.2f
        }
    }

    /**
     * Current WebView
     */
    private fun getCurrentWebFrag(): XAppFragment? {
        return if (webViewFragList.size > currentPosition && currentPosition >= 0) {
            return webViewFragList[currentPosition].webViewFrag
        } else null
    }

    private fun getCurrentMultiWebData(): MultiWebData? {
        return if (webViewFragList.size > currentPosition && currentPosition >= 0) {
            return webViewFragList[currentPosition]
        } else null
    }

    private fun getCurrentWebView(): WebView {
        return if (webViewFragList.size > currentPosition && currentPosition >= 0) {
            return webViewFragList[currentPosition].webViewFrag.webView
        } else WebView(this)
    }

    //scan code info
    private fun getTextResource(lastText: String) = when {
        WalletHelper.isValidAddress(lastText) -> {
            R.string.found_address
        }
        lastText.startsWith("wc:") -> {
            R.string.wallet_connect
        }
        WalletHelper.isValidPrivateKey(lastText) -> {
            R.string.private_key
        }
        else -> {
            R.string.content
        }
    }

    override fun onBackPressed() {
        if (mBinding.flMulti.isShown && webViewFragList.size > 0) {
            mBinding.flMulti.setGone()
            getCurrentWebFrag()?.show()
            mBinding.tvTitle.text = getCurrentWebView().title
            return
        }
        if (getCurrentWebView().canGoBack()) {
            getCurrentWebView().goBack()
            return
        }
        super.onBackPressed()
    }

    private fun changeUrl(url: String) {
        mBinding.etUrlLand.setText(url)
        mBinding.etUrlLand.setSelection(url.length)
        multiFrag.setUrl(url)
        if (url.startsWith("https")) {
            mBinding.ivWebSafe.setImageResource(R.drawable.web_safe_https)
            mBinding.ivWebSafeLand.setImageResource(R.drawable.web_safe_https)
        } else {
            mBinding.ivWebSafe.setImageResource(R.drawable.web_safe_http)
            mBinding.ivWebSafeLand.setImageResource(R.drawable.web_safe_http)
        }
    }

    override fun initData() {
        mViewModel.giftDAppTask()
        mViewModel.dAppPointLiveData.observe(this) {
            val bundle = Bundle()
            val address = Constant.currentWallet?.address ?: ""
            bundle.putString("address", address)
            bundle.putString("device_id", DeviceUtils.getUniqueDeviceId())
            bundle.putString("dapp_url", url)
            FireBaseUtil.logFireBase(FireBaseEvent.POINT_TASK_BROWSER, bundle)
        }
    }
}
