package io.iotex.iopay.xapp

import android.app.Application
import androidx.lifecycle.MutableLiveData
import io.iotex.iopay.base.BaseLaunchVM
import io.iotex.iopay.repo.GiftRepo
import io.iotex.iopay.util.WalletHelper
import kotlinx.coroutines.delay

class XAppsViewModel(application: Application) : BaseLaunchVM(application) {

    private val giftRepo by lazy {
        GiftRepo()
    }

    val dAppPointLiveData = MutableLiveData<String>()

    fun giftDAppTask() {
        addLaunch {
            val pointTaskEntry = giftRepo.hasDAppTask()
            if (pointTaskEntry?.released == true) {
                delay(5000)
                val address = WalletHelper.getCurWallet()?.address ?: return@addLaunch
                val result = giftRepo.updateEvent(pointTaskEntry.id, address)
                if (result?.ok == true) {
                    dAppPointLiveData.postValue(pointTaskEntry.point)
                }
            }
        }
    }
}