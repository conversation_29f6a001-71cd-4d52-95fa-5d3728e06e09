package io.iotex.iopay.xapp.bitcoin

import android.webkit.JavascriptInterface
import android.webkit.WebView
import androidx.fragment.app.FragmentActivity
import com.blankj.utilcode.util.GsonUtils
import com.blankj.utilcode.util.TimeUtils
import com.blankj.utilcode.util.Utils
import com.google.gson.Gson
import io.iotex.iopay.R
import io.iotex.iopay.dapp.dialog.DAppNewWalletTipsDialog
import io.iotex.iopay.data.UserStore
import io.iotex.iopay.data.db.ACTION_TYPE_BITCOIN_TRANSFER
import io.iotex.iopay.data.db.AddressType_Taproot
import io.iotex.iopay.data.db.AppDatabase
import io.iotex.iopay.data.db.STATUS_SUCCESS
import io.iotex.iopay.network.dialog.NetworkSwitchDialog
import io.iotex.iopay.repo.BitcoinRepo
import io.iotex.iopay.transaction.BitcoinTransactionDialog
import io.iotex.iopay.transaction.bean.ExtraHead
import io.iotex.iopay.transaction.bean.OptionEntry
import io.iotex.iopay.transaction.connect.ConnectWalletDialog
import io.iotex.iopay.transaction.sign.SignMessageDialog
import io.iotex.iopay.util.Config
import io.iotex.iopay.util.EncryptUtil
import io.iotex.iopay.util.TokenUtil
import io.iotex.iopay.util.UrlUtils
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.util.extension.fromSatoshis
import io.iotex.iopay.util.extension.i
import io.iotex.iopay.util.extension.toHexByteArray
import io.iotex.iopay.util.extension.toHexString
import io.iotex.iopay.wallet.bitcoin.BitcoinHelper
import io.iotex.iopay.wallet.dialog.NoteDialog
import io.iotex.iopay.wallet.dialog.SwitchNetworkDialog
import io.iotex.iopay.xapp.DappBridge
import io.iotex.iopay.wallet.web3.Web3Delegate
import io.iotex.iopay.xapp.trust.sendBitcoinError
import io.iotex.iopay.xapp.trust.sendBitcoinResult
import io.iotex.iopay.xapp.trust.sendBitcoinResults
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.bitcoin.Crypto
import org.bitcoinj.core.Address
import org.bitcoinj.core.Transaction
import org.bitcoinj.script.Script
import org.json.JSONArray
import org.json.JSONObject
import org.psbt.Psbt
import org.psbt.PsbtUtil
import org.psbt.UserToSignInput
import java.math.BigInteger

class BitcoinDappBridge(
    private val webview: WebView,
    private val context: FragmentActivity,
    private val dappName: String,
    private val dappLogo: String
) : DappBridge(webview, context, dappName, dappLogo) {

    private val bitcoinRepo = BitcoinRepo()
    private var bitcoinTransactionDialog: BitcoinTransactionDialog? = null

    @JavascriptInterface
    override fun postMessage(json: String) {
        "postMessage -> $json".i()
        val available = availableNetwork()
        if (!available) return
        val obj = JSONObject(json)
        val id = obj.getLong("id")
        when (obj.getString("method")) {
            "getAccounts", "requestAccounts" -> {
                handleRequestAccounts(id)
            }

            "getPublicKey" -> {
                handleGetPublicKey(id)
            }

            "getBalance" -> {
                handleGetBalance(id)
            }

            "getNetwork" -> {
                handleGetNetwork(id)
            }

            "sendBitcoin" -> {}
            "switchNetwork" -> {
                handleSwitchNetwork(obj)
            }
            "signMessage" -> {
                handleSignMessage(obj)
            }

            "signPsbt" -> {
                handleSignPsbt(obj)
            }

            "signPsbts" -> {
                handleSignPsbts(obj)
            }

            "pushPsbt" -> {
                handlePushPsbt(obj)
            }

            "pushTx" -> {
                handlePushTx(obj)
            }

            else -> {
                unsupportedMethod(id)
            }
        }
    }

    private fun availableNetwork(): Boolean {
        if (!WalletHelper.isBitcoinNetwork()) {
            MainScope().launch {
                NoteDialog().apply {
                    message = Utils.getApp().getString(R.string.the_current_network_is_not_bitcoin)
                    onConfirm= {
                        NetworkSwitchDialog().apply {
                            onItemClick = {
                                webview.reload()
                            }
                        }.show(<EMAIL>,System.currentTimeMillis().toString())
                    }
                }.show(context.supportFragmentManager, System.currentTimeMillis().toString())
            }
            return false
        }
        return true
    }

    private fun handleRequestAccounts(id: Long) {
        MainScope().launch {
            val address = withContext(Dispatchers.IO) {
                val wallet = WalletHelper.getCurWallet()
                wallet?.getBitcoinWallet()?.bitcoinAddress ?: wallet?.address
            }
            if (address.isNullOrBlank()) {
                if (dAppNewWalletTipsDialog == null) {
                    dAppNewWalletTipsDialog = DAppNewWalletTipsDialog()
                }
                dAppNewWalletTipsDialog?.show(
                    context.supportFragmentManager,
                    System.currentTimeMillis().toString()
                )
                return@launch
            }

            val host = UrlUtils.getDomain(webview.url ?: "")
            val trust = withContext(Dispatchers.IO) {
                val trustDApp = AppDatabase.getInstance(context)
                    .trustDapp()
                    .queryTrustDApp(address, host)
                trustDApp != null
            }
            if (trust) {
                connectAddress = address
                webview.sendBitcoinResults(id, address)
            } else {
                if (connectWalletDialog == null) {
                    val url = Config.HTTPS_SCHEME_HEAR + host
                    connectWalletDialog = ConnectWalletDialog(
                        dappLogo, url, address
                    ).apply {
                        onResult = {
                            connectWalletDialog = null
                            if (it) {
                                connectAddress = address
                                webview.sendBitcoinResults(id, address)
                                saveTrustDApp(address, host)
                            } else {
                                webview.sendBitcoinError(id, "Cancel")
                            }
                        }
                    }
                }
                if (connectWalletDialog?.isAdded == false) {
                    connectWalletDialog?.show(context.supportFragmentManager, System.currentTimeMillis().toString())
                    connectWalletDialog = null
                }
            }
        }
    }

    private fun handleGetPublicKey(id: Long) {
        val wallet = WalletHelper.getCurWallet() ?: return
        val mnemonicId = wallet.mnemonicId
        val mnemonic =
            AppDatabase.getInstance(Utils.getApp()).mnemonicDao().queryByMnemonicId(mnemonicId)
        val mnemonicPhrase = mnemonic?.encryptedMnemonic?.let {
            EncryptUtil.decrypt(it)
        }
        val ecKey = BitcoinHelper.getECKey(wallet, wallet.getBitcoinWallet()?.path, mnemonicPhrase)
        webview.sendBitcoinResult(id, ecKey.publicKeyAsHex)
    }

    private fun handleGetBalance(id: Long) {
        val address = WalletHelper.getCurWallet()?.getBitcoinWallet(WalletHelper.getCurChainId())?.bitcoinAddress ?: return
        val walletCache = AppDatabase.getInstance(Utils.getApp()).walletCacheDao()
            .queryWalletCache(address, WalletHelper.getCurChainId())
        webview.sendBitcoinResult(id, walletCache?.balance ?: "0")
    }

    private fun handleGetNetwork(id: Long) {
        if (WalletHelper.getCurChainId() == Config.BITCOIN_TEST_CHAIN_ID) {
            webview.sendBitcoinResult(id, Config.BITCOIN_TEST_NETWORK)
        } else if (WalletHelper.getCurChainId() == Config.BITCOIN_MAIN_CHAIN_ID) {
            webview.sendBitcoinResult(id, Config.BITCOIN_MAIN_NETWORK)
        } else {
            webview.sendBitcoinError(id, "No unsupported network")
        }
    }

    private fun handleSwitchNetwork(jsonObj: JSONObject) {
        val id = jsonObj.getLong("id")
        val exceptionHandler = CoroutineExceptionHandler { _, throwable ->
            webview.sendBitcoinError(id, "Switch network error")
        }
        MainScope().launch(Dispatchers.IO + exceptionHandler) {
            val params = jsonObj.getJSONObject("params")
            val network = params.getString("network")
            val fromRPCNetwork = WalletHelper.getCurNetwork() ?: return@launch
            val toRPCNetwork = if (network == Config.BITCOIN_TEST_NETWORK) {
                AppDatabase.getInstance(Utils.getApp()).rpcNetworkDao()
                    .queryRPCNetworkByChainId(Config.BITCOIN_TEST_CHAIN_ID)
            } else if (network == Config.BITCOIN_MAIN_NETWORK) {
                AppDatabase.getInstance(Utils.getApp()).rpcNetworkDao()
                    .queryRPCNetworkByChainId(Config.BITCOIN_MAIN_CHAIN_ID)
            } else null

            if (toRPCNetwork == null) {
                webview.sendBitcoinError(id, "No unsupported network")
            } else {
                if (fromRPCNetwork.chainId == toRPCNetwork.chainId) return@launch kotlin.run {
                    webview.sendBitcoinResult(id, network)
                }
                withContext(Dispatchers.Main) {
                    SwitchNetworkDialog().apply {
                        addValues(SwitchNetworkDialog.KEY_FROM_CHAIN, fromRPCNetwork.name)
                        addValues(SwitchNetworkDialog.KEY_TO_CHAIN, toRPCNetwork.name)
                        addValues(SwitchNetworkDialog.KEY_DAPP_HOST, UrlUtils.getDomain(webview.url ?: ""))
                        addValues(SwitchNetworkDialog.KEY_DAPP_LOGO, dappLogo)
                        commitAddValues()
                        setFragmentResultCallback { _, resultCode, _ ->
                            if (resultCode == SwitchNetworkDialog.AGREE_ACTION) {
                                WalletHelper.switchNetwork(toRPCNetwork)
                                webview.sendBitcoinResult(id, network)
                            }
                        }
                    }.showAllowingStateLoss(context)
                }
            }
        }
    }

    private fun handleSignMessage(jsonObj: JSONObject) {
        val id = jsonObj.getLong("id")
        val exceptionHandler = CoroutineExceptionHandler { _, exception ->
            webview.sendBitcoinError(id, "SignPsbt Error")
        }
        MainScope().launch(Dispatchers.IO + exceptionHandler) {
            val bitcoinWallet = WalletHelper.getCurWallet()?.getBitcoinWallet() ?: return@launch
            val address = BitcoinHelper.packAddress(bitcoinWallet.bitcoinAddress) ?: return@launch
            val params = jsonObj.getJSONObject("params")
            val text = params.getString("text")
            val type = if (params.has("type")) {
                params.getString("type")
            } else "ecdsa"

            withContext(Dispatchers.Main) {
                SignMessageDialog(dappLogo, webview.url ?: "", null, text)
                    .apply {
                        onResult = { confirm ->
                            if (confirm) {
                                actionSignMessage(address, type, text, id)
                            } else {
                                webview.sendBitcoinError(id, "User cancel")
                            }
                        }
                    }.show(context.supportFragmentManager, System.currentTimeMillis().toString())
            }
        }
    }

    private fun actionSignMessage(address: Address, type: String, message: String, id: Long) {
        val exceptionHandler = CoroutineExceptionHandler { _, exception ->
            webview.sendBitcoinError(id, "SignPsbt Error")
        }
        MainScope().launch(Dispatchers.IO + exceptionHandler) {
            val ecKey = BitcoinHelper.getECKeyWithAuth()
            when (type) {
                "ecdsa" -> {
                    val res = Crypto.signMessage(message, ecKey.privKeyBytes)
                    webview.sendBitcoinResult(id, res)
                }

                "bip322-simple" -> {
                    val res = Crypto.signMessageOfBIP322Simple(
                        BitcoinHelper.getBitcoinNetworkParams(),
                        address,
                        ecKey,
                        message
                    )
                    webview.sendBitcoinResult(id, res)
                }
            }
        }
    }

    private fun handleSignPsbt(jsonObj: JSONObject) {
        val id = jsonObj.getLong("id")
        val exceptionHandler = CoroutineExceptionHandler { _, exception ->
            webview.sendBitcoinError(id, "SignPsbt Error")
        }
        MainScope().launch(Dispatchers.IO + exceptionHandler) {
            val params = jsonObj.getJSONObject("params")
            val hex = params.getString("psbtHex")
            val isTaproot = WalletHelper.getCurWallet()?.addressType == AddressType_Taproot
            val psbt = Psbt.fromPsbtHex(BitcoinHelper.getBitcoinNetworkParams(), hex.toHexByteArray(), isTaproot)
            var options = emptyList<UserToSignInput>()
            if (params.has("options")) {
                kotlin.runCatching {
                    val optionsObj = params.getJSONObject("options")
                    val toSignInputs = optionsObj.getJSONArray("toSignInputs")
                    options = formatOptionsToSignInputs(psbt, toSignInputs)
                }
            }
            val signedPsbt = BitcoinHelper.signPbst(psbt, options)
            if (signedPsbt != null) {
                val signedHex = psbt.serializedPsbtHex()
                webview.sendBitcoinResult(id, signedHex)
            } else {
                webview.sendBitcoinError(id, "Signature error")
            }
        }
    }

    private fun formatOptionsToSignInputs(psbt: Psbt, toSignInputs: JSONArray?): List<UserToSignInput> {
        return if (toSignInputs != null) {
            val userToSignInputList = mutableListOf<UserToSignInput>()
            for (i in 0 until toSignInputs.length()) {
                val obj = toSignInputs.getJSONObject(i)
                val index = obj.getInt("index")
                val publicKey = obj.getString("publicKey")
                val sighashTypeJson = obj.getString("sighashTypes")
                val sighashTypeArr = Gson().fromJson(sighashTypeJson, Array<Long>::class.java)
                val sighashTypes = sighashTypeArr.map {
                    PsbtUtil.resolveSignHashType(it)
                }
                val disableTweakSigner = obj.getBoolean("disableTweakSigner")
                userToSignInputList.add(UserToSignInput(index, publicKey, sighashTypes, disableTweakSigner))
            }
            return userToSignInputList
        } else {
            val userToSignInputList = mutableListOf<UserToSignInput>()
            psbt.inputUpdates.forEachIndexed { index, input ->
                val isSigned = input.finalScriptSig != null || input.finalScriptWitness != null
                if (!isSigned) {
                    val sighashTypes = input.sighashType?.let {
                        listOf(PsbtUtil.resolveSignHashType(it))
                    }
                    userToSignInputList.add(UserToSignInput(index, "", sighashTypes, null))
                }
            }
            return userToSignInputList
        }
    }

    private fun handleSignPsbts(jsonObj: JSONObject) {
        val id = jsonObj.getLong("id")
        val exceptionHandler = CoroutineExceptionHandler { _, exception ->
            webview.sendBitcoinError(id, "SignPsbts Error")
        }
        MainScope().launch(Dispatchers.IO + exceptionHandler) {
            val params = jsonObj.getJSONObject("params")
            val hexs = params.getString("psbtHexs")
            val hexList = GsonUtils.fromJson<List<String>>(hexs, GsonUtils.getListType(String::class.java))
            val isTaproot = WalletHelper.getCurWallet()?.addressType == AddressType_Taproot

            val signedHexList = hexList.map { hex ->
                val psbt = Psbt.fromPsbtHex(BitcoinHelper.getBitcoinNetworkParams(), hex.toHexByteArray(), isTaproot)
                val signedPsbt = BitcoinHelper.signPbst(psbt)
                if (signedPsbt == null) {
                    webview.sendBitcoinError(id, "Signature error")
                    return@launch
                }
                signedPsbt.serializedPsbtHex()
            }
            webview.sendBitcoinResult(id, GsonUtils.toJson(signedHexList))
        }
    }

    private fun handlePushPsbt(jsonObj: JSONObject) {
        val id = jsonObj.getLong("id")
        val exceptionHandler = CoroutineExceptionHandler { _, exception ->
            webview.sendBitcoinError(id, exception.message ?: "PushTx Error")
        }
        MainScope().launch(Dispatchers.IO + exceptionHandler) {
            val params = jsonObj.getJSONObject("params")
            val hex = params.getString("psbtHex")
            val isTaproot = WalletHelper.getCurWallet()?.addressType == AddressType_Taproot
            val psbt = Psbt.fromPsbtHex(BitcoinHelper.getBitcoinNetworkParams(), hex.toHexByteArray(), isTaproot)
            val txHex = psbt.globalMap.unsignedTx.bitcoinSerialize().toHexString()
            val transactionId = BitcoinRepo().broadcastTx(txHex)
            if (!transactionId.isNullOrBlank()) {
                webview.sendBitcoinResult(id, transactionId)
            } else {
                webview.sendBitcoinError(id, "PushTx Error")
            }
        }
    }

    private fun handlePushTx(jsonObj: JSONObject) {
        val id = jsonObj.getLong("id")
        val exceptionHandler = CoroutineExceptionHandler { _, exception ->
            webview.sendBitcoinError(id, exception.message ?: "PushTx Error")
        }
        MainScope().launch(Dispatchers.IO + exceptionHandler) {
            val params = jsonObj.getJSONObject("params")
            val txHex = params.getString("rawtx")
            val transaction = Transaction(BitcoinHelper.getBitcoinNetworkParams(), txHex.toHexByteArray())
            val value = transaction.outputSum.value.toBigInteger()
            val from = WalletHelper.getCurWallet()?.getBitcoinWallet()?.bitcoinAddress ?: ""
            val to = BitcoinHelper.resolveBitcoinAddress(Script(transaction.outputs[0].scriptBytes)) ?: ""
            val network = WalletHelper.getCurNetwork()
            val list = ArrayList<OptionEntry>()
            list.add(OptionEntry(Utils.getApp().getString(R.string.method), Utils.getApp().getString(R.string.transfer)))
            list.add(OptionEntry(Utils.getApp().getString(R.string.amount), value.fromSatoshis()))
            list.add(OptionEntry(Utils.getApp().getString(R.string.from), from))
            list.add(OptionEntry(Utils.getApp().getString(R.string.to), to))
            val extraHead = ExtraHead(network?.currencyLogo ?: "", network?.currencySymbol ?: "", false)
            withContext(Dispatchers.Main) {
                bitcoinTransactionDialog = BitcoinTransactionDialog(to, value, list, extraHead).apply {
                    onCancel = { bitcoinTransactionDialog = null }
                    onTransactionConfirm = { to, value, transaction ->
                        actionSendTransaction(id, from, to, value, txHex)
                    }
                }
                bitcoinTransactionDialog?.show(context.supportFragmentManager, System.currentTimeMillis().toString())
            }
        }
    }

    private fun actionSendTransaction(id: Long, from: String, to: String, value: BigInteger, txHex: String) {
        val exceptionHandler = CoroutineExceptionHandler { _, exception ->
            webview.sendBitcoinError(id, exception.message ?: "PushTx Error")
        }
        MainScope().launch(Dispatchers.IO + exceptionHandler) {
            val transactionId = bitcoinRepo.broadcastTx(txHex)
            val timestamp = TimeUtils.getNowMills().toString()
            if (!transactionId.isNullOrBlank()) {
                webview.sendBitcoinResult(id, transactionId)
                Web3Delegate.insertTransaction(
                    timestamp,
                    transactionId,
                    "",
                    from,
                    to,
                    value.fromSatoshis(),
                    type = ACTION_TYPE_BITCOIN_TRANSFER,
                    decimals = TokenUtil.getCurrencyDecimal().toString(),
                    symbol = UserStore.getNetworkSymbol()
                )
                val transactionDetail = bitcoinRepo.waitForTransactionDetail(transactionId)
                if (transactionDetail?.status?.confirmed == true) {
                    Web3Delegate.updateAction(
                        timestamp,
                        transactionId,
                        STATUS_SUCCESS,
                    )
                }
            } else {
                webview.sendBitcoinError(id, "PushTx Error")
            }
        }
    }

    private fun unsupportedMethod(id: Long) {
        MainScope().launch {
            NoteDialog().apply {
                message = Utils.getApp().getString(R.string.note_unsuported_feature)
                onCancel = {
                    webview.sendBitcoinError(id, "Method not supported")
                }
            }.show(context.supportFragmentManager, NoteDialog::class.java.name)
        }
    }
}