package io.iotex.iopay.xapp.bitcoin

import android.webkit.WebView
import com.blankj.utilcode.util.Utils
import io.iotex.iopay.R
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch

object BitcoinScriptConfig {

    fun injectProvider(webView: WebView) {
        CoroutineScope(Dispatchers.IO).launch {
            val providerJs = loadProviderJs()
            val script = loadInitJs()
            MainScope().launch {
                webView.evaluateJavascript(providerJs, null)
                webView.evaluateJavascript(script, null)
            }
        }
    }

    private fun loadProviderJs(): String {
        return Utils.getApp().resources.openRawResource(R.raw.unisat).bufferedReader().use { it.readText() }
    }

    private fun loadInitJs(): String {
        val source = """
        (function() {
            const provider = new window.bitcoin.Provider();
            window.bitcoin.postMessage = (json) => {
                window._iopay_.postMessage(JSON.stringify(json))
            }
            window.unisat = provider
            window.iopay = window.unisat
        })();
        """
        return source
    }
}