package io.iotex.iopay.xapp.entity

import kotlinx.serialization.Serializable

object DappHelper {

    @Serializable
    data class WebData(val reqId: Int, val envelop: String, val type: String)

    @Serializable
    data class WebLoginData(val reqId: Int, val type: String)

    @Serializable
    data class SignWebData(val reqId: Int, val message: String?, val type: String)

    object ActionMethod {
        const val LOGIN = "login"
        const val CLAIM = "claim"
        const val TRANSFER = "transfer"
        const val BID = "bid"
        const val DEPOSIT_N2E = "deposit"
        const val NATIVE_STAKING = "createPygg"
        const val RESTAKE = "restake"
        const val UNSTAKE = "unstake"
        const val REVOTE = "revote"
        const val WITHDRAW = "withdraw"
    }

    object HanderMethod {
        const val GET_ACCOUNTS = "GET_ACCOUNTS"
        const val SIGN_AND_SEND = "SIGN_AND_SEND"
        const val SIGN = "SIGN"
        const val GET_ACCOUNTS_WITH_DEVICE = "GET_ACCOUNTS_WITH_DEVICE"
    }
}