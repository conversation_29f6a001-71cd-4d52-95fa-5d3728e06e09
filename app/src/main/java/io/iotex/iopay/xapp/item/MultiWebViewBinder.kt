package io.iotex.iopay.xapp.item

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.RecyclerView
import androidx.viewbinding.ViewBinding
import com.drakeet.multitype.ItemViewBinder
import io.iotex.iopay.R
import io.iotex.iopay.databinding.ItemMultiWebBinding
import io.iotex.iopay.xapp.entity.MultiWebData

class MultiWebViewBinder : ItemViewBinder<MultiWebData, MultiWebViewBinder.VH>() {

    var closeClick: ((Int) -> Unit)? = null
    var itemClick: ((Int,MultiWebData) -> Unit)? = null

    inner class VH(viewBinding: ViewBinding) : RecyclerView.ViewHolder(viewBinding.root) {
        val bind = viewBinding
    }

    override fun onBindViewHolder(holder: VH, item: MultiWebData) {
        val bind = holder.bind as ItemMultiWebBinding
        bind.tvTitle.text = item.title
        bind.ivImage.setImageBitmap(item.bitmap)

        bind.ivClose.setOnClickListener {
            closeClick?.invoke(getPosition(holder))
            adapter.notifyItemRemoved(getPosition(holder))
        }

        bind.root.setOnClickListener {
            itemClick?.invoke(getPosition(holder), item)
        }
    }

    override fun onCreateViewHolder(inflater: LayoutInflater, parent: ViewGroup): VH {
        val bind = DataBindingUtil.inflate<ItemMultiWebBinding>(
            inflater,
            R.layout.item_multi_web,
            parent,
            false
        )
        return VH(bind)
    }
}