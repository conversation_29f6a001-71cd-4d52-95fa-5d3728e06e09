package io.iotex.iopay.xapp.item

import android.view.LayoutInflater
import android.view.ViewGroup
import com.drakeet.multitype.ItemViewBinder
import com.machinefi.w3bstream.utils.extension.ellipsis
import io.iotex.base.bindbase.BaseBindVH
import io.iotex.iopay.R
import io.iotex.iopay.data.db.WalletConnectSessionEntry
import io.iotex.iopay.databinding.ItemWcConnectDappBinding
import io.iotex.iopay.util.extension.loadSvgOrImage

class WcConnectDAppBinder :
    ItemViewBinder<WalletConnectSessionEntry, BaseBindVH<ItemWcConnectDappBinding>>() {

    var onClickDisConnect: ((WalletConnectSessionEntry) -> Unit)? = null

    override fun onBindViewHolder(
        holder: BaseBindVH<ItemWcConnectDappBinding>,
        item: WalletConnectSessionEntry
    ) {
        val bind = holder.bind
        bind.ivLogo.loadSvgOrImage(item.logo, R.drawable.ic_dapp_placeholder)
        bind.tvName.text = item.name
        bind.tvUrl.text = item.url
        bind.ivWalletLogo.loadSvgOrImage(item.walletLogo, R.drawable.icon_wallet_default)
        bind.tvWalletName.text = item.walletName
        bind.tvWalletAddress.text = item.address.ellipsis()
        bind.logoView.setNetworkChain(item.chains)
        bind.ivDisConnect.setOnClickListener {
            onClickDisConnect?.invoke(item)
        }
    }

    override fun onCreateViewHolder(
        inflater: LayoutInflater,
        parent: ViewGroup
    ): BaseBindVH<ItemWcConnectDappBinding> {
        return BaseBindVH(ItemWcConnectDappBinding.inflate(inflater, parent, false))
    }
}