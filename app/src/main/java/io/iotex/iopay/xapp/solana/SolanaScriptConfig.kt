package io.iotex.iopay.xapp.solana

import android.webkit.WebView
import com.blankj.utilcode.util.Utils
import io.iotex.iopay.R
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch

object SolanaScriptConfig {

    fun injectProvider(webView: WebView) {
        CoroutineScope(Dispatchers.IO).launch {
            val providerJs = loadProviderJs()
            val initJs = loadInitJs()
            MainScope().launch {
                webView.evaluateJavascript(providerJs, null)
                webView.evaluateJavascript(initJs, null)
            }
        }
    }

    private fun loadProviderJs(): String {
        return Utils.getApp().resources.openRawResource(R.raw.solana).bufferedReader().use { it.readText() }
    }

    private fun loadInitJs(): String {
        val source = """
        (function() {
            var config = {                
                cluster: "mainnet-beta",
                isPhantom: true,
                isDebug: true
            };
            window.phantom.postMessage = (json) => {
                window._phantom_.postMessage(JSON.stringify(json));
            }
            window.phantom.solana = new window.phantom.Provider(config);
            window.iopay = window.phantom.solana;
        })();
        """
        return source
    }
}