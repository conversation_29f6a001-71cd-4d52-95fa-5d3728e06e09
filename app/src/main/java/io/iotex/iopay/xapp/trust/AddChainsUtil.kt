package io.iotex.iopay.xapp.trust

import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.FragmentActivity
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.ToastUtils
import com.blankj.utilcode.util.Utils
import com.machinefi.walletconnect2.bean.ChainInfo
import io.iotex.iopay.R
import io.iotex.iopay.data.db.AppDatabase
import io.iotex.iopay.data.db.MODE_MAIN
import io.iotex.iopay.data.db.RPCNetwork
import io.iotex.iopay.util.Constant
import io.iotex.iopay.util.UrlUtils
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.wallet.aawallet.dialog.NotSupportAAWalletDialog
import io.iotex.iopay.wallet.dialog.AddNetworkDialog
import io.iotex.iopay.wallet.dialog.SwitchNetworkDialog
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import org.jetbrains.anko.doAsync

object AddChainsUtil {

    fun addOrSwitchEthereumChain(dAppLogo:String,dAppName:String,dAppLogoUrl:String,chainInfo: ChainInfo,callback:((Boolean)->Unit)?=null) {
        if (notSupportedAction()) {
            callback?.invoke(false)
            return
        }
        CoroutineScope(Dispatchers.IO).launch {
            val chainIdStr = chainInfo.chainId
            val chainName = chainInfo.chainName
            val rpcUrls = chainInfo.rpcUrls
            val blockExplorerUrls = chainInfo.blockExplorerUrls
            val nativeCurrency = chainInfo.nativeCurrency

            val chainId = if (chainIdStr?.startsWith("0x") == true) {
                chainIdStr.substring(2, chainIdStr.length)
            } else {
                chainIdStr
            }?.toInt(16)

            if (chainId == null) {
                ToastUtils.showShort(R.string.chain_id_error)
                return@launch
            }

            val networks = AppDatabase.getInstance(Utils.getApp()).rpcNetworkDao()
                .queryAllRPCNetwork()

            val targetNetwork = networks.find {
                it.chainId == chainId
            }

            if (targetNetwork != null) {
                showSwitchNetworkDialog(dAppLogo,dAppLogoUrl,targetNetwork,callback)
                return@launch
            }

            var rpc = if (!rpcUrls.isNullOrEmpty()) {
                rpcUrls[0]
            } else {
                ""
            }

            var explorer = if (!blockExplorerUrls.isNullOrEmpty()) {
                blockExplorerUrls[0]
            } else {
                ""
            }

            var symbol = nativeCurrency?.symbol?.uppercase()
            var name = nativeCurrency?.name
            val decimals = nativeCurrency?.decimals?:18

            val networkLocal = AppDatabase.getInstance(Utils.getApp()).networkChainDao()
                .queryNetworkByChainId(chainId)
            if (rpc.isEmpty()) {
                rpc = networkLocal?.rpc ?: ""
            }
            if (name.isNullOrEmpty()) {
                name = networkLocal?.name ?: ""
            }
            if (explorer.isEmpty()) {
                explorer = networkLocal?.infoURL ?: ""
            }

            if (symbol.isNullOrEmpty()) {
                symbol = networkLocal?.symbol ?: ""
            }

            MainScope().launch {
                AddNetworkDialog().apply {
                    addValues(AddNetworkDialog.KEY_CHAIN_ID, chainId?:1)
                    addValues(AddNetworkDialog.KEY_CHAIN_NAME, name?:"")
                    addValues(AddNetworkDialog.KEY_RPC_URL, rpc)
                    addValues(AddNetworkDialog.KEY_RPC_SYMBOL, symbol?:"")
                    addValues(AddNetworkDialog.KEY_BLOCK_EXPLORER_URL, explorer)
                    commitAddValues()
                    setFragmentResultCallback { _, resultCode, _ ->
                        if (resultCode == AddNetworkDialog.AGREE_ACTION) {
                            callback?.invoke(true)
                            doAsync {
                                val network = RPCNetwork(
                                    System.currentTimeMillis().toString(),
                                    chainId?:1,
                                    name?:"",
                                    name?:"",
                                    "",
                                    rpc,
                                    explorer,
                                    networkLocal?.icon?:"",
                                    symbol?:"",
                                    name?:"",
                                    "",
                                    decimals?:18,
                                    "0",
                                    "Token List",
                                    "",
                                    explorer,
                                    MODE_MAIN,
                                    "#617AFF",
                                    "#617AFF",
                                    "",
                                    0,
                                    immutable = false
                                )

                                AppDatabase.getInstance(Utils.getApp()).rpcNetworkDao()
                                    .insertRPCNetworkDao(network)

                                WalletHelper.switchNetwork(network)
                            }
                        }else{
                            callback?.invoke(false)
                        }
                    }
                }.showAllowingStateLoss(ActivityUtils.getTopActivity() as? FragmentActivity)
            }
        }
    }

    fun showSwitchNetworkDialog(dAppLogo:String,dAppLogoUrl:String,toNetwork: RPCNetwork,callback:((Boolean)->Unit)?=null) {
        CoroutineScope(Dispatchers.IO).launch {
            val fromChainId = WalletHelper.getCurChainId()
            if (fromChainId == toNetwork.chainId) {
                callback?.invoke(true)
                return@launch
            }

            val fromNetwork = AppDatabase.getInstance(Utils.getApp())
                .rpcNetworkDao()
                .queryRPCNetworkByChainId(fromChainId)

            MainScope().launch {
                SwitchNetworkDialog().apply {
                    addValues(SwitchNetworkDialog.KEY_FROM_CHAIN, fromNetwork?.name ?: "")
                    addValues(SwitchNetworkDialog.KEY_TO_CHAIN, toNetwork.name)
                    addValues(SwitchNetworkDialog.KEY_DAPP_HOST, UrlUtils.getDomain(dAppLogoUrl))
                    addValues(SwitchNetworkDialog.KEY_DAPP_LOGO, dAppLogo)
                    commitAddValues()
                    setFragmentResultCallback { _, resultCode, _ ->
                        if (resultCode == SwitchNetworkDialog.AGREE_ACTION) {
                            WalletHelper.switchNetwork(toNetwork)
                            callback?.invoke(true)
                        }else{
                            callback?.invoke(false)
                        }
                    }
                }.showAllowingStateLoss(ActivityUtils.getTopActivity() as? FragmentActivity)
            }
        }

    }

    fun notSupportedAction(): Boolean {
        if (Constant.currentWallet?.isAAWallet() != false) {
            ActivityUtils.getTopActivity()?.let {
                it.runOnUiThread {
                    if (it is AppCompatActivity) {
                        NotSupportAAWalletDialog()
                            .show(
                                it.supportFragmentManager,
                                NotSupportAAWalletDialog::class.java.name
                            )
                    }
                }
            }
            return true
        }
        return false
    }
}