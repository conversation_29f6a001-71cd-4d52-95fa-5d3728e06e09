package io.iotex.iopay.xapp.trust

import android.webkit.JavascriptInterface
import android.webkit.WebView
import androidx.fragment.app.FragmentActivity
import com.blankj.utilcode.util.TimeUtils
import com.blankj.utilcode.util.Utils
import com.machinefi.walletconnect2.bean.ChainInfo
import io.iotex.iopay.R
import io.iotex.iopay.dapp.dialog.DAppNewWalletTipsDialog
import io.iotex.iopay.data.UserStore
import io.iotex.iopay.data.db.AppDatabase
import io.iotex.iopay.data.db.LIKE_STATUS_LIKE
import io.iotex.iopay.data.db.TokenCacheEntry
import io.iotex.iopay.data.db.TokenEntry
import io.iotex.iopay.support.eventbus.AddERC20Event
import io.iotex.iopay.support.eventbus.FavoriteOrDislikeERC20Event
import io.iotex.iopay.transaction.connect.ConnectWalletDialog
import io.iotex.iopay.util.Config
import io.iotex.iopay.util.Constant
import io.iotex.iopay.util.UrlUtils
import io.iotex.iopay.util.WalletHelper
import io.iotex.iopay.util.extension.toHexString
import io.iotex.iopay.util.extension.toast
import io.iotex.iopay.wallet.dialog.NoteDialog
import io.iotex.iopay.wallet.web3.Web3Delegate
import io.iotex.iopay.wallet.web3.Web3Repository
import io.iotex.iopay.wallet.web3.crypto.Numeric
import io.iotex.iopay.xapp.DappBridge
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.greenrobot.eventbus.EventBus
import org.json.JSONObject
import org.web3j.protocol.core.Response

const val REQUEST_ACCOUNTS = "requestAccounts"
const val SIGN_TRANSACTION = "signTransaction"
const val SIGN_MESSAGE = "signMessage"
const val SIGN_TYPED_DATA_V4 = "signTypedDataV4"
const val SIGN_PERSONAL_MESSAGE = "signPersonalMessage"
const val SIGN_TYPED_MESSAGE = "signTypedMessage"
const val SWITCH_ETHEREUM_CHAIN = "switchEthereumChain"
const val ADD_ETHEREUM_CHAIN = "addEthereumChain"
const val WATCH_ASSET = "watchAsset"

class Web3DappBridge(
    private val webview: WebView,
    private val context: FragmentActivity,
    private val dappName: String,
    private val dappLogo: String
): DappBridge(webview, context, dappName, dappLogo) {

    @JavascriptInterface
    override fun postMessage(json: String) {
        val available = availableNetwork()
        if (!available) return
        val obj = JSONObject(json)
        val id = obj.getLong("id")
        when (obj.getString("name")) {
            REQUEST_ACCOUNTS -> {
                handleRequestAccounts(obj)
            }

            SIGN_TRANSACTION -> {
                handleSignTransaction(obj)
            }

            SIGN_MESSAGE -> {
                val data = extractMessage(obj)
                handleSignMessage(id, data, addPrefix = false)
            }

            SIGN_PERSONAL_MESSAGE -> {
                val data = extractMessage(obj)
                handleSignMessage(id, data, addPrefix = true)
            }

            SIGN_TYPED_MESSAGE -> {
                val data = extractMessage(obj)
                val raw = extractRaw(obj)
                handleSignTypedMessage(id, data, raw)
            }

            SWITCH_ETHEREUM_CHAIN -> {
                handleSwitchChain(obj)
            }

            ADD_ETHEREUM_CHAIN -> {
                addEthereumChain(obj)
            }

            WATCH_ASSET -> {
                addTokenDialog(obj)
            }

            SIGN_TYPED_DATA_V4 -> {
                val data = extractMessage(obj)
                handleSignTypedData(id, data.toHexString())
            }

            else -> {}
        }
    }

    private fun availableNetwork(): Boolean {
        if (WalletHelper.isBitcoinNetwork() || WalletHelper.isSolanaNetwork()) {
            MainScope().launch {
                if(context.supportFragmentManager.isStateSaved) return@launch
                NoteDialog().apply {
                    message = Utils.getApp().getString(R.string.dapp_not_support_current_network)
                }.show(context.supportFragmentManager, System.currentTimeMillis().toString())
            }
            return false
        }
        return true
    }

    private fun handleRequestAccounts(jsonObj: JSONObject) {
//        {"id":*************,"name":"requestAccounts","object":{},"network":"ethereum"}
        val id = jsonObj.getLong("id")
        val network = kotlin.runCatching { jsonObj.getString("network") }.getOrNull()
        val address = WalletHelper.getWeb3Address()
        MainScope().launch {
            if (address.isEmpty()) {
                if(context.supportFragmentManager.isStateSaved) return@launch
                dAppNewWalletTipsDialog = DAppNewWalletTipsDialog()
                dAppNewWalletTipsDialog?.show(
                    context.supportFragmentManager,
                    System.currentTimeMillis().toString()
                )
                return@launch
            }

            val host = UrlUtils.getDomain(webview.url ?: "")
            val trust = withContext(Dispatchers.IO) {
                val trustDApp = AppDatabase.getInstance(context)
                    .trustDapp()
                    .queryTrustDApp(address, host)
                trustDApp != null
            }
            val binoUrl = UserStore.getBinoAiUrl().ifEmpty {
                Config.BINO_AI_WEB_URL
            }
            if(network == "ethereum"){
                if(WalletHelper.isSolanaNetwork() || WalletHelper.isBitcoinNetwork()){
                    NoteDialog().apply {
                        message = Utils.getApp().getString(R.string.dapp_not_support_current_network)
                    }.show(context.supportFragmentManager, System.currentTimeMillis().toString())
                    return@launch
                }
            }
            if (trust || webview.url?.contains(binoUrl) == true) {
                connectAddress = address
                webview.trustAddress(address)
                webview.sendResults(listOf(address), id)
            } else {
                val url = Config.HTTPS_SCHEME_HEAR + host
                connectWalletDialog = ConnectWalletDialog(
                    dappLogo, url, address
                ).apply {
                    onResult = {
                        connectWalletDialog = null
                        if (it) {
                            connectAddress = address
                            webview.trustAddress(address)
                            webview.sendResults(listOf(address), id)
                            saveTrustDApp(address, host)
                        } else {
                            webview.sendError("Cancel", id)
                        }
                    }
                }
                if(context.supportFragmentManager.isStateSaved) return@launch
                connectWalletDialog?.show(
                    context.supportFragmentManager,
                    System.currentTimeMillis().toString()
                )
            }
        }
    }

    private fun handleSignTransaction(jsonObj: JSONObject) {
        if (WalletHelper.checkWalletExpired()) return
        val id = jsonObj.getLong("id")
        val params = jsonObj.getJSONObject("object")
        val from = params.getString("from")
        val to = if (!params.isNull("to")) {
            params.getString("to")
        } else ""
        val data = if (!params.isNull("data")) {
            params.getString("data")
        } else ""
        val gas = if (!params.isNull("gas")) {
            params.getString("gas")
        } else null
        val gasPrice = if (!params.isNull("gasPrice")) {
            params.getString("gasPrice")
        } else null
        val value = if (!params.isNull("value")) {
            params.getString("value")
        } else null

        MainScope().launch {
            val url = Config.HTTPS_SCHEME_HEAR + UrlUtils.getDomain(webview.url ?: "")
            Web3Repository.signTransaction(
                id,
                from,
                to,
                data,
                value,
                dappName,
                dappLogo,
                url,
                null,
                gas,
                gasPrice,
                null,
                ::handleResponse
            )
        }
    }

    private fun extractMessage(json: JSONObject): ByteArray {
        val param = json.getJSONObject("object")
        val data = param.getString("data")
        return Numeric.hexStringToByteArray(data)
    }

    private fun extractRaw(json: JSONObject): String {
        val param = json.getJSONObject("object")
        return param.getString("raw")
    }

    private fun handleSignMessage(id: Long, data: ByteArray, addPrefix: Boolean) {
        MainScope().launch {
            if (notSupportedAction()) return@launch
            val url = Config.HTTPS_SCHEME_HEAR + UrlUtils.getDomain(webview.url ?: "")
            Web3Repository.handleSignMessage(
                id,
                data,
                addPrefix,
                appUrl = url,
                appLogo = dappLogo,
                response = ::handleResponse
            )
        }
    }

    private fun handleSignTypedMessage(id: Long, data: ByteArray, raw: String) {
        MainScope().launch {
            if (notSupportedAction()) return@launch
            val url = Config.HTTPS_SCHEME_HEAR + UrlUtils.getDomain(webview.url ?: "")
            Web3Repository.handleSignMessage(id, data, false, raw, url, dappLogo, ::handleResponse)
        }
    }

    private fun handleSignTypedData(id: Long, data: String) {
        MainScope().launch {
            if (notSupportedAction()) return@launch
            val url = Config.HTTPS_SCHEME_HEAR + UrlUtils.getDomain(webview.url ?: "")
            Web3Repository.handleSignTypedData(id, data, url, dappLogo, ::handleResponse)
        }
    }

    private fun handleSwitchChain(jsonObj: JSONObject) {
        MainScope().launch {
            val param = kotlin.runCatching {
                jsonObj.getJSONObject("object")
            }.getOrNull()
            val chainIdStr = kotlin.runCatching {
                param?.getString("chainId")
            }.getOrNull()

            AddChainsUtil.addOrSwitchEthereumChain(
                dappLogo, dappName, webview.url ?: "", ChainInfo(
                    chainIdStr, arrayListOf(""), "", ChainInfo.NativeCurrency(
                        "", "", 18
                    ), arrayListOf("")
                )
            )
        }
    }

    private fun addEthereumChain(jsonObj: JSONObject) {
        if (notSupportedAction()) return
        val param = jsonObj.getJSONObject("object")
        val chainIdStr = kotlin.runCatching { param.getString("chainId") }.getOrNull()?:""
        val chainName = kotlin.runCatching { param.getString("chainName") }.getOrNull()
        val rpcUrls = kotlin.runCatching { param.getJSONArray("rpcUrls") }.getOrNull()
        val blockExplorerUrls = kotlin.runCatching { param.getJSONArray("blockExplorerUrls") }.getOrNull()
        val nativeCurrency = kotlin.runCatching { param.getJSONObject("nativeCurrency") }.getOrNull()
        val symbol = kotlin.runCatching { nativeCurrency?.getString("symbol") }.getOrNull()?:""
        val decimals = kotlin.runCatching { nativeCurrency?.getInt("decimals") }.getOrNull()?:18
        val name = kotlin.runCatching { nativeCurrency?.getString("name") }.getOrNull()?:""

        val rpc = if ((rpcUrls?.length()?:0) > 0) {
            rpcUrls?.get(0)?.toString()?:""
        } else {
            ""
        }

        val explorer = if ((blockExplorerUrls?.length()?:0) > 0) {
            blockExplorerUrls?.get(0)?.toString()?:""
        } else {
            ""
        }

        AddChainsUtil.addOrSwitchEthereumChain(
            dappLogo, dappName, webview.url ?: "", ChainInfo(
                chainIdStr, arrayListOf(explorer), chainName, ChainInfo.NativeCurrency(
                    name, symbol, decimals
                ), arrayListOf(rpc)
            )
        )
    }

    private fun addTokenDialog(jsonObj: JSONObject){
        MainScope().launch {
            NoteDialog().apply {
                title = Utils.getApp().getString(R.string.add_token)
                message = Utils.getApp().getString(R.string.would_you_like_to_import_these_tokens)
                onConfirm = {
                    CoroutineScope(Dispatchers.IO).launch {
                        addToken(jsonObj)
                    }
                }
            }.show(context.supportFragmentManager, NoteDialog::class.java.name)
        }
    }

    private fun addToken(jsonObj: JSONObject) {
        Constant.currentWallet?.let { wallet ->
            val chainId = WalletHelper.getCurChainId()
            val params = jsonObj.getJSONObject("object")
            val contract = WalletHelper.convertWeb3Address(params.getString("contract")).lowercase()
            val symbol = kotlin.runCatching { params.getString("symbol") }.getOrNull()?:""
            val decimals = kotlin.runCatching { params.getInt("decimals") }.getOrNull()?:18
            val logo = kotlin.runCatching { params.getString("logo") }.getOrNull()?:""
            var erc20 = AppDatabase.getInstance(Utils.getApp()).tokenDao()
                .queryByAddress(chainId, contract.lowercase())

            if (erc20 != null) {
                erc20.status = LIKE_STATUS_LIKE
                AppDatabase.getInstance(Utils.getApp()).tokenDao().insertOrReplace(erc20)
                EventBus.getDefault().post(FavoriteOrDislikeERC20Event())
            } else {
                erc20 = TokenEntry(
                    System.currentTimeMillis().toString(),
                    chainId,
                    contract,
                    "",
                    symbol,
                    decimals,
                    logo,
                    "0",
                    false,
                    "",
                    true,
                    0,
                    "",
                    false,
                    isOfficial = false,
                    TimeUtils.getNowMills().toString(),
                    "",
                    "",
                    ""
                )
                AppDatabase.getInstance(Utils.getApp()).tokenDao().insertOrReplace(erc20)
                val tokenCache = TokenCacheEntry(
                    wallet.address,
                    chainId,
                    contract,
                    Web3Delegate.getErc20Balance(contract).firstOrNull().toString(),
                    LIKE_STATUS_LIKE,
                )
                AppDatabase.getInstance(Utils.getApp()).tokenCacheDao().insertOrReplace(tokenCache)
                EventBus.getDefault().post(AddERC20Event())
            }
            Utils.getApp().getString(R.string.saved_successfully).toast()
        }
    }

    private fun handleResponse(requestId: Long, res: String?, error: Response.Error?) {
        if (error != null) {
            webview.sendError(error.message, requestId)
        }
        if (res != null) {
            webview.sendResult(res, requestId)
        }
    }


}
