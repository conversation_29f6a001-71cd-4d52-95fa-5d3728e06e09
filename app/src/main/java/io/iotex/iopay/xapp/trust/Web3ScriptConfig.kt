package io.iotex.iopay.xapp.trust

import android.webkit.WebView
import com.blankj.utilcode.util.SPUtils
import com.blankj.utilcode.util.Utils
import io.iotex.iopay.R
import io.iotex.iopay.util.Config.IOTEX_RPC_URL
import io.iotex.iopay.util.SPConstant.SP_RPC_NETWORK_URL
import io.iotex.iopay.util.WalletHelper
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch

object Web3ScriptConfig {

    val chainId: Int
        get() {
            return WalletHelper.getCurChainId()
        }

    val rpcUrl: String
        get() {
            return SPUtils.getInstance().getString(SP_RPC_NETWORK_URL,IOTEX_RPC_URL)
        }

    fun injectProvider(webView: WebView) {
        CoroutineScope(Dispatchers.IO).launch {
            val providerJs = loadProviderJs()
            val initJs = loadInitJs()
            MainScope().launch {
                webView.evaluateJavascript(providerJs, null)
                webView.evaluateJavascript(initJs, null)
            }
        }
    }

    private fun loadProviderJs(): String {
        return Utils.getApp().resources.openRawResource(R.raw.trust).bufferedReader().use { it.readText() }
    }

    private fun loadInitJs(): String {
        val source = """
        (function() {
            var config = {                
                ethereum: {
                    chainId: $chainId,
                    rpcUrl: "$rpcUrl",
                    isMetaMask: true,
                },
                isDebug: true
            };
            trustwallet.ethereum = new trustwallet.Provider(config);
            trustwallet.postMessage = (json) => {
                window._tw_.postMessage(JSON.stringify(json));
            }
            window.ethereum = trustwallet.ethereum;
        })();
        """
        return source
    }
}