package io.iotex.iopay.xapp.trust

import android.util.Log
import android.webkit.WebView
import io.iotex.iopay.util.Constant
import io.iotex.iopay.util.extension.toJson

fun WebView.sendError(message: String, methodId: Long) {
    val script = "window.ethereum.sendError($methodId, \"$message\")"
    this.post {
        this.evaluateJavascript(script) {}
    }
}

fun WebView.trustAddress(address: String) {
    val script = "window.ethereum.setAddress(\"$address\");"
    this.post {
        this.evaluateJavascript(script) {}
    }
}

fun WebView.sendRpcError(code: Long, message: String, methodId: Long) {
    val script = "window.ethereum.sendRpcError($methodId, $code, \"$message\")"
    this.post {
        this.evaluateJavascript(script) {}
    }
}

fun WebView.sendResult(message: String, methodId: Long) {
    val script = "window.ethereum.sendResponse($methodId, \"$message\")"
    this.post {
        this.evaluateJavascript(script) {}
    }
}

fun WebView.sendResults(messages: List<String>, methodId: Long) {
    val message = messages.joinToString(separator = ",")
    val script = "window.ethereum.sendResponse($methodId, [\'$message\'])"
    this.post {
        this.evaluateJavascript(script) {}
    }
}

fun WebView.sendChainChanged(chainId: Int) {
    val script = "window.ethereum.emitChainChanged($chainId)"
    this.post {
        this.evaluateJavascript(script) {}
    }
}

fun WebView.setConfig(chainId: Int, rpcUrl: String) {
    val script = "window.ethereum.setConfig({ethereum:" +
            "{chainId: $chainId," +
            "rpcUrl: \"$rpcUrl\"," +
            "address: \"${Constant.currentWallet?.address}\"" +
            "}," +
            "isDebug: true" +
            "})"
    this.post {
        this.evaluateJavascript(script) {}
    }
}

fun WebView.sendBitcoinNetworkChanged(network: String) {
    val script = "window.unisat.sendNetworkChangeEvent(\'$network\')"
    this.post {
        this.evaluateJavascript(script) {}
    }
}

fun WebView.sendBitcoinError(id: Long, message: String) {
    val script = "window.unisat.sendError($id, \"$message\")"
    this.post {
        this.evaluateJavascript(script) {}
    }
}

fun WebView.sendBitcoinResults(id: Long, message: String) {
    val script = "window.unisat.sendResponse($id, [\'$message\'])"
    this.post {
        this.evaluateJavascript(script) {}
    }
}

fun WebView.sendBitcoinResult(id: Long, message: String) {
    val script = "window.unisat.sendResponse($id, \'$message\')"
    this.post {
        this.evaluateJavascript(script) {}
    }
}


fun WebView.setSolanaAddress(id: Long, address: String) {
    val script = """
        window.phantom.solana.sendResponse($id, "$address")
    """.trimIndent()
    this.post {
        this.evaluateJavascript(script) {}
    }
}

fun WebView.sendSolanaSignature(id: Long, signature: String) {
    val script = """
        window.phantom.solana.sendResponse($id, {signature: "$signature"})
    """.trimIndent()
    this.post {
        this.evaluateJavascript(script) {}
    }
}

fun WebView.sendSolanaTransactionSignature(id: Long, signature: String) {
    val script = """
        window.phantom.solana.sendResponse($id, "$signature")
    """.trimIndent()
    this.post {
        this.evaluateJavascript(script) {}
    }
}

fun WebView.sendSolanaV0TransactionSignature(id: Long, signatures: List<String>) {
    val script = """
        window.phantom.solana.sendResponse($id, ${signatures.toJson()})
    """.trimIndent()
    this.post {
        this.evaluateJavascript(script) {}
    }
}

fun WebView.sendSolanaSignMessageResult(id: Long, publicKey: String, data: ByteArray) {
    val script = """
        window.phantom.solana.sendResponse(
            $id, 
            {
                signature: {
                    type: "Buffer",
                    data: ${data.toJson()}
                },
                publicKey: "$publicKey"
            }
        )
    """.trimIndent()
    this.post {
        this.evaluateJavascript(script) {}
    }
}

fun WebView.sendSolanaSignAllTransaction(
    id: Long,
    transactionSignatures: List<String>,
    versionedTransactionSignatures: List<List<String>>
) {
    val script = """
        window.phantom.solana.sendResponse(
            $id, 
            {
               transactionSignatures:  ${transactionSignatures.toJson()},
               versionedTransactionSignatures:  ${versionedTransactionSignatures.toJson()},
            }
        )
    """.trimIndent()
    this.post {
        this.evaluateJavascript(script) {}
    }
}

fun WebView.sendSolanaSignAllTransactionResult(id: Long, signatures: List<String>) {
    val script = """
        window.phantom.solana.sendResponse(
            $id, 
            ${signatures.toJson()}
        )
    """.trimIndent()
    this.post {
        this.evaluateJavascript(script) {}
    }
}

fun WebView.sendSolanaSignAllV0TransactionResult(id: Long, signatures: List<List<String>>) {
    val script = """
        window.phantom.solana.sendResponse(
            $id, 
            ${signatures.toJson()}
        )
    """.trimIndent()
    this.post {
        this.evaluateJavascript(script) {}
    }
}

fun WebView.sendSolanaError(id: Long, message: String) {
    val script = "window.phantom.solana.sendError($id, \"$message\")"
    this.post {
        this.evaluateJavascript(script) {}
    }
}

fun WebView.sendSolanaCancel(id: Long) {
    val script =
        "window.phantom.solana.sendError($id, \"User rejected the request.\")"
    this.post {
        this.evaluateJavascript(script) {}
    }
}

fun WebView.sendMessage(msg: String,address: String) {
    val script =
        "window.receiveMessageFromNative(\'$msg\', \'$address\')"
    this.post {
        this.evaluateJavascript(script) {}
    }
}

fun WebView.presetAddress(address: String) {
    val script =
        "window.presetAddress(\'$address\')"
    this.post {
        this.evaluateJavascript(script) {}
    }
}
