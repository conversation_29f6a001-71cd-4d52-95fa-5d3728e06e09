package io.iotex.iopay.xapp.wc

import android.content.Intent
import androidx.core.view.isVisible
import com.blankj.utilcode.util.ActivityUtils
import com.machinefi.walletconnect2.WC2Helper
import com.machinefi.walletconnect2.WalletConnectStatusEvent
import io.iotex.base.BaseViewModel
import io.iotex.iopay.R
import io.iotex.iopay.base.BaseBindToolbarActivity
import io.iotex.iopay.data.UserStore
import io.iotex.iopay.data.db.AppDatabase
import io.iotex.iopay.databinding.ActivityWcAuthBinding
import io.iotex.iopay.network.dialog.AuthorizedNetworkDialog
import io.iotex.iopay.network.widget.NetworkChainLogoView
import io.iotex.iopay.support.eventbus.SwitchWalletEvent
import io.iotex.iopay.util.*
import io.iotex.iopay.util.extension.loadSvgOrImage
import io.iotex.iopay.wallet.list.SwitchWalletDialog
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

class WcAuthActivity :
    BaseBindToolbarActivity<BaseViewModel, ActivityWcAuthBinding>(R.layout.activity_wc_auth) {

    private val name by lazy { intent.getStringExtra(SOURCE_NAME) }
    private val url by lazy { intent.getStringExtra(SOURCE_URL) }
    private val risk by lazy { intent.getStringExtra(SOURCE_RISK) }
    private val icon by lazy { intent.getStringExtra(SOURCE_ICON) }
    private val chains by lazy { intent.getStringArrayExtra(SOURCE_CHAINS) }

    override fun initView() {
        EventBus.getDefault().register(this)
        setToolbarTitle(getString(R.string.wallet_connect))
        setToolbarBackClick {
            finish()
            FireBaseUtil.logFireBase("action_wc_click_cancel_button")
        }
        renderInfo()
        mBinding.tvSource.text = name
        mBinding.tvLink.text = url
        mBinding.llRisk.isVisible = risk?.lowercase() == "invalid"
        mBinding.ivLogo.loadSvgOrImage(icon, R.drawable.icon_wallet_connect)
        mBinding.tvCancel.setOnClickListener {
            WC2Helper.connectReject()
            finish()
            FireBaseUtil.logFireBase("action_wc_click_cancel_button")
        }
        mBinding.tvConnect.setOnClickListener {
            var address = Constant.currentWallet?.address ?: ""
            if (!WalletHelper.isEvmAddress(address)) {
                address = ""
            }
            var solanaAddress =
                Constant.currentWallet?.getCurNetworkAddress(Config.SOLANA_MAIN_CHAIN_ID) ?: ""
            if (!WalletHelper.isSolanaAddress(solanaAddress)) {
                solanaAddress = ""
            }
            showLoading()
            WC2Helper.connectApprove(
                WalletHelper.convertWeb3Address(address),
                solanaAddress
            )
            FireBaseUtil.logFireBase(FireBaseEvent.ACTION_WC_CLICK_CONNECT_BUTTON)
        }

        mBinding.rlWallet.setOnClickListener {
            SwitchWalletDialog().show(supportFragmentManager, System.currentTimeMillis().toString())
        }
    }

    private fun renderInfo() {
        MainScope().launch {
            val curNetwork = withContext(Dispatchers.IO) {
                WalletHelper.getCurNetwork()
            } ?: return@launch
            mBinding.ivNetwork.loadSvgOrImage(curNetwork.logo, R.drawable.ic_network_default)
            chains?.let { chainList ->
                NetworkChainLogoView.parseChainNetwork(chainList.toMutableList()) { networks ->
                    curNetwork.let {
                        networks.remove(it)
                        networks.add(0, it)
                    }
                    mBinding.logoView.setNetwork(networks)
                    mBinding.llNetwork.setOnClickListener {
                        AuthorizedNetworkDialog(items = networks)
                            .show(supportFragmentManager, System.currentTimeMillis().toString())
                    }
                }
            }

            val wallet = withContext(Dispatchers.IO) {
                val wallet = WalletHelper.getCurWallet()
                val walletCache =
                    AppDatabase.getInstance(this@WcAuthActivity).walletCacheDao().queryWalletCache(
                        wallet?.getCurNetworkAddress() ?: "", curNetwork.chainId
                    )
                wallet?.curBalance = walletCache?.balance ?: ""
                wallet
            } ?: return@launch
            mBinding.tvWalletName.text = wallet.alias
            mBinding.ivWallet.loadSvgOrImage(wallet.avatar, R.drawable.icon_wallet_default)
            mBinding.tvAddress.text = wallet.getCurNetworkAddress()

            val unit = curNetwork.currencySymbol

            val balance =
                TokenUtil.weiToTokenBN(wallet.curBalance, UserStore.getNetworkDecimals().toLong())
            val formatBalance = TokenUtil.displayBalance(balance)
            mBinding.tvWalletBalance.text = "$formatBalance $unit"
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onWalletConnectStatusEvent(event: WalletConnectStatusEvent) {
        hideLoading()
        if (event.connected) {
            finish()
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onWalletConnectStatusEvent(event: SwitchWalletEvent) {
        renderInfo()
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        EventBus.getDefault().unregister(this)
    }

    companion object {
        const val SOURCE_ICON = "source_icon"
        const val SOURCE_NAME = "source_name"
        const val SOURCE_URL = "source_url"
        const val SOURCE_RISK = "source_risk"
        const val SOURCE_CHAINS = "source_chains"

        fun start(
            icon: String,
            name: String,
            url: String,
            risk: String,
            chains: List<String>?
        ) {
            val i = Intent(ActivityUtils.getTopActivity(), WcAuthActivity::class.java)
            i.addFlags(Intent.FLAG_ACTIVITY_NO_HISTORY)
            i.putExtra(SOURCE_ICON, icon)
            i.putExtra(SOURCE_NAME, name)
            i.putExtra(SOURCE_URL, url)
            i.putExtra(SOURCE_RISK, risk)
            i.putExtra(SOURCE_CHAINS, chains?.toTypedArray())
            ActivityUtils.getTopActivity()?.startActivity(i)
        }
    }
}