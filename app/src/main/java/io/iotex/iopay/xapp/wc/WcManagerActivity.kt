package io.iotex.iopay.xapp.wc

import android.content.Context
import android.content.Intent
import androidx.recyclerview.widget.LinearLayoutManager
import com.drakeet.multitype.MultiTypeAdapter
import com.machinefi.walletconnect2.WC2Helper
import com.machinefi.walletconnect2.WalletConnectDApp
import com.machinefi.walletconnect2.WalletConnectDAppEvent
import io.iotex.iopay.R
import io.iotex.iopay.base.BaseBindToolbarActivity
import io.iotex.iopay.data.db.WalletConnectSessionEntry
import io.iotex.iopay.databinding.ActivityWcManagerBinding
import io.iotex.iopay.xapp.item.WcConnectDAppBinder
import org.greenrobot.eventbus.EventBus

class WcManagerActivity :
    BaseBindToolbarActivity<WcManagerViewModel, ActivityWcManagerBinding>(R.layout.activity_wc_manager) {

    private val mAdapter = MultiTypeAdapter()

    companion object {
        fun startActivity(context: Context) {
            context.startActivity(Intent(context, WcManagerActivity::class.java))
        }
    }

    override fun initView() {
        setToolbarTitle(getString(R.string.connected_dapp))
        mAdapter.register(WalletConnectSessionEntry::class.java, WcConnectDAppBinder().apply {
            onClickDisConnect = {
                WC2Helper.disconnect(it.topic)
                mViewModel.deleteConnect(it.topic)
            }
        })
        mBinding.recyclerView.layoutManager = LinearLayoutManager(this)
        mBinding.recyclerView.adapter = mAdapter
        mBinding.tvDisConnect.setOnClickListener {
            mViewModel.disConnectAll()
        }
    }

    override fun initData() {
        mViewModel.queryConnectDApp()
        mViewModel.connectLiveData.observe(this) {
            if (it.isEmpty()) {
                EventBus.getDefault().post(
                    WalletConnectDAppEvent(
                        false, WalletConnectDApp(
                            ""
                        )
                    )
                )
                finish()
            } else {
                mAdapter.items = it
                mAdapter.notifyDataSetChanged()
            }
        }
    }
}