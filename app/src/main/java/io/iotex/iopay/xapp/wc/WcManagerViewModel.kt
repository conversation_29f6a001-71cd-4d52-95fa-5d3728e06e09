package io.iotex.iopay.xapp.wc

import android.app.Application
import androidx.lifecycle.MutableLiveData
import com.blankj.utilcode.util.Utils
import com.machinefi.walletconnect2.WC2Helper
import com.machinefi.walletconnect2.WalletConnectDApp
import io.iotex.iopay.base.BaseLaunchVM
import io.iotex.iopay.data.UserStore
import io.iotex.iopay.data.db.AppDatabase
import io.iotex.iopay.data.db.WalletConnectSessionEntry
import io.iotex.iopay.util.Constant
import io.iotex.iopay.util.extension.toJson

class WcManagerViewModel(application: Application) : BaseLaunchVM(application) {

    val connectLiveData = MutableLiveData<List<WalletConnectSessionEntry>>()

    fun queryConnectDApp() {
        addLaunch {
            val list = AppDatabase.getInstance(Utils.getApp()).walletConnectSessionDao().queryAll()
            list.forEach {
                val wallet = AppDatabase.getInstance(Utils.getApp()).walletDao().queryWalletByAddress(it.address)
                if (wallet == null) {
                    deleteConnect(it.topic)
                } else {
                    it.walletName = wallet.alias
                    it.walletLogo = wallet.avatar
                }
            }
            connectLiveData.postValue(list)
        }
    }

    fun disConnectAll(){
        addLaunch {
            val list = AppDatabase.getInstance(Utils.getApp()).walletConnectSessionDao().queryAll()
            list.forEach {
                WC2Helper.disconnect(it.topic)
                deleteConnect(it.topic)
            }
        }
    }

    fun deleteConnect(topic: String) {
        addLaunch {
            val connect = AppDatabase.getInstance(Utils.getApp()).walletConnectSessionDao()
                .queryByTopic(topic)
            connect?.let {
                AppDatabase.getInstance(Utils.getApp()).walletConnectSessionDao().delete(connect)
            }
            queryConnectDApp()
        }
    }

    fun insetConnect(walletConnectDApp: WalletConnectDApp) {
        addLaunch {
            AppDatabase.getInstance(Utils.getApp()).walletConnectSessionDao().insertOrReplace(
                WalletConnectSessionEntry(
                    walletConnectDApp.topic,
                    walletConnectDApp.dAppLogo,
                    walletConnectDApp.dAppName,
                    walletConnectDApp.dAppUrl,
                    Constant.currentWallet?.getCurNetworkAddress() ?: UserStore.getWalletAddress(),
                    walletConnectDApp.chains?.toJson() ?: ""
                )
            )
            queryConnectDApp()
        }
    }
}