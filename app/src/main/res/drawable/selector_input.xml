<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">

    <item android:state_enabled="false">
        <shape android:shape="rectangle">
            <solid android:color="@color/color_292936_50" />
            <corners android:radius="5dp"/>
        </shape>
    </item>

    <item android:state_focused="false">
        <shape android:shape="rectangle">
            <solid android:color="#33666C80" />
            <corners android:radius="5dp"/>
        </shape>
    </item>

    <item android:state_focused="true">
        <shape android:shape="rectangle">
            <solid android:color="#33666C80" />
            <stroke android:color="@color/colorPrimary" android:width="1px"/>
            <corners android:radius="5dp"/>
        </shape>
    </item>

</selector>