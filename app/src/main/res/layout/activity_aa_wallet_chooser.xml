<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools">

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:background="@color/color_card_back"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/llCreateAAWallet"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_15"
            android:layout_marginTop="@dimen/dp_12"
            android:layout_marginEnd="@dimen/dp_15"
            android:visibility="gone"
            tools:visibility="visible"
            android:padding="@dimen/dp_20"
            android:gravity="center"
            android:background="@drawable/shape_back_r6">

            <LinearLayout
                android:orientation="vertical"
                android:layout_weight="1"
                android:layout_width="0dp"
                android:layout_marginEnd="@dimen/dp_12"
                android:layout_height="wrap_content">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/create_new_aa_wallet"
                    android:textColor="@color/color_title"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:drawableTint="@color/color_title"
                    android:drawableEnd="@drawable/ic_arrow_right_white" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp_8"
                    android:text="@string/create_new_aa_wallet_caption"
                    android:textColor="@color/color_title_sub"
                    android:textSize="14sp" />
            </LinearLayout>

            <ImageView
                android:layout_width="@dimen/dp_120"
                android:layout_height="@dimen/dp_120"
                android:src="@drawable/icon_add_wallet_aa" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/llRecoverAAWallet"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_15"
            android:layout_marginTop="@dimen/dp_12"
            android:layout_marginEnd="@dimen/dp_15"
            android:padding="@dimen/dp_20"
            android:gravity="center"
            android:background="@drawable/shape_back_r6">

            <LinearLayout
                android:layout_weight="1"
                android:orientation="vertical"
                android:layout_marginEnd="@dimen/dp_12"
                android:layout_width="0dp"
                android:layout_height="wrap_content">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/recover_aa_wallet"
                    android:textColor="@color/color_title"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:drawableTint="@color/color_title"
                    android:drawableEnd="@drawable/ic_arrow_right_white" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp_8"
                    android:text="@string/recover_aa_wallet_caption"
                    android:textColor="@color/color_title_sub"
                    android:textSize="14sp" />
            </LinearLayout>

            <ImageView
                android:layout_width="@dimen/dp_100"
                android:layout_height="@dimen/dp_85"
                android:src="@drawable/icon_add_wallet_aa_recover" />

        </LinearLayout>


    </LinearLayout>
</layout>