<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center_horizontal"
        android:orientation="vertical">

        <ImageView
            android:id="@+id/ivWalletIcon"
            android:layout_width="@dimen/dp_150"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_65"
            android:adjustViewBounds="true"
            android:src="@drawable/icon_aa_process" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_46"
            android:layout_marginTop="@dimen/dp_28"
            android:layout_marginEnd="@dimen/dp_46"
            android:gravity="center_horizontal"
            android:text="@string/the_process_of_activate_aa"
            android:textColor="@color/color_title_sub"
            android:textSize="14sp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_28"
            android:gravity="center">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/network_status"
                android:textColor="@color/color_title_sub"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/tvStatusAvailable"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_2"
                android:text="@string/available"
                android:textColor="@color/color_1eb752"
                android:textSize="14sp" />

            <LinearLayout
                android:id="@+id/llStatusNotAvailable"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_2"
                android:gravity="center"
                tools:visibility="visible"
                android:visibility="gone">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/not_available"
                    android:textColor="@color/color_E53737"
                    android:textSize="14sp" />

                <ImageView
                    android:layout_width="@dimen/dp_16"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp_4"
                    android:adjustViewBounds="true"
                    android:src="@drawable/icon_aa_not_available" />

            </LinearLayout>

        </LinearLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rvStatus"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:layout_marginTop="@dimen/dp_20"
            android:layout_marginBottom="@dimen/dp_70"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            tools:itemCount="3"
            tools:listitem="@layout/item_activate_aa_process" />

    </LinearLayout>
</layout>
