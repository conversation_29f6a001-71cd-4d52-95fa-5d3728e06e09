<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <LinearLayout
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:fillViewport="true">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:paddingStart="@dimen/dp_15"
                android:paddingEnd="@dimen/dp_15">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp_15"
                    android:text="@string/network_name"
                    android:alpha="0.8"
                    android:textColor="@color/color_title_sub"
                    android:textSize="@dimen/font_size_common" />

                <io.iotex.iopay.ui.widget.EditTextStateful
                    android:id="@+id/mEtNetworkName"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="bottom"
                    android:layout_marginTop="@dimen/dp_10"
                    app:height="@dimen/dp_56"
                    app:hint="@string/network_name"
                    app:textColor="@color/color_title"
                    app:hintColor="@color/color_hint"
                    app:inputType="text"
                    app:paddingEnd="@dimen/dp_15"
                    app:paddingStart="@dimen/dp_15"
                    app:textSize="@dimen/font_size_middle" />

                <LinearLayout
                    android:id="@+id/llEditRpc"
                    android:orientation="vertical"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp_24"
                        android:text="@string/new_rpc_url"
                        android:alpha="0.8"
                        android:textColor="@color/color_title_sub"
                        android:textSize="@dimen/font_size_common" />

                    <io.iotex.iopay.ui.widget.EditTextStateful
                        android:id="@+id/mEtRpcUrl"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="bottom"
                        android:layout_marginTop="@dimen/dp_10"
                        app:height="@dimen/dp_56"
                        app:hint="@string/new_rpc_url"
                        app:textColor="@color/color_title"
                        app:hintColor="@color/color_hint"
                        app:inputType="text"
                        app:paddingEnd="@dimen/dp_15"
                        app:paddingStart="@dimen/dp_15"
                        app:textSize="@dimen/font_size_middle" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp_24"
                        android:text="@string/chain_id"
                        android:textColor="@color/color_title_sub"
                        android:textSize="@dimen/font_size_common" />

                    <io.iotex.iopay.ui.widget.EditTextStateful
                        android:id="@+id/mEtChainId"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="bottom"
                        android:layout_marginTop="@dimen/dp_10"
                        app:height="@dimen/dp_56"
                        app:hint="@string/chain_id"
                        app:textColor="@color/color_title"
                        app:hintColor="@color/color_hint"
                        app:inputType="number"
                        app:paddingEnd="@dimen/dp_15"
                        app:paddingStart="@dimen/dp_15"
                        app:textSize="@dimen/font_size_middle" />

                </LinearLayout>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp_24"
                    android:text="@string/symbol"
                    android:textColor="@color/color_title_sub"
                    android:textSize="@dimen/font_size_common" />

                <io.iotex.iopay.ui.widget.EditTextStateful
                    android:id="@+id/mEtCurrencySymbol"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="bottom"
                    android:layout_marginTop="@dimen/dp_10"
                    app:height="@dimen/dp_56"
                    app:hint="@string/symbol"
                    app:textColor="@color/color_title"
                    app:hintColor="@color/color_hint"
                    app:inputType="text"
                    app:paddingEnd="@dimen/dp_15"
                    app:paddingStart="@dimen/dp_15"
                    app:textSize="@dimen/font_size_middle" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="24dp"
                    android:text="@string/explorer_url_optional"
                    android:textColor="@color/color_title_sub"
                    android:textSize="@dimen/font_size_common" />

                <io.iotex.iopay.ui.widget.EditTextStateful
                    android:id="@+id/mEtExplorerUrl"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="bottom"
                    android:layout_marginTop="@dimen/dp_10"
                    app:height="@dimen/dp_56"
                    app:hint="@string/explorer_url_optional"
                    app:textColor="@color/color_title"
                    app:hintColor="@color/color_hint"
                    app:inputType="text"
                    app:paddingEnd="@dimen/dp_15"
                    app:paddingStart="@dimen/dp_15"
                    app:textSize="@dimen/font_size_middle" />

                <LinearLayout
                    android:id="@+id/llRpc"
                    android:orientation="vertical"
                    android:layout_marginTop="@dimen/dp_24"
                    android:paddingStart="@dimen/dp_15"
                    android:paddingEnd="@dimen/dp_15"
                    android:background="@drawable/shape_card_back"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp_24"
                        android:text="@string/chain_id"
                        android:textColor="@color/color_title_sub"
                        android:textSize="@dimen/font_size_common" />

                    <TextView
                        android:id="@+id/tvChainId"
                        android:layout_marginTop="@dimen/dp_12"
                        android:layout_marginBottom="@dimen/dp_15"
                        android:textSize="16sp"
                        android:alpha="0.8"
                        android:textColor="@color/color_title_sub"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"/>
                    <View
                        android:background="@color/color_line_diver"
                        android:layout_width="wrap_content"
                        android:layout_height="1dp"/>
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp_24"
                        android:text="@string/rpc_url"
                        android:textColor="@color/color_title_sub"
                        android:textSize="@dimen/font_size_common" />

                    <TextView
                        android:id="@+id/tvRpc"
                        android:alpha="0.8"
                        android:layout_marginTop="@dimen/dp_12"
                        android:layout_marginBottom="@dimen/dp_15"
                        android:textSize="16sp"
                        android:textColor="@color/color_title_sub"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"/>
                </LinearLayout>

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp_12"
                    android:text="@string/add_network_tips"
                    android:textColor="@color/color_title_sub"
                    android:textSize="@dimen/font_size_small" />

                <TextView
                    android:id="@+id/mTvDelete"
                    android:textSize="14sp"
                    android:textColor="@color/color_617AFF"
                    android:text="@string/delete_network"
                    android:layout_marginTop="@dimen/dp_30"
                    android:layout_marginBottom="@dimen/dp_20"
                    android:layout_gravity="center_horizontal"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"/>
            </LinearLayout>
        </androidx.core.widget.NestedScrollView>
    </LinearLayout>
</layout>