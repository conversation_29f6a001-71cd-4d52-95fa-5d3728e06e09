<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">


        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_12"
            android:background="@color/color_card_back">

            <Switch
                android:id="@+id/switch_login"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_marginEnd="0dp"
                android:checked="true"
                android:gravity="start|center"
                android:padding="@dimen/common_padding_horizontal"
                android:text="@string/auth_before_login"
                android:textColor="@color/color_title"
                android:textSize="@dimen/font_size_middle" />
        </RelativeLayout>
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_12"
            android:background="@color/color_card_back">

            <Switch
                android:id="@+id/switch_transfer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_alignParentEnd="true"
                android:gravity="start|center"
                android:checked="true"
                android:padding="@dimen/common_padding_horizontal"
                android:text="@string/auth_before_transaction"
                android:textColor="@color/color_title"
                android:textSize="@dimen/font_size_middle" />
        </RelativeLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_12"
            android:background="@color/color_card_back">

            <Switch
                android:id="@+id/switch_biometrics"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_alignParentEnd="true"
                android:gravity="start|center"
                android:checked="true"
                android:padding="@dimen/common_padding_horizontal"
                android:text="@string/biometrics_authentication"
                android:textColor="@color/color_title"
                android:textSize="@dimen/font_size_middle" />
        </RelativeLayout>

    </LinearLayout>

</layout>