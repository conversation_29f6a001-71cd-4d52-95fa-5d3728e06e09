<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <LinearLayout
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginLeft="@dimen/dp_20"
            android:layout_marginRight="@dimen/dp_20"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_20"
                android:text="@string/new_address"
                android:textColor="@color/color_title_sub"
                android:textSize="@dimen/font_size_common"
                android:textStyle="bold" />

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/mTilAddress"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:errorIconDrawable="@null"
                app:errorEnabled="true">

                <io.iotex.iopay.ui.ClearEditText
                    android:id="@+id/etAddress"
                    style="@style/InputTheme"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/color_card_back"
                    android:hint="@string/type_your_new_address"
                    android:inputType="text"
                    android:textColor="@color/color_title"
                    android:textSize="@dimen/font_size_common" />
            </com.google.android.material.textfield.TextInputLayout>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_40"
                android:text="@string/name"
                android:textColor="@color/color_title_sub"
                android:textSize="@dimen/font_size_common"
                android:textStyle="bold" />

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/mTilName"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:errorIconDrawable="@null"
                app:errorEnabled="true">

                <io.iotex.iopay.ui.ClearEditText
                    android:id="@+id/etName"
                    style="@style/InputTheme"
                    android:maxEms="30"
                    android:maxLength="30"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/color_card_back"
                    android:hint="@string/give_it_a_name"
                    android:inputType="text"
                    android:textColor="@color/color_title"
                    android:textSize="@dimen/font_size_common" />
            </com.google.android.material.textfield.TextInputLayout>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_40"
                android:text="@string/describe"
                android:textColor="@color/color_title_sub"
                android:textSize="@dimen/font_size_common"
                android:textStyle="bold" />

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/mTilDescribe"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:errorIconDrawable="@null"
                app:errorEnabled="true">

                <io.iotex.iopay.ui.ClearEditText
                    android:id="@+id/mEtDescribe"
                    style="@style/InputTheme"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/color_card_back"
                    android:inputType="text"
                    android:textColor="@color/color_title"
                    android:textSize="@dimen/font_size_common" />
            </com.google.android.material.textfield.TextInputLayout>
        </LinearLayout>
    </LinearLayout>


</layout>