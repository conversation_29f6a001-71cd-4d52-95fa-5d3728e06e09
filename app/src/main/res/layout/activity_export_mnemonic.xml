<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <LinearLayout
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <TextView
            android:id="@+id/mTvPath"
            android:textSize="14sp"
            android:layout_marginStart="@dimen/dp_15"
            android:layout_marginTop="@dimen/dp_15"
            android:layout_marginEnd="@dimen/dp_15"
            android:textColor="@color/color_title_sub"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_15"
            android:layout_marginTop="@dimen/dp_15"
            android:layout_marginEnd="@dimen/dp_15">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/mRvMnemonic"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/shape_card_back_stroke_gradient"
                android:minHeight="@dimen/dp_120"
                android:padding="7dp"
                app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:spanCount="3"
                tools:itemCount="12"
                tools:listitem="@layout/item_mnemonic" />

            <LinearLayout
                android:id="@+id/llEye"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:background="@drawable/shape_card_back_stroke_gradient"
                android:gravity="center"
                android:orientation="vertical"
                app:layout_constraintBottom_toBottomOf="@+id/mRvMnemonic"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@+id/mRvMnemonic">

                <androidx.appcompat.widget.AppCompatImageView
                    android:layout_width="@dimen/dp_24"
                    android:layout_height="@dimen/dp_24"
                    android:tint="@color/color_title"
                    android:src="@drawable/icon_eye_close_white" />

                <TextView
                    android:textSize="13sp"
                    android:textColor="@color/color_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/click_to_see_mnemonics" />

            </LinearLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_27"
            android:gravity="center">

            <ImageView
                android:id="@+id/mIvCopy"
                android:layout_width="@dimen/dp_50"
                android:layout_height="wrap_content"
                android:adjustViewBounds="true"
                android:src="@drawable/icon_copy_with_bg" />

            <ImageView
                android:id="@+id/mIvViewer"
                android:layout_width="@dimen/dp_50"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_40"
                android:adjustViewBounds="true"
                android:src="@drawable/icon_eye_close_with_bg" />

        </LinearLayout>

        <LinearLayout
            android:orientation="vertical"
            android:layout_marginStart="@dimen/dp_15"
            android:layout_marginEnd="@dimen/dp_15"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <LinearLayout
                android:orientation="vertical"
                android:background="@drawable/shape_card_back"
                android:layout_marginTop="@dimen/dp_10"
                android:paddingStart="@dimen/dp_15"
                android:paddingEnd="@dimen/dp_15"
                android:paddingTop="@dimen/dp_15"
                android:paddingBottom="@dimen/dp_20"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <LinearLayout
                    android:gravity="center_vertical"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content">
                    <ImageView
                        android:src="@drawable/ic_warning"
                        android:layout_width="@dimen/dp_20"
                        android:layout_height="@dimen/dp_20"/>
                    <TextView
                        android:textSize="14sp"
                        android:textStyle="bold"
                        android:textColor="@color/color_title"
                        android:layout_marginStart="@dimen/dp_7"
                        android:text="@string/backup_tips"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"/>
                </LinearLayout>
                <View
                    android:background="@color/color_line_diver"
                    android:layout_marginTop="14dp"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp_0_5"/>
                <TextView
                    android:textSize="14sp"
                    android:layout_marginTop="13dp"
                    android:textColor="@color/color_title_sub"
                    android:alpha="0.8"
                    android:text="@string/obtaining_recovery_phrase_equals_owning_all_assets"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"/>
                <TextView
                    android:textSize="14sp"
                    android:textColor="@color/color_title_sub"
                    android:alpha="0.8"
                    android:text="@string/copy_it_on_the_paper_and_keep_in_a_safe_place"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"/>
                <TextView
                    android:textSize="14sp"
                    android:textColor="@color/color_title_sub"
                    android:alpha="0.8"
                    android:text="@string/once_the_recovery_phrase_gets_lost"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"/>
                <TextView
                    android:textSize="14sp"
                    android:textColor="@color/color_title_sub"
                    android:alpha="0.8"
                    android:text="@string/Please_be_sure_to_back_up_the_recovery_phrase"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"/>
            </LinearLayout>
        </LinearLayout>
    </LinearLayout>

</layout>