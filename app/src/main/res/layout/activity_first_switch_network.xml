<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">


    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.scwang.smartrefresh.layout.SmartRefreshLayout
            android:id="@+id/refreshLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:srlEnableLoadMore="false"
            app:srlEnablePreviewInEditMode="true"
            app:srlHeaderMaxDragRate="1.5">

            <io.iotex.iopay.ui.widget.IoPayHeader
                android:id="@+id/refreshLayoutHeader"
                android:layout_width="match_parent"
                android:layout_height="@dimen/refresh_header_height" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_above="@+id/enter_wallet"
                android:layout_marginStart="@dimen/dp_15"
                android:layout_marginTop="@dimen/dp_15"
                android:layout_marginEnd="@dimen/dp_15"
                android:layout_marginBottom="@dimen/dp_15" />

        </com.scwang.smartrefresh.layout.SmartRefreshLayout>

        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/enter_wallet"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_margin="20dp"
            android:background="@drawable/btn_shape_gradient_common"
            android:text="@string/enter_wallet"
            android:textAllCaps="false"
            android:textColor="@android:color/white"
            android:textSize="18sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.448"
            app:layout_constraintStart_toStartOf="parent" />

    </RelativeLayout>

</layout>