<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">
    <LinearLayout
        android:orientation="vertical"
        android:paddingStart="@dimen/dp_16"
        android:paddingEnd="@dimen/dp_16"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <com.makeramen.roundedimageview.RoundedImageView
            app:riv_corner_radius="@dimen/dp_8"
            android:scaleType="centerCrop"
            android:layout_marginTop="@dimen/dp_12"
            android:src="@drawable/icon_home_invited_code"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"/>

        <LinearLayout
            android:orientation="vertical"
            android:background="@drawable/shape_card_back"
            android:layout_marginTop="@dimen/dp_25"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_96">
            <LinearLayout
                android:gravity="center"
                android:paddingStart="@dimen/dp_12"
                android:paddingEnd="@dimen/dp_12"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_48">
                <TextView
                    android:text="@string/referral_code"
                    android:textSize="14sp"
                    android:textColor="@color/color_title_sub"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"/>
                <TextView
                    android:id="@+id/tvCode"
                    tools:text="FRIENDS"
                    android:textSize="14sp"
                    android:textColor="@color/color_title"
                    android:layout_weight="1"
                    android:gravity="end"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"/>

                <ImageView
                    android:id="@+id/ivCopyCode"
                    android:layout_marginStart="@dimen/dp_3"
                    android:src="@drawable/icon_copy"
                    android:layout_width="@dimen/dp_20"
                    android:layout_height="@dimen/dp_20"/>

            </LinearLayout>
            <View
                android:layout_marginStart="@dimen/dp_12"
                android:layout_marginEnd="@dimen/dp_12"
                android:background="@color/color_line_diver"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp_1"/>

            <LinearLayout
                android:gravity="center"
                android:paddingStart="@dimen/dp_12"
                android:paddingEnd="@dimen/dp_12"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_48">
                <TextView
                    android:text="@string/referral_link"
                    android:textSize="14sp"
                    android:textColor="@color/color_title_sub"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"/>
                <TextView
                    android:id="@+id/tvLink"
                    android:text="https://iopay.me"
                    android:textSize="14sp"
                    android:textColor="@color/color_title"
                    android:layout_weight="1"
                    android:gravity="end"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"/>

                <ImageView
                    android:id="@+id/ivCopyLink"
                    android:layout_marginStart="@dimen/dp_3"
                    android:src="@drawable/icon_copy"
                    android:layout_width="@dimen/dp_20"
                    android:layout_height="@dimen/dp_20"/>

            </LinearLayout>

        </LinearLayout>

        <TextView
            android:id="@+id/tvInvite"
            android:textColor="@color/white"
            android:textSize="14sp"
            android:gravity="center"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="@dimen/dp_20"
            android:paddingStart="@dimen/dp_30"
            android:paddingEnd="@dimen/dp_30"
            android:background="@drawable/btn_shape_gradient_common"
            android:text="@string/invite_friends_to_earn_points"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp_40"/>
        <LinearLayout
            android:layout_marginTop="@dimen/dp_40"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">
            <ImageView
                android:src="@drawable/ic_warning"
                android:layout_width="@dimen/dp_20"
                android:layout_height="@dimen/dp_20"/>
            <TextView
                android:text="@string/tips"
                android:textSize="14sp"
                android:textColor="@color/color_title"
                android:layout_marginStart="@dimen/dp_6"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
        </LinearLayout>
        <TextView
            android:text="@string/invited_users_must_enter_the_referral_code"
            android:textSize="12sp"
            android:layout_marginTop="@dimen/dp_16"
            android:textColor="@color/color_title_thr"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>
        <TextView
            android:text="@string/invitations_are_limited_to_a_single_device"
            android:textSize="12sp"
            android:layout_marginTop="@dimen/dp_16"
            android:textColor="@color/color_title_thr"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>
        <TextView
            android:text="@string/iopay_reserves_the_right_to_interpret_the_rules"
            android:textSize="12sp"
            android:layout_marginTop="@dimen/dp_16"
            android:textColor="@color/color_title_thr"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>
    </LinearLayout>

</layout>