<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">
    <FrameLayout
        android:keepScreenOn="true"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <com.journeyapps.barcodescanner.DecoratedBarcodeView
            android:id="@+id/barcodeView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"/>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/top_action_bar_height"
            android:paddingStart="@dimen/dp_10"
            android:paddingEnd="@dimen/dp_10"
            android:gravity="center"
            android:background="@drawable/scan_action_bar">

            <ImageView
                android:id="@+id/ivClose"
                android:layout_width="@dimen/dp_44"
                android:layout_height="@dimen/dp_44"
                android:padding="@dimen/dp_10"
                android:contentDescription="@string/cd_close_button"
                android:src="@drawable/ic_close_vd_white_24"/>

            <View
                android:layout_weight="1"
                android:layout_width="0dp"
                android:layout_height="@dimen/dp_1"/>

            <ImageView
                android:id="@+id/ivFlash"
                android:padding="@dimen/dp_10"
                android:layout_width="@dimen/dp_44"
                android:layout_height="@dimen/dp_44"
                android:contentDescription="@string/cd_flash_button"
                android:src="@drawable/camera_flash"/>
            <ImageView
                android:id="@+id/ivImage"
                android:padding="@dimen/dp_10"
                android:layout_width="@dimen/dp_44"
                android:layout_height="@dimen/dp_44"
                android:contentDescription="@string/cd_flash_button"
                android:src="@drawable/ic_image"/>

        </LinearLayout>

        <FrameLayout
            android:id="@+id/flContainer"
            android:visibility="gone"
            tools:visibility="visible"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom"
            android:paddingBottom="@dimen/barcode_field_padding_vertical"
            android:paddingStart="@dimen/barcode_field_padding_horizontal"
            android:paddingEnd="@dimen/barcode_field_padding_horizontal"
            android:background="@color/white">

            <TextView
                android:id="@+id/tvValue"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/barcode_field_padding_vertical"
                android:padding="@dimen/barcode_field_value_padding"
                android:minHeight="@dimen/dp_100"
                android:background="@drawable/barcode_field_box"
                android:textColor="@color/barcode_field_value"
                android:textSize="@dimen/font_size_middle"/>

            <TextView
                android:id="@+id/tvLabel"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/barcode_field_value_padding"
                android:padding="@dimen/barcode_field_label_padding"
                android:background="@color/white"
                android:textColor="@color/black"
                android:textSize="@dimen/font_size_small"/>

            <ImageView
                android:id="@+id/ivSelect"
                android:src="@drawable/done"
                android:elevation="3dp"
                android:padding="10dp"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:layout_gravity="bottom|end"
                android:background="@drawable/shape_circle_code"/>

        </FrameLayout>
    </FrameLayout>


</layout>