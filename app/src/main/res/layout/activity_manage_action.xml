<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <LinearLayout
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <LinearLayout
            android:id="@+id/mRlManagePrivateKey"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_16"
            android:layout_marginStart="@dimen/dp_16"
            android:layout_marginEnd="@dimen/dp_16"
            android:background="@drawable/shape_card_back"
            android:paddingStart="@dimen/dp_20"
            android:paddingTop="@dimen/common_padding_horizontal"
            android:paddingEnd="@dimen/dp_20"
            android:paddingBottom="@dimen/common_padding_horizontal">

            <TextView
                android:layout_weight="1"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:text="@string/manage_wallet"
                android:textColor="@color/color_title"
                android:textSize="@dimen/font_size_common" />
            <androidx.appcompat.widget.AppCompatImageView
                android:tint="@color/color_title"
                android:src="@drawable/icon_setting_arrow_right"
                android:layout_width="@dimen/dp_24"
                android:layout_height="@dimen/dp_24"/>
        </LinearLayout>

        <TextView
            android:text="@string/manage_your_private_key_wallet_and_aa_wallet"
            android:textSize="14sp"
            android:textColor="@color/color_title_sub"
            android:layout_marginTop="@dimen/dp_10"
            android:layout_marginStart="@dimen/dp_20"
            android:layout_marginEnd="@dimen/dp_20"
            android:layout_marginBottom="@dimen/dp_16"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

        <LinearLayout
            android:id="@+id/mRlManageMnemonic"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_16"
            android:layout_marginStart="@dimen/dp_16"
            android:layout_marginEnd="@dimen/dp_16"
            android:background="@drawable/shape_card_back"
            android:paddingStart="@dimen/dp_20"
            android:paddingTop="@dimen/common_padding_horizontal"
            android:paddingEnd="@dimen/dp_20"
            android:paddingBottom="@dimen/common_padding_horizontal">

            <TextView
                android:layout_weight="1"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:text="@string/manage_mnemonic"
                android:textColor="@color/color_title"
                android:textSize="@dimen/font_size_common" />

            <androidx.appcompat.widget.AppCompatImageView
                android:tint="@color/color_title"
                android:src="@drawable/icon_setting_arrow_right"
                android:layout_width="@dimen/dp_24"
                android:layout_height="@dimen/dp_24"/>
        </LinearLayout>

        <TextView
            android:text="@string/by_managing_your_recovery_phrase"
            android:textSize="14sp"
            android:textColor="@color/color_title_sub"
            android:layout_marginTop="@dimen/dp_10"
            android:layout_marginStart="@dimen/dp_20"
            android:layout_marginEnd="@dimen/dp_20"
            android:layout_marginBottom="@dimen/dp_16"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>
    </LinearLayout>

</layout>
