<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                android:id="@+id/view2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_20"
                android:layout_marginTop="@dimen/dp_20"
                android:layout_marginEnd="@dimen/dp_20"
                android:text="@string/mnemonic_verify_tips"
                android:textColor="@color/color_title_sub"
                android:textSize="14sp"
                android:textStyle="bold" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/mRvMnemonic01"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/view2"
                android:layout_marginStart="@dimen/dp_20"
                android:layout_marginTop="@dimen/dp_20"
                android:layout_marginEnd="@dimen/dp_20"
                android:background="@drawable/shape_card_back_stroke_gradient"
                android:padding="7dp"
                app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                app:spanCount="3"
                tools:itemCount="12"
                tools:listitem="@layout/item_mnemonic" />

            <TextView
                android:id="@+id/mTvVerifyError"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/mRvMnemonic01"
                android:layout_marginStart="@dimen/dp_20"
                android:layout_marginTop="@dimen/dp_20"
                android:layout_marginEnd="@dimen/dp_20"
                android:text="@string/mnemonic_verify_error"
                android:textColor="@color/color_E53737"
                android:textSize="12sp"
                android:visibility="invisible" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/mRvMnemonic02"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/mTvVerifyError"
                android:layout_marginStart="@dimen/dp_20"
                android:layout_marginTop="@dimen/dp_20"
                android:layout_marginEnd="@dimen/dp_20"
                android:layout_marginBottom="40dp"
                app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                app:spanCount="3"
                tools:itemCount="12"
                tools:listitem="@layout/item_mnemonic" />

            <TextView
                android:id="@+id/mBtnNext"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_44"
                android:gravity="center"
                android:layout_below="@+id/mRvMnemonic02"
                android:layout_marginStart="@dimen/dp_20"
                android:layout_marginEnd="@dimen/dp_20"
                android:layout_marginBottom="@dimen/dp_60"
                android:background="@drawable/btn_shape_gradient_common"
                android:enabled="false"
                android:text="@string/confirm"
                android:textColor="@color/white" />

        </RelativeLayout>
    </ScrollView>
</layout>
