<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">
    <LinearLayout
        android:orientation="vertical"
        android:gravity="center"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <LinearLayout
            android:orientation="vertical"
            android:layout_weight="1"
            android:gravity="center"
            android:layout_width="match_parent"
            android:layout_height="0dp">
            <ImageView
                android:src="@drawable/icon_launch_network_error"
                android:layout_width="@dimen/dp_180"
                android:layout_height="@dimen/dp_130"/>
            <TextView
                android:text="@string/you_are_currently_offline"
                android:textSize="17sp"
                android:layout_marginTop="@dimen/dp_20"
                android:textColor="@color/color_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
            <TextView
                android:text="@string/connection_to_the_blockchain_host_is_unavailable"
                android:textSize="14sp"
                android:layout_marginTop="@dimen/dp_20"
                android:textColor="@color/color_title_sub"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
        </LinearLayout>

        <TextView
            android:id="@+id/tvRetry"
            android:text="@string/retry"
            android:gravity="center"
            android:textSize="14sp"
            android:textColor="@color/white"
            android:background="@drawable/btn_shape_gradient_common"
            android:layout_marginStart="@dimen/dp_20"
            android:layout_marginEnd="@dimen/dp_20"
            android:layout_marginBottom="@dimen/dp_100"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_44"/>
    </LinearLayout>

</layout>