<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <LinearLayout
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingStart="@dimen/dp_15"
            android:paddingEnd="@dimen/dp_15">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_15"
                android:layout_marginBottom="@dimen/dp_12"
                android:text="@string/current_network"
                android:textColor="@color/color_title"
                android:textSize="17sp" />

            <LinearLayout
                android:id="@+id/llSwitchNetwork"
                android:background="@drawable/shape_card_back_stroke_gradient"
                android:gravity="center_vertical"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <com.makeramen.roundedimageview.RoundedImageView
                    android:id="@+id/ivLogo"
                    android:layout_marginStart="@dimen/dp_12"
                    android:layout_width="@dimen/dp_24"
                    android:layout_height="@dimen/dp_24"
                    app:riv_corner_radius="@dimen/dp_12"
                    android:src="@drawable/ic_network_default" />
                <LinearLayout
                    android:orientation="vertical"
                    android:layout_marginStart="@dimen/dp_8"
                    android:layout_weight="1"
                    android:layout_width="0dp"
                    android:layout_marginTop="@dimen/dp_12"
                    android:layout_marginBottom="@dimen/dp_12"
                    android:layout_height="wrap_content">
                    <TextView
                        android:id="@+id/tvName"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        tools:text="IoTeX Mainnet"
                        android:textColor="@color/color_title"
                        android:textSize="14sp"/>
                    <TextView
                        android:id="@+id/tvChain"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        tools:text="Chain ID:4689"
                        android:layout_marginTop="@dimen/dp_6"
                        android:textColor="@color/color_title_sub"
                        android:textSize="13sp"/>
                </LinearLayout>
                <ImageView
                    android:layout_marginEnd="@dimen/dp_12"
                    android:src="@drawable/icon_switch_white"
                    android:layout_width="@dimen/dp_20"
                    android:layout_height="@dimen/dp_20"/>
            </LinearLayout>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/default_node"
                android:layout_marginTop="@dimen/dp_20"
                android:layout_marginBottom="@dimen/dp_5"
                android:textColor="@color/color_title_sub"
                android:textSize="14sp"/>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rvDefaultNode"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                tools:itemCount="3"
                tools:listitem="@layout/item_rpc_network_node" />

            <LinearLayout
                android:layout_marginTop="@dimen/dp_20"
                android:layout_marginBottom="@dimen/dp_5"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <TextView
                    android:id="@+id/tvCustom"
                    android:layout_weight="1"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:text="@string/custom_node"
                    android:textColor="@color/color_title_sub"
                    android:textSize="14sp"/>

                <TextView
                    android:id="@+id/tvAddNode"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:drawablePadding="@dimen/dp_12"
                    android:includeFontPadding="false"
                    android:text="@string/add_a_node"
                    android:textColor="@color/color_617AFF"
                    android:textSize="@dimen/font_size_common" />
            </LinearLayout>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rvCustomNode"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                tools:itemCount="3"
                tools:listitem="@layout/item_rpc_network_node" />

        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

</layout>
