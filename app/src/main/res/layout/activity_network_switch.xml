<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">
    <LinearLayout
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_40"
            android:layout_margin="@dimen/dp_15"
            android:gravity="center_vertical"
            android:background="@drawable/shape_card_back_r20">

            <ImageView
                android:layout_marginStart="@dimen/dp_13"
                android:src="@drawable/btn_search"
                android:layout_width="@dimen/dp_16"
                android:layout_height="@dimen/dp_16" />

            <EditText
                android:id="@+id/mEtSearch"
                android:layout_marginStart="@dimen/dp_10"
                android:layout_weight="1"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:background="@null"
                android:hint="@string/search"
                android:gravity="start"
                android:imeOptions="actionSearch"
                android:inputType="text|textAutoComplete"
                android:textColor="@color/color_title"
                android:textColorHint="@color/color_hint"
                android:textSize="@dimen/font_size_common" />

            <ImageView
                android:id="@+id/mIvDelete"
                android:padding="@dimen/dp_5"
                android:layout_width="@dimen/dp_36"
                android:layout_height="@dimen/dp_36"
                android:visibility="invisible"
                android:src="@drawable/btn_delete" />
        </LinearLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerView"
            android:layout_width="match_parent"
            tools:itemCount="5"
            tools:listitem="@layout/item_network_switch"
            android:layout_height="wrap_content" />
    </LinearLayout>

</layout>