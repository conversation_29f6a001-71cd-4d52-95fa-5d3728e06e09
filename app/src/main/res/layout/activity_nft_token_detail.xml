<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize" >

            <TextView
                android:id="@+id/toolbarTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:gravity="center"
                android:maxLines="1"
                android:autoSizeTextType="uniform"
                android:autoSizeMaxTextSize="@dimen/font_size_large"
                android:autoSizeMinTextSize="@dimen/font_size_very_little"
                android:maxWidth="@dimen/toolbar_title_max"
                android:textSize="17sp"
                tools:text="Title"
                android:textColor="@color/color_title" />

            <LinearLayout
                android:id="@+id/llRight"
                android:layout_width="wrap_content"
                android:orientation="horizontal"
                android:paddingStart="@dimen/dp_16"
                android:paddingEnd="@dimen/dp_16"
                android:gravity="center_vertical"
                android:layout_gravity="end|center_vertical"
                android:layout_height="match_parent">

                <androidx.appcompat.widget.AppCompatImageView
                    android:layout_width="@dimen/dp_24"
                    android:tint="@color/color_title"
                    android:src="@drawable/icon_nft_share"
                    android:layout_height="@dimen/dp_24" />
            </LinearLayout>

        </androidx.appcompat.widget.Toolbar>

        <FrameLayout
            android:layout_weight="1"
            android:layout_width="match_parent"
            android:layout_height="0dp">
            <androidx.core.widget.NestedScrollView
                android:id="@+id/scrollView"
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <FrameLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp_20"
                        android:layout_marginTop="@dimen/dp_20"
                        android:layout_marginEnd="@dimen/dp_20">

                        <ImageView
                            android:id="@+id/mIvTokenImage"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:scaleType="fitCenter"
                            android:background="@drawable/shape_card_back"
                            android:src="@drawable/icon_nft_default" />

                        <ImageView
                            android:id="@+id/ivSbt"
                            android:layout_width="@dimen/dp_56"
                            android:layout_height="@dimen/dp_20"
                            android:layout_gravity="end"
                            android:layout_margin="@dimen/dp_10"
                            android:src="@drawable/icon_nft_sbt_info" />

                    </FrameLayout>

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp_20"
                        android:paddingStart="@dimen/dp_20"
                        android:paddingEnd="@dimen/dp_20">

                        <TextView
                            android:id="@+id/mTvTokenName"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentStart="true"
                            android:layout_toStartOf="@+id/mTvAmount"
                            android:ellipsize="end"
                            android:fontFamily="@font/roboto_bold"
                            android:includeFontPadding="false"
                            android:lines="1"
                            android:maxLines="1"
                            android:textColor="@color/color_title_sub"
                            android:textSize="16sp"
                            tools:text="Ucam Pioneer" />

                        <TextView
                            android:id="@+id/mTvAmount"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentEnd="true"
                            android:layout_centerVertical="true"
                            android:layout_marginStart="@dimen/dp_10"
                            android:fontFamily="@font/roboto_regular"
                            android:textColor="@color/gray_9a9fb1"
                            android:textSize="14sp"
                            android:visibility="gone"
                            tools:text="x2" />

                    </RelativeLayout>

                    <TextView
                        android:layout_marginTop="@dimen/dp_30"
                        android:layout_marginStart="@dimen/dp_20"
                        android:layout_marginEnd="@dimen/dp_20"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:ellipsize="end"
                        android:fontFamily="@font/roboto_medium"
                        android:includeFontPadding="false"
                        android:lines="1"
                        android:text="@string/details"
                        android:textColor="@color/color_title_sub"
                        android:textSize="16sp" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp_20"
                        android:layout_marginTop="@dimen/dp_10"
                        android:layout_marginEnd="@dimen/dp_20"
                        android:layout_marginBottom="@dimen/dp_20"
                        android:background="@drawable/shape_card_back"
                        android:orientation="vertical"
                        android:paddingStart="@dimen/dp_15"
                        android:paddingTop="@dimen/dp_20"
                        android:paddingEnd="@dimen/dp_15"
                        android:paddingBottom="@dimen/dp_20">

                        <RelativeLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:fontFamily="@font/roboto_regular"
                                android:text="@string/contract_address"
                                android:textColor="@color/color_title_sub"
                                android:textSize="14sp" />

                            <TextView
                                android:id="@+id/mTvContract"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_centerVertical="true"
                                android:layout_marginEnd="@dimen/dp_10"
                                android:layout_toStartOf="@+id/mIvCopyAddress"
                                android:fontFamily="@font/roboto_regular"
                                android:textColor="@color/color_title_sub"
                                android:textSize="14sp"
                                tools:text="io132k…dvnyad78" />

                            <androidx.appcompat.widget.AppCompatImageView
                                android:id="@+id/mIvCopyAddress"
                                android:layout_width="@dimen/dp_16"
                                android:layout_height="@dimen/dp_16"
                                android:layout_alignParentEnd="true"
                                android:layout_centerVertical="true"
                                android:adjustViewBounds="true"
                                android:tint="@color/color_title"
                                android:src="@drawable/icon_copy_white" />

                        </RelativeLayout>

                        <LinearLayout
                            android:gravity="center"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/dp_20">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:fontFamily="@font/roboto_regular"
                                android:text="@string/token_id"
                                android:textColor="@color/color_title_sub"
                                android:textSize="14sp" />

                            <TextView
                                android:id="@+id/mTvTokenId"
                                android:gravity="end"
                                android:layout_width="0dp"
                                android:ellipsize="middle"
                                android:maxLines="1"
                                android:layout_weight="1"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="@dimen/dp_10"
                                android:layout_marginStart="@dimen/dp_10"
                                android:fontFamily="@font/roboto_regular"
                                android:textColor="@color/color_title_sub"
                                android:textSize="14sp"
                                tools:text="12" />

                            <androidx.appcompat.widget.AppCompatImageView
                                android:id="@+id/mIvCopyTokenId"
                                android:layout_width="@dimen/dp_16"
                                android:layout_height="@dimen/dp_16"
                                android:adjustViewBounds="true"
                                android:tint="@color/color_title"
                                android:src="@drawable/icon_copy_white" />

                        </LinearLayout>

                        <RelativeLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/dp_20">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_centerVertical="true"
                                android:fontFamily="@font/roboto_regular"
                                android:text="@string/token_standard"
                                android:textColor="@color/color_title_sub"
                                android:textSize="14sp" />

                            <TextView
                                android:id="@+id/mTvProtocol"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_alignParentEnd="true"
                                android:layout_centerVertical="true"
                                android:fontFamily="@font/roboto_regular"
                                android:textColor="@color/color_title_sub"
                                android:textSize="14sp"
                                tools:text="ERC-1155" />

                        </RelativeLayout>

                        <RelativeLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/dp_20">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_centerVertical="true"
                                android:fontFamily="@font/roboto_regular"
                                android:text="@string/blockchain"
                                android:textColor="@color/color_title_sub"
                                android:textSize="14sp" />

                            <TextView
                                android:id="@+id/mTvChain"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_alignParentEnd="true"
                                android:layout_centerVertical="true"
                                android:fontFamily="@font/roboto_regular"
                                android:textColor="@color/color_title_sub"
                                android:textSize="14sp"
                                tools:text="Ethereum" />

                        </RelativeLayout>

                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/llBucket"
                        android:visibility="gone"
                        tools:visibility="visible"
                        android:orientation="vertical"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent">
                        <LinearLayout
                            android:layout_marginTop="@dimen/dp_20"
                            android:layout_marginStart="@dimen/dp_20"
                            android:layout_marginEnd="@dimen/dp_20"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content">
                            <TextView
                                android:text="@string/staking_details"
                                android:textSize="16sp"
                                android:textColor="@color/color_title_sub"
                                android:layout_weight="1"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"/>
                            <TextView
                                android:id="@+id/tvViewScan"
                                android:text="@string/view_on_iotexscan"
                                android:textSize="14sp"
                                android:textColor="@color/color_617AFF"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"/>
                        </LinearLayout>

                        <LinearLayout
                            android:orientation="vertical"
                            android:layout_marginTop="@dimen/dp_12"
                            android:layout_marginStart="@dimen/dp_20"
                            android:layout_marginEnd="@dimen/dp_20"
                            android:paddingStart="@dimen/dp_15"
                            android:paddingEnd="@dimen/dp_15"
                            android:paddingTop="@dimen/dp_10"
                            android:paddingBottom="@dimen/dp_10"
                            android:background="@drawable/shape_card_back"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content">

                            <LinearLayout
                                android:gravity="center"
                                android:layout_width="match_parent"
                                android:layout_height="@dimen/dp_36">
                                <TextView
                                    android:textColor="@color/color_title_sub"
                                    android:textSize="14sp"
                                    android:text="@string/amount"
                                    android:layout_weight="1"
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"/>
                                <TextView
                                    android:id="@+id/tvStakeAmount"
                                    android:textColor="@color/color_title_sub"
                                    android:textSize="14sp"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"/>
                            </LinearLayout>
                            <LinearLayout
                                android:gravity="center"
                                android:layout_width="match_parent"
                                android:layout_height="@dimen/dp_36">
                                <TextView
                                    android:textColor="@color/color_title_sub"
                                    android:textSize="14sp"
                                    android:text="@string/vote_for"
                                    android:layout_weight="1"
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"/>
                                <TextView
                                    android:id="@+id/tvStakeVote"
                                    android:textColor="@color/color_title_sub"
                                    android:textSize="14sp"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"/>
                            </LinearLayout>
                            <LinearLayout
                                android:gravity="center"
                                android:layout_width="match_parent"
                                android:layout_height="@dimen/dp_36">
                                <TextView
                                    android:textColor="@color/color_title_sub"
                                    android:textSize="14sp"
                                    android:text="@string/lock_duration"
                                    android:layout_weight="1"
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"/>
                                <TextView
                                    android:id="@+id/tvStakeDuration"
                                    android:textColor="@color/color_title_sub"
                                    android:textSize="14sp"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"/>
                            </LinearLayout>
                            <LinearLayout
                                android:gravity="center"
                                android:layout_width="match_parent"
                                android:layout_height="@dimen/dp_36">
                                <TextView
                                    android:textColor="@color/color_title_sub"
                                    android:textSize="14sp"
                                    android:text="@string/stake_lock_detail"
                                    android:layout_weight="1"
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"/>
                                <TextView
                                    android:id="@+id/tvStakeLock"
                                    android:textColor="@color/color_title_sub"
                                    android:textSize="14sp"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"/>
                            </LinearLayout>
                            <LinearLayout
                                android:gravity="center"
                                android:layout_width="match_parent"
                                android:layout_height="@dimen/dp_36">
                                <TextView
                                    android:textColor="@color/color_title_sub"
                                    android:textSize="14sp"
                                    android:text="@string/bucket_status"
                                    android:layout_weight="1"
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"/>
                                <TextView
                                    android:id="@+id/tvStakeStatus"
                                    android:textColor="@color/color_title_sub"
                                    android:textSize="14sp"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"/>
                            </LinearLayout>
                        </LinearLayout>
                    </LinearLayout>

                </LinearLayout>
            </androidx.core.widget.NestedScrollView>

            <ImageView
                android:id="@+id/ivDown"
                android:visibility="gone"
                tools:visibility="visible"
                android:padding="@dimen/dp_5"
                android:layout_gravity="center_horizontal|bottom"
                android:src="@drawable/icon_sign_message_down"
                android:layout_width="@dimen/dp_23"
                android:layout_height="@dimen/dp_21"/>
        </FrameLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_20"
            android:layout_marginTop="@dimen/dp_10"
            android:layout_marginEnd="@dimen/dp_20"
            android:layout_marginBottom="@dimen/dp_30">

            <LinearLayout
                android:id="@+id/mLlSend"
                android:layout_width="0dp"
                android:layout_height="@dimen/dp_44"
                android:layout_weight="1"
                android:background="@drawable/stroke_617aff_r6"
                android:gravity="center">

                <TextView
                    android:id="@+id/mTvSend"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/roboto_regular"
                    android:gravity="center"
                    android:text="@string/xrc_send"
                    android:textColor="@color/color_617AFF"
                    android:textSize="14sp" />

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/mIvSend"
                    android:layout_width="@dimen/dp_20"
                    android:tint="@color/color_617AFF"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp_6"
                    android:adjustViewBounds="true"
                    android:src="@drawable/ic_send"/>
            </LinearLayout>

            <LinearLayout
                android:id="@+id/mLlMarket"
                android:layout_width="0dp"
                android:layout_height="44dp"
                android:layout_weight="1"
                android:layout_marginStart="@dimen/dp_15"
                android:background="@drawable/btn_shape_gradient_common"
                android:gravity="center">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/roboto_regular"
                    android:gravity="center"
                    android:text="@string/nft_marketplace"
                    android:textColor="@color/white" />

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>
</layout>
