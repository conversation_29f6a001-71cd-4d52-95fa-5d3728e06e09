<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <LinearLayout
            android:orientation="vertical"
            android:gravity="center_horizontal"
            android:paddingStart="@dimen/dp_16"
            android:paddingEnd="@dimen/dp_16"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <TextView
                android:text="@string/your_private_key"
                android:textSize="17sp"
                android:layout_marginTop="@dimen/dp_12"
                android:textColor="@color/color_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
            <TextView
                android:text="@string/keep_the_private_key_only_to_yourself"
                android:textSize="14sp"
                android:layout_marginTop="@dimen/dp_12"
                android:textColor="@color/color_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"/>

            <FrameLayout
                android:background="@drawable/shape_card_back_stroke_gradient"
                android:layout_marginTop="@dimen/dp_12"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_150">
                <TextView
                    android:id="@+id/tvPrivateKey"
                    android:textColor="@color/color_title"
                    android:textSize="13sp"
                    android:layout_margin="@dimen/dp_20"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"/>
                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/ivCopy"
                    android:layout_gravity="end|bottom"
                    android:tint="@color/color_title"
                    android:src="@drawable/icon_copy_white"
                    android:padding="@dimen/dp_10"
                    android:layout_width="@dimen/dp_44"
                    android:layout_height="@dimen/dp_44"/>

                <LinearLayout
                    android:id="@+id/llEye"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp_150"
                    android:background="@drawable/shape_card_back_stroke_gradient"
                    android:gravity="center"
                    tools:visibility="gone"
                    android:orientation="vertical">

                    <androidx.appcompat.widget.AppCompatImageView
                        android:layout_width="@dimen/dp_24"
                        android:layout_height="@dimen/dp_24"
                        android:src="@drawable/icon_eye_close_gradient" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@color/color_title"
                        android:textSize="13sp"
                        android:text="@string/click_to_see_private_key" />

                </LinearLayout>
            </FrameLayout>

            <TextView
                android:textColor="@color/color_title"
                android:textSize="17sp"
                android:layout_marginTop="@dimen/dp_60"
                android:text="@string/verify_private_key"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
            <EditText
                android:id="@+id/etPrivateKey"
                android:padding="@dimen/dp_20"
                android:textColor="@color/color_title"
                android:textSize="14sp"
                android:gravity="top"
                android:textColorHint="@color/color_hint"
                android:hint="@string/input_verify_private_key"
                android:background="@drawable/shape_card_back_stroke_gradient"
                android:layout_marginTop="@dimen/dp_12"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_150"/>
            <TextView
                android:id="@+id/tvSave"
                android:gravity="center"
                android:background="@drawable/btn_shape_gradient_common"
                android:layout_marginTop="@dimen/dp_60"
                android:text="@string/view_wallet_cap"
                android:textColor="@color/white"
                android:textSize="14sp"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_44"/>
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

</layout>