<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_15"
            android:layout_marginTop="@dimen/dp_15"
            android:layout_marginEnd="@dimen/dp_15"
            android:text="@string/recover_aa_wallet_tips"
            android:textColor="@color/color_title_sub"
            android:textSize="14sp" />

        <LinearLayout
            android:id="@+id/llEmailContainer"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_56"
            android:layout_marginStart="@dimen/dp_15"
            android:layout_marginTop="@dimen/dp_28"
            android:layout_marginEnd="@dimen/dp_15"
            android:background="@drawable/shape_card_back"
            android:gravity="center_vertical"
            android:paddingStart="@dimen/dp_15"
            android:paddingEnd="@dimen/dp_20">

            <EditText
                android:id="@+id/etEmail"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:background="@null"
                android:hint="@string/input_your_email"
                android:maxLines="1"
                android:singleLine="true"
                android:textColor="@color/color_title"
                android:textColorHint="@color/color_hint"
                android:textSize="16sp" />

            <ImageView
                android:id="@+id/ivClear"
                android:layout_width="@dimen/dp_16"
                android:layout_height="@dimen/dp_16"
                android:layout_marginStart="@dimen/dp_8"
                android:src="@drawable/ic_close" />
        </LinearLayout>

        <TextView
            android:id="@+id/tvEmailError"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_20"
            android:layout_marginTop="@dimen/dp_4"
            android:layout_marginEnd="@dimen/dp_20"
            android:text="@string/invalid_email"
            android:textColor="@color/error_red"
            android:textSize="12sp"
            android:visibility="invisible" />

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <LinearLayout
                android:id="@+id/llProgress"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_50"
                android:gravity="center_horizontal"
                android:orientation="vertical"
                android:visibility="gone">

                <ProgressBar
                    android:layout_width="@dimen/dp_32"
                    android:layout_height="@dimen/dp_32" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp_6"
                    android:text="@string/loading"
                    android:textColor="@color/color_617AFF"
                    android:textSize="14sp" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <LinearLayout
                    android:id="@+id/llContent"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp_15"
                        android:layout_marginTop="@dimen/dp_15"
                        android:layout_marginEnd="@dimen/dp_15"
                        android:text="@string/aa_wallet_list_tips"
                        android:textColor="@color/color_title_sub"
                        android:textSize="14sp" />

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rvWallets"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp_15"
                        android:layout_marginTop="@dimen/dp_12"
                        android:layout_marginEnd="@dimen/dp_15"
                        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                        tools:itemCount="2"
                        tools:listitem="@layout/item_recover_aa_wallet" />

                </LinearLayout>


                <LinearLayout
                    android:id="@+id/llEmpty"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:visibility="gone"
                    tools:visibility="visible"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:gravity="center_vertical"
                        android:layout_marginStart="@dimen/dp_16"
                        android:layout_marginEnd="@dimen/dp_16"
                        android:layout_height="wrap_content">

                        <androidx.appcompat.widget.AppCompatImageView
                            android:layout_width="@dimen/dp_14"
                            android:layout_height="wrap_content"
                            android:adjustViewBounds="true"
                            android:src="@drawable/icon_warning_red"
                            android:tint="@color/color_ed8112" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp_3"
                            android:text="@string/query_failed_and_retry"
                            android:textColor="@color/color_title_sub"
                            android:textSize="12sp" />

                    </LinearLayout>

                    <androidx.appcompat.widget.AppCompatButton
                        android:id="@+id/btnTryAgain"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dp_44"
                        android:layout_marginHorizontal="@dimen/common_margin"
                        android:layout_marginTop="@dimen/dp_20"
                        android:background="@drawable/btn_shape_gradient_common"
                        android:text="@string/try_again"
                        android:textAllCaps="false"
                        android:textColor="@android:color/white"
                        android:textSize="14sp" />

                </LinearLayout>

            </LinearLayout>

        </FrameLayout>

    </LinearLayout>
</layout>
