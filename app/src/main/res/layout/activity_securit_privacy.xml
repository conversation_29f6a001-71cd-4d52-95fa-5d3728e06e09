<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">
        <LinearLayout
            android:id="@+id/view_change_pin"
            android:background="@drawable/shape_card_back"
            android:layout_width="match_parent"
            android:layout_marginTop="@dimen/common_padding_vertical"
            android:layout_marginStart="@dimen/dp_16"
            android:layout_marginEnd="@dimen/dp_16"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:padding="@dimen/common_padding_horizontal"
            android:layout_height="wrap_content">
            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/change_pin"
                android:textColor="@color/color_title"
                android:textSize="@dimen/font_size_middle"
                android:gravity="start|center"
                />
            <androidx.appcompat.widget.AppCompatImageView
                android:tint="@color/color_title"
                android:src="@drawable/meta_setting_more"
                android:layout_width="@dimen/dp_24"
                android:layout_height="@dimen/dp_24"/>
        </LinearLayout>

        <TextView
            android:text="@string/to_change_your_iopay_password"
            android:textSize="12sp"
            android:layout_marginTop="@dimen/dp_10"
            android:layout_marginBottom="@dimen/dp_10"
            android:layout_marginStart="@dimen/dp_15"
            android:layout_marginEnd="@dimen/dp_15"
            android:textColor="@color/color_title_sub"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"/>
        <LinearLayout
            android:id="@+id/view_dapp_auth"
            android:background="@drawable/shape_card_back"
            android:layout_width="match_parent"
            android:layout_marginTop="@dimen/common_padding_vertical"
            android:layout_marginStart="@dimen/dp_16"
            android:layout_marginEnd="@dimen/dp_16"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:padding="@dimen/common_padding_horizontal"
            android:layout_height="wrap_content">
            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/clear_auth_cache"
                android:textColor="@color/color_title"
                android:textSize="@dimen/font_size_middle"
                android:gravity="start|center"
                />
            <androidx.appcompat.widget.AppCompatImageView
                android:tint="@color/color_title"
                android:src="@drawable/meta_setting_more"
                android:layout_width="@dimen/dp_24"
                android:layout_height="@dimen/dp_24"/>
        </LinearLayout>

        <TextView
            android:text="@string/after_clearing_the_allowance_record"
            android:textSize="12sp"
            android:layout_marginTop="@dimen/dp_10"
            android:layout_marginBottom="@dimen/dp_10"
            android:layout_marginStart="@dimen/dp_15"
            android:layout_marginEnd="@dimen/dp_15"
            android:textColor="@color/color_title_sub"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"/>

        <LinearLayout
            android:id="@+id/view_authentication"
            android:background="@drawable/shape_card_back"
            android:layout_width="match_parent"
            android:layout_marginTop="@dimen/common_padding_vertical"
            android:layout_marginStart="@dimen/dp_16"
            android:layout_marginEnd="@dimen/dp_16"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:padding="@dimen/common_padding_horizontal"
            android:layout_height="wrap_content">
            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/authentication"
                android:textColor="@color/color_title"
                android:textSize="@dimen/font_size_middle"
                android:gravity="start|center"
                />
            <androidx.appcompat.widget.AppCompatImageView
                android:tint="@color/color_title"
                android:src="@drawable/meta_setting_more"
                android:layout_width="@dimen/dp_24"
                android:layout_height="@dimen/dp_24"/>
        </LinearLayout>

        <TextView
            android:text="@string/configure_your_authentication"
            android:textSize="12sp"
            android:layout_marginTop="@dimen/dp_10"
            android:layout_marginBottom="@dimen/dp_10"
            android:layout_marginStart="@dimen/dp_15"
            android:layout_marginEnd="@dimen/dp_15"
            android:textColor="@color/color_title_sub"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"/>
    </LinearLayout>


</layout>