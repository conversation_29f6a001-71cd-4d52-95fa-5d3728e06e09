<?xml version="1.0" encoding="utf-8"?>

<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fitsSystemWindows="true"
        android:orientation="vertical">
        <androidx.core.widget.NestedScrollView
            android:id="@+id/svContainer"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:fillViewport="true"
            android:overScrollMode="never">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp_15"
                    android:layout_marginTop="@dimen/dp_28"
                    android:text="@string/select_hd_path"
                    android:textColor="@color/color_title"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tvPathCaption"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp_15"
                    android:layout_marginTop="@dimen/dp_6"
                    android:layout_marginEnd="@dimen/dp_15"
                    android:text="@string/iopay_supports_the_default_bip44_standard"
                    android:textColor="@color/color_title_sub"
                    android:textSize="14sp" />

                <LinearLayout
                    android:id="@+id/llIotexPath"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp_52"
                    android:layout_marginStart="@dimen/dp_15"
                    android:layout_marginTop="@dimen/dp_16"
                    android:layout_marginEnd="@dimen/dp_15"
                    android:background="@drawable/shape_card_back_stroke_gradient"
                    android:gravity="center_vertical"
                    android:paddingStart="@dimen/dp_15"
                    android:paddingEnd="@dimen/dp_15">

                    <ImageView
                        android:layout_width="@dimen/dp_28"
                        android:layout_height="@dimen/dp_28"
                        android:src="@drawable/icon_iotex_network_logo" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp_10"
                        android:text="IoTeX Standard (IoTeX HD PATH)"
                        android:textColor="@color/color_title"
                        android:textSize="14sp" />

                </LinearLayout>


                <LinearLayout
                    android:id="@+id/llEthPath"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp_52"
                    android:layout_marginStart="@dimen/dp_15"
                    android:layout_marginTop="@dimen/dp_20"
                    android:layout_marginEnd="@dimen/dp_15"
                    android:background="@drawable/shape_card_back"
                    android:gravity="center_vertical"
                    android:paddingStart="@dimen/dp_15"
                    android:paddingEnd="@dimen/dp_15">

                    <ImageView
                        android:layout_width="@dimen/dp_28"
                        android:layout_height="@dimen/dp_28"
                        android:src="@drawable/icon_eth_network_logo" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp_10"
                        android:text="ETH Standard (ETH HD PATH)"
                        android:textColor="@color/color_title"
                        android:textSize="14sp" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/llNativeSegwit"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp_52"
                    android:layout_marginStart="@dimen/dp_15"
                    android:layout_marginTop="@dimen/dp_16"
                    android:layout_marginEnd="@dimen/dp_15"
                    android:background="@drawable/shape_card_back"
                    android:gravity="center_vertical"
                    android:paddingStart="@dimen/dp_15"
                    android:paddingEnd="@dimen/dp_15">

                    <ImageView
                        android:layout_width="@dimen/dp_28"
                        android:layout_height="@dimen/dp_28"
                        android:src="@drawable/icon_bitcoin_logo" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp_10"
                        android:text="BTC Standard (BTC HD PATH)"
                        android:textColor="@color/color_title"
                        android:textSize="14sp" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/mLlPathSolana"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp_52"
                    android:layout_marginStart="@dimen/dp_15"
                    android:layout_marginTop="@dimen/dp_16"
                    android:layout_marginEnd="@dimen/dp_15"
                    android:background="@drawable/shape_card_back"
                    android:gravity="center_vertical"
                    android:paddingStart="@dimen/dp_15"
                    android:paddingEnd="@dimen/dp_12">

                    <ImageView
                        android:src="@drawable/icon_solana_logo"
                        android:layout_width="@dimen/dp_28"
                        android:layout_height="@dimen/dp_28"/>

                    <TextView
                        android:layout_marginStart="@dimen/dp_8"
                        android:id="@+id/mTvPathSolana"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:ellipsize="end"
                        android:lines="1"
                        android:textColor="@color/color_title"
                        android:textSize="14sp"
                        android:text="Solana Standard (Solana HD PATH)" />


                </LinearLayout>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp_15"
                    android:layout_marginTop="@dimen/dp_8"
                    android:layout_marginEnd="@dimen/dp_15"
                    android:text="@string/customize_path_desc"
                    android:textColor="@color/color_617AFF"
                    android:textSize="12sp" />

                <LinearLayout
                    android:id="@+id/llCustomizePath"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp_15"
                    android:layout_marginTop="@dimen/dp_8"
                    android:layout_marginEnd="@dimen/dp_15"
                    android:layout_marginBottom="@dimen/dp_28"
                    android:background="@drawable/shape_card_back"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dp_52"
                        android:gravity="center_vertical"
                        android:paddingStart="@dimen/dp_15"
                        android:paddingEnd="@dimen/dp_15">

                        <ImageView
                            android:layout_width="@dimen/dp_28"
                            android:layout_height="@dimen/dp_28"
                            android:src="@drawable/ic_fee_costomize" />

                        <TextView
                            android:id="@+id/tvCustomizePath"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp_10"
                            android:text="@string/customize"
                            android:textColor="@color/color_title"
                            android:textSize="14sp" />

                        <View
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1" />

                        <ImageView
                            android:id="@id/ivArrow"
                            android:layout_width="@dimen/dp_32"
                            android:layout_height="@dimen/dp_27"
                            android:layout_marginStart="@dimen/dp_10"
                            android:padding="@dimen/dp_10"
                            android:src="@drawable/ic_arrow_down" />

                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/llCustomizePanel"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:paddingStart="@dimen/dp_15"
                        android:paddingEnd="@dimen/dp_15"
                        android:visibility="gone"
                        tools:visibility="visible">

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="1px"
                            android:layout_marginTop="@dimen/dp_8"
                            android:alpha="0.3"
                            android:background="@color/color_line_diver" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/dp_20"
                            android:layout_marginBottom="@dimen/dp_20"
                            android:orientation="vertical">

                            <EditText
                                android:id="@+id/etCustomizePath"
                                android:layout_width="match_parent"
                                android:layout_height="@dimen/dp_40"
                                android:background="@drawable/shape_stroke_4d808080_r4"
                                android:hint="@string/input_the_path"
                                android:lines="1"
                                android:paddingStart="@dimen/dp_8"
                                android:paddingEnd="@dimen/dp_8"
                                android:textColor="@color/color_title"
                                android:textColorHint="@color/color_hint"
                                android:textSize="12sp" />

                            <TextView
                                android:id="@+id/tvError"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="@dimen/dp_4"
                                android:includeFontPadding="false"
                                android:textColor="@color/color_f10124"
                                android:textSize="@dimen/font_size_small"
                                android:visibility="gone"
                                tools:text="asdfadfdasdfasdf" />

                        </LinearLayout>

                        <TextView
                            android:id="@+id/tvCustomizeConfirm"
                            android:layout_width="match_parent"
                            android:layout_height="@dimen/dp_44"
                            android:layout_marginTop="@dimen/dp_30"
                            android:layout_marginBottom="@dimen/dp_18"
                            android:alpha="0.5"
                            android:background="@drawable/btn_shape_gradient_common"
                            android:enabled="false"
                            android:gravity="center"
                            android:text="@string/confirm"
                            android:textColor="@android:color/white" />

                    </LinearLayout>

                </LinearLayout>


            </LinearLayout>

        </androidx.core.widget.NestedScrollView>
    </LinearLayout>

</layout>