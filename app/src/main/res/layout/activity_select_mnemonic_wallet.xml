<?xml version="1.0" encoding="utf-8"?>
<layout>

    <FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fitsSystemWindows="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <androidx.core.widget.NestedScrollView
                android:id="@+id/mNslContainer"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:overScrollMode="never">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_marginStart="@dimen/dp_15"
                        android:layout_marginTop="@dimen/dp_15"
                        android:gravity="center_vertical"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/select_network"
                            android:textColor="@color/color_title"
                            android:textSize="16sp"
                            android:textStyle="bold" />
                        <ImageView
                            android:id="@+id/ivHdPath"
                            android:layout_marginStart="@dimen/dp_5"
                            android:src="@drawable/icon_about_select_path"
                            android:layout_width="@dimen/dp_18"
                            android:layout_height="@dimen/dp_18"/>
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/mLlNetwork"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dp_54"
                        android:layout_marginStart="@dimen/dp_15"
                        android:layout_marginTop="@dimen/dp_12"
                        android:layout_marginEnd="@dimen/dp_15"
                        android:background="@drawable/shape_card_back"
                        android:gravity="center_vertical"
                        android:paddingStart="@dimen/dp_15"
                        android:paddingEnd="@dimen/dp_12">

                        <com.makeramen.roundedimageview.RoundedImageView
                            android:id="@+id/mIvNetwork"
                            app:riv_corner_radius="@dimen/dp_14"
                            android:layout_width="@dimen/dp_28"
                            android:layout_height="wrap_content"
                            android:adjustViewBounds="true"
                            android:src="@drawable/icon_iotex_network_logo" />

                        <TextView
                            android:id="@+id/mTvNetwork"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp_10"
                            android:layout_weight="1"
                            android:ellipsize="end"
                            android:lines="1"
                            android:textColor="@color/color_title"
                            android:textSize="14sp"
                            tools:text="IoTeX Network Mainnet" />

                        <androidx.appcompat.widget.AppCompatImageView
                            android:layout_width="wrap_content"
                            android:layout_height="@dimen/dp_12"
                            android:adjustViewBounds="true"
                            android:src="@drawable/ic_arrow_right"
                            android:tint="@color/gray_B4B8CB" />

                    </LinearLayout>

                    <androidx.legacy.widget.Space
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dp_20" />

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/mRvWallet"
                        android:layout_width="match_parent"
                        android:layout_height="0dp"
                        android:layout_marginStart="@dimen/dp_15"
                        android:layout_marginEnd="@dimen/dp_15"
                        android:layout_weight="1"
                        android:overScrollMode="never"
                        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                        tools:itemCount="2"
                        tools:listitem="@layout/item_select_mnemonic_wallet" />

                    <androidx.legacy.widget.Space
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dp_30" />

                    <TextView
                        android:id="@+id/mTvLoadMore"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center_horizontal"
                        android:text="@string/load_more_underline"
                        android:textColor="@color/color_617AFF"
                        android:textSize="14sp"
                        android:visibility="gone" />

                    <LinearLayout
                        android:id="@+id/mLlLoadingContainer"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center_horizontal"
                        android:orientation="vertical"
                        android:visibility="gone">

                        <ProgressBar
                            android:layout_width="@dimen/dp_32"
                            android:layout_height="@dimen/dp_32" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/dp_6"
                            android:text="@string/loading"
                            android:textColor="@color/color_617AFF"
                            android:textSize="14sp" />

                    </LinearLayout>

                    <androidx.legacy.widget.Space
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dp_30" />

                </LinearLayout>

            </androidx.core.widget.NestedScrollView>

        </LinearLayout>

    </FrameLayout>
</layout>

