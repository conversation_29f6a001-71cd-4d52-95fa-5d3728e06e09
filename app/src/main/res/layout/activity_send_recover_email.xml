<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_15"
            android:layout_marginEnd="@dimen/dp_15"
            android:layout_marginTop="@dimen/dp_15"
            android:text="@string/send_recover_email_tips"
            android:textColor="@color/color_title_sub"
            android:textSize="13sp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_15"
            android:layout_marginTop="@dimen/dp_30"
            android:layout_marginEnd="@dimen/dp_15">

            <ImageView
                android:layout_width="@dimen/dp_20"
                android:layout_height="@dimen/dp_20"
                android:src="@drawable/ic_warning" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_6"
                android:text="@string/send_recover_email_tips"
                android:textColor="@color/color_title_sub"
                android:textSize="13sp" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_15"
            android:layout_marginTop="@dimen/dp_15"
            android:layout_marginEnd="@dimen/dp_15"
            android:background="@drawable/shape_card_back"
            android:orientation="vertical"
            android:padding="@dimen/dp_15">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/to_colon"
                    android:textColor="@color/color_title_sub"
                    android:textSize="14sp" />

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/ivCopyEmail"
                    android:layout_width="@dimen/dp_16"
                    android:layout_height="@dimen/dp_16"
                    android:tint="@color/color_title"
                    android:src="@drawable/icon_copy_white" />

            </LinearLayout>

            <TextView
                android:id="@+id/tvReceiptEmail"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_6"
                android:textColor="@color/color_title_sub"
                android:textSize="14sp"
                tools:text="<EMAIL>" />

            <View
                android:layout_width="match_parent"
                android:layout_height="1px"
                android:layout_marginTop="@dimen/dp_15"
                android:background="@color/color_363647" />

            <LinearLayout
                android:layout_marginTop="@dimen/dp_10"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/subject_colon"
                    android:textColor="@color/color_title_sub"
                    android:textSize="14sp" />

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/ivCopySubject"
                    android:layout_width="@dimen/dp_16"
                    android:layout_height="@dimen/dp_16"
                    android:tint="@color/color_title"
                    android:src="@drawable/icon_copy_white" />

            </LinearLayout>

            <TextView
                android:id="@+id/tvEmailSubject"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_6"
                android:textColor="@color/color_title_sub"
                android:textSize="14sp"
                tools:text="0146900xdb874e9d157abe744b8d6f9950b578fa06a6069c134dba58f6a5a7fa99554c87b34146980043181d3be1c326ed8e2e67f6b50857ca7f5e6958cb6cbd6cc1c18c02c66b4ba56cb678f76f7fcf8f35e4afe8db43b8f35e4afe8db43b8f35e4afe8db48db4" />

        </LinearLayout>

        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/btnSend"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_44"
            android:layout_marginStart="@dimen/dp_20"
            android:layout_marginTop="@dimen/dp_20"
            android:layout_marginEnd="@dimen/dp_20"
            android:background="@drawable/btn_shape_gradient_common"
            android:text="@string/transfer_send"
            android:textAllCaps="false"
            android:textColor="@color/white" />

        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/btnHasSent"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_44"
            android:layout_marginStart="@dimen/dp_20"
            android:layout_marginTop="@dimen/dp_20"
            android:layout_marginEnd="@dimen/dp_20"
            android:background="@drawable/btn_shape_gradient_common"
            android:text="@string/have_sent_email"
            android:textAllCaps="false"
            android:textColor="@color/white" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_15"
            android:layout_marginTop="@dimen/dp_12"
            android:layout_marginEnd="@dimen/dp_15"
            android:text="@string/recover_email_caption"
            android:textColor="@color/color_title_sub"
            android:textSize="13sp" />

    </LinearLayout>
</layout>
