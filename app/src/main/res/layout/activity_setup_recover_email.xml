<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_20"
            android:layout_marginTop="@dimen/dp_30"
            android:layout_marginEnd="@dimen/dp_20"
            android:text="@string/email"
            android:textColor="@color/color_title_sub"
            android:textSize="14sp" />

        <LinearLayout
            android:id="@+id/llEmailContainer"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_48"
            android:layout_marginStart="@dimen/dp_20"
            android:layout_marginTop="@dimen/dp_10"
            android:layout_marginEnd="@dimen/dp_20"
            android:background="@drawable/shape_card_back"
            android:gravity="center_vertical"
            android:paddingStart="@dimen/dp_16"
            android:paddingEnd="@dimen/dp_16">

            <EditText
                android:id="@+id/etEmail"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/dp_12"
                android:layout_weight="1"
                android:background="@null"
                android:hint="@string/enter_email"
                android:maxLines="1"
                android:singleLine="true"
                android:textColor="@color/color_title"
                android:textColorHint="@color/color_hint"
                android:textSize="16sp"
                tools:text="<EMAIL>" />

            <TextView
                android:id="@+id/tvSendCode"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:enabled="false"
                android:text="@string/send_code"
                android:textColor="@color/selector_enable_617aff_a50617aff"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/tvCountDown"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="60s"
                android:textColor="@color/color_617AFF"
                android:textSize="14sp"
                android:visibility="gone" />
        </LinearLayout>

        <TextView
            android:id="@+id/tvEmailTips"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_20"
            android:layout_marginTop="@dimen/dp_4"
            android:layout_marginEnd="@dimen/dp_20"
            android:text="@string/setup_email_tips"
            android:textColor="@color/color_title_sub"
            android:textSize="12sp" />

        <TextView
            android:id="@+id/tvEmailError"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_20"
            android:layout_marginTop="@dimen/dp_4"
            android:layout_marginEnd="@dimen/dp_20"
            android:text="@string/invalid_email"
            android:textColor="@color/error_red"
            android:textSize="12sp"
            android:visibility="gone" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_20"
            android:layout_marginTop="@dimen/dp_24"
            android:layout_marginEnd="@dimen/dp_20"
            android:text="@string/code"
            android:textColor="@color/color_title_sub"
            android:textSize="14sp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_48"
            android:layout_marginStart="@dimen/dp_20"
            android:layout_marginTop="@dimen/dp_10"
            android:layout_marginEnd="@dimen/dp_20"
            android:background="@drawable/shape_card_back"
            android:gravity="center_vertical"
            android:paddingStart="@dimen/dp_16"
            android:paddingEnd="@dimen/dp_16">

            <EditText
                android:id="@+id/etCode"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:background="@null"
                android:hint="@string/enter_code"
                android:inputType="number"
                android:singleLine="true"
                android:maxLines="1"
                android:maxLength="6"
                android:textColor="@color/color_title"
                android:textColorHint="@color/color_hint"
                android:textSize="16sp" />
        </LinearLayout>

        <TextView
            android:id="@+id/tvCodeError"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_20"
            android:layout_marginTop="@dimen/dp_4"
            android:layout_marginEnd="@dimen/dp_20"
            android:text="@string/invalid_code"
            android:textColor="@color/error_red"
            android:textSize="12sp"
            android:visibility="invisible" />

        <TextView
            android:id="@+id/tvRemainFreeGasFee"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_20"
            android:layout_marginTop="@dimen/dp_8"
            android:layout_marginEnd="@dimen/dp_20"
            android:text="@string/remain_free_gas_fee"
            android:textColor="@color/color_title_sub"
            android:textSize="@dimen/font_size_common" />

        <LinearLayout
            android:id="@+id/llApplyGasFee"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_20"
            android:layout_marginTop="@dimen/dp_8"
            android:layout_marginEnd="@dimen/dp_20"
            android:gravity="center_vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/apply_free_gas_fee_now_underline"
                android:textColor="@color/color_617AFF"
                android:textSize="12sp" />

            <androidx.appcompat.widget.AppCompatImageView
                android:tint="@color/color_617AFF"
                android:layout_width="@dimen/dp_18"
                android:layout_height="@dimen/dp_18"
                android:layout_marginStart="@dimen/dp_4"
                android:src="@drawable/ic_warning" />
        </LinearLayout>

        <View
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:layout_weight="1" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:visibility="gone"
            tools:visibility="visible"
            android:layout_marginBottom="@dimen/dp_24"
            android:gravity="center">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/learn_more_about"
                android:textColor="@color/color_title_sub"
                android:textSize="12sp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_4"
                android:text="@string/aa_wallet_underline"
                android:textColor="@color/color_617AFF"
                android:textSize="12sp" />

        </LinearLayout>

    </LinearLayout>
</layout>