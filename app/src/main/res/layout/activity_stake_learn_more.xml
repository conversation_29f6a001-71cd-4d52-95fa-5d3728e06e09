<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">
    <LinearLayout
        android:orientation="vertical"
        android:paddingStart="@dimen/dp_16"
        android:paddingEnd="@dimen/dp_16"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <ScrollView
            android:layout_weight="1"
            android:layout_width="match_parent"
            android:layout_height="0dp">
            <LinearLayout
                android:orientation="vertical"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <LinearLayout
                    android:id="@+id/llStartStake"
                    android:orientation="vertical"
                    android:padding="@dimen/dp_16"
                    android:layout_marginTop="@dimen/dp_12"
                    android:background="@drawable/shape_card_back"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">
                    <LinearLayout
                        android:orientation="vertical"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">
                        <TextView
                            android:textSize="14sp"
                            android:text="@string/stake_with_iotex"
                            android:textColor="@color/color_title_sub"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"/>
                        <LinearLayout
                            android:layout_marginTop="@dimen/dp_4"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content">
                            <TextView
                                android:textSize="14sp"
                                android:text="@string/earn"
                                android:textColor="@color/color_title"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"/>
                            <TextView
                                android:textSize="18sp"
                                android:text="5.5%"
                                android:textStyle="bold"
                                android:layout_marginStart="@dimen/dp_2"
                                android:layout_marginEnd="@dimen/dp_2"
                                android:textColor="@color/color_855eff"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"/>
                            <TextView
                                android:textSize="14sp"
                                android:text="@string/per_year"
                                android:textColor="@color/color_title"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"/>
                        </LinearLayout>
                        <ImageView
                            android:layout_marginTop="@dimen/dp_10"
                            android:scaleType="fitXY"
                            android:src="@drawable/icon_detail_stake_line"
                            android:layout_width="match_parent"
                            android:layout_height="@dimen/dp_120"/>
                    </LinearLayout>

                </LinearLayout>

                <LinearLayout
                    android:layout_marginTop="@dimen/dp_20"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">
                    <ImageView
                        android:src="@drawable/icon_stake_secure_chain"
                        android:layout_width="@dimen/dp_24"
                        android:layout_height="@dimen/dp_24"/>
                    <LinearLayout
                        android:layout_marginStart="@dimen/dp_12"
                        android:orientation="vertical"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">
                        <TextView
                            android:textColor="@color/color_title"
                            android:textSize="16sp"
                            android:text="@string/secure_chain"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"/>
                        <TextView
                            android:text="@string/by_staking_token_holders_enhance"
                            android:textSize="16sp"
                            android:textColor="@color/color_title_sub"
                            android:layout_marginTop="@dimen/dp_8"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"/>
                    </LinearLayout>

                </LinearLayout>
                <LinearLayout
                    android:layout_marginTop="@dimen/dp_20"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">
                    <ImageView
                        android:src="@drawable/icon_stake_earn"
                        android:layout_width="@dimen/dp_24"
                        android:layout_height="@dimen/dp_24"/>
                    <LinearLayout
                        android:layout_marginStart="@dimen/dp_12"
                        android:orientation="vertical"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">
                        <TextView
                            android:textColor="@color/color_title"
                            android:textSize="16sp"
                            android:text="@string/earn_rewards"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"/>
                        <TextView
                            android:text="@string/with_lotex_the_staking"
                            android:textSize="16sp"
                            android:textColor="@color/color_title_sub"
                            android:layout_marginTop="@dimen/dp_8"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"/>
                    </LinearLayout>

                </LinearLayout>
                <LinearLayout
                    android:layout_marginTop="@dimen/dp_20"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">
                    <ImageView
                        android:src="@drawable/icon_stake_vote"
                        android:layout_width="@dimen/dp_24"
                        android:layout_height="@dimen/dp_24"/>
                    <LinearLayout
                        android:layout_marginStart="@dimen/dp_12"
                        android:orientation="vertical"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">
                        <TextView
                            android:textColor="@color/color_title"
                            android:textSize="16sp"
                            android:text="@string/vote"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"/>
                        <TextView
                            android:text="@string/any_token_holder_that_stakes"
                            android:textSize="16sp"
                            android:textColor="@color/color_title_sub"
                            android:layout_marginTop="@dimen/dp_8"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"/>
                    </LinearLayout>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp_15"
                    android:background="@drawable/shape_card_back"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="@dimen/dp_40"
                        android:layout_marginStart="@dimen/dp_12"
                        android:gravity="center_vertical">

                        <ImageView
                            android:layout_width="@dimen/dp_20"
                            android:layout_height="@dimen/dp_20"
                            android:layout_margin="@dimen/dp_5"
                            android:src="@drawable/icon_warn_net_work" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/note_dot"
                            android:textColor="@color/color_title"
                            android:textSize="14sp" />
                    </LinearLayout>

                    <View
                        android:layout_width="wrap_content"
                        android:layout_height="@dimen/dp_1"
                        android:layout_marginStart="@dimen/dp_12"
                        android:layout_marginEnd="@dimen/dp_12"
                        android:background="@color/color_line_diver" />

                    <TextView
                        android:id="@+id/tvTips01"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp_12"
                        android:layout_marginStart="@dimen/dp_12"
                        android:layout_marginEnd="@dimen/dp_12"
                        android:layout_marginBottom="@dimen/dp_8"
                        android:text="@string/past_performance_does_not_guarantee"
                        android:textColor="@color/color_title_sub"
                        android:textSize="12sp" />

                    <TextView
                        android:id="@+id/tvTips02"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp_6"
                        android:layout_marginStart="@dimen/dp_12"
                        android:layout_marginEnd="@dimen/dp_12"
                        android:layout_marginBottom="@dimen/dp_16"
                        android:text="@string/the_estimated_apy_is_partially_derived"
                        android:textColor="@color/color_title_sub"
                        android:textSize="12sp" />
                </LinearLayout>
            </LinearLayout>
        </ScrollView>

        <TextView
            android:layout_marginBottom="@dimen/dp_20"
            android:layout_marginTop="@dimen/dp_20"
            android:id="@+id/tvStakeStart"
            android:text="@string/start_earning"
            android:gravity="center"
            android:textSize="17sp"
            android:textColor="@color/white"
            android:layout_marginStart="@dimen/dp_8"
            android:background="@drawable/btn_shape_gradient_common"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_44"/>
    </LinearLayout>
</layout>