<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <LinearLayout
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <LinearLayout
            android:layout_marginTop="@dimen/dp_10"
            android:layout_marginStart="@dimen/dp_11"
            android:layout_marginEnd="@dimen/dp_16"
            android:gravity="center_vertical"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <ImageView
                android:id="@+id/ivBack"
                android:src="@drawable/icon_back"
                android:padding="@dimen/dp_5"
                android:layout_width="@dimen/dp_34"
                android:layout_height="@dimen/dp_34"/>
            <View
                android:layout_weight="1"
                android:layout_width="0dp"
                android:layout_height="@dimen/dp_1"/>
            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/ivScan"
                android:tint="@color/color_title"
                android:src="@drawable/icon_scan_white"
                android:layout_width="@dimen/dp_24"
                android:layout_height="@dimen/dp_24"/>
            <FrameLayout
                android:layout_marginStart="@dimen/dp_12"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content">
                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/ivNotice"
                    android:tint="@color/color_title"
                    android:src="@drawable/icon_setting_notice"
                    android:layout_width="@dimen/dp_24"
                    android:layout_height="@dimen/dp_24"/>
                <View
                    android:id="@+id/viewNoticeRed"
                    android:layout_gravity="end"
                    android:visibility="gone"
                    tools:visibility="visible"
                    android:background="@drawable/bg_circle_red"
                    android:layout_width="@dimen/dp_6"
                    android:layout_height="@dimen/dp_6"/>
            </FrameLayout>
        </LinearLayout>
        <LinearLayout
            android:id="@+id/llBack"
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="match_parent">
            <LinearLayout
                android:id="@+id/llWallet"
                android:gravity="center_vertical"
                android:paddingStart="@dimen/dp_16"
                android:paddingEnd="@dimen/dp_16"
                android:layout_marginTop="@dimen/dp_10"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <com.makeramen.roundedimageview.RoundedImageView
                    app:riv_corner_radius="@dimen/dp_17"
                    android:id="@+id/ivWalletIcon"
                    android:src="@drawable/icon_wallet_default"
                    android:layout_width="@dimen/dp_34"
                    android:layout_height="@dimen/dp_34"/>
                <LinearLayout
                    android:layout_weight="1"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content">
                    <TextView
                        android:id="@+id/tvWalletName"
                        android:textSize="14sp"
                        android:ellipsize="end"
                        android:maxLines="1"
                        android:maxWidth="@dimen/dp_100"
                        android:layout_marginStart="@dimen/dp_8"
                        android:textColor="@color/color_title"
                        tools:text="钱包 120钱包 120钱包 120钱包 120钱包 120钱包 120钱包 120"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"/>
                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/ivEdit"
                        android:layout_marginStart="@dimen/dp_5"
                        android:src="@drawable/ic_edit"
                        android:layout_width="@dimen/dp_28"
                        android:layout_height="@dimen/dp_18"/>
                </LinearLayout>
                <LinearLayout
                    android:id="@+id/llAddWallet"
                    android:background="@drawable/shape_gradient_617aff_855eff_r12"
                    android:paddingStart="@dimen/dp_8"
                    android:paddingEnd="@dimen/dp_8"
                    android:gravity="center"
                    android:layout_marginStart="@dimen/dp_15"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/dp_24">

                    <androidx.appcompat.widget.AppCompatImageView
                        android:src="@drawable/icon_right_add"
                        android:layout_width="@dimen/dp_10"
                        android:layout_height="@dimen/dp_10"/>

                    <TextView
                        android:layout_marginStart="@dimen/dp_5"
                        android:text="@string/add_a_wallet"
                        android:textSize="12sp"
                        android:textColor="@color/white"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"/>

                </LinearLayout>
            </LinearLayout>

            <include android:id="@+id/addLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                tools:visibility="gone"
                layout="@layout/view_visitor_setting_add_wallet"/>
            <View
                android:layout_marginTop="@dimen/dp_16"
                android:background="@color/color_line_diver"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp_1"/>

            <androidx.core.widget.NestedScrollView
                android:layout_weight="1"
                android:layout_width="match_parent"
                android:layout_height="0dp">
                <LinearLayout
                    android:orientation="vertical"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">
                    <LinearLayout
                        android:id="@+id/llBasic"
                        android:orientation="vertical"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">
                        <TextView
                            android:layout_marginTop="@dimen/dp_20"
                            android:layout_marginStart="@dimen/dp_16"
                            android:textColor="@color/color_title_sub"
                            android:textSize="14sp"
                            android:text="@string/basic_functions"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"/>
                        <LinearLayout
                            android:id="@+id/llReceive"
                            android:gravity="center_vertical"
                            android:layout_marginTop="@dimen/dp_10"
                            android:paddingStart="@dimen/dp_16"
                            android:paddingEnd="@dimen/dp_16"
                            android:paddingTop="@dimen/dp_10"
                            android:paddingBottom="@dimen/dp_10"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content">
                            <androidx.appcompat.widget.AppCompatImageView
                                android:tint="@color/color_title"
                                android:src="@drawable/icon_setting_receive"
                                android:layout_width="@dimen/dp_24"
                                android:layout_height="@dimen/dp_24"/>
                            <TextView
                                android:textSize="14sp"
                                android:layout_marginStart="@dimen/dp_8"
                                android:text="@string/receive"
                                android:textColor="@color/color_title"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"/>
                            <View
                                android:layout_weight="1"
                                android:layout_width="0dp"
                                android:layout_height="@dimen/dp_1"/>
                            <androidx.appcompat.widget.AppCompatImageView
                                android:tint="@color/color_title"
                                android:src="@drawable/icon_setting_arrow_right"
                                android:layout_width="@dimen/dp_14"
                                android:layout_height="@dimen/dp_14"/>

                        </LinearLayout>
                        <LinearLayout
                            android:id="@+id/llBackUp"
                            android:gravity="center_vertical"
                            android:paddingStart="@dimen/dp_16"
                            android:paddingEnd="@dimen/dp_16"
                            android:paddingTop="@dimen/dp_10"
                            android:paddingBottom="@dimen/dp_10"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content">
                            <androidx.appcompat.widget.AppCompatImageView
                                android:tint="@color/color_title"
                                android:src="@drawable/icon_setting_private_key"
                                android:layout_width="@dimen/dp_24"
                                android:layout_height="@dimen/dp_24"/>
                            <TextView
                                android:textSize="14sp"
                                android:layout_marginStart="@dimen/dp_8"
                                android:text="@string/back_up"
                                android:textColor="@color/color_title"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"/>
                            <View
                                android:layout_weight="1"
                                android:layout_width="0dp"
                                android:layout_height="@dimen/dp_1"/>
                            <androidx.appcompat.widget.AppCompatImageView
                                android:tint="@color/color_title"
                                android:src="@drawable/icon_setting_arrow_right"
                                android:layout_width="@dimen/dp_14"
                                android:layout_height="@dimen/dp_14"/>

                        </LinearLayout>
                        <LinearLayout
                            android:id="@+id/llAddressBook"
                            android:gravity="center_vertical"
                            android:paddingStart="@dimen/dp_16"
                            android:paddingEnd="@dimen/dp_16"
                            android:paddingTop="@dimen/dp_10"
                            android:paddingBottom="@dimen/dp_10"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content">
                            <androidx.appcompat.widget.AppCompatImageView
                                android:tint="@color/color_title"
                                android:src="@drawable/icon_setting_address_book"
                                android:layout_width="@dimen/dp_24"
                                android:layout_height="@dimen/dp_24"/>
                            <TextView
                                android:textSize="14sp"
                                android:layout_marginStart="@dimen/dp_8"
                                android:text="@string/address_book"
                                android:textColor="@color/color_title"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"/>
                            <View
                                android:layout_weight="1"
                                android:layout_width="0dp"
                                android:layout_height="@dimen/dp_1"/>
                            <androidx.appcompat.widget.AppCompatImageView
                                android:tint="@color/color_title"
                                android:src="@drawable/icon_setting_arrow_right"
                                android:layout_width="@dimen/dp_14"
                                android:layout_height="@dimen/dp_14"/>

                        </LinearLayout>
                        <View
                            android:layout_marginTop="@dimen/dp_10"
                            android:background="@color/color_line_diver"
                            android:layout_width="match_parent"
                            android:layout_height="@dimen/dp_1"/>
                    </LinearLayout>

                    <TextView
                        android:layout_marginTop="@dimen/dp_20"
                        android:layout_marginBottom="@dimen/dp_12"
                        android:layout_marginStart="@dimen/dp_16"
                        android:textColor="@color/color_title_sub"
                        android:textSize="14sp"
                        android:text="@string/iopay_settings"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"/>
                    <LinearLayout
                        android:id="@+id/llNetwork"
                        android:gravity="center_vertical"
                        android:paddingStart="@dimen/dp_16"
                        android:paddingEnd="@dimen/dp_16"
                        android:paddingTop="@dimen/dp_10"
                        android:paddingBottom="@dimen/dp_10"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">
                        <androidx.appcompat.widget.AppCompatImageView
                            android:tint="@color/color_title"
                            android:src="@drawable/icon_setting_network"
                            android:layout_width="@dimen/dp_24"
                            android:layout_height="@dimen/dp_24"/>
                        <TextView
                            android:textSize="14sp"
                            android:layout_marginStart="@dimen/dp_8"
                            android:text="@string/networks"
                            android:textColor="@color/color_title"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"/>
                        <View
                            android:layout_weight="1"
                            android:layout_width="0dp"
                            android:layout_height="@dimen/dp_1"/>
                        <androidx.appcompat.widget.AppCompatImageView
                            android:tint="@color/color_title"
                            android:src="@drawable/icon_setting_arrow_right"
                            android:layout_width="@dimen/dp_14"
                            android:layout_height="@dimen/dp_14"/>

                    </LinearLayout>
                    <LinearLayout
                        android:id="@+id/llGeneral"
                        android:gravity="center_vertical"
                        android:paddingStart="@dimen/dp_16"
                        android:paddingEnd="@dimen/dp_16"
                        android:paddingTop="@dimen/dp_10"
                        android:paddingBottom="@dimen/dp_10"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">
                        <androidx.appcompat.widget.AppCompatImageView
                            android:src="@drawable/icon_setting_general"
                            android:tint="@color/color_title"
                            android:layout_width="@dimen/dp_24"
                            android:layout_height="@dimen/dp_24"/>
                        <TextView
                            android:textSize="14sp"
                            android:layout_marginStart="@dimen/dp_8"
                            android:text="@string/general"
                            android:textColor="@color/color_title"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"/>
                        <View
                            android:layout_weight="1"
                            android:layout_width="0dp"
                            android:layout_height="@dimen/dp_1"/>
                        <androidx.appcompat.widget.AppCompatImageView
                            android:src="@drawable/icon_setting_arrow_right"
                            android:tint="@color/color_title"
                            android:layout_width="@dimen/dp_14"
                            android:layout_height="@dimen/dp_14"/>

                    </LinearLayout>
                    <LinearLayout
                        android:id="@+id/llManageWallet"
                        android:gravity="center_vertical"
                        android:paddingStart="@dimen/dp_16"
                        android:paddingEnd="@dimen/dp_16"
                        android:paddingTop="@dimen/dp_10"
                        android:paddingBottom="@dimen/dp_10"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">
                        <androidx.appcompat.widget.AppCompatImageView
                            android:tint="@color/color_title"
                            android:src="@drawable/icon_setting_manege_wallet"
                            android:layout_width="@dimen/dp_24"
                            android:layout_height="@dimen/dp_24"/>
                        <TextView
                            android:textSize="14sp"
                            android:layout_marginStart="@dimen/dp_8"
                            android:text="@string/manage_wallet"
                            android:textColor="@color/color_title"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"/>
                        <View
                            android:layout_weight="1"
                            android:layout_width="0dp"
                            android:layout_height="@dimen/dp_1"/>
                        <androidx.appcompat.widget.AppCompatImageView
                            android:tint="@color/color_title"
                            android:src="@drawable/icon_setting_arrow_right"
                            android:layout_width="@dimen/dp_14"
                            android:layout_height="@dimen/dp_14"/>

                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/llGiftCode"
                        android:visibility="gone"
                        android:gravity="center_vertical"
                        android:paddingStart="@dimen/dp_16"
                        android:paddingEnd="@dimen/dp_16"
                        android:paddingTop="@dimen/dp_10"
                        android:paddingBottom="@dimen/dp_10"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">
                        <androidx.appcompat.widget.AppCompatImageView
                            android:tint="@color/color_title"
                            android:src="@drawable/icon_invite_friend"
                            android:layout_width="@dimen/dp_24"
                            android:layout_height="@dimen/dp_24"/>
                        <TextView
                            android:textSize="14sp"
                            android:layout_marginStart="@dimen/dp_8"
                            android:text="@string/your_referral_code"
                            android:textColor="@color/color_title"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"/>
                        <View
                            android:layout_weight="1"
                            android:layout_width="0dp"
                            android:layout_height="@dimen/dp_1"/>
                        <androidx.appcompat.widget.AppCompatImageView
                            android:tint="@color/color_title"
                            android:src="@drawable/icon_setting_arrow_right"
                            android:layout_width="@dimen/dp_14"
                            android:layout_height="@dimen/dp_14"/>

                    </LinearLayout>
                    <LinearLayout
                        android:id="@+id/llPrivate"
                        android:gravity="center_vertical"
                        android:paddingStart="@dimen/dp_16"
                        android:paddingEnd="@dimen/dp_16"
                        android:paddingTop="@dimen/dp_10"
                        android:paddingBottom="@dimen/dp_10"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">
                        <androidx.appcompat.widget.AppCompatImageView
                            android:tint="@color/color_title"
                            android:src="@drawable/icon_setting_private"
                            android:layout_width="@dimen/dp_24"
                            android:layout_height="@dimen/dp_24"/>
                        <TextView
                            android:textSize="14sp"
                            android:layout_marginStart="@dimen/dp_8"
                            android:text="@string/security_privacy"
                            android:textColor="@color/color_title"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"/>
                        <View
                            android:layout_width="0dp"
                            android:layout_height="@dimen/dp_1"
                            android:layout_weight="1" />

                        <androidx.appcompat.widget.AppCompatImageView
                            android:layout_width="@dimen/dp_14"
                            android:layout_height="@dimen/dp_14"
                            android:src="@drawable/icon_setting_arrow_right"
                            android:tint="@color/color_title" />

                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/llUserFeedBack"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:paddingStart="@dimen/dp_16"
                        android:paddingTop="@dimen/dp_10"
                        android:paddingEnd="@dimen/dp_16"
                        android:paddingBottom="@dimen/dp_10">

                        <androidx.appcompat.widget.AppCompatImageView
                            android:layout_width="@dimen/dp_24"
                            android:layout_height="@dimen/dp_24"
                            android:src="@drawable/icon_feed_back"
                            android:tint="@color/color_title" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp_8"
                            android:text="@string/user_feedback"
                            android:textColor="@color/color_title"
                            android:textSize="14sp" />

                        <View
                            android:layout_weight="1"
                            android:layout_width="0dp"
                            android:layout_height="@dimen/dp_1"/>
                        <androidx.appcompat.widget.AppCompatImageView
                            android:tint="@color/color_title"
                            android:src="@drawable/icon_setting_arrow_right"
                            android:layout_width="@dimen/dp_14"
                            android:layout_height="@dimen/dp_14"/>

                    </LinearLayout>
                </LinearLayout>
            </androidx.core.widget.NestedScrollView>
            <View
                android:background="@color/color_line_diver"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp_1"/>
            <LinearLayout
                android:id="@+id/llAbout"
                android:gravity="center_vertical"
                android:padding="@dimen/dp_16"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <androidx.appcompat.widget.AppCompatImageView
                    android:tint="@color/color_title"
                    android:src="@drawable/icon_setting_about"
                    android:layout_width="@dimen/dp_24"
                    android:layout_height="@dimen/dp_24"/>
                <TextView
                    android:textSize="14sp"
                    android:layout_marginStart="@dimen/dp_8"
                    android:text="@string/about_ioPay"
                    android:textColor="@color/color_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"/>
                <TextView
                    android:id="@+id/tvVersion"
                    android:layout_weight="1"
                    android:layout_width="0dp"
                    android:alpha="0.5"
                    android:layout_marginStart="@dimen/dp_5"
                    android:textColor="@color/color_title_sub"
                    android:textSize="12sp"
                    android:layout_height="wrap_content"/>
                <ImageView
                    android:id="@+id/ivUpdateTag"
                    android:src="@drawable/icon_aa_new"
                    android:visibility="gone"
                    tools:visibility="visible"
                    android:layout_marginEnd="@dimen/dp_10"
                    android:layout_width="@dimen/dp_32"
                    android:layout_height="@dimen/dp_13"/>
                <androidx.appcompat.widget.AppCompatImageView
                    android:tint="@color/color_title"
                    android:src="@drawable/icon_setting_arrow_right"
                    android:layout_width="@dimen/dp_14"
                    android:layout_height="@dimen/dp_14"/>

            </LinearLayout>
        </LinearLayout>


    </LinearLayout>

</layout>