<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:id="@+id/llAllNetwork"
            android:visibility="gone"
            tools:visibility="visible"
            android:orientation="vertical"
            android:layout_marginStart="@dimen/dp_15"
            android:layout_marginEnd="@dimen/dp_15"
            android:layout_marginBottom="@dimen/dp_15"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_15"
                android:layout_marginBottom="@dimen/dp_12"
                android:text="@string/current_network"
                android:textColor="@color/color_title"
                android:textSize="16sp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:background="@drawable/shape_card_back_stroke_gradient"
                android:paddingStart="@dimen/dp_15"
                android:paddingTop="@dimen/dp_18"
                android:paddingEnd="@dimen/dp_20"
                android:paddingBottom="@dimen/font_size_small_2">

                <ImageView
                    android:layout_width="@dimen/dp_28"
                    android:layout_height="@dimen/dp_28"
                    android:src="@drawable/icon_all_network" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp_10"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/dp_10"
                        android:textColor="@color/color_title"
                        android:textSize="@dimen/font_size_common"
                        android:text="@string/all_networks" />

                </LinearLayout>
            </LinearLayout>
        </LinearLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rvNetwork"
            android:overScrollMode="never"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_above="@+id/tvDevelop"
            android:layout_below="@+id/llAllNetwork"
            android:layout_marginStart="@dimen/dp_15"
            android:layout_marginEnd="@dimen/dp_15"
            android:layout_marginBottom="@dimen/dp_15"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            tools:itemCount="2"
            tools:listitem="@layout/item_network_list" />

        <TextView
            android:id="@+id/tvDevelop"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_centerHorizontal="true"
            android:layout_marginBottom="@dimen/dp_50"
            android:text="@string/developer_mode_test"
            android:textColor="@color/colorAccent"
            android:textSize="@dimen/font_size_common"
            android:visibility="gone" />
    </RelativeLayout>
</layout>
