<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools" >
    <LinearLayout
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="?actionBarSize">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/ivBack"
                android:tint="@color/color_title"
                android:src="@drawable/ic_back"
                android:padding="@dimen/dp_5"
                android:layout_marginStart="@dimen/dp_10"
                android:layout_centerVertical="true"
                android:layout_width="@dimen/dp_34"
                android:layout_height="@dimen/dp_34"/>
            <TextView
                android:id="@+id/tvToolbarTitle"
                android:gravity="center"
                android:textColor="@color/color_title"
                android:textSize="17sp"
                tools:text="Title"
                android:maxLines="1"
                android:layout_centerInParent="true"
                android:ellipsize="middle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>

            <LinearLayout
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:layout_marginEnd="@dimen/dp_16"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/ivSetting"
                    android:tint="@color/color_title"
                    android:layout_marginStart="@dimen/dp_8"
                    android:src="@drawable/icon_wallet_setting"
                    android:layout_width="@dimen/dp_24"
                    android:layout_height="@dimen/dp_24"/>
            </LinearLayout>
        </RelativeLayout>

        <LinearLayout
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="match_parent">
            <androidx.core.widget.NestedScrollView
                android:layout_weight="1"
                android:layout_width="match_parent"
                android:layout_height="0dp">
                <LinearLayout
                    android:orientation="vertical"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">
                    <LinearLayout
                        android:id="@+id/llIoWarning"
                        android:visibility="gone"
                        android:layout_marginStart="@dimen/dp_16"
                        android:layout_marginEnd="@dimen/dp_16"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content">
                        <ImageView
                            android:src="@drawable/ic_warning"
                            android:layout_width="@dimen/dp_20"
                            android:layout_height="@dimen/dp_20"/>
                        <TextView
                            android:textSize="14sp"
                            android:layout_marginStart="@dimen/dp_4"
                            android:text="@string/ucam_login_currently_only_supports_iotex"
                            android:textColor="@color/color_title_sub"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"/>
                    </LinearLayout>

                    <LinearLayout
                        android:layout_marginTop="@dimen/dp_12"
                        android:layout_marginStart="@dimen/dp_16"
                        android:layout_marginEnd="@dimen/dp_16"
                        android:gravity="center_vertical"
                        android:background="@drawable/shape_card_back_r20"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dp_40">

                        <androidx.appcompat.widget.AppCompatImageView
                            android:tint="@color/color_title"
                            android:src="@drawable/ic_search"
                            android:layout_marginStart="@dimen/dp_15"
                            android:layout_marginEnd="@dimen/dp_15"
                            android:layout_width="@dimen/dp_20"
                            android:layout_height="@dimen/dp_20"/>
                        <EditText
                            android:id="@+id/etSearch"
                            android:background="@color/transparent"
                            android:textColor="@color/color_title"
                            android:textSize="14sp"
                            android:hint="@string/search_for_wallet_name_or_address"
                            android:textColorHint="@color/color_hint"
                            android:layout_weight="1"
                            android:layout_width="0dp"
                            android:layout_height="match_parent"/>

                        <ImageView
                            android:id="@+id/ivDelete"
                            android:padding="@dimen/dp_5"
                            android:layout_width="@dimen/dp_36"
                            android:layout_height="@dimen/dp_36"
                            android:visibility="invisible"
                            android:src="@drawable/btn_delete" />

                    </LinearLayout>
                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/recyclerView"
                        android:paddingTop="@dimen/dp_12"
                        android:clipToPadding="false"
                        android:clipChildren="false"
                        android:overScrollMode="never"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"/>
                </LinearLayout>
            </androidx.core.widget.NestedScrollView>
            <LinearLayout
                android:id="@+id/llNewWallet"
                android:background="@color/theme_window_back"
                android:padding="@dimen/dp_16"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <TextView
                    android:id="@+id/tvAddWallet"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp_44"
                    android:background="@drawable/btn_shape_gradient_common"
                    android:gravity="center"
                    android:text="@string/wallet_new_button"
                    android:textColor="@color/white"
                    android:textSize="14sp" />
            </LinearLayout>
        </LinearLayout>

    </LinearLayout>

</layout>