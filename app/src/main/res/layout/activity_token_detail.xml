<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <LinearLayout
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="?actionBarSize">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/ivBack"
                    android:tint="@color/color_title"
                    android:src="@drawable/ic_back"
                    android:padding="@dimen/dp_5"
                    android:layout_marginStart="@dimen/dp_10"
                    android:layout_centerVertical="true"
                    android:layout_width="@dimen/dp_34"
                    android:layout_height="@dimen/dp_34"/>

                <LinearLayout
                    android:gravity="center"
                    android:layout_centerInParent="true"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content">
                    <RelativeLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content">

                        <com.makeramen.roundedimageview.RoundedImageView
                            app:riv_corner_radius="@dimen/dp_10"
                            android:id="@+id/mIvLogo"
                            android:layout_width="@dimen/dp_20"
                            android:layout_height="@dimen/dp_20"
                            android:scaleType="centerCrop"
                            android:src="@drawable/icon_token_default" />

                        <io.iotex.iopay.dapp.widget.DAppChainTagView
                            android:id="@+id/tags"
                            android:visibility="gone"
                            android:layout_marginStart="@dimen/dp_30"
                            android:layout_alignStart="@+id/mIvLogo"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"/>
                    </RelativeLayout>

                    <LinearLayout
                        android:gravity="center"
                        android:orientation="vertical"
                        android:layout_marginStart="@dimen/dp_4"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content">
                        <TextView
                            android:id="@+id/mTvSymbol"
                            android:gravity="center"
                            android:textColor="@color/color_title"
                            android:textSize="16sp"
                            tools:text="Title"
                            android:maxLines="1"
                            android:ellipsize="middle"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"/>
                        <TextView
                            android:id="@+id/tvName"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/color_title_sub"
                            android:textSize="10sp"
                            tools:text="ETH" />
                    </LinearLayout>
                </LinearLayout>

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/ivDex"
                    android:layout_toStartOf="@+id/ivWebsite"
                    android:layout_centerVertical="true"
                    android:tint="@color/color_title"
                    android:layout_gravity="center"
                    android:layout_width="@dimen/dp_24"
                    android:layout_height="@dimen/dp_24"
                    android:src="@drawable/icon_dex_scan" />

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/ivWebsite"
                    android:layout_alignParentEnd="true"
                    android:layout_marginEnd="@dimen/dp_16"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="@dimen/dp_8"
                    android:src="@drawable/icon_action_explorer"
                    android:layout_width="@dimen/dp_24"
                    android:layout_height="@dimen/dp_24"/>
            </RelativeLayout>

            <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
                android:id="@+id/mSrfRefresh"
                android:layout_width="match_parent"
                android:layout_height="match_parent">
                <LinearLayout
                    android:orientation="vertical"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">
                    <androidx.core.widget.NestedScrollView
                        android:fillViewport="true"
                        android:overScrollMode="never"
                        android:layout_weight="1"
                        android:layout_width="match_parent"
                        android:layout_height="0dp">
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:gravity="center_horizontal"
                            android:orientation="vertical">

                            <FrameLayout
                                android:layout_marginStart="@dimen/dp_16"
                                android:layout_marginEnd="@dimen/dp_16"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content">

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:orientation="vertical"
                                    android:paddingBottom="@dimen/dp_16">
                                    <com.robinhood.ticker.TickerView
                                        android:id="@+id/mTvBalance"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:textColor="@color/color_title"
                                        android:textSize="40sp"
                                        tools:text="100" />

                                    <io.iotex.iopay.widget.DINTextView
                                        android:id="@+id/tvPrice"
                                        tools:text="$ 707.6"
                                        android:textSize="15sp"
                                        android:textColor="@color/color_title"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"/>

                                </LinearLayout>
                            </FrameLayout>

                            <FrameLayout
                                android:id="@+id/flStock"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content">
                                <io.iotex.iopay.token.view.StockShadeView
                                    android:id="@+id/stockShadeView"
                                    android:layout_marginStart="@dimen/dp_16"
                                    android:layout_marginEnd="@dimen/dp_16"
                                    android:layout_width="match_parent"
                                    android:layout_height="@dimen/dp_230"/>
                                <LinearLayout
                                    android:id="@+id/llEmpty"
                                    android:orientation="vertical"
                                    android:gravity="center"
                                    android:visibility="gone"
                                    android:background="@color/theme_window_back"
                                    android:layout_width="match_parent"
                                    android:layout_height="@dimen/dp_200">
                                    <ImageView
                                        android:src="@drawable/icon_token_stock_empty"
                                        android:layout_width="@dimen/dp_50"
                                        android:layout_height="@dimen/dp_50"/>
                                    <TextView
                                        android:textSize="14sp"
                                        android:layout_marginTop="@dimen/dp_16"
                                        android:textColor="@color/color_title_sub"
                                        android:text="@string/no_history"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"/>
                                </LinearLayout>
                                <LinearLayout
                                    android:id="@+id/llError"
                                    android:orientation="vertical"
                                    android:gravity="center"
                                    android:visibility="gone"
                                    android:background="@color/theme_window_back"
                                    android:layout_width="match_parent"
                                    android:layout_height="@dimen/dp_200">
                                    <ImageView
                                        android:src="@drawable/icon_token_stock_error"
                                        android:layout_width="@dimen/dp_50"
                                        android:layout_height="@dimen/dp_50"/>
                                    <TextView
                                        android:textSize="14sp"
                                        android:layout_marginTop="@dimen/dp_16"
                                        android:textColor="@color/color_title_sub"
                                        android:text="@string/network_error_please_try_again"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"/>
                                </LinearLayout>
                                <androidx.recyclerview.widget.RecyclerView
                                    android:id="@+id/recyclerViewStock"
                                    android:orientation="horizontal"
                                    android:layout_gravity="bottom"
                                    tools:itemCount="4"
                                    tools:listitem="@layout/item_stock_time"
                                    tools:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"/>
                            </FrameLayout>

                            <LinearLayout
                                android:id="@+id/llAction"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="@dimen/dp_20"
                                android:layout_marginBottom="@dimen/dp_20"
                                android:orientation="horizontal"
                                android:paddingStart="@dimen/dp_15"
                                android:paddingEnd="@dimen/dp_15">

                                <LinearLayout
                                    android:layout_weight="1"
                                    android:layout_width="0dp"
                                    android:gravity="center"
                                    android:layout_height="wrap_content">
                                    <LinearLayout
                                        android:id="@+id/mLlSend"
                                        android:orientation="vertical"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:gravity="center">
                                        <FrameLayout
                                            android:background="@drawable/shape_circle_home_card_back"
                                            android:layout_width="@dimen/dp_42"
                                            android:layout_height="@dimen/dp_42">
                                            <ImageView
                                                android:layout_width="@dimen/dp_18"
                                                android:layout_height="@dimen/dp_18"
                                                android:layout_gravity="center"
                                                android:src="@drawable/icon_card_send" />
                                        </FrameLayout>

                                        <TextView
                                            android:id="@+id/mTvSend"
                                            android:layout_marginTop="@dimen/dp_6"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:gravity="center"
                                            android:text="@string/xrc_send"
                                            android:textColor="@color/color_title"
                                            android:textSize="14sp" />

                                    </LinearLayout>
                                </LinearLayout>

                                <LinearLayout
                                    android:layout_weight="1"
                                    android:layout_width="0dp"
                                    android:gravity="center"
                                    android:layout_height="wrap_content">
                                    <LinearLayout
                                        android:id="@+id/mLlReceive"
                                        android:orientation="vertical"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:gravity="center">

                                        <FrameLayout
                                            android:background="@drawable/shape_circle_home_card_back"
                                            android:layout_width="@dimen/dp_42"
                                            android:layout_height="@dimen/dp_42">
                                            <ImageView
                                                android:layout_width="@dimen/dp_18"
                                                android:layout_height="@dimen/dp_18"
                                                android:layout_gravity="center"
                                                android:src="@drawable/icon_card_receive" />
                                        </FrameLayout>

                                        <TextView
                                            android:id="@+id/mTvReceive"
                                            android:layout_marginTop="@dimen/dp_6"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:gravity="center"
                                            android:text="@string/xrc_receive"
                                            android:textColor="@color/color_title"
                                            android:textSize="14sp" />

                                    </LinearLayout>
                                </LinearLayout>

                                <LinearLayout
                                    android:id="@+id/llSwapLayout"
                                    android:layout_weight="1"
                                    android:layout_width="0dp"
                                    android:gravity="center"
                                    android:layout_height="wrap_content">
                                    <LinearLayout
                                        android:id="@+id/mLlSwap"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:gravity="center"
                                        android:orientation="vertical">

                                        <FrameLayout
                                            android:background="@drawable/shape_circle_home_card_back"
                                            android:layout_width="@dimen/dp_42"
                                            android:layout_height="@dimen/dp_42">
                                            <ImageView
                                                android:layout_width="@dimen/dp_18"
                                                android:layout_height="@dimen/dp_18"
                                                android:layout_gravity="center"
                                                android:src="@drawable/icon_card_swap" />
                                        </FrameLayout>
                                        <TextView
                                            android:id="@+id/mTvSwap"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:layout_marginTop="@dimen/dp_6"
                                            android:gravity="center"
                                            android:text="@string/xrc_swap"
                                            android:textColor="@color/color_title" />

                                    </LinearLayout>
                                </LinearLayout>

                                <LinearLayout
                                    android:id="@+id/llLaunchLayout"
                                    android:layout_weight="1"
                                    android:layout_width="0dp"
                                    android:gravity="center"
                                    android:visibility="gone"
                                    tools:visibility="visible"
                                    android:layout_height="wrap_content">
                                    <LinearLayout
                                        android:id="@+id/mLlLaunch"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:gravity="center"
                                        android:orientation="vertical">

                                        <FrameLayout
                                            android:background="@drawable/shape_circle_home_card_back"
                                            android:layout_width="@dimen/dp_42"
                                            android:layout_height="@dimen/dp_42">
                                            <ImageView
                                                android:layout_width="@dimen/dp_18"
                                                android:layout_height="@dimen/dp_18"
                                                android:layout_gravity="center"
                                                android:src="@drawable/icon_card_launch" />
                                        </FrameLayout>

                                        <TextView
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:layout_marginTop="@dimen/dp_6"
                                            android:gravity="center"
                                            android:text="@string/launch_app"
                                            android:textColor="@color/color_title" />

                                    </LinearLayout>
                                </LinearLayout>

                            </LinearLayout>

                            <LinearLayout
                                android:id="@+id/llBino"
                                android:visibility="gone"
                                tools:visibility="visible"
                                android:orientation="vertical"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content">
                                <LinearLayout
                                    android:layout_marginTop="@dimen/dp_12"
                                    android:paddingStart="@dimen/dp_16"
                                    android:paddingEnd="@dimen/dp_16"
                                    android:gravity="center_vertical"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content">
                                    <ImageView
                                        android:src="@drawable/icon_tab_bino_select"
                                        android:layout_width="@dimen/dp_24"
                                        android:layout_height="@dimen/dp_24"/>
                                    <TextView
                                        android:layout_marginStart="@dimen/dp_4"
                                        android:text="@string/iopay_binoai"
                                        android:textSize="16sp"
                                        android:textColor="@color/color_title"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"/>

                                </LinearLayout>

                                <LinearLayout
                                    android:gravity="center"
                                    android:layout_marginTop="@dimen/dp_12"
                                    android:paddingStart="@dimen/dp_16"
                                    android:paddingEnd="@dimen/dp_16"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content">
                                    <androidx.appcompat.widget.AppCompatImageView
                                        android:id="@+id/ivLeft"
                                        android:tint="@color/color_title"
                                        android:src="@drawable/ic_arrow_left_white"
                                        android:layout_width="@dimen/dp_20"
                                        android:layout_height="@dimen/dp_20"/>
                                    <androidx.recyclerview.widget.RecyclerView
                                        android:id="@+id/recyclerViewBino"
                                        android:orientation="horizontal"
                                        android:layout_marginStart="@dimen/dp_5"
                                        android:layout_marginEnd="@dimen/dp_5"
                                        tools:listitem="@layout/item_bino_question"
                                        tools:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                                        android:layout_weight="1"
                                        android:layout_width="0dp"
                                        android:layout_height="wrap_content"/>
                                    <androidx.appcompat.widget.AppCompatImageView
                                        android:id="@+id/ivRight"
                                        android:tint="@color/color_title"
                                        android:src="@drawable/ic_arrow_right_white"
                                        android:layout_width="@dimen/dp_20"
                                        android:layout_height="@dimen/dp_20"/>
                                </LinearLayout>
                            </LinearLayout>

                            <LinearLayout
                                android:id="@+id/llStake"
                                android:orientation="vertical"
                                android:paddingStart="@dimen/dp_16"
                                android:paddingEnd="@dimen/dp_16"
                                android:layout_marginTop="@dimen/dp_12"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content">
                                <TextView
                                    android:textColor="@color/color_title"
                                    android:textSize="16sp"
                                    android:text="@string/your_stake"
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"/>
                                <LinearLayout
                                    android:id="@+id/llStakeInfo"
                                    android:padding="@dimen/dp_16"
                                    android:visibility="gone"
                                    android:background="@drawable/shape_card_back"
                                    android:layout_marginTop="@dimen/dp_8"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content">
                                    <FrameLayout
                                        android:layout_width="@dimen/dp_42"
                                        android:layout_height="@dimen/dp_42">
                                        <ImageView
                                            android:src="@drawable/icon_iotex_network_logo"
                                            android:layout_width="@dimen/dp_40"
                                            android:layout_height="@dimen/dp_40"/>
                                        <ImageView
                                            android:layout_gravity="end|bottom"
                                            android:src="@drawable/icon_iotex_network_logo"
                                            android:layout_width="@dimen/dp_18"
                                            android:layout_height="@dimen/dp_18"/>
                                    </FrameLayout>
                                    <LinearLayout
                                        android:orientation="vertical"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content">
                                        <LinearLayout
                                            android:gravity="center"
                                            android:layout_marginStart="@dimen/dp_10"
                                            android:layout_width="match_parent"
                                            android:layout_height="wrap_content">
                                            <TextView
                                                android:text="@string/staked_iotx"
                                                android:textColor="@color/color_title"
                                                android:textSize="16sp"
                                                android:layout_width="wrap_content"
                                                android:layout_height="wrap_content"/>
                                            <io.iotex.iopay.widget.DINTextView
                                                android:id="@+id/tvStakeValue"
                                                tools:text="$0.0441"
                                                android:gravity="end"
                                                android:textSize="20sp"
                                                android:textColor="@color/color_title"
                                                android:layout_width="match_parent"
                                                android:layout_height="wrap_content"/>
                                        </LinearLayout>
                                        <LinearLayout
                                            android:gravity="center"
                                            android:layout_marginStart="@dimen/dp_10"
                                            android:layout_width="match_parent"
                                            android:layout_height="wrap_content">
                                            <TextView
                                                android:id="@+id/tvStakeAmount"
                                                tools:text="0.00123 IOTX"
                                                android:textColor="@color/color_title_sub"
                                                android:textSize="12sp"
                                                android:layout_width="wrap_content"
                                                android:layout_height="wrap_content"/>
                                            <io.iotex.iopay.widget.DINTextView
                                                android:id="@+id/tvStakeChange"
                                                tools:text="$0.0441"
                                                android:gravity="end"
                                                android:textSize="14sp"
                                                android:textColor="@color/color_title"
                                                android:layout_width="match_parent"
                                                android:layout_height="wrap_content"/>
                                        </LinearLayout>
                                    </LinearLayout>

                                </LinearLayout>
                                <LinearLayout
                                    android:id="@+id/llStartStake"
                                    android:orientation="vertical"
                                    android:padding="@dimen/dp_16"
                                    android:visibility="gone"
                                    tools:visibility="visible"
                                    android:layout_marginTop="@dimen/dp_8"
                                    android:background="@drawable/shape_card_back"
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent">
                                    <LinearLayout
                                        android:orientation="vertical"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content">
                                        <TextView
                                            android:textSize="14sp"
                                            android:text="@string/stake_with_iotx"
                                            android:textColor="@color/color_title_sub"
                                            android:layout_width="match_parent"
                                            android:layout_height="wrap_content"/>
                                        <LinearLayout
                                            android:layout_marginTop="@dimen/dp_4"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content">
                                            <TextView
                                                android:textSize="14sp"
                                                android:text="@string/annual_yield"
                                                android:textColor="@color/color_title"
                                                android:layout_width="wrap_content"
                                                android:layout_height="wrap_content"/>
                                            <TextView
                                                android:textSize="18sp"
                                                android:text="5.5%"
                                                android:textStyle="bold"
                                                android:layout_marginStart="@dimen/dp_5"
                                                android:layout_marginEnd="@dimen/dp_2"
                                                android:textColor="@color/color_855eff"
                                                android:layout_width="wrap_content"
                                                android:layout_height="wrap_content"/>
                                        </LinearLayout>
                                        <ImageView
                                            android:layout_marginTop="@dimen/dp_10"
                                            android:scaleType="fitXY"
                                            android:src="@drawable/icon_detail_stake_line"
                                            android:layout_width="match_parent"
                                            android:layout_height="@dimen/dp_120"/>
                                    </LinearLayout>

                                    <LinearLayout
                                        android:layout_marginTop="@dimen/dp_20"
                                        android:gravity="center"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content">
                                        <TextView
                                            android:id="@+id/tvStakeLearn"
                                            android:text="@string/learn_more_cap"
                                            android:layout_weight="1"
                                            android:gravity="center"
                                            android:layout_marginEnd="@dimen/dp_8"
                                            android:background="@drawable/btn_stroke_gradient_common"
                                            android:layout_width="0dp"
                                            android:layout_height="@dimen/dp_44"/>
                                        <TextView
                                            android:id="@+id/tvStakeStart"
                                            android:text="@string/start_earning"
                                            android:layout_weight="1"
                                            android:gravity="center"
                                            android:textSize="17sp"
                                            android:textColor="@color/white"
                                            android:layout_marginStart="@dimen/dp_8"
                                            android:background="@drawable/btn_shape_gradient_common"
                                            android:layout_width="0dp"
                                            android:layout_height="@dimen/dp_44"/>
                                    </LinearLayout>
                                </LinearLayout>
                            </LinearLayout>

                            <LinearLayout
                                android:id="@+id/llAddress"
                                android:orientation="vertical"
                                android:paddingStart="@dimen/dp_16"
                                android:paddingEnd="@dimen/dp_16"
                                android:layout_marginTop="@dimen/dp_12"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content">

                                <TextView
                                    android:text="@string/contract_addr"
                                    android:textSize="16sp"
                                    android:textColor="@color/color_title"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"/>

                                <LinearLayout
                                    android:layout_marginTop="@dimen/dp_8"
                                    android:gravity="center"
                                    android:paddingStart="@dimen/dp_12"
                                    android:paddingEnd="@dimen/dp_12"
                                    android:background="@drawable/shape_card_back"
                                    android:layout_width="match_parent"
                                    android:layout_height="@dimen/dp_44">

                                    <io.iotex.iopay.widget.DINTextView
                                        android:id="@+id/tvAddress"
                                        android:textColor="@color/color_title_sub"
                                        android:textSize="15sp"
                                        tools:text="io13…ad78"
                                        android:layout_weight="1"
                                        android:layout_width="0dp"
                                        android:layout_height="wrap_content"/>
                                    <androidx.appcompat.widget.AppCompatImageView
                                        android:id="@+id/ivCopy"
                                        android:layout_marginStart="@dimen/dp_12"
                                        android:src="@drawable/icon_copy"
                                        android:layout_width="@dimen/dp_24"
                                        android:layout_height="@dimen/dp_24"/>
                                </LinearLayout>

                            </LinearLayout>

                            <LinearLayout
                                android:orientation="vertical"
                                android:paddingStart="@dimen/dp_16"
                                android:paddingEnd="@dimen/dp_16"
                                android:layout_marginTop="@dimen/dp_12"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content">
                                <TextView
                                    android:id="@+id/tvMarket"
                                    android:textColor="@color/color_title"
                                    android:textSize="16sp"
                                    android:text="@string/market_details"
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"/>
                                <androidx.recyclerview.widget.RecyclerView
                                    android:id="@+id/recyclerView"
                                    android:overScrollMode="never"
                                    android:layout_marginTop="@dimen/dp_12"
                                    tools:itemCount="3"
                                    android:background="@drawable/shape_card_back"
                                    tools:listitem="@layout/item_token_market"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"/>

                            </LinearLayout>

                        </LinearLayout>
                    </androidx.core.widget.NestedScrollView>
                    <LinearLayout
                        android:id="@+id/llNewWallet"
                        android:background="@color/theme_window_back"
                        android:padding="@dimen/dp_16"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <TextView
                            android:id="@+id/tvAddWallet"
                            android:layout_width="match_parent"
                            android:layout_height="@dimen/dp_44"
                            android:background="@drawable/btn_shape_gradient_common"
                            android:gravity="center"
                            android:text="@string/add_wallet"
                            android:textColor="@color/white"
                            android:textSize="14sp" />
                    </LinearLayout>
                </LinearLayout>
            </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

        </LinearLayout>
        <FrameLayout
            android:id="@+id/flBino"
            android:visibility="gone"
            android:orientation="vertical"
            android:background="@color/dialog_shade"
            android:layout_width="match_parent"
            android:layout_height="match_parent">
            <View
                android:id="@+id/vBack"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_170"/>
            <LinearLayout
                android:layout_marginTop="@dimen/dp_150"
                android:orientation="vertical"
                android:background="@drawable/shape_back_dialog_bottom"
                android:layout_width="match_parent"
                android:layout_height="match_parent">
                <FrameLayout
                    android:layout_gravity="bottom"
                    android:layout_weight="1"
                    android:layout_width="match_parent"
                    android:layout_height="0dp">
                    <LinearLayout
                        android:id="@+id/llContent"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="vertical"
                        android:gravity="center">
                        <androidx.appcompat.widget.AppCompatTextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/dp_20"
                            android:text="@string/iopay_binoai"
                            android:textColor="@color/color_title"
                            android:textSize="17sp"
                            android:textStyle="bold" />

                        <FrameLayout
                            android:id="@+id/flWebView"
                            android:layout_marginTop="@dimen/dp_12"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"/>

                    </LinearLayout>

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/ivClose"
                        android:tint="@color/color_dialog_close"
                        android:layout_width="@dimen/dp_24"
                        android:layout_height="@dimen/dp_24"
                        android:layout_marginTop="@dimen/dp_16"
                        android:layout_marginEnd="@dimen/dp_16"
                        android:layout_gravity="end"
                        android:src="@drawable/ic_close" />
                </FrameLayout>
                <View
                    android:id="@+id/keyboardView"
                    android:layout_width="wrap_content"
                    android:layout_height="0dp"/>
            </LinearLayout>
        </FrameLayout>
        <io.iotex.iopay.ui.widget.DragFloatActionView
            android:id="@+id/mDragBtnBino"
            android:layout_width="@dimen/dp_44"
            android:layout_height="@dimen/dp_44"
            android:layout_gravity="end|bottom"
            android:layout_marginEnd="@dimen/dp_15"
            android:layout_marginBottom="@dimen/dp_180"
            android:alpha="0.5"
            android:focusable="true"
            android:clickable="true"
            tools:visibility="visible">
            <com.makeramen.roundedimageview.RoundedImageView
                android:layout_width="@dimen/dp_44"
                android:layout_height="@dimen/dp_44"
                app:riv_corner_radius="@dimen/dp_22"
                android:layout_centerInParent="true"
                android:src="@drawable/icon_bino_float" />
        </io.iotex.iopay.ui.widget.DragFloatActionView>
    </FrameLayout>

</layout>