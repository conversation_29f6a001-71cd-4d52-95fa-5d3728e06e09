<?xml version="1.0" encoding="utf-8"?>
<layout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <LinearLayout
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <RelativeLayout
            android:layout_width="match_parent"
            android:background="@drawable/shape_card_back_r20"
            android:layout_marginTop="@dimen/common_margin"
            android:layout_marginLeft="@dimen/common_padding_vertical"
            android:layout_marginRight="@dimen/common_padding_vertical"
            android:layout_height="@dimen/dp_40">
            <EditText
                android:id="@+id/et_search"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@null"
                android:drawableLeft="@drawable/btn_search"
                android:padding="@dimen/common_padding_small"
                android:layout_centerVertical="true"
                android:drawablePadding="@dimen/common_padding_small"
                android:gravity="start"
                android:textSize="@dimen/font_size_common"
                android:imeOptions="actionSearch"
                android:inputType="text|textAutoComplete"
                android:textColor="@color/color_title"
                android:textColorHint="@color/color_hint"
                />
            <ImageView
                android:id="@+id/iv_delete"
                android:layout_width="wrap_content"
                android:src="@drawable/btn_delete"
                android:visibility="invisible"
                android:layout_centerVertical="true"
                android:layout_marginRight="@dimen/common_padding_small"
                android:layout_alignParentRight="true"
                android:layout_height="wrap_content"/>
        </RelativeLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="@dimen/common_margin">


            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />
            <include
                android:id="@+id/no_history"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:visibility="gone"
                android:gravity="center"
                android:layout_centerInParent="true"
                layout="@layout/empty"/>

        </RelativeLayout>
    </LinearLayout>


</layout>