<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

    <LinearLayout
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">
            <ScrollView
                android:id="@+id/scrollView"
                android:fillViewport="true"
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <LinearLayout
                    android:orientation="vertical"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/theme_window_back">

                    <androidx.appcompat.widget.AppCompatTextView
                        android:layout_margin="@dimen/dp_15"
                        android:id="@+id/agreement_content"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp_20"
                        android:lineSpacingExtra="@dimen/line_spacing_extra"
                        android:lineSpacingMultiplier="1.5"
                        android:text="@string/user_agreement_content"
                        android:textColor="@color/color_title"/>

                    <LinearLayout
                        android:id="@+id/llCheck"
                        android:layout_margin="@dimen/dp_15"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">
                        <CheckBox
                            android:id="@+id/checkbox"
                            android:button="@null"
                            android:layout_margin="@dimen/dp_3"
                            android:background="@drawable/selector_check_box"
                            android:layout_width="@dimen/dp_16"
                            android:layout_height="@dimen/dp_16"/>
                        <TextView
                            android:textSize="14sp"
                            android:textColor="@color/color_title_sub"
                            android:layout_marginStart="@dimen/dp_10"
                            android:text="@string/i_agree_to_the_terms_of_use"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"/>
                    </LinearLayout>

                    <androidx.appcompat.widget.AppCompatButton
                        android:id="@+id/btnNext"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp_20"
                        android:layout_marginEnd="@dimen/dp_20"
                        android:layout_marginBottom="@dimen/dp_30"
                        android:background="@drawable/btn_shape_gradient_common"
                        android:text="@string/continue_text"
                        android:textAllCaps="false"
                        android:textColor="@android:color/white"
                        android:textSize="@dimen/font_size_large" />

                </LinearLayout>
            </ScrollView>
            <TextView
                android:id="@+id/tvScroll"
                android:padding="@dimen/dp_10"
                android:gravity="center"
                android:alpha="0.95"
                android:textSize="14sp"
                android:textColor="@color/color_title"
                android:background="@color/theme_window_back"
                android:layout_alignParentBottom="true"
                android:text="@string/please_scroll_to_read_all_sections"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"/>
        </RelativeLayout>
    </LinearLayout>
</layout>