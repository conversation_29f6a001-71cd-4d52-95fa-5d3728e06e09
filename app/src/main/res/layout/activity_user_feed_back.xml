<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:paddingStart="@dimen/dp_16"
            android:paddingEnd="@dimen/dp_16">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_12"
                android:text="@string/your_feedback"
                android:textColor="@color/color_title"
                android:textSize="16sp" />

            <EditText
                android:id="@+id/etContent"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_180"
                android:layout_marginTop="@dimen/dp_12"
                android:background="@drawable/shape_card_back"
                android:ellipsize="end"
                android:gravity="top"
                android:hint="@string/your_feedback"
                android:maxLength="300"
                android:padding="@dimen/dp_10"
                android:textColor="@color/color_title"
                android:textColorHint="@color/color_hint"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/tvTextSize"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="end"
                android:layout_marginTop="@dimen/dp_8"
                android:text="0/300"
                android:textColor="@color/color_title_sub"
                android:textSize="12sp" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_30"
                android:text="@string/contact_email"
                android:textColor="@color/color_title"
                android:textSize="16sp" />

            <EditText
                android:id="@+id/etEmail"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_60"
                android:layout_marginTop="@dimen/dp_12"
                android:background="@drawable/shape_card_back"
                android:ellipsize="end"
                android:gravity="center|start"
                android:hint="@string/enter_email_address"
                android:maxLines="1"
                android:padding="@dimen/dp_10"
                android:textColor="@color/color_title"
                android:textColorHint="@color/color_hint"
                android:textSize="14sp" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_30"
                android:gravity="center_vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/problem_picture_caption"
                    android:textColor="@color/color_title"
                    android:textSize="16sp" />

                <TextView
                    android:id="@+id/tvImageSize"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp_5"
                    android:text="0/9"
                    android:textColor="@color/color_title_sub"
                    android:textSize="12sp" />
            </LinearLayout>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_12"
                android:overScrollMode="never" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_12"
                android:gravity="center_vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/find_us"
                    android:textColor="@color/color_title"
                    android:textSize="16sp" />

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/ivTg"
                    android:layout_width="@dimen/dp_20"
                    android:layout_height="@dimen/dp_20"
                    android:layout_marginStart="@dimen/dp_10"
                    android:src="@drawable/icon_feed_tg"
                    android:tint="@color/color_title" />

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/ivDiscord"
                    android:layout_width="@dimen/dp_20"
                    android:layout_height="@dimen/dp_20"
                    android:layout_marginStart="@dimen/dp_10"
                    android:src="@drawable/icon_feed_discord"
                    android:tint="@color/color_title" />
            </LinearLayout>

            <TextView
                android:id="@+id/tvConfirm"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_44"
                android:layout_marginTop="@dimen/dp_30"
                android:background="@drawable/btn_shape_gradient_common"
                android:gravity="center"
                android:enabled="false"
                android:alpha="0.5"
                android:text="@string/confirm"
                android:textColor="@color/white"
                android:textSize="16sp" />

        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
</layout>