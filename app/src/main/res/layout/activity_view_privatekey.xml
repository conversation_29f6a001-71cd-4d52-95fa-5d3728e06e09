<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:paddingLeft="16dp"
            android:paddingTop="28dp"
            android:paddingRight="16dp">

            <LinearLayout
                android:id="@+id/llWifContainer"
                android:layout_width="match_parent"
                android:orientation="vertical"
                android:visibility="gone"
                android:layout_height="wrap_content">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp_20"
                    android:text="@string/wif_private_key"
                    android:textColor="@color/color_title_sub"
                    android:textSize="15sp" />

                <TextView
                    android:id="@+id/tvWifPrivateKey"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp_10"
                    android:background="@drawable/shape_card_back_stroke_gradient"
                    android:gravity="start"
                    android:lines="4"
                    android:padding="@dimen/dp_10"
                    android:textColor="@color/color_title_sub"
                    android:textSize="20sp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="15dp"
                    android:gravity="center"
                    android:orientation="horizontal">

                    <androidx.appcompat.widget.AppCompatButton
                        android:id="@+id/btnWifCopy"
                        android:layout_width="@dimen/btn_group_large"
                        android:layout_height="@dimen/btn_group_large"
                        android:layout_margin="@dimen/common_padding_horizontal"
                        android:background="@drawable/icon_copy"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <androidx.appcompat.widget.AppCompatButton
                        android:id="@+id/btnWifView"
                        android:layout_width="@dimen/btn_group_large"
                        android:layout_height="@dimen/btn_group_large"
                        android:layout_margin="@dimen/common_padding_horizontal"
                        android:background="@drawable/icon_hide"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <androidx.appcompat.widget.AppCompatButton
                        android:id="@+id/btnWifQrCode"
                        android:layout_width="@dimen/btn_group_large"
                        android:layout_height="@dimen/btn_group_large"
                        android:layout_margin="@dimen/common_padding_horizontal"
                        android:background="@drawable/icon_qr_code"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />
                </LinearLayout>

                <com.makeramen.roundedimageview.RoundedImageView
                    android:id="@+id/ivWifQrCode"
                    android:layout_width="150dp"
                    android:layout_height="150dp"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="30dp"
                    android:visibility="gone"
                    app:riv_corner_radius="@dimen/dp_6"
                    tools:visibility="visible" />

            </LinearLayout>

            <TextView
                android:id="@+id/tvHexKeyLabel"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_30"
                android:text="@string/hex_private_key"
                android:textColor="@color/color_title_sub"
                android:visibility="gone"
                android:textSize="15sp" />

            <TextView
                android:id="@+id/tvHexPrivateKey"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_10"
                android:background="@drawable/shape_card_back_stroke_gradient"
                android:gravity="start"
                android:lines="6"
                android:padding="@dimen/dp_10"
                android:textColor="@color/color_title"
                android:textSize="20sp" />

            <LinearLayout
                android:id="@+id/private_key_action"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="15dp"
                android:gravity="center"
                android:orientation="horizontal"
                app:layout_constraintTop_toBottomOf="@+id/private_key">

                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/btnHexCopy"
                    android:layout_width="@dimen/btn_group_large"
                    android:layout_height="@dimen/btn_group_large"
                    android:layout_margin="@dimen/common_padding_horizontal"
                    android:background="@drawable/icon_copy"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/btnHexView"
                    android:layout_width="@dimen/btn_group_large"
                    android:layout_height="@dimen/btn_group_large"
                    android:layout_margin="@dimen/common_padding_horizontal"
                    android:background="@drawable/icon_hide"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/btnHexQrCode"
                    android:layout_width="@dimen/btn_group_large"
                    android:layout_height="@dimen/btn_group_large"
                    android:layout_margin="@dimen/common_padding_horizontal"
                    android:background="@drawable/icon_qr_code"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />
            </LinearLayout>

            <com.makeramen.roundedimageview.RoundedImageView
                android:id="@+id/ivHexQrCode"
                android:layout_width="150dp"
                android:layout_height="150dp"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="30dp"
                android:visibility="gone"
                app:riv_corner_radius="@dimen/dp_6"
                tools:visibility="visible" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_30"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/shape_card_back"
                    android:orientation="vertical"
                    android:paddingStart="@dimen/dp_15"
                    android:paddingTop="@dimen/dp_15"
                    android:paddingEnd="@dimen/dp_15"
                    android:layout_marginBottom="@dimen/dp_50"
                    android:paddingBottom="@dimen/dp_20">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical">

                        <ImageView
                            android:layout_width="@dimen/dp_20"
                            android:layout_height="@dimen/dp_20"
                            android:src="@drawable/ic_warning" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp_7"
                            android:text="@string/backup_tips"
                            android:textColor="@color/color_title"
                            android:textSize="14sp"
                            android:textStyle="bold" />
                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dp_0_5"
                        android:layout_marginTop="14dp"
                        android:alpha="0.06"
                        android:background="@color/color_line_diver" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="13dp"
                        android:text="@string/obtaining_private_key_equals_owning_all_assets"
                        android:textColor="@color/color_title_sub"
                        android:textSize="14sp" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/copy_it_on_the_paper_and_keep_in_a_safe_place"
                        android:textColor="@color/color_title_sub"
                        android:textSize="14sp" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/once_the_private_key_gets_lost"
                        android:textColor="@color/color_title_sub"
                        android:textSize="14sp" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/Please_be_sure_to_back_up_the_private_key"
                        android:textColor="@color/color_title_sub"
                        android:textSize="14sp" />
                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</layout>
