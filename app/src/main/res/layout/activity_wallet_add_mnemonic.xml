<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android" >

    <LinearLayout
        android:orientation="vertical"
        android:background="@color/color_card_back"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:id="@+id/mSelectorCreateWallet"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_15"
            android:layout_marginTop="@dimen/dp_12"
            android:layout_marginEnd="@dimen/dp_15"
            android:padding="@dimen/dp_20"
            android:gravity="center"
            android:background="@drawable/shape_back_r6">

            <LinearLayout
                android:layout_weight="1"
                android:orientation="vertical"
                android:layout_width="0dp"
                android:layout_marginEnd="@dimen/dp_12"
                android:layout_height="wrap_content">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/create_new_wallet"
                    android:textColor="@color/color_title"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:drawableTint="@color/color_title"
                    android:drawableEnd="@drawable/ic_arrow_right_white"/>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp_8"
                    android:text="@string/create_new_wallet_caption_02"
                    android:textColor="@color/color_title_sub"
                    android:textSize="14sp" />
            </LinearLayout>

            <ImageView
                android:layout_width="@dimen/dp_90"
                android:layout_height="@dimen/dp_80"
                android:src="@drawable/icon_add_wallet_create"/>

        </LinearLayout>

        <LinearLayout
            android:id="@+id/mSelectorImportWallet"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_15"
            android:layout_marginTop="@dimen/dp_12"
            android:layout_marginEnd="@dimen/dp_15"
            android:padding="@dimen/dp_20"
            android:gravity="center"
            android:background="@drawable/shape_back_r6">

            <LinearLayout
                android:orientation="vertical"
                android:layout_weight="1"
                android:layout_width="0dp"
                android:layout_marginEnd="@dimen/dp_12"
                android:layout_height="wrap_content">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/import_exist_wallet"
                    android:textColor="@color/color_title"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:drawableTint="@color/color_title"
                    android:drawableEnd="@drawable/ic_arrow_right_white"/>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp_8"
                    android:text="@string/import_exist_wallet_caption_02"
                    android:textColor="@color/color_title_sub"
                    android:textSize="14sp" />

            </LinearLayout>
            <ImageView
                android:layout_width="@dimen/dp_100"
                android:layout_height="@dimen/dp_85"
                android:src="@drawable/icon_add_wallet_import" />

        </LinearLayout>
    </LinearLayout>

</layout>