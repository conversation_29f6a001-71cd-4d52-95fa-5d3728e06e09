<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <LinearLayout
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <TextView
            android:id="@+id/tvImportMethod"
            android:layout_marginTop="@dimen/dp_16"
            android:layout_marginStart="@dimen/dp_16"
            android:text="@string/private_key"
            android:textColor="@color/color_title"
            android:textSize="16sp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

        <FrameLayout
            android:layout_marginStart="@dimen/dp_15"
            android:layout_marginTop="@dimen/dp_16"
            android:layout_marginEnd="@dimen/dp_15"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <EditText
                android:id="@+id/mEtInput"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/shape_card_back_stroke_gradient"
                android:enabled="true"
                android:gravity="start"
                android:hint="@string/input_private_key"
                android:letterSpacing="0.05"
                android:lineSpacingExtra="@dimen/dp_3"
                android:lines="6"
                android:padding="@dimen/dp_15"
                android:textColor="@color/color_title_sub"
                android:textColorHint="@color/color_hint"
                android:textSize="14sp" />
            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/ivKeyEye"
                android:layout_margin="@dimen/dp_10"
                android:layout_gravity="end|bottom"
                android:tint="@color/color_title"
                android:src="@drawable/icon_eye_close_white"
                android:layout_width="@dimen/dp_24"
                android:layout_height="@dimen/dp_24"/>
        </FrameLayout>

        <LinearLayout
            android:layout_marginTop="@dimen/dp_12"
            android:layout_marginStart="@dimen/dp_16"
            android:layout_marginEnd="@dimen/dp_16"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <TextView
                android:id="@+id/tvDesc"
                android:textSize="14sp"
                android:textColor="@color/color_title_sub"
                android:text="@string/iopay_now_supports_evm_solana_and_btc_private_key"
                android:layout_weight="1"
                android:drawableStart="@drawable/ic_warning"
                android:drawablePadding="@dimen/dp_5"
                android:layout_width="0dp"
                android:layout_height="wrap_content"/>

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/ivScan"
                android:layout_marginStart="@dimen/dp_10"
                android:layout_gravity="end|top"
                android:tint="@color/color_title"
                android:src="@drawable/icon_scan_white"
                android:layout_width="@dimen/dp_24"
                android:layout_height="@dimen/dp_24"/>
        </LinearLayout>

        <LinearLayout
            android:id="@+id/mLlPasswordContainer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_32"
            android:orientation="vertical"
            android:paddingStart="@dimen/dp_15"
            android:paddingEnd="@dimen/dp_15"
            android:visibility="gone"
            tools:visibility="visible">

            <LinearLayout
                android:gravity="center_vertical"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <androidx.appcompat.widget.AppCompatTextView
                    android:layout_weight="1"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:text="@string/password"
                    android:textColor="@color/color_title" />

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/ivPasswordEye"
                    android:tint="@color/color_title"
                    android:src="@drawable/icon_eye_close_white"
                    android:layout_width="@dimen/dp_24"
                    android:layout_height="@dimen/dp_24"/>
            </LinearLayout>
            <EditText
                android:id="@+id/mEtPassword"
                android:layout_marginTop="@dimen/dp_10"
                android:layout_marginBottom="@dimen/dp_5"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/color_back"
                android:hint="@string/type_password"
                android:textSize="14sp"
                android:textColor="@color/color_title"
                android:textColorHint="@color/color_hint" />
            <View
                android:layout_marginTop="@dimen/dp_5"
                android:background="@color/color_line_diver"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_1"/>
        </LinearLayout>

        <View
            android:layout_weight="1"
            android:layout_width="wrap_content"
            android:layout_height="0dp"/>

        <TextView
            android:id="@+id/tvNext"
            android:gravity="center"
            android:text="@string/next_pf"
            android:textColor="@color/white"
            android:textSize="14sp"
            android:layout_marginBottom="@dimen/dp_70"
            android:background="@drawable/btn_shape_gradient_common"
            android:layout_marginStart="@dimen/dp_20"
            android:layout_marginEnd="@dimen/dp_20"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_44"/>
    </LinearLayout>

</layout>