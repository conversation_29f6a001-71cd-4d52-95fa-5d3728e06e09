<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android" >

    <LinearLayout
        android:orientation="vertical"
        android:background="@color/color_card_back"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <LinearLayout
            android:id="@+id/mSelectorPrivateKey"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_15"
            android:layout_marginTop="@dimen/dp_12"
            android:layout_marginEnd="@dimen/dp_15"
            android:padding="@dimen/dp_20"
            android:gravity="center"
            android:background="@drawable/shape_back_r6">

            <LinearLayout
                android:orientation="vertical"
                android:layout_weight="1"
                android:layout_marginEnd="@dimen/dp_12"
                android:layout_width="0dp"
                android:layout_height="wrap_content">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/private_key"
                    android:textColor="@color/color_title"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:drawableTint="@color/color_title"
                    android:drawableEnd="@drawable/ic_arrow_right_white"/>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp_8"
                    android:text="@string/import_via_private_key_caption_02"
                    android:textColor="@color/color_title_sub"
                    android:textSize="14sp" />
            </LinearLayout>

            <ImageView
                android:layout_width="@dimen/dp_100"
                android:layout_height="@dimen/dp_85"
                android:src="@drawable/icon_import_wallet_private_key" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/mSelectorMnemonic"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_15"
            android:layout_marginTop="@dimen/dp_12"
            android:layout_marginEnd="@dimen/dp_15"
            android:padding="@dimen/dp_20"
            android:gravity="center"
            android:background="@drawable/shape_back_r6">

            <LinearLayout
                android:layout_weight="1"
                android:orientation="vertical"
                android:layout_marginEnd="@dimen/dp_12"
                android:layout_width="0dp"
                android:layout_height="wrap_content">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/mnemonic"
                    android:textColor="@color/color_title"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:drawableTint="@color/color_title"
                    android:drawableEnd="@drawable/ic_arrow_right_white" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp_8"
                    android:text="@string/import_via_mnemonic_caption"
                    android:textColor="@color/color_title_sub"
                    android:textSize="14sp"/>
            </LinearLayout>

            <ImageView
                android:layout_width="@dimen/dp_100"
                android:layout_height="@dimen/dp_85"
                android:src="@drawable/icon_import_wallet_mnemonic"/>

        </LinearLayout>

        <LinearLayout
            android:id="@+id/mSelectorKeystore"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_15"
            android:layout_marginTop="@dimen/dp_12"
            android:layout_marginEnd="@dimen/dp_15"
            android:padding="@dimen/dp_20"
            android:gravity="center"
            android:background="@drawable/shape_back_r6">

            <LinearLayout
                android:layout_weight="1"
                android:orientation="vertical"
                android:layout_marginEnd="@dimen/dp_12"
                android:layout_width="0dp"
                android:layout_height="wrap_content">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/key_store"
                    android:textColor="@color/color_title"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:drawableTint="@color/color_title"
                    android:drawableEnd="@drawable/ic_arrow_right_white" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp_8"
                    android:text="@string/import_via_keystore_caption_02"
                    android:textColor="@color/color_title_sub"
                    android:textSize="14sp" />
            </LinearLayout>

            <ImageView
                android:layout_width="@dimen/dp_100"
                android:layout_height="@dimen/dp_85"
                android:src="@drawable/icon_import_wallet_keystore" />

        </LinearLayout>
    </LinearLayout>
</layout>