<?xml version="1.0" encoding="utf-8"?>
<layout
    xmlns:android="http://schemas.android.com/apk/res/android">
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <LinearLayout
            android:layout_width="match_parent"
            android:orientation="vertical"
            android:layout_height="wrap_content">

            <TextView
                android:layout_width="wrap_content"
                android:text="@string/wallet_learn_more_content"
                android:layout_marginTop="@dimen/dp_30"
                android:layout_marginLeft="@dimen/dp_20"
                android:textSize="@dimen/font_size_common"
                android:textColor="@color/color_title"
                android:layout_marginRight="@dimen/dp_20"
                android:layout_height="wrap_content"/>
            <TextView
                android:layout_width="wrap_content"
                android:text="@string/wallet_learn_more_content1"
                android:layout_marginTop="@dimen/dp_16"
                android:layout_marginLeft="@dimen/dp_20"
                android:textColor="@color/color_title_sub"
                android:textSize="@dimen/font_size_common"
                android:layout_marginRight="@dimen/dp_20"
                android:layout_height="wrap_content"/>
            <TextView
                android:layout_width="wrap_content"
                android:text="@string/wallet_learn_more_content2"
                android:layout_marginTop="@dimen/dp_10"
                android:layout_marginLeft="@dimen/dp_20"
                android:textSize="@dimen/font_size_common"
                android:textColor="@color/color_title_sub"
                android:layout_marginRight="@dimen/dp_20"
                android:layout_height="wrap_content"/>
            <TextView
                android:layout_width="wrap_content"
                android:text="@string/what_does_it_mean"
                android:layout_marginTop="@dimen/dp_60"
                android:layout_marginLeft="@dimen/dp_20"
                android:textColor="@color/color_title"
                android:textStyle="bold"
                android:textSize="@dimen/font_size_common"
                android:layout_marginRight="@dimen/dp_20"
                android:layout_height="wrap_content"/>
            <TextView
                android:layout_width="wrap_content"
                android:text="@string/wallet_learn_more_content3"
                android:layout_marginTop="@dimen/dp_16"
                android:layout_marginLeft="@dimen/dp_20"
                android:textSize="@dimen/font_size_common"
                android:textColor="@color/color_title_sub"
                android:layout_marginRight="@dimen/dp_20"
                android:layout_height="wrap_content"/>
        </LinearLayout>
    </ScrollView>
</layout>
