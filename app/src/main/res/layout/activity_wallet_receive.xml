<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <LinearLayout
            android:orientation="vertical"
            android:paddingBottom="@dimen/dp_8"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerView"
                android:overScrollMode="never"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"/>
            <LinearLayout
                android:id="@+id/llBtcLayout"
                android:orientation="vertical"
                android:layout_marginTop="@dimen/dp_8"
                android:layout_marginStart="@dimen/dp_16"
                android:layout_marginEnd="@dimen/dp_16"
                android:background="@drawable/shape_card_back"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <LinearLayout
                    android:id="@+id/llBtc"
                    android:gravity="center"
                    android:paddingStart="@dimen/dp_12"
                    android:paddingEnd="@dimen/dp_12"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp_60">
                    <ImageView
                        android:src="@drawable/icon_bitcoin_logo"
                        android:layout_width="@dimen/dp_36"
                        android:layout_height="@dimen/dp_36"/>
                    <TextView
                        android:layout_marginStart="@dimen/dp_10"
                        android:textColor="@color/color_title"
                        android:textSize="15sp"
                        android:text="@string/btc"
                        android:layout_weight="1"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"/>
                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/ivArrow"
                        android:rotation="270"
                        android:tint="@color/color_title_sub"
                        android:src="@drawable/ic_arrow_right_white"
                        android:layout_width="@dimen/dp_23"
                        android:layout_height="@dimen/dp_23"/>
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/llBtcContent"
                    android:orientation="vertical"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">
                    <LinearLayout
                        android:id="@+id/llLegacy"
                        android:gravity="center"
                        android:paddingStart="@dimen/dp_16"
                        android:paddingEnd="@dimen/dp_16"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dp_60">
                        <LinearLayout
                            android:layout_weight="1"
                            android:layout_width="0dp"
                            android:orientation="vertical"
                            android:layout_height="wrap_content">
                            <TextView
                                android:id="@+id/tvLegacyName"
                                android:textColor="@color/color_title"
                                android:textSize="15sp"
                                tools:text="Legacy"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"/>
                            <TextView
                                android:id="@+id/tvLegacyAddress"
                                android:textColor="@color/color_title_sub"
                                android:textSize="12sp"
                                tools:text="iotex"
                                android:layout_marginTop="@dimen/dp_6"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"/>
                        </LinearLayout>
                        <ImageView
                            android:src="@drawable/icon_receive_code"
                            android:layout_width="@dimen/dp_36"
                            android:layout_height="@dimen/dp_36"/>
                        <ImageView
                            android:id="@+id/ivCopyLegacy"
                            android:layout_marginStart="@dimen/dp_12"
                            android:src="@drawable/icon_receive_copy_bg"
                            android:layout_width="@dimen/dp_36"
                            android:layout_height="@dimen/dp_36"/>
                    </LinearLayout>

                    <View
                        android:layout_margin="@dimen/dp_10"
                        android:background="@color/color_line_diver"
                        android:layout_width="wrap_content"
                        android:layout_height="@dimen/dp_1"/>

                    <LinearLayout
                        android:id="@+id/llNativeSegwit"
                        android:gravity="center"
                        android:paddingStart="@dimen/dp_16"
                        android:paddingEnd="@dimen/dp_16"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dp_60">
                        <LinearLayout
                            android:layout_weight="1"
                            android:layout_width="0dp"
                            android:orientation="vertical"
                            android:layout_height="wrap_content">
                            <TextView
                                android:id="@+id/tvNativeName"
                                android:textColor="@color/color_title"
                                android:textSize="15sp"
                                tools:text="Legacy"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"/>
                            <TextView
                                android:id="@+id/tvNativeAddress"
                                android:textColor="@color/color_title_sub"
                                android:textSize="12sp"
                                tools:text="iotex"
                                android:layout_marginTop="@dimen/dp_6"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"/>
                        </LinearLayout>
                        <ImageView
                            android:src="@drawable/icon_receive_code"
                            android:layout_width="@dimen/dp_36"
                            android:layout_height="@dimen/dp_36"/>
                        <ImageView
                            android:id="@+id/ivCopyNative"
                            android:layout_marginStart="@dimen/dp_12"
                            android:src="@drawable/icon_receive_copy_bg"
                            android:layout_width="@dimen/dp_36"
                            android:layout_height="@dimen/dp_36"/>
                    </LinearLayout>

                    <View
                        android:layout_margin="@dimen/dp_10"
                        android:background="@color/color_line_diver"
                        android:layout_width="wrap_content"
                        android:layout_height="@dimen/dp_1"/>

                    <LinearLayout
                        android:id="@+id/llNestedSegwit"
                        android:gravity="center"
                        android:paddingStart="@dimen/dp_16"
                        android:paddingEnd="@dimen/dp_16"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dp_60">
                        <LinearLayout
                            android:layout_weight="1"
                            android:layout_width="0dp"
                            android:orientation="vertical"
                            android:layout_height="wrap_content">
                            <TextView
                                android:id="@+id/tvNestedName"
                                android:textColor="@color/color_title"
                                android:textSize="15sp"
                                tools:text="Legacy"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"/>
                            <TextView
                                android:id="@+id/tvNestedAddress"
                                android:textColor="@color/color_title_sub"
                                android:textSize="12sp"
                                tools:text="iotex"
                                android:layout_marginTop="@dimen/dp_6"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"/>
                        </LinearLayout>
                        <ImageView
                            android:src="@drawable/icon_receive_code"
                            android:layout_width="@dimen/dp_36"
                            android:layout_height="@dimen/dp_36"/>
                        <ImageView
                            android:id="@+id/ivCopyNested"
                            android:layout_marginStart="@dimen/dp_12"
                            android:src="@drawable/icon_receive_copy_bg"
                            android:layout_width="@dimen/dp_36"
                            android:layout_height="@dimen/dp_36"/>
                    </LinearLayout>

                    <View
                        android:layout_margin="@dimen/dp_10"
                        android:background="@color/color_line_diver"
                        android:layout_width="wrap_content"
                        android:layout_height="@dimen/dp_1"/>

                    <LinearLayout
                        android:id="@+id/llTaproot"
                        android:gravity="center"
                        android:paddingStart="@dimen/dp_16"
                        android:paddingEnd="@dimen/dp_16"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dp_60">
                        <LinearLayout
                            android:layout_weight="1"
                            android:layout_width="0dp"
                            android:orientation="vertical"
                            android:layout_height="wrap_content">
                            <TextView
                                android:id="@+id/tvTaprootName"
                                android:textColor="@color/color_title"
                                android:textSize="15sp"
                                tools:text="Legacy"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"/>
                            <TextView
                                android:id="@+id/tvTaprootAddress"
                                android:textColor="@color/color_title_sub"
                                android:textSize="12sp"
                                tools:text="iotex"
                                android:layout_marginTop="@dimen/dp_6"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"/>
                        </LinearLayout>
                        <ImageView
                            android:src="@drawable/icon_receive_code"
                            android:layout_width="@dimen/dp_36"
                            android:layout_height="@dimen/dp_36"/>
                        <ImageView
                            android:id="@+id/ivCopyTaproot"
                            android:layout_marginStart="@dimen/dp_12"
                            android:src="@drawable/icon_receive_copy_bg"
                            android:layout_width="@dimen/dp_36"
                            android:layout_height="@dimen/dp_36"/>
                    </LinearLayout>
                </LinearLayout>
            </LinearLayout>
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerViewB"
                android:overScrollMode="never"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"/>
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

</layout>