<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <LinearLayout
                    android:id="@+id/llTab"
                    android:layout_marginTop="@dimen/dp_16"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp_44"
                    android:layout_marginStart="@dimen/dp_32"
                    android:layout_marginEnd="@dimen/dp_32"
                    android:background="@drawable/shape_card_back_stroke_gradient"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tabEth"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:text="@string/web3_string"
                        android:textColor="@color/color_title"
                        android:textSize="@dimen/font_size_middle">

                    </TextView>

                    <TextView
                        android:id="@+id/tabIotex"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:text="@string/native_string"
                        android:textColor="@color/color_title"
                        android:textSize="@dimen/font_size_middle"/>

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/llRoot"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:paddingTop="@dimen/dp_20"
                    android:paddingBottom="@dimen/dp_20">

                    <FrameLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp_32"
                            android:layout_marginTop="@dimen/dp_20"
                            android:layout_marginEnd="@dimen/dp_32"
                            android:paddingTop="@dimen/dp_60"
                            android:background="@drawable/shape_card_back"
                            android:clipChildren="false"
                            android:clipToPadding="false"
                            android:gravity="center_horizontal"
                            android:orientation="vertical">

                            <TextView
                                android:id="@+id/tvAddressType"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:gravity="center"
                                android:textColor="@color/color_title"
                                android:textSize="14sp"
                                android:visibility="gone"
                                tools:visibility="visible"
                                android:paddingStart="@dimen/dp_10"
                                android:paddingEnd="@dimen/dp_10"
                                android:includeFontPadding="false"
                                android:paddingTop="@dimen/dp_3"
                                android:paddingBottom="@dimen/dp_3"
                                android:background="@drawable/shape_card_back_stroke_gradient"
                                tools:text="Taproot (P2SH-P2WPKH)" />

                            <LinearLayout
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="@dimen/dp_4"
                                android:orientation="vertical"
                                android:gravity="center">

                                <TextView
                                    android:id="@+id/tvAddress"
                                    android:layout_marginStart="@dimen/dp_20"
                                    android:layout_marginEnd="@dimen/dp_20"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:gravity="center"
                                    android:textColor="@color/color_title"
                                    android:textSize="12sp"
                                    android:visibility="visible"
                                    tools:text="io132k…dvnyad78" />

                                <androidx.appcompat.widget.AppCompatImageView
                                    android:id="@+id/copyAddress"
                                    android:layout_width="@dimen/dp_20"
                                    android:layout_height="@dimen/dp_20"
                                    android:layout_margin="@dimen/dp_4"
                                    android:tint="@color/color_title"
                                    android:src="@drawable/icon_copy_white" />
                            </LinearLayout>

                            <com.makeramen.roundedimageview.RoundedImageView
                                android:id="@+id/ivQrCode"
                                android:src="@drawable/icon_qr_code"
                                android:layout_width="160dp"
                                android:layout_height="160dp"
                                android:layout_marginTop="@dimen/dp_20"
                                android:gravity="center"
                                app:riv_corner_radius="@dimen/dp_6" />

                            <LinearLayout
                                android:layout_marginTop="@dimen/dp_15"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:gravity="center">

                                <ImageView
                                    android:layout_width="@dimen/dp_26"
                                    android:layout_height="wrap_content"
                                    android:adjustViewBounds="true"
                                    android:src="@drawable/icon_app_logo_dark" />

                                <androidx.appcompat.widget.AppCompatImageView
                                    android:layout_width="@dimen/dp_36"
                                    android:layout_height="wrap_content"
                                    android:layout_marginStart="8dp"
                                    android:adjustViewBounds="true"
                                    android:tint="@color/color_title"
                                    android:src="@drawable/ic_iopay" />

                            </LinearLayout>

                            <FrameLayout
                                android:layout_marginTop="@dimen/dp_15"
                                android:background="@drawable/shape_card_back_on_card_bottom_r6"
                                android:layout_width="match_parent"
                                android:layout_height="@dimen/dp_40">
                                <ImageView
                                    android:id="@+id/ivSave"
                                    android:layout_gravity="end|center_vertical"
                                    android:layout_marginEnd="@dimen/dp_17"
                                    android:src="@drawable/icon_receive_save"
                                    android:layout_width="@dimen/dp_24"
                                    android:layout_height="@dimen/dp_24"/>
                            </FrameLayout>
                        </LinearLayout>

                        <FrameLayout
                            android:layout_width="@dimen/dp_70"
                            android:layout_height="@dimen/dp_70"
                            android:layout_gravity="center_horizontal">

                            <io.iotex.iopay.ui.widget.CircleImageView
                                android:id="@+id/ivAvatar"
                                android:layout_width="@dimen/dp_70"
                                android:layout_height="@dimen/dp_70"
                                android:layout_gravity="center_horizontal"
                                android:scaleType="centerCrop"
                                android:src="@drawable/icon_wallet_default" />

                            <ImageView
                                android:id="@+id/ivAA"
                                android:layout_width="@dimen/dp_20"
                                android:layout_height="@dimen/dp_20"
                                android:layout_gravity="end"
                                android:src="@drawable/icon_wallet_aa"
                                tools:visibility="visible"
                                android:visibility="gone" />
                        </FrameLayout>

                    </FrameLayout>

                    <LinearLayout
                        android:id="@+id/llInfo"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp_32"
                        android:layout_marginTop="@dimen/dp_15"
                        android:layout_marginEnd="@dimen/dp_32"
                        android:background="@drawable/shape_card_back"
                        android:orientation="vertical">

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="@dimen/dp_40"
                            android:layout_marginStart="@dimen/dp_5"
                            android:gravity="center_vertical">

                            <ImageView
                                android:layout_width="@dimen/dp_20"
                                android:layout_height="@dimen/dp_20"
                                android:layout_margin="@dimen/dp_5"
                                android:src="@drawable/icon_warn_net_work" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/important_tips"
                                android:textColor="@color/color_title"
                                android:textSize="14sp" />
                        </LinearLayout>

                        <View
                            android:layout_width="wrap_content"
                            android:layout_height="@dimen/dp_1"
                            android:background="@color/color_line_diver" />

                        <TextView
                            android:id="@+id/tvTips01"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/dp_15"
                            android:layout_marginStart="@dimen/dp_15"
                            android:layout_marginEnd="@dimen/dp_15"
                            android:layout_marginBottom="@dimen/dp_8"
                            android:text="@string/this_address_only_accepts_native"
                            android:textColor="@color/color_title_sub"
                            android:textSize="12sp" />

                        <TextView
                            android:id="@+id/tvTips02"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/dp_8"
                            android:layout_marginStart="@dimen/dp_15"
                            android:layout_marginEnd="@dimen/dp_15"
                            android:layout_marginBottom="@dimen/dp_10"
                            android:text="@string/aa_wallet_operates_on_smart_contracts"
                            android:textColor="@color/color_title_sub"
                            android:textSize="12sp" />
                    </LinearLayout>

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/llHowTo"
                    android:orientation="vertical"
                    android:background="@drawable/shape_card_back"
                    android:layout_marginStart="@dimen/dp_32"
                    android:layout_marginEnd="@dimen/dp_32"
                    android:layout_marginBottom="@dimen/dp_20"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">
                    <LinearLayout
                        android:id="@+id/llOtherWallet"
                        android:gravity="center_vertical"
                        android:paddingStart="@dimen/dp_12"
                        android:paddingEnd="@dimen/dp_12"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dp_48">
                        <ImageView
                            android:src="@drawable/icon_receive_from_wallet"
                            android:layout_width="@dimen/dp_20"
                            android:layout_height="@dimen/dp_20"/>
                        <TextView
                            android:textColor="@color/color_title_sub"
                            android:layout_marginStart="@dimen/dp_5"
                            android:layout_marginEnd="@dimen/dp_5"
                            android:textSize="14sp"
                            android:text="@string/how_to_receive_from_other_wallets"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"/>
                        <androidx.appcompat.widget.AppCompatImageView
                            android:layout_width="@dimen/dp_14"
                            android:layout_height="@dimen/dp_14"
                            android:src="@drawable/icon_setting_arrow_right"
                            android:tint="@color/color_title_sub" />
                    </LinearLayout>
                    <View
                        android:layout_marginStart="@dimen/dp_12"
                        android:layout_marginEnd="@dimen/dp_12"
                        android:background="@color/color_line_diver"
                        android:layout_width="wrap_content"
                        android:layout_height="@dimen/dp_1"/>
                    <LinearLayout
                        android:id="@+id/llExchange"
                        android:gravity="center_vertical"
                        android:paddingStart="@dimen/dp_12"
                        android:paddingEnd="@dimen/dp_12"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dp_48">
                        <ImageView
                            android:src="@drawable/icon_receive_from_exchange"
                            android:layout_width="@dimen/dp_20"
                            android:layout_height="@dimen/dp_20"/>
                        <TextView
                            android:textColor="@color/color_title_sub"
                            android:layout_marginStart="@dimen/dp_5"
                            android:layout_marginEnd="@dimen/dp_5"
                            android:textSize="14sp"
                            android:text="@string/how_to_receive_from_exchanges"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"/>
                        <androidx.appcompat.widget.AppCompatImageView
                            android:layout_width="@dimen/dp_14"
                            android:layout_height="@dimen/dp_14"
                            android:src="@drawable/icon_setting_arrow_right"
                            android:tint="@color/color_title_sub" />
                    </LinearLayout>
                </LinearLayout>
            </LinearLayout>
        </androidx.core.widget.NestedScrollView>
    </FrameLayout>
</layout>