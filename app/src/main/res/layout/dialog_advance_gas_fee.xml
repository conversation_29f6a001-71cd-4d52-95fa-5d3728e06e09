<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/shape_back_dialog_bottom"
            android:orientation="vertical"
            android:paddingStart="@dimen/dp_20"
            android:paddingTop="@dimen/dp_20"
            android:paddingEnd="@dimen/dp_20">

            <LinearLayout
                android:id="@+id/mTlNav"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical">

                <ImageView
                    android:id="@+id/ivBack"
                    android:layout_width="@dimen/dp_16"
                    android:layout_height="@dimen/dp_16"
                    android:src="@drawable/ic_arrow_left_white" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/dp_16"
                    android:gravity="center"
                    android:text="@string/advanced_gas_fee"
                    android:textColor="@color/color_title"
                    android:textSize="@dimen/font_size_middle"
                    android:textStyle="bold" />

            </LinearLayout>

            <LinearLayout
                android:gravity="center_vertical"
                android:layout_marginTop="@dimen/dp_28"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/max_base_fee_with_unit"
                    android:textColor="@color/color_title_sub"
                    android:textSize="14sp" />
                <ImageView
                    android:id="@+id/ivBaseFee"
                    android:layout_marginStart="@dimen/dp_4"
                    android:src="@drawable/ic_warning"
                    android:layout_width="@dimen/dp_24"
                    android:layout_height="@dimen/dp_24"/>
            </LinearLayout>

            <io.iotex.iopay.ui.widget.EditTextStateful
                android:id="@+id/etMaxBaseFee"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="bottom"
                android:layout_marginTop="@dimen/dp_15"
                android:background="@color/transparent"
                android:inputType="numberDecimal"
                android:textColor="@color/color_title"
                android:textSize="@dimen/font_size_common"
                app:height="@dimen/dp_44"
                app:hint="123.123123123"
                app:inputType="number"
                app:textSize="14sp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_6">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/current"
                    android:textColor="@color/color_title_sub"
                    android:textSize="12sp" />

                <TextView
                    android:id="@+id/tvCurBaseFee"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/color_title_sub"
                    android:textSize="12sp"
                    tools:text="122.68 GWEI" />

                <TextView
                    android:id="@+id/tvBaseFeeValue"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="end"
                    android:textColor="@color/transparent_80_white"
                    android:textSize="15sp"
                    tools:text="~0.00123123123" />

                <TextView
                    android:id="@+id/tvBaseFeeSymbol"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp_6"
                    android:text="IOTX"
                    android:textColor="@color/color_617AFF"
                    android:textSize="15sp" />


            </LinearLayout>

            <LinearLayout
                android:gravity="center_vertical"
                android:layout_marginTop="@dimen/dp_28"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/priority_fee_with_unit"
                    android:textColor="@color/color_title_sub"
                    android:textSize="14sp" />
                <ImageView
                    android:id="@+id/ivPriorityFee"
                    android:layout_marginStart="@dimen/dp_4"
                    android:src="@drawable/ic_warning"
                    android:layout_width="@dimen/dp_24"
                    android:layout_height="@dimen/dp_24"/>
            </LinearLayout>


            <io.iotex.iopay.ui.widget.EditTextStateful
                android:id="@+id/etPriorityFee"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="bottom"
                android:layout_marginTop="@dimen/dp_15"
                android:background="@drawable/selector_input"
                android:inputType="numberDecimal"
                android:textColor="@color/color_title"
                android:textSize="@dimen/font_size_common"
                app:height="@dimen/dp_44"
                app:hint="123.123123123"
                app:inputType="number"
                app:textSize="14sp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_6">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/current"
                    android:textColor="@color/color_title_sub"
                    android:textSize="12sp" />

                <TextView
                    android:id="@+id/tvCurPriorityFee"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/color_title_sub"
                    android:textSize="12sp"
                    tools:text="122.68 GWEI" />

                <TextView
                    android:id="@+id/tvPriorityFeeValue"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="end"
                    android:textColor="@color/transparent_80_white"
                    android:textSize="15sp"
                    tools:text="~0.00123123123" />

                <TextView
                    android:id="@+id/tvPriorityFeeSymbol"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp_6"
                    android:text="IOTX"
                    android:textColor="@color/color_617AFF"
                    android:textSize="15sp" />


            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_1"
                android:layout_marginTop="@dimen/dp_28"
                android:layout_marginBottom="@dimen/dp_28"
                android:background="@color/color_4d808080" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center">

                <CheckBox
                    android:id="@+id/checkbox"
                    android:layout_width="@dimen/dp_18"
                    android:layout_height="@dimen/dp_18"
                    android:layout_margin="@dimen/dp_3"
                    android:background="@drawable/selector_check_box"
                    android:button="@null" />

                <TextView
                    android:id="@+id/tvAdvance"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp_12"
                    android:text="@string/save_selection"
                    android:textColor="@color/color_title_sub"
                    android:textSize="14sp" />

            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_1"
                android:layout_marginTop="@dimen/dp_28"
                android:layout_marginBottom="@dimen/dp_28"
                android:background="@color/color_4d808080" />

            <LinearLayout
                android:id="@+id/llDisplayGasLimit"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/gas_limit"
                    android:textColor="@color/color_title_sub"
                    android:textSize="14sp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_marginStart="@dimen/dp_4"
                    android:textColor="@color/color_title_sub"
                    android:textSize="14sp"
                    tools:text="21000" />

                <TextView
                    android:id="@+id/tvEditGasLimit"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp_4"
                    android:text="@string/edit"
                    android:textColor="@color/color_617AFF"
                    android:textSize="14sp" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/llEditGasLimit"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="gone">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/gas_limit"
                    android:textColor="@color/gray_CCB4B8CB"
                    android:textSize="14sp" />

                <io.iotex.iopay.ui.widget.EditTextStateful
                    android:id="@+id/etGasLimit"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="bottom"
                    android:layout_marginTop="@dimen/dp_8"
                    android:background="@drawable/selector_input"
                    android:inputType="numberDecimal"
                    android:textColor="@color/white"
                    android:textSize="@dimen/font_size_common"
                    app:height="@dimen/dp_56"
                    app:hint="123.123123123"
                    app:inputType="number"
                    app:textSize="16sp" />

            </LinearLayout>

            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/btnConfirm"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_44"
                android:layout_marginTop="@dimen/dp_40"
                android:layout_marginBottom="@dimen/dp_20"
                android:background="@drawable/btn_shape_gradient_common"
                android:text="@string/confirm"
                android:textAllCaps="false"
                android:textColor="@color/white"
                android:textSize="14sp" />

        </LinearLayout>
    </ScrollView>
</layout>
