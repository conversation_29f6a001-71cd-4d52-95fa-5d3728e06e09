<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

    <LinearLayout
        android:id="@+id/content"
        android:layout_gravity="bottom"
        android:gravity="center_horizontal"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/shape_back_dialog_bottom"
        android:orientation="vertical"
        android:showDividers="middle">

        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_20"
            android:text="@string/authorized_network"
            android:textStyle="bold"
            android:textColor="@color/color_title"
            android:textSize="17sp" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerView"
            android:layout_width="match_parent"
            android:layout_marginTop="@dimen/dp_20"
            android:layout_marginStart="@dimen/dp_15"
            android:layout_marginEnd="@dimen/dp_15"
            android:layout_height="wrap_content" />

    </LinearLayout>

</layout>