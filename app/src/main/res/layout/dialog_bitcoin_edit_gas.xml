<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:background="@drawable/shape_back_dialog_bottom"
        android:orientation="vertical"
        android:paddingStart="@dimen/dp_20"
        android:paddingTop="@dimen/dp_20"
        android:paddingEnd="@dimen/dp_20">

        <LinearLayout
            android:id="@+id/mTlNav"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/dp_20"
            android:gravity="center_vertical">

            <ImageView
                android:id="@+id/ivBack"
                android:layout_width="@dimen/dp_16"
                android:layout_height="@dimen/dp_16"
                android:src="@drawable/ic_arrow_left_white" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/dp_16"
                android:gravity="center"
                android:text="@string/edit_gas_fee"
                android:textColor="@color/color_title"
                android:textSize="@dimen/font_size_middle"
                android:textStyle="bold" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/llSlowGasFee"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_64"
            android:layout_marginBottom="@dimen/dp_16"
            android:background="@drawable/shape_card_back"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingStart="@dimen/dp_12"
            android:paddingTop="@dimen/dp_10"
            android:paddingEnd="@dimen/dp_12"
            android:paddingBottom="@dimen/dp_10">

            <ImageView
                android:layout_width="@dimen/dp_24"
                android:layout_height="@dimen/dp_24"
                android:src="@drawable/ic_fee_slow" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_8"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/slow"
                    android:textColor="@color/color_title_sub"
                    android:textSize="15sp" />

                <TextView
                    android:id="@+id/tvSlowFee"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp_2"
                    android:textColor="@color/color_title_thr"
                    android:textSize="12sp"
                    tools:text="20 Sat/vB" />
            </LinearLayout>

            <View
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="end"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tvSlowTime"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/transaction_time"
                    android:textColor="@color/color_title_sub"
                    android:textSize="15sp" />

                <TextView
                    android:id="@+id/tvSlowSpentFee"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp_2"
                    android:visibility="gone"
                    android:textColor="@color/color_999999"
                    android:textSize="12sp"
                    tools:text="0.8 BTC ≈ $1.96" />

            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:id="@+id/llAverageGasFee"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_64"
            android:layout_marginBottom="@dimen/dp_16"
            android:background="@drawable/shape_card_back"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingStart="@dimen/dp_12"
            android:paddingTop="@dimen/dp_10"
            android:paddingEnd="@dimen/dp_12"
            android:paddingBottom="@dimen/dp_10">

            <ImageView
                android:layout_width="@dimen/dp_24"
                android:layout_height="@dimen/dp_24"
                android:src="@drawable/ic_fee_average" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_8"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/average"
                    android:textColor="@color/color_title_sub"
                    android:textSize="15sp" />

                <TextView
                    android:id="@+id/tvAverageFee"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp_2"
                    android:textColor="@color/color_title_thr"
                    android:textSize="12sp"
                    tools:text="20 Sat/vB" />
            </LinearLayout>

            <View
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="end"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tvAverageTime"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/transaction_time"
                    android:textColor="@color/color_title_sub"
                    android:textSize="15sp" />

                <TextView
                    android:id="@+id/tvAverageSpentFee"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp_2"
                    android:visibility="gone"
                    android:textColor="@color/color_999999"
                    android:textSize="12sp"
                    tools:text="0.8 BTC ≈ $1.96" />

            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:id="@+id/llFastGasFee"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_64"
            android:layout_marginBottom="@dimen/dp_16"
            android:background="@drawable/shape_card_back"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingStart="@dimen/dp_12"
            android:paddingTop="@dimen/dp_10"
            android:paddingEnd="@dimen/dp_12"
            android:paddingBottom="@dimen/dp_10">

            <ImageView
                android:layout_width="@dimen/dp_24"
                android:layout_height="@dimen/dp_24"
                android:src="@drawable/ic_fee_fast" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_8"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/fast"
                    android:textColor="@color/color_title_sub"
                    android:textSize="15sp" />

                <TextView
                    android:id="@+id/tvFastFee"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp_2"
                    android:textColor="@color/color_title_thr"
                    android:textSize="12sp"
                    tools:text="20 Sat/vB" />
            </LinearLayout>

            <View
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="end"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tvFastTime"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/transaction_time"
                    android:textColor="@color/color_title_sub"
                    android:textSize="15sp" />

                <TextView
                    android:id="@+id/tvFastSpentFee"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp_2"
                    android:visibility="gone"
                    android:textColor="@color/color_999999"
                    android:textSize="12sp"
                    tools:text="0.8 BTC ≈ $1.96" />

            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:id="@+id/llCustomizeGasFee"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/dp_16"
            android:background="@drawable/shape_card_back"
            android:orientation="vertical"
            android:paddingStart="@dimen/dp_12"
            android:paddingTop="@dimen/dp_10"
            android:paddingEnd="@dimen/dp_12"
            android:paddingBottom="@dimen/dp_10">

            <LinearLayout
                android:id="@+id/llEdit"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical">

                <ImageView
                    android:layout_width="@dimen/dp_24"
                    android:layout_height="@dimen/dp_24"
                    android:src="@drawable/ic_fee_costomize" />

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp_8"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/customize"
                        android:textColor="@color/color_title_sub"
                        android:textSize="15sp" />

                    <TextView
                        android:id="@+id/tvCustomizeFee"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp_2"
                        android:textColor="@color/color_title_thr"
                        android:textSize="12sp"
                        android:text="0 Sat/vB" />
                </LinearLayout>

                <View
                    android:layout_width="0dp"
                    android:layout_height="@dimen/dp_1"
                    android:layout_weight="1" />

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center">

                    <ImageView
                        android:id="@+id/ivCustomEdit"
                        android:src="@drawable/icon_edit_green"
                        android:layout_width="@dimen/dp_18"
                        android:layout_height="@dimen/dp_18" />

                    <ImageView
                        android:id="@+id/ivCustomArrow"
                        android:layout_marginStart="@dimen/dp_10"
                        android:rotation="180"
                        android:src="@drawable/ic_arrow_down"
                        android:layout_width="@dimen/dp_14"
                        android:layout_height="@dimen/dp_14"/>

                </LinearLayout>


            </LinearLayout>

            <LinearLayout
                android:id="@+id/llCustomizePanel"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone"
                tools:visibility="visible"
                android:orientation="vertical">

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1px"
                    android:layout_marginTop="@dimen/dp_8"
                    android:alpha="0.3"
                    android:background="@color/color_line_diver" />

                <TextView
                    android:id="@+id/tvCurCustomizeFee"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp_16"
                    android:alpha="0.5"
                    android:textColor="@color/color_title_sub"
                    android:textSize="12sp"
                    tools:text="20 Sat/vB" />

                <io.iotex.iopay.ui.widget.EditTextStateful
                    android:id="@+id/etCustomizeFee"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="bottom"
                    android:layout_marginBottom="16dp"
                    android:layout_marginTop="@dimen/dp_8"
                    android:background="@drawable/shape_stroke_4d808080_r4"
                    android:textColor="@color/color_title"
                    android:textSize="@dimen/font_size_common"
                    app:height="@dimen/dp_40"
                    app:inputType="number"
                    app:textSize="14sp" />

                <TextView
                    android:id="@+id/tvCustomizeConfirm"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp_44"
                    android:layout_marginTop="@dimen/dp_30"
                    android:layout_marginBottom="@dimen/dp_18"
                    android:alpha="0.5"
                    android:background="@drawable/btn_shape_gradient_common"
                    android:enabled="false"
                    android:gravity="center"
                    android:text="@string/confirm"
                    android:textColor="@android:color/white" />

            </LinearLayout>

        </LinearLayout>

        <TextView
            android:id="@+id/tvConfirm"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_44"
            android:layout_marginBottom="@dimen/dp_18"
            android:background="@drawable/btn_shape_gradient_common"
            android:gravity="center"
            android:text="@string/confirm"
            android:textColor="@android:color/white" />


    </LinearLayout>

</layout>
