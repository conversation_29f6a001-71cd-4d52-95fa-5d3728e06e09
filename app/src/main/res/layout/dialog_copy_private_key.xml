<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">
    <LinearLayout
        android:orientation="vertical"
        android:background="@drawable/shape_back_dialog_center"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <ImageView
            android:id="@+id/ivClose"
            android:src="@drawable/ic_close"
            android:padding="10dp"
            android:layout_margin="5dp"
            android:layout_gravity="end"
            android:layout_width="44dp"
            android:layout_height="44dp"/>
        <LinearLayout
            android:orientation="vertical"
            android:paddingStart="20dp"
            android:paddingEnd="20dp"
            android:paddingBottom="30dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <TextView
                android:text="@string/for_the_safety"
                android:textStyle="bold"
                android:textSize="17sp"
                android:textColor="@color/color_title_sub"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"/>
            <TextView
                android:text="@string/please_copy_in_sections"
                android:textSize="17sp"
                android:textStyle="bold"
                android:textColor="@color/color_title_sub"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"/>

            <LinearLayout
                android:layout_marginTop="@dimen/dp_12"
                android:background="@drawable/shape_card_back"
                android:layout_width="match_parent"
                android:gravity="center"
                android:padding="@dimen/dp_15"
                android:layout_height="wrap_content">
                <TextView
                    android:id="@+id/tvKeyOne"
                    android:textSize="14sp"
                    android:alpha="0.6"
                    android:layout_weight="1"
                    android:textColor="@color/color_title_sub"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"/>
                <ImageView
                    android:id="@+id/ivEyeOne"
                    android:layout_marginStart="@dimen/dp_10"
                    android:layout_marginEnd="@dimen/dp_20"
                    android:src="@drawable/icon_eye_close"
                    android:layout_width="@dimen/dp_20"
                    android:layout_height="@dimen/dp_20"/>
                <ImageView
                    android:id="@+id/ivCopyOne"
                    android:src="@drawable/icon_copy"
                    android:layout_width="@dimen/dp_20"
                    android:layout_height="@dimen/dp_20"/>
            </LinearLayout>
            <LinearLayout
                android:layout_marginTop="@dimen/dp_12"
                android:background="@drawable/shape_card_back"
                android:layout_width="match_parent"
                android:gravity="center"
                android:padding="@dimen/dp_15"
                android:layout_height="wrap_content">
                <TextView
                    android:id="@+id/tvKeyTwo"
                    android:textSize="14sp"
                    android:alpha="0.6"
                    android:layout_weight="1"
                    android:textColor="@color/color_title_sub"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"/>
                <ImageView
                    android:id="@+id/ivEyeTwo"
                    android:layout_marginStart="@dimen/dp_10"
                    android:layout_marginEnd="@dimen/dp_20"
                    android:src="@drawable/icon_eye_close"
                    android:layout_width="@dimen/dp_20"
                    android:layout_height="@dimen/dp_20"/>
                <ImageView
                    android:id="@+id/ivCopyTwo"
                    android:src="@drawable/icon_copy"
                    android:layout_width="@dimen/dp_20"
                    android:layout_height="@dimen/dp_20"/>
            </LinearLayout>
            <TextView
                android:id="@+id/tvCopyAll"
                android:layout_marginTop="@dimen/dp_25"
                android:text="@string/copy_all"
                android:textColor="@color/white"
                android:textSize="14sp"
                android:gravity="center"
                android:background="@drawable/btn_shape_gradient_common"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_40"/>
        </LinearLayout>
    </LinearLayout>

</layout>