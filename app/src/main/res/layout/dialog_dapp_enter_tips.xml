<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

    <LinearLayout
        android:orientation="vertical"
        android:gravity="center"
        android:background="@drawable/shape_back_dialog_center"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <ImageView
            android:id="@+id/ivClose"
            android:src="@drawable/ic_close"
            android:padding="10dp"
            android:layout_margin="5dp"
            android:layout_gravity="end"
            android:layout_width="44dp"
            android:layout_height="44dp"/>
        <ImageView
            android:layout_marginTop="-10dp"
            android:src="@drawable/icon_dapp_enter_tip"
            android:layout_width="64dp"
            android:layout_height="64dp"/>

        <TextView
            android:id="@+id/tvTitle"
            android:textStyle="bold"
            android:layout_marginStart="@dimen/dp_50"
            android:layout_marginEnd="@dimen/dp_50"
            android:gravity="center"
            android:layout_marginTop="5dp"
            android:textColor="@color/color_title"
            android:textSize="14sp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>
        <TextView
            android:id="@+id/tvContent"
            android:textColor="@color/color_title_sub"
            android:textSize="14sp"
            android:background="@drawable/shape_card_back"
            android:padding="@dimen/dp_15"
            android:layout_marginTop="@dimen/dp_20"
            android:layout_marginStart="15dp"
            android:layout_marginEnd="15dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>
        <LinearLayout
            android:id="@+id/llCheckboxWrapper"
            android:gravity="center_vertical"
            android:layout_marginTop="@dimen/dp_20"
            android:layout_marginStart="@dimen/dp_20"
            android:layout_marginEnd="@dimen/dp_20"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <CheckBox
                android:id="@+id/checkbox"
                android:button="@null"
                android:background="@drawable/selector_check_box"
                android:layout_width="@dimen/dp_16"
                android:layout_height="@dimen/dp_16"/>
            <TextView
                android:layout_marginStart="@dimen/dp_10"
                android:text="@string/i_understood_do_not_show_this_again"
                android:textColor="@color/color_title_sub"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
        </LinearLayout>
        <TextView
            android:id="@+id/tvGetIt"
            android:text="@string/i_got_it"
            android:textSize="14sp"
            android:background="@drawable/btn_shape_gradient_common"
            android:textColor="@color/white"
            android:gravity="center"
            android:layout_marginTop="25dp"
            android:layout_marginStart="20dp"
            android:layout_marginEnd="20dp"
            android:layout_marginBottom="30dp"
            android:layout_width="match_parent"
            android:layout_height="36dp"/>
    </LinearLayout>

</layout>