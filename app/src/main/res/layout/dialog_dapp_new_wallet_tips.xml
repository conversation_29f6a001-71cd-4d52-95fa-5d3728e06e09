<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

    <LinearLayout
        android:orientation="vertical"
        android:gravity="center"
        android:layout_gravity="bottom"
        android:background="@drawable/shape_back_dialog_center"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <ImageView
            android:id="@+id/ivClose"
            android:src="@drawable/ic_close"
            android:padding="10dp"
            android:layout_margin="5dp"
            android:layout_gravity="end"
            android:layout_width="44dp"
            android:layout_height="44dp"/>
        <ImageView
            android:layout_marginTop="-10dp"
            android:src="@drawable/icon_dapp_new_wallet"
            android:layout_width="64dp"
            android:layout_height="64dp"/>

        <TextView
            android:textStyle="bold"
            android:layout_marginStart="@dimen/dp_50"
            android:layout_marginEnd="@dimen/dp_50"
            android:gravity="center"
            android:text="@string/connect_wallet"
            android:layout_marginTop="5dp"
            android:textColor="@color/color_title"
            android:textSize="14sp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>
        <TextView
            android:text="@string/since_you_are_currently_in_visitor_mode"
            android:textColor="@color/color_title_sub"
            android:textSize="14sp"
            android:layout_marginTop="@dimen/dp_20"
            android:layout_marginStart="15dp"
            android:layout_marginEnd="15dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

        <LinearLayout
            android:layout_marginTop="25dp"
            android:layout_marginStart="20dp"
            android:layout_marginEnd="20dp"
            android:layout_marginBottom="30dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <TextView
                android:id="@+id/tvVisitor"
                android:text="@string/import_wallet_button"
                android:textSize="14sp"
                android:background="@drawable/stroke_617aff_r6"
                android:textColor="@color/color_617aff"
                android:gravity="center"
                android:layout_marginEnd="@dimen/dp_6"
                android:layout_weight="1"
                android:layout_width="0dp"
                android:layout_height="36dp"/>
            <TextView
                android:id="@+id/tvNewWallet"
                android:text="@string/wallet_new_button"
                android:textSize="14sp"
                android:background="@drawable/btn_shape_gradient_common"
                android:textColor="@color/white"
                android:gravity="center"
                android:layout_marginStart="@dimen/dp_6"
                android:layout_weight="1"
                android:layout_width="0dp"
                android:layout_height="36dp"/>
        </LinearLayout>
    </LinearLayout>

</layout>