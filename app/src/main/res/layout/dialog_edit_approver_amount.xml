<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">
    <LinearLayout
        android:layout_gravity="bottom"
        android:background="@drawable/shape_back_dialog_bottom"
        android:orientation="vertical"
        android:paddingStart="@dimen/common_padding_vertical"
        android:paddingEnd="@dimen/common_padding_vertical"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <androidx.appcompat.widget.Toolbar
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:contentInsetStart="0dp">

            <ImageView
                android:id="@+id/mIvBack"
                android:layout_width="@dimen/dp_24"
                android:layout_height="@dimen/dp_24"
                android:src="@drawable/icon_dialog_back" />

            <TextView
                android:layout_gravity="center"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/edit_approve_amount"
                android:textColor="@color/color_title"
                android:textSize="@dimen/font_size_middle"
                android:textStyle="bold" />

        </androidx.appcompat.widget.Toolbar>

        <com.makeramen.roundedimageview.RoundedImageView
            android:id="@+id/ivLogo"
            android:layout_gravity="center_horizontal"
            app:riv_corner_radius="@dimen/dp_20"
            android:layout_marginTop="@dimen/dp_20"
            android:src="@drawable/icon_token_default"
            android:layout_width="@dimen/dp_40"
            android:layout_height="@dimen/dp_40"/>
        <TextView
            android:id="@+id/tvSymbol"
            tools:text="DWIN"
            android:layout_gravity="center_horizontal"
            android:textSize="14sp"
            android:layout_marginTop="@dimen/dp_12"
            android:textColor="@color/color_title_sub"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

        <LinearLayout
            android:layout_marginTop="@dimen/dp_20"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <TextView
                android:textColor="@color/color_title_sub"
                android:textSize="14sp"
                android:layout_weight="1"
                android:text="@string/set_approve_allowance"
                android:layout_width="0dp"
                android:layout_height="wrap_content"/>
            <TextView
                android:id="@+id/tvDefault"
                android:textColor="@color/color_617aff"
                android:textSize="14sp"
                android:text="@string/use_default"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
        </LinearLayout>
        <LinearLayout
            android:gravity="center"
            android:background="@drawable/shape_card_back"
            android:layout_marginTop="@dimen/dp_8"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_48">
            <EditText
                android:id="@+id/etAmount"
                android:layout_weight="1"
                android:layout_width="0dp"
                android:textSize="14sp"
                android:inputType="numberDecimal"
                android:textColorHint="@color/color_hint"
                android:layout_marginStart="@dimen/dp_16"
                android:hint="@string/enter_a_number"
                android:background="@color/transparent"
                android:textColor="@color/color_title"
                android:layout_height="match_parent"/>
            <TextView
                android:id="@+id/tvMax"
                android:textColor="@color/color_617aff"
                android:textSize="14sp"
                android:padding="@dimen/dp_12"
                android:text="@string/max_big"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
        </LinearLayout>

        <TextView
            android:layout_marginTop="@dimen/dp_10"
            android:textColor="@color/color_title_sub"
            android:textSize="14sp"
            android:text="@string/only_enter_a_number_that_you"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>
        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/mBtnSave"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_40"
            android:layout_marginTop="@dimen/common_padding_large_3"
            android:layout_marginBottom="@dimen/common_padding_vertical"
            android:background="@drawable/btn_shape_gradient_common"
            android:text="@string/save"
            android:textAllCaps="false"
            android:textColor="@color/white" />
    </LinearLayout>

</layout>