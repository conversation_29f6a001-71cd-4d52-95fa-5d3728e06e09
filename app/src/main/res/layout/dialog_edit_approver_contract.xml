<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">
    <LinearLayout
        android:layout_gravity="bottom"
        android:background="@drawable/shape_back_dialog_bottom"
        android:orientation="vertical"
        android:paddingStart="@dimen/common_padding_vertical"
        android:paddingEnd="@dimen/common_padding_vertical"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <androidx.appcompat.widget.Toolbar
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:contentInsetStart="0dp">

            <ImageView
                android:id="@+id/mIvBack"
                android:layout_width="@dimen/dp_24"
                android:layout_height="@dimen/dp_24"
                android:src="@drawable/icon_dialog_back" />

            <TextView
                android:id="@+id/tvTitle"
                android:layout_gravity="center"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/edit_contract_name"
                android:textColor="@color/color_title"
                android:textSize="@dimen/font_size_middle"
                android:textStyle="bold" />

        </androidx.appcompat.widget.Toolbar>

        <LinearLayout
            android:id="@+id/llCertified"
            android:gravity="center_vertical"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_48">
            <TextView
                android:textColor="@color/color_title_sub"
                android:textSize="14sp"
                android:text="@string/contract_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
            <TextView
                android:id="@+id/mTvContract"
                android:gravity="end"
                android:layout_marginStart="@dimen/dp_15"
                tools:text="name"
                android:textColor="@color/color_title_sub"
                android:textSize="14sp"
                android:layout_weight="1"
                android:layout_width="0dp"
                android:layout_height="wrap_content"/>

            <ImageView
                android:src="@drawable/icon_certified"
                android:layout_marginStart="@dimen/dp_6"
                android:layout_width="@dimen/dp_24"
                android:layout_height="@dimen/dp_24"/>
        </LinearLayout>

        <LinearLayout
            android:layout_marginTop="@dimen/dp_10"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_46">

            <TextView
                android:textColor="@color/color_title_sub"
                android:textSize="14sp"
                android:text="@string/contract_addr"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
            <TextView
                android:id="@+id/tvContract"
                android:textColor="@color/color_title_sub"
                android:textSize="14sp"
                android:gravity="end"
                android:layout_marginStart="@dimen/dp_15"
                tools:text="io1vcnpavaw9y9uf8h44rt9szjtwu3ltt8f94kha4"
                android:layout_weight="1"
                android:layout_width="0dp"
                android:layout_height="wrap_content"/>
            <ImageView
                android:id="@+id/ivCopy"
                android:src="@drawable/ic_copy"
                android:padding="@dimen/dp_5"
                android:layout_width="@dimen/dp_26"
                android:layout_height="@dimen/dp_26"/>
        </LinearLayout>

        <TextView
            android:id="@+id/tvWeb"
            android:layout_marginTop="@dimen/dp_10"
            android:layout_gravity="end"
            android:textColor="@color/color_617aff"
            android:textSize="14sp"
            android:text="@string/view_on_explorer"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

        <LinearLayout
            android:id="@+id/llEdit"
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:layout_marginTop="@dimen/dp_20"
                android:textColor="@color/color_title_sub"
                android:textSize="14sp"
                android:text="@string/contract_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>

            <EditText
                android:id="@+id/etName"
                android:layout_width="match_parent"
                android:textSize="14sp"
                android:paddingStart="@dimen/dp_16"
                android:paddingEnd="@dimen/dp_16"
                app:inputType="numberDecimal"
                android:layout_marginTop="@dimen/dp_8"
                android:textColorHint="@color/color_hint"
                android:hint="@string/name"
                android:background="@drawable/shape_card_back"
                android:textColor="@color/color_title"
                android:layout_height="@dimen/dp_48"/>

            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/mBtnSave"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_40"
                android:layout_marginTop="@dimen/common_padding_large_3"
                android:layout_marginBottom="@dimen/common_padding_vertical"
                android:background="@drawable/btn_shape_gradient_common"
                android:text="@string/save"
                android:textAllCaps="false"
                android:textColor="@color/white" />
        </LinearLayout>
    </LinearLayout>

</layout>