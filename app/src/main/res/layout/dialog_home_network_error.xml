<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <FrameLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginStart="@dimen/dp_36"
        android:layout_marginEnd="@dimen/dp_36">

        <LinearLayout
            android:id="@+id/mLlRoot"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/shape_back_dialog_center"
            android:gravity="center"
            android:minHeight="160dp"
            android:orientation="vertical"
            android:paddingTop="@dimen/dp_35"
            android:paddingBottom="@dimen/dp_35">

            <ImageView
                android:id="@+id/ivIcon"
                android:layout_width="@dimen/dp_72"
                android:layout_height="@dimen/dp_72"
                android:src="@drawable/icon_home_network_error" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_24"
                android:text="@string/network_failed"
                android:textColor="@color/color_title"
                android:textSize="17sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tvDescription"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_27"
                android:layout_marginTop="@dimen/dp_32"
                android:layout_marginEnd="@dimen/dp_27"
                android:gravity="center"
                android:text="@string/there_appears_to_be_an_issue_with_the_network"
                android:textColor="@color/color_title_sub"
                android:textSize="14sp" />

            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/btnTryAgain"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_35"
                android:layout_marginStart="@dimen/dp_20"
                android:layout_marginTop="@dimen/dp_52"
                android:layout_marginEnd="@dimen/dp_20"
                android:background="@drawable/btn_shape_gradient_common"
                android:text="@string/i_got_it"
                android:textAllCaps="false"
                android:textColor="@color/white"
                android:textSize="14sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent" />
        </LinearLayout>

        <ImageView
            android:id="@+id/ivClose"
            android:layout_width="20dp"
            android:layout_height="wrap_content"
            android:layout_gravity="end"
            android:layout_marginTop="@dimen/dp_15"
            android:layout_marginEnd="@dimen/dp_15"
            android:adjustViewBounds="true"
            android:src="@drawable/ic_close" />

    </FrameLayout>

</layout>
