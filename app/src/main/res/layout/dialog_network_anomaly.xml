<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:layout_marginStart="@dimen/dp_38"
    android:layout_marginEnd="@dimen/dp_38"
    android:background="@drawable/shape_back_dialog_center"
    android:gravity="center_horizontal"
    android:orientation="vertical"
    android:paddingStart="@dimen/dp_20"
    android:paddingEnd="@dimen/dp_20">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/mIvClose"
        android:layout_width="@dimen/dp_24"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:adjustViewBounds="true"
        android:src="@drawable/icon_close_grey"
        android:tint="@color/color_title"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/mIvThumb"
        android:layout_width="@dimen/dp_64"
        android:layout_height="wrap_content"
        android:layout_marginTop="40dp"
        android:adjustViewBounds="true"
        android:src="@drawable/ic_network_anomaly"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/mTvTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="5dp"
        android:textColor="@color/color_title"
        android:textSize="17sp"
        android:textStyle="bold"
        android:text="@string/network_anomaly"
        app:layout_constraintEnd_toEndOf="@+id/mIvThumb"
        app:layout_constraintStart_toStartOf="@+id/mIvThumb"
        app:layout_constraintTop_toBottomOf="@+id/mIvThumb" />

    <TextView
        android:id="@+id/mTvDesc"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="14sp"
        android:textColor="@color/color_title_sub"
        android:layout_marginTop="24dp"
        android:text="@string/network_anomaly_desc"
        app:layout_constraintEnd_toEndOf="@+id/mIvThumb"
        app:layout_constraintHorizontal_bias="0.477"
        app:layout_constraintStart_toStartOf="@+id/mIvThumb"
        app:layout_constraintTop_toBottomOf="@+id/mTvTitle" />

    <LinearLayout
        android:id="@+id/llCheck"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/mTvDesc"
        android:gravity="center_vertical"
        android:layout_marginTop="@dimen/dp_20"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <CheckBox
            android:id="@+id/checkbox"
            android:button="@null"
            android:background="@drawable/selector_check_box"
            android:layout_width="@dimen/dp_16"
            android:layout_height="@dimen/dp_16"/>
        <TextView
            android:layout_marginStart="@dimen/dp_10"
            android:text="@string/i_understood_do_not_show_this_again_today"
            android:textColor="@color/color_title_sub"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>
    </LinearLayout>

    <androidx.appcompat.widget.AppCompatButton
        android:id="@+id/mBtnSetting"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_35"
        android:layout_marginTop="30dp"
        android:layout_marginBottom="30dp"
        android:background="@drawable/btn_shape_gradient_common"
        android:text="@string/go_to_setting"
        android:textAllCaps="false"
        android:textColor="@color/white"
        android:textSize="14sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/llCheck"
        tools:layout_editor_absoluteX="32dp" />

</androidx.constraintlayout.widget.ConstraintLayout>