<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:background="@drawable/shape_back_dialog_center"
        android:gravity="center_horizontal"
        android:orientation="vertical"
        android:paddingTop="@dimen/dp_40"
        android:paddingBottom="@dimen/dp_48">

        <ImageView
            android:layout_width="@dimen/dp_64"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:adjustViewBounds="true"
            android:src="@drawable/icon_alert_warning" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_50"
            android:layout_marginTop="@dimen/dp_28"
            android:layout_marginEnd="@dimen/dp_50"
            android:gravity="center_horizontal"
            android:text="@string/not_support_aa_wallet_caption"
            android:textColor="@color/color_title_sub"
            android:textSize="14sp" />

        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/btnSwitchWallet"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_36"
            android:layout_marginTop="@dimen/dp_48"
            android:layout_marginStart="@dimen/dp_50"
            android:layout_marginEnd="@dimen/dp_50"
            android:background="@drawable/btn_shape_gradient_common"
            android:text="@string/switch_wallet"
            android:textAllCaps="false"
            android:textColor="@color/white"
            android:textSize="14sp" />
    </LinearLayout>
</layout>
