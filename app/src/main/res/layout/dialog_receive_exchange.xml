<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <LinearLayout
        android:orientation="vertical"
        android:layout_gravity="bottom"
        android:background="@drawable/shape_back_dialog_bottom"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <ImageView
            android:id="@+id/ivClose"
            android:layout_gravity="end"
            android:src="@drawable/ic_close"
            android:padding="@dimen/dp_10"
            android:layout_width="@dimen/dp_44"
            android:layout_height="@dimen/dp_44"/>

        <TextView
            android:textSize="16sp"
            android:layout_gravity="center"
            android:textColor="@color/color_title"
            android:text="@string/how_to_receive_from_exchanges"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

        <LinearLayout
            android:layout_marginTop="@dimen/dp_20"
            android:layout_marginBottom="@dimen/dp_20"
            android:padding="@dimen/dp_30"
            android:layout_marginStart="@dimen/dp_16"
            android:layout_marginEnd="@dimen/dp_16"
            android:background="@drawable/shape_card_back"
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <TextView
                android:textSize="16sp"
                android:textColor="@color/color_title"
                android:text="@string/steps_in_the_exchange_app"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
            <LinearLayout
                android:layout_marginTop="@dimen/dp_20"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <LinearLayout
                    android:orientation="vertical"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content">
                    <View
                        android:layout_marginTop="@dimen/dp_5"
                        android:background="@drawable/shape_circle_855eff"
                        android:layout_width="@dimen/dp_9"
                        android:layout_height="@dimen/dp_9"/>
                    <io.iotex.iopay.ui.widget.DashLineView
                        android:layout_width="@dimen/dp_1"
                        app:lineOrientation="vertical"
                        app:lineColor="@color/color_line_diver"
                        android:layout_marginStart="@dimen/dp_4"
                        android:layout_height="@dimen/dp_30"/>
                </LinearLayout>
                <TextView
                    android:textSize="14sp"
                    android:layout_marginStart="@dimen/dp_8"
                    android:textColor="@color/color_title_sub"
                    android:text="@string/select_iotx_to_withdraw"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"/>
            </LinearLayout>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <LinearLayout
                    android:orientation="vertical"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content">
                    <io.iotex.iopay.ui.widget.DashLineView
                        android:layout_width="@dimen/dp_1"
                        android:layout_marginTop="@dimen/dp_1"
                        app:lineOrientation="vertical"
                        app:lineColor="@color/color_line_diver"
                        android:layout_marginStart="@dimen/dp_4"
                        android:layout_height="@dimen/dp_4"/>
                    <View
                        android:background="@drawable/shape_circle_855eff"
                        android:layout_width="@dimen/dp_9"
                        android:layout_height="@dimen/dp_9"/>
                    <io.iotex.iopay.ui.widget.DashLineView
                        android:layout_width="@dimen/dp_1"
                        app:lineOrientation="vertical"
                        app:lineColor="@color/color_line_diver"
                        android:layout_marginStart="@dimen/dp_4"
                        android:layout_height="@dimen/dp_100"/>
                </LinearLayout>
                <LinearLayout
                    android:orientation="vertical"
                    android:layout_marginStart="@dimen/dp_8"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content">
                    <TextView
                        android:textSize="14sp"
                        android:textColor="@color/color_title_sub"
                        android:text="@string/enter_your_iopay_wallet_address_as_the_recipient"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"/>
                    <LinearLayout
                        android:layout_marginTop="@dimen/dp_12"
                        android:gravity="center_vertical"
                        android:background="@drawable/shape_card_back_on_card_dark_r6"
                        android:layout_width="wrap_content"
                        android:layout_height="@dimen/dp_36">
                        <TextView
                            android:id="@+id/tvAddress"
                            android:layout_marginStart="@dimen/dp_8"
                            android:textSize="14sp"
                            tools:text="0x132k…dvnyad78"
                            android:textColor="@color/color_title_thr"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"/>
                        <ImageView
                            android:id="@+id/ivCopy"
                            android:src="@drawable/icon_copy"
                            android:layout_marginStart="@dimen/dp_8"
                            android:layout_marginEnd="@dimen/dp_10"
                            android:layout_width="@dimen/dp_20"
                            android:layout_height="@dimen/dp_20"/>
                    </LinearLayout>
                </LinearLayout>
            </LinearLayout>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <LinearLayout
                    android:orientation="vertical"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content">
                    <io.iotex.iopay.ui.widget.DashLineView
                        android:layout_width="@dimen/dp_1"
                        android:layout_marginTop="@dimen/dp_1"
                        app:lineOrientation="vertical"
                        app:lineColor="@color/color_line_diver"
                        android:layout_marginStart="@dimen/dp_4"
                        android:layout_height="@dimen/dp_4"/>
                    <View
                        android:background="@drawable/shape_circle_855eff"
                        android:layout_width="@dimen/dp_9"
                        android:layout_height="@dimen/dp_9"/>
                    <io.iotex.iopay.ui.widget.DashLineView
                        android:layout_width="@dimen/dp_1"
                        app:lineOrientation="vertical"
                        app:lineColor="@color/color_line_diver"
                        android:layout_marginStart="@dimen/dp_4"
                        android:layout_height="@dimen/dp_100"/>
                </LinearLayout>
                <LinearLayout
                    android:orientation="vertical"
                    android:layout_marginStart="@dimen/dp_8"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content">
                    <TextView
                        android:textSize="14sp"
                        android:textColor="@color/color_title_sub"
                        android:text="@string/select_the_network_that_matches_your_iopay_wallet_address"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"/>
                    <LinearLayout
                        android:layout_marginTop="@dimen/dp_12"
                        android:gravity="center_vertical"
                        android:background="@drawable/shape_card_back_on_card_dark_r6"
                        android:layout_width="wrap_content"
                        android:layout_height="@dimen/dp_50">
                        <ImageView
                            android:layout_marginStart="@dimen/dp_8"
                            android:src="@drawable/icon_iotex_network_logo"
                            android:layout_width="@dimen/dp_24"
                            android:layout_height="@dimen/dp_24"/>
                        <LinearLayout
                            android:orientation="vertical"
                            android:layout_marginEnd="@dimen/dp_10"
                            android:layout_marginStart="@dimen/dp_8"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content">
                            <TextView
                                android:textSize="14sp"
                                android:text="@string/network"
                                android:textColor="@color/color_title_thr"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"/>
                            <TextView
                                android:textSize="14sp"
                                android:text="@string/iotex_network_mainnet"
                                android:textColor="@color/color_title"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"/>
                        </LinearLayout>
                    </LinearLayout>
                </LinearLayout>
            </LinearLayout>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <LinearLayout
                    android:orientation="vertical"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content">
                    <io.iotex.iopay.ui.widget.DashLineView
                        android:layout_width="@dimen/dp_1"
                        android:layout_marginTop="@dimen/dp_1"
                        app:lineOrientation="vertical"
                        app:lineColor="@color/color_line_diver"
                        android:layout_marginStart="@dimen/dp_4"
                        android:layout_height="@dimen/dp_4"/>
                    <View
                        android:background="@drawable/shape_circle_855eff"
                        android:layout_width="@dimen/dp_9"
                        android:layout_height="@dimen/dp_9"/>
                </LinearLayout>
                <TextView
                    android:textSize="14sp"
                    android:layout_marginStart="@dimen/dp_8"
                    android:textColor="@color/color_title_sub"
                    android:text="@string/enter_amount_confirm_withdrawal"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"/>
            </LinearLayout>
        </LinearLayout>

        <TextView
            android:layout_marginStart="@dimen/dp_16"
            android:textColor="@color/color_title"
            android:textSize="14sp"
            android:text="@string/receive_iotx_from_exchanges"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>
        <LinearLayout
            android:layout_marginStart="@dimen/dp_16"
            android:layout_marginEnd="@dimen/dp_16"
            android:layout_marginTop="@dimen/dp_16"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">
            <LinearLayout
                android:id="@+id/llKuCoin"
                android:gravity="center"
                android:background="@drawable/stroke_receive_exchange_r16"
                android:paddingStart="@dimen/dp_5"
                android:paddingEnd="@dimen/dp_10"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp_32">
                <ImageView
                    android:src="@drawable/icon_exchange_kucoin"
                    android:layout_width="@dimen/dp_24"
                    android:layout_height="@dimen/dp_24"/>
                <TextView
                    android:text="KuCoin"
                    android:textSize="14sp"
                    android:layout_marginStart="@dimen/dp_6"
                    android:textColor="@color/color_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"/>
            </LinearLayout>

            <LinearLayout
                android:id="@+id/llBinance"
                android:layout_marginStart="@dimen/dp_16"
                android:gravity="center"
                android:background="@drawable/stroke_receive_exchange_r16"
                android:paddingStart="@dimen/dp_5"
                android:paddingEnd="@dimen/dp_10"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp_32">
                <ImageView
                    android:src="@drawable/icon_exchange_binance"
                    android:layout_width="@dimen/dp_24"
                    android:layout_height="@dimen/dp_24"/>
                <TextView
                    android:text="Binance"
                    android:textSize="14sp"
                    android:layout_marginStart="@dimen/dp_6"
                    android:textColor="@color/color_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"/>
            </LinearLayout>
            <LinearLayout
                android:id="@+id/llGate"
                android:layout_marginStart="@dimen/dp_16"
                android:gravity="center"
                android:background="@drawable/stroke_receive_exchange_r16"
                android:paddingStart="@dimen/dp_5"
                android:paddingEnd="@dimen/dp_10"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp_32">
                <ImageView
                    android:src="@drawable/icon_exchange_gate"
                    android:layout_width="@dimen/dp_24"
                    android:layout_height="@dimen/dp_24"/>
                <TextView
                    android:text="Gate.io"
                    android:textSize="14sp"
                    android:layout_marginStart="@dimen/dp_6"
                    android:textColor="@color/color_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"/>
            </LinearLayout>
        </LinearLayout>
        <LinearLayout
            android:layout_marginEnd="@dimen/dp_12"
            android:layout_marginTop="@dimen/dp_12"
            android:layout_marginStart="@dimen/dp_16"
            android:layout_marginBottom="@dimen/dp_16"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">
            <LinearLayout
                android:id="@+id/llUpbit"
                android:gravity="center"
                android:background="@drawable/stroke_receive_exchange_r16"
                android:paddingStart="@dimen/dp_5"
                android:paddingEnd="@dimen/dp_10"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp_32">
                <ImageView
                    android:src="@drawable/icon_exchange_updit"
                    android:layout_width="@dimen/dp_24"
                    android:layout_height="@dimen/dp_24"/>
                <TextView
                    android:text="Upbit"
                    android:textSize="14sp"
                    android:layout_marginStart="@dimen/dp_6"
                    android:textColor="@color/color_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"/>
            </LinearLayout>

            <LinearLayout
                android:id="@+id/llBitget"
                android:layout_marginStart="@dimen/dp_16"
                android:gravity="center"
                android:background="@drawable/stroke_receive_exchange_r16"
                android:paddingStart="@dimen/dp_5"
                android:paddingEnd="@dimen/dp_10"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp_32">
                <ImageView
                    android:src="@drawable/icon_exchage_bitget"
                    android:layout_width="@dimen/dp_24"
                    android:layout_height="@dimen/dp_24"/>
                <TextView
                    android:text="Bitget"
                    android:textSize="14sp"
                    android:layout_marginStart="@dimen/dp_6"
                    android:textColor="@color/color_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"/>
            </LinearLayout>
            <LinearLayout
                android:id="@+id/llhtx"
                android:layout_marginStart="@dimen/dp_16"
                android:gravity="center"
                android:background="@drawable/stroke_receive_exchange_r16"
                android:paddingStart="@dimen/dp_5"
                android:paddingEnd="@dimen/dp_10"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp_32">
                <ImageView
                    android:src="@drawable/icon_exchange_htx"
                    android:layout_width="@dimen/dp_24"
                    android:layout_height="@dimen/dp_24"/>
                <TextView
                    android:text="HTX"
                    android:textSize="14sp"
                    android:layout_marginStart="@dimen/dp_6"
                    android:textColor="@color/color_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"/>
            </LinearLayout>
        </LinearLayout>
    </LinearLayout>

</layout>