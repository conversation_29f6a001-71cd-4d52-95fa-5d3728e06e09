<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_110"
        android:background="@drawable/shape_back_dialog_center"
        android:gravity="center_horizontal"
        android:orientation="vertical"
        android:paddingStart="@dimen/dp_30"
        android:layout_gravity="bottom"
        android:paddingEnd="@dimen/dp_30"
        android:paddingTop="@dimen/dp_40"
        android:paddingBottom="@dimen/dp_54">

        <ImageView
            android:layout_width="@dimen/dp_120"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:adjustViewBounds="true"
            android:src="@drawable/icon_aa_recovering" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_25"
            android:text="@string/recover_aa_wallet"
            android:textColor="@color/color_title"
            android:textSize="17sp"
            android:textStyle="bold" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_40"
            android:gravity="start"
            android:text="@string/recover_aa_wallet_caption_2"
            android:textColor="@color/color_title_sub"
            android:textSize="15sp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_36"
            android:text="@string/please_be_patient"
            android:textColor="@color/color_697cde"
            android:textSize="14sp"
            android:textStyle="bold" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_28"
            android:gravity="center_vertical">

            <TextView
                android:id="@+id/tvHours"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/color_617AFF"
                android:textSize="30sp"
                android:textStyle="bold"
                android:text="--" />

            <ImageView
                android:layout_width="@dimen/dp_36"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_aa_colon" />

            <TextView
                android:id="@+id/tvMins"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/color_617AFF"
                android:textSize="30sp"
                android:textStyle="bold"
                android:text="--" />

            <ImageView
                android:layout_width="@dimen/dp_36"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_aa_colon" />

            <TextView
                android:id="@+id/tvSecs"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/color_617AFF"
                android:textSize="30sp"
                android:textStyle="bold"
                android:text="--" />

        </LinearLayout>

        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/btnConfirm"
            android:layout_width="@dimen/dp_260"
            android:layout_height="@dimen/dp_36"
            android:layout_marginStart="@dimen/dp_24"
            android:layout_marginTop="@dimen/dp_18"
            android:layout_marginEnd="@dimen/dp_24"
            android:background="@drawable/btn_shape_gradient_common"
            android:text="@string/confirm"
            android:textAllCaps="false"
            android:textColor="@color/white"
            android:textSize="14sp" />

    </LinearLayout>

</layout>
