<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/dialog_background"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/dialog_background">

    <RelativeLayout
        android:id="@+id/delegate_detail"
        android:layout_width="match_parent"
        android:layout_centerInParent="true"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_38"
        android:layout_marginEnd="@dimen/dp_38"
        android:orientation="vertical">
        <LinearLayout
            android:layout_width="match_parent"
            android:orientation="vertical"
            android:background="@drawable/shape_back_dialog_center"
            android:gravity="center_horizontal"
            android:layout_height="wrap_content">
            <ImageView
                android:layout_width="@dimen/dp_52"
                android:src="@drawable/icon_tips_notice"
                android:layout_marginTop="@dimen/dp_40"
                android:layout_centerHorizontal="true"
                android:layout_height="@dimen/dp_52"/>
            <TextView
                android:layout_width="wrap_content"
                android:layout_marginTop="@dimen/dp_4"
                android:textColor="@color/color_title"
                android:textSize="@dimen/font_size_title"
                android:text="@string/notice"
                android:layout_height="wrap_content"/>

            <TextView
                android:layout_width="wrap_content"
                android:layout_marginTop="@dimen/dp_26"
                android:textColor="@color/color_title_sub"
                android:textSize="@dimen/font_size_common"
                android:text="@string/remove_token_confirm_message"
                android:layout_height="wrap_content"/>


            <LinearLayout
                android:layout_width="match_parent"
                android:orientation="horizontal"
                android:layout_marginStart="@dimen/dp_20"
                android:layout_marginBottom="@dimen/dp_30"
                android:layout_marginEnd="@dimen/dp_20"
                android:layout_marginTop="@dimen/dp_46"
                android:layout_height="wrap_content">
                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/btn_cancel"
                    android:layout_width="@dimen/transfer_key_width"
                    android:layout_height="@dimen/wallet_info_btn_height"
                    android:layout_weight="1"
                    android:visibility="visible"
                    android:background="@drawable/btn_stroke_gradient_common"
                    android:text="@string/cancel"
                    android:layout_marginEnd="@dimen/dp_10"
                    android:textAllCaps="false"
                    android:textColor="@color/colorPrimary"
                    android:textSize="@dimen/font_size_common" />
                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/btn_confirm"
                    android:layout_width="@dimen/transfer_key_width"
                    android:layout_weight="1"
                    android:layout_height="@dimen/wallet_info_btn_height"
                    android:layout_marginStart="@dimen/dp_10"
                    android:visibility="visible"
                    android:background="@drawable/btn_shape_gradient_common"
                    android:text="@string/yes"
                    android:textAllCaps="false"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/font_size_common" />
            </LinearLayout>
        </LinearLayout>

        <ImageView
            android:id="@+id/iv_close"
            android:layout_width="wrap_content"
            android:src="@drawable/ic_close_gray"
            android:layout_alignParentRight="true"
            android:padding="@dimen/dp_16"
            android:layout_height="wrap_content"/>

    </RelativeLayout>
</RelativeLayout>