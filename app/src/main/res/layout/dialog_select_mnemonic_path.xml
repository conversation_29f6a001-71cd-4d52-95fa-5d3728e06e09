<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_alignParentBottom="true"
    android:layout_gravity="bottom"
    android:background="@color/color_back"
    android:orientation="vertical"
    android:paddingStart="@dimen/dp_15"
    android:paddingTop="@dimen/dp_20"
    android:paddingEnd="@dimen/dp_15"
    android:paddingBottom="@dimen/dp_20">

    <TextView
        android:layout_gravity="center_horizontal"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/common_margin"
        android:text="@string/select_hd_path"
        android:textColor="@color/color_title"
        android:textSize="@dimen/font_size_large" />

    <LinearLayout
        android:id="@+id/mLlPathIotex"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_50"
        android:layout_marginTop="@dimen/dp_15"
        android:background="@drawable/shape_card_back"
        android:gravity="center_vertical"
        android:paddingStart="@dimen/dp_15"
        android:paddingEnd="@dimen/dp_12">

        <ImageView
            android:src="@drawable/icon_iotex_network_logo"
            android:layout_width="@dimen/dp_28"
            android:layout_height="@dimen/dp_28"/>

        <TextView
            android:layout_marginStart="@dimen/dp_8"
            android:id="@+id/mTvPathIotex"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:ellipsize="end"
            android:lines="1"
            android:textColor="@color/color_title"
            android:textSize="14sp"
            android:text="IoTeX Standard (IoTeX HD PATH)" />


    </LinearLayout>

    <LinearLayout
        android:id="@+id/mLlPathEvm"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_50"
        android:layout_marginTop="@dimen/dp_15"
        android:background="@drawable/shape_card_back"
        android:gravity="center_vertical"
        android:paddingStart="@dimen/dp_15"
        android:paddingEnd="@dimen/dp_12">

        <ImageView
            android:src="@drawable/icon_eth_network_logo"
            android:layout_width="@dimen/dp_28"
            android:layout_height="@dimen/dp_28"/>

        <TextView
            android:layout_marginStart="@dimen/dp_8"
            android:id="@+id/mTvPathEvm"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:ellipsize="end"
            android:lines="1"
            android:textColor="@color/color_title"
            android:textSize="14sp"
            android:text="ETH Standard (ETH HD PATH)" />


    </LinearLayout>

    <LinearLayout
        android:id="@+id/mLlPathBitCoin"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_50"
        android:layout_marginTop="@dimen/dp_15"
        android:background="@drawable/shape_card_back"
        android:gravity="center_vertical"
        android:paddingStart="@dimen/dp_15"
        android:paddingEnd="@dimen/dp_12">

        <ImageView
            android:src="@drawable/icon_bitcoin_logo"
            android:layout_width="@dimen/dp_28"
            android:layout_height="@dimen/dp_28"/>

        <TextView
            android:layout_marginStart="@dimen/dp_8"
            android:id="@+id/mTvPathBitcoin"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:ellipsize="end"
            android:lines="1"
            android:textColor="@color/color_title"
            android:textSize="14sp"
            android:text="Bitcoin Standard (Bitcoin HD PATH)" />


    </LinearLayout>

    <LinearLayout
        android:id="@+id/mLlPathSolana"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_50"
        android:layout_marginTop="@dimen/dp_15"
        android:background="@drawable/shape_card_back"
        android:gravity="center_vertical"
        android:paddingStart="@dimen/dp_15"
        android:paddingEnd="@dimen/dp_12">

        <ImageView
            android:src="@drawable/icon_solana_logo"
            android:layout_width="@dimen/dp_28"
            android:layout_height="@dimen/dp_28"/>

        <TextView
            android:layout_marginStart="@dimen/dp_8"
            android:id="@+id/mTvPathSolana"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:ellipsize="end"
            android:lines="1"
            android:textColor="@color/color_title"
            android:textSize="14sp"
            android:text="Solana Standard (Solana HD PATH)" />


    </LinearLayout>

</LinearLayout>