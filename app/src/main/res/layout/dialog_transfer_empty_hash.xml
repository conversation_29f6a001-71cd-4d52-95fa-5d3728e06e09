<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android" >
    <LinearLayout
        android:orientation="vertical"
        android:layout_gravity="center"
        android:background="@drawable/shape_back_dialog_center"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <ImageView
            android:id="@+id/ivClose"
            android:layout_gravity="end"
            android:src="@drawable/ic_close"
            android:padding="@dimen/dp_10"
            android:layout_width="@dimen/dp_44"
            android:layout_height="@dimen/dp_44"/>

        <TextView
            android:text="@string/transaction"
            android:textSize="17sp"
            android:layout_gravity="center_horizontal"
            android:textColor="@color/color_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerView"
            android:layout_marginTop="@dimen/dp_24"
            android:layout_marginStart="@dimen/dp_15"
            android:layout_marginEnd="@dimen/dp_15"
            android:layout_marginBottom="@dimen/dp_20"
            android:background="@drawable/shape_dialog_card_back"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"/>
        <LinearLayout
            android:paddingStart="@dimen/dp_16"
            android:paddingEnd="@dimen/dp_16"
            android:gravity="center"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <TextView
                android:text="@string/status"
                android:textSize="14sp"
                android:textColor="@color/gray_9a9fb1"
                android:layout_weight="1"
                android:layout_width="0dp"
                android:layout_height="wrap_content"/>
            <ImageView
                android:src="@drawable/icon_transfer_error"
                android:layout_width="@dimen/dp_20"
                android:layout_height="@dimen/dp_20"/>
            <TextView
                android:text="@string/failed"
                android:textSize="14sp"
                android:layout_marginStart="@dimen/dp_3"
                android:textColor="@color/color_E53737"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
        </LinearLayout>

        <View
            android:layout_marginTop="@dimen/dp_20"
            android:background="@color/color_4d808080"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp_1"/>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerGas"
            android:layout_marginTop="@dimen/dp_16"
            android:layout_marginBottom="@dimen/dp_20"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"/>

    </LinearLayout>

</layout>