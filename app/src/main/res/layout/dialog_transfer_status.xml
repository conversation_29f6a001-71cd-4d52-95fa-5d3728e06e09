<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:id="@+id/llRoot"
    android:gravity="bottom"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dp_54">

    <LinearLayout
        android:background="@drawable/shape_card_back"
        android:gravity="center_vertical"
        android:elevation="@dimen/dp_6"
        android:layout_margin="@dimen/dp_6"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_48">
        <ImageView
            android:id="@+id/ivTransferStatus"
            android:layout_marginStart="@dimen/dp_16"
            android:src="@drawable/icon_transfer_status_loading"
            android:layout_width="@dimen/dp_16"
            android:layout_height="@dimen/dp_16"/>
        <LinearLayout
            android:orientation="vertical"
            android:layout_marginStart="@dimen/dp_9"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">
            <TextView
                android:id="@+id/tvTransferStatus"
                android:text="@string/transaction_sending"
                android:textSize="12sp"
                android:textColor="@color/color_title"
                android:textStyle="bold"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"/>
            <TextView
                android:id="@+id/tvHash"
                android:visibility="gone"
                android:text="@string/view_on_explorer"
                android:textSize="12sp"
                android:textColor="@color/color_617AFF"
                android:textStyle="bold"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"/>
        </LinearLayout>
    </LinearLayout>

    <LinearLayout
        android:id="@+id/llEmpty"
        android:background="@drawable/shape_card_back"
        android:layout_marginTop="@dimen/dp_4"
        android:visibility="gone"
        android:elevation="@dimen/dp_6"
        android:layout_margin="@dimen/dp_6"
        android:gravity="center_vertical"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_48">
        <ImageView
            android:layout_marginStart="@dimen/dp_16"
            android:src="@drawable/icon_transfer_status_loading"
            android:layout_width="@dimen/dp_16"
            android:layout_height="@dimen/dp_16"/>
        <LinearLayout
            android:orientation="vertical"
            android:layout_marginStart="@dimen/dp_9"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">
            <TextView
                android:id="@+id/tvNext"
                android:text="@string/transaction_sending"
                android:textSize="12sp"
                android:textColor="@color/color_title"
                android:textStyle="bold"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"/>
            <TextView
                android:visibility="gone"
                android:text="@string/view_on_explorer"
                android:textSize="12sp"
                android:textColor="@color/color_617AFF"
                android:textStyle="bold"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"/>
        </LinearLayout>
    </LinearLayout>

</LinearLayout>
