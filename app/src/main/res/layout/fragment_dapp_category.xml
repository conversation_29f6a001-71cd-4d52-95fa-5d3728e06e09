<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerViewCategory"
                android:layout_width="@dimen/dp_60"
                android:layout_height="match_parent"/>
            <View
                android:background="@color/color_line_diver"
                android:layout_width="@dimen/dp_1"
                android:layout_height="match_parent"/>
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />
        </LinearLayout>
        <LinearLayout
            android:id="@+id/emptyLayout"
            android:visibility="gone"
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:gravity="center"
            android:layout_marginStart="@dimen/dp_60"
            android:layout_height="match_parent">

            <ImageView
                android:layout_width="@dimen/dp_80"
                android:layout_height="@dimen/dp_80"
                android:layout_marginTop="@dimen/dp_20"
                android:src="@drawable/icon_search_empty" />

            <TextView
                android:id="@+id/empty_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/common_margin"
                android:textColor="@color/color_title_sub"
                android:text="@string/no_dapp" />
        </LinearLayout>
    </FrameLayout>
</layout>