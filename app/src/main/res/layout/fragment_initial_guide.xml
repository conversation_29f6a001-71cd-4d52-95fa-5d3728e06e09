<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ImageView
        android:id="@+id/initial_guide_img"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/ic_guide_image_1"
        android:layout_marginTop="100dp"
        app:layout_constraintBottom_toTopOf="@+id/initial_guide_tip"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/initial_guide_tip"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_54"
        android:textSize="20sp"
        android:text="@string/app_initial_guide_tip_1"
        android:layout_marginStart="@dimen/dp_32"
        android:layout_marginEnd="32dp"
        android:textColor="@color/color_title"
        app:layout_constraintBottom_toTopOf="@+id/initial_guide_sub_tip"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/initial_guide_img" />

    <TextView
        android:id="@+id/initial_guide_sub_tip"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_12"
        android:text="@string/app_initial_guide_sub_tip_1"
        android:layout_marginEnd="32dp"
        android:layout_marginStart="@dimen/dp_32"
        android:textColor="@color/color_title_sub"
        android:textSize="14sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/initial_guide_tip" />


</androidx.constraintlayout.widget.ConstraintLayout>