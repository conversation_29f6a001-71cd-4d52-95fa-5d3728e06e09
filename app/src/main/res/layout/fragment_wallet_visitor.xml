<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <ImageView
            android:id="@+id/ivRoot"
            android:scaleType="centerCrop"
            android:layout_width="match_parent"
            android:layout_height="match_parent"/>

        <LinearLayout
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="match_parent">
            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <LinearLayout
                    android:orientation="vertical"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">
                    <LinearLayout
                        android:gravity="center"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content">
                        <FrameLayout
                            android:layout_marginTop="@dimen/dp_5"
                            android:layout_marginBottom="@dimen/dp_5"
                            android:layout_marginStart="@dimen/dp_16"
                            android:layout_width="@dimen/dp_34"
                            android:layout_height="@dimen/dp_34">
                            <androidx.appcompat.widget.AppCompatImageView
                                android:id="@+id/ivSetting"
                                android:tint="@color/color_title"
                                android:layout_gravity="center"
                                android:layout_width="@dimen/dp_24"
                                android:layout_height="@dimen/dp_24"
                                android:src="@drawable/icon_home_menu" />
                            <View
                                android:id="@+id/viewMenuRed"
                                android:layout_width="@dimen/dp_6"
                                android:layout_height="@dimen/dp_6"
                                android:layout_gravity="end"
                                android:background="@drawable/bg_circle_red"
                                android:visibility="gone"
                                tools:visibility="visible" />
                        </FrameLayout>

                        <RelativeLayout
                            android:id="@+id/rlGift"
                            android:layout_marginStart="@dimen/dp_5"
                            android:layout_width="@dimen/dp_34"
                            android:layout_height="@dimen/dp_34">

                            <androidx.appcompat.widget.AppCompatImageView
                                android:id="@+id/ivGift"
                                android:layout_width="@dimen/dp_24"
                                android:layout_height="@dimen/dp_24"
                                android:layout_centerInParent="true"
                                android:src="@drawable/icon_home_gift" />

                            <View
                                android:id="@+id/viewGiftRed"
                                android:layout_width="@dimen/dp_9"
                                android:layout_height="@dimen/dp_11"
                                android:layout_alignParentEnd="true"
                                android:background="@drawable/icon_dapp_hot"
                                tools:visibility="visible" />
                        </RelativeLayout>
                    </LinearLayout>
                </LinearLayout>
            </FrameLayout>
            <com.scwang.smartrefresh.layout.SmartRefreshLayout
                android:id="@+id/refreshLayout"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:srlEnableLoadMore="false"
                app:srlEnablePreviewInEditMode="true"
                app:srlHeaderMaxDragRate="1.5">
                <io.iotex.iopay.ui.widget.IoPayHeader
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/refresh_header_height" />
                <androidx.core.widget.NestedScrollView
                    android:id="@+id/scrollView"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">
                    <LinearLayout
                        android:layout_marginTop="@dimen/dp_10"
                        android:orientation="vertical"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent">

                        <include
                            android:id="@+id/addLayout"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            layout="@layout/view_visitor_add_wallet"/>

                        <com.youth.banner.Banner
                            android:id="@+id/banner"
                            android:background="@drawable/default_banner"
                            android:layout_width="match_parent"
                            android:layout_height="@dimen/dp_110"
                            android:layout_marginStart="@dimen/dp_16"
                            android:layout_marginTop="@dimen/dp_16"
                            android:layout_marginEnd="@dimen/dp_16"
                            app:banner_loop_time="5500"
                            app:banner_auto_loop="true"
                            app:banner_radius="@dimen/dp_6" />

                        <LinearLayout
                            android:orientation="vertical"
                            android:layout_marginTop="@dimen/dp_16"
                            android:layout_marginStart="@dimen/dp_15"
                            android:layout_marginEnd="@dimen/dp_15"
                            android:layout_marginBottom="@dimen/dp_20"
                            android:background="@drawable/shape_card_back"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent">

                            <LinearLayout
                                android:id="@+id/mLlTabToken"
                                android:layout_width="wrap_content"
                                android:paddingStart="@dimen/dp_14"
                                android:paddingEnd="@dimen/dp_14"
                                android:layout_marginStart="@dimen/dp_2"
                                android:layout_height="@dimen/dp_56"
                                android:gravity="center"
                                android:orientation="vertical">

                                <androidx.appcompat.widget.AppCompatTextView
                                    android:id="@+id/mTvTabToken"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:gravity="center"
                                    android:includeFontPadding="false"
                                    android:text="@string/token_lower_case"
                                    android:textColor="@color/color_title"
                                    android:textSize="14sp" />

                                <View
                                    android:id="@+id/mIndicatorToken"
                                    android:layout_width="@dimen/dp_30"
                                    android:layout_height="@dimen/dp_2"
                                    android:layout_marginTop="@dimen/dp_7"
                                    android:background="@color/color_title" />

                            </LinearLayout>

                            <androidx.recyclerview.widget.RecyclerView
                                android:id="@+id/recyclerView"
                                android:overScrollMode="never"
                                android:layout_marginStart="@dimen/dp_15"
                                android:layout_marginEnd="@dimen/dp_15"
                                tools:listitem="@layout/item_visitor_token"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"/>
                        </LinearLayout>

                    </LinearLayout>
                </androidx.core.widget.NestedScrollView>
            </com.scwang.smartrefresh.layout.SmartRefreshLayout>

        </LinearLayout>

    </FrameLayout>
</layout>