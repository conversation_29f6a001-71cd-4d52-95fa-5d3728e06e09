<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_56"
        android:layout_marginStart="@dimen/dp_16"
        android:layout_marginTop="@dimen/dp_16"
        android:layout_marginEnd="@dimen/dp_16"
        android:background="@drawable/shape_card_back"
        android:gravity="center"
        android:paddingStart="@dimen/dp_16"
        android:paddingEnd="@dimen/dp_16">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dp_10"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tvTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="14sp"
                android:textColor="@color/color_title_sub"
                tools:text="@string/verify_recovery_email" />

            <LinearLayout
                android:id="@+id/llTryAgain"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_4"
                android:gravity="center_vertical">

                <TextView
                    android:id="@+id/tvStatus"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/verified_failed"
                    android:textColor="@color/color_E53737"
                    android:textSize="11sp" />

                <TextView
                    android:id="@+id/tvTryAgain"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp_6"
                    android:text="@string/try_again_underline"
                    android:textColor="@color/color_617AFF"
                    android:textSize="11sp" />

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/ivArrowRight"
                    android:layout_width="@dimen/dp_10"
                    android:layout_height="@dimen/dp_10"
                    android:layout_marginStart="@dimen/dp_2"
                    android:adjustViewBounds="true"
                    android:src="@drawable/ic_arrow_right"
                    android:tint="@color/color_617AFF" />

            </LinearLayout>

        </LinearLayout>

        <ImageView
            android:id="@+id/ivStatus"
            android:layout_width="@dimen/dp_16"
            android:layout_height="@dimen/dp_16"
            android:src="@drawable/icon_aa_ready" />

        <ProgressBar
            android:id="@+id/progress"
            style="?android:attr/progressBarStyleSmall"
            android:layout_width="@dimen/dp_16"
            android:layout_height="@dimen/dp_16"
            android:layout_gravity="center"
            android:indeterminate="true"
            android:indeterminateTint="@color/color_617AFF"
            android:indeterminateTintMode="src_in"
            android:visibility="gone" />

    </LinearLayout>
</layout>
