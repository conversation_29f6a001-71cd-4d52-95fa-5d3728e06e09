<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <io.iotex.iopay.ui.widget.SquareLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:padding="@dimen/dp_10">

        <com.makeramen.roundedimageview.RoundedImageView
            android:id="@+id/ivImage"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="centerCrop"
            app:riv_corner_radius="@dimen/dp_6" />

        <FrameLayout
            android:id="@+id/flAdd"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/shape_card_back">

            <ImageView
                android:layout_width="@dimen/dp_25"
                android:layout_height="@dimen/dp_25"
                android:layout_gravity="center"
                android:src="@drawable/icon_feed_add_image" />
        </FrameLayout>

        <ImageView
            android:id="@+id/ivDelete"
            android:layout_width="@dimen/dp_25"
            android:layout_height="@dimen/dp_25"
            android:layout_gravity="end"
            android:layout_margin="@dimen/dp_5"
            android:background="@drawable/shape_delete_back"
            android:padding="@dimen/dp_5"
            android:src="@drawable/icon_dapp_close" />
    </io.iotex.iopay.ui.widget.SquareLayout>
</layout>
