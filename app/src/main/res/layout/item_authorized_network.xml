<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <LinearLayout
        android:gravity="center"
        android:paddingTop="@dimen/dp_10"
        android:paddingBottom="@dimen/dp_10"
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <com.makeramen.roundedimageview.RoundedImageView
            app:riv_corner_radius="@dimen/dp_20"
            android:id="@+id/ivLogo"
            android:src="@drawable/ic_network_default"
            android:layout_width="@dimen/dp_40"
            android:layout_height="@dimen/dp_40"/>

        <TextView
            tools:text="@string/iotex_string"
            android:id="@+id/tvName"
            android:layout_marginTop="@dimen/dp_5"
            android:textColor="@color/color_title_sub"
            android:textSize="12sp"
            android:gravity="center"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>
    </LinearLayout>

</layout>