<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <LinearLayout
        android:id="@+id/llContent"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/dp_12"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/llTop"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_15"
            android:layout_marginBottom="@dimen/dp_15">

            <TextView
                android:id="@+id/tvName"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:textColor="@color/color_title"
                android:textSize="16sp"
                tools:text="wallet name" />

            <TextView
                android:id="@+id/tvBalance"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/color_title"
                android:textSize="14sp"
                tools:text="0.000123 BTC" />

        </LinearLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            tools:listitem="@layout/item_bitcoin_address" />

    </LinearLayout>

</layout>