<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <LinearLayout
        android:id="@+id/llRoot"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_64"
        android:background="@drawable/btn_stroke_gradient_common"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingStart="@dimen/dp_12"
        android:paddingTop="@dimen/dp_10"
        android:paddingEnd="@dimen/dp_12"
        android:paddingBottom="@dimen/dp_10">

        <ImageView
            android:id="@+id/ivIndicator"
            android:layout_width="@dimen/dp_24"
            android:layout_height="@dimen/dp_24"
            android:src="@drawable/ic_fee_slow" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_8"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tvLevel"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/slow"
                android:textColor="@color/color_title"
                android:textSize="15sp" />

            <TextView
                android:id="@+id/tvFee"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_2"
                android:textColor="@color/color_title_thr"
                android:textSize="12sp"
                tools:text="20 Sat/vB" />
        </LinearLayout>

        <Space
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tvTransactionTime"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/transaction_time"
                android:textColor="@color/color_title"
                android:textSize="15sp" />

            <TextView
                android:id="@+id/tvSpentFee"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_2"
                android:textColor="@color/color_title_thr"
                android:textSize="12sp"
                tools:text="0.8 BTC ≈ $1.96" />

        </LinearLayout>

    </LinearLayout>
</layout>
