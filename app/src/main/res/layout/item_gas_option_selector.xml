<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingTop="@dimen/dp_10"
        android:paddingBottom="@dimen/dp_10">

        <ImageView
            android:id="@+id/ivIndicator"
            android:layout_width="@dimen/dp_16"
            android:layout_height="@dimen/dp_16"
            android:src="@drawable/ic_ring" />

        <TextView
            android:id="@+id/tvLevel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_12"
            android:text="@string/low"
            android:textColor="@color/color_title_sub"
            android:textSize="14sp" />

        <Space
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1" />

        <TextView
            android:id="@+id/tvGas"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/color_title_sub"
            android:textSize="14sp"
            tools:text="—— ——" />

        <ImageView
            android:id="@+id/ivEdit"
            android:layout_width="@dimen/dp_24"
            android:layout_height="@dimen/dp_24"
            android:visibility="gone"
            android:layout_marginStart="@dimen/dp_5"
            android:src="@drawable/icon_edit_green" />

    </LinearLayout>
</layout>
