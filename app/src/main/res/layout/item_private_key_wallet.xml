<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <LinearLayout
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <LinearLayout
            android:layout_marginStart="@dimen/dp_16"
            android:layout_marginTop="@dimen/dp_12"
            android:layout_marginBottom="@dimen/dp_12"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">
            <TextView
                android:id="@+id/tvType"
                android:text="@string/private_key_wallet"
                android:paddingStart="@dimen/dp_5"
                android:paddingEnd="@dimen/dp_5"
                android:textColor="@color/color_20202c"
                android:textSize="9sp"
                android:gravity="center"
                android:background="@drawable/shape_wallet_pk_back_r2"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp_19"/>

            <ImageView
                android:id="@+id/ivExpired"
                android:visibility="gone"
                android:layout_marginStart="@dimen/dp_5"
                android:src="@drawable/icon_warning_red"
                android:layout_width="@dimen/dp_20"
                android:layout_height="@dimen/dp_20"/>
        </LinearLayout>
        <com.daimajia.swipe.SwipeLayout
            android:id="@+id/swipeLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:clickToClose="true">
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:orientation="horizontal">

                <LinearLayout
                    android:id="@+id/llDelete"
                    android:layout_width="@dimen/swipeLayout_button_width"
                    android:orientation="vertical"
                    android:layout_height="match_parent"
                    android:background="@color/color_card_back"
                    android:layout_marginStart="@dimen/dp_5"
                    android:gravity="center">

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@drawable/icon_ashbin" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/delete"
                        android:textColor="@color/colorWarning"
                        android:textSize="@dimen/font_size_little" />
                </LinearLayout>

            </LinearLayout>
            <LinearLayout
                android:id="@+id/llContent"
                android:orientation="vertical"
                android:background="@drawable/shape_card_back"
                android:paddingTop="@dimen/dp_15"
                android:paddingStart="@dimen/dp_15"
                android:paddingBottom="@dimen/dp_15"
                android:paddingEnd="@dimen/dp_15"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <LinearLayout
                    android:layout_marginTop="@dimen/dp_5"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center">

                    <com.makeramen.roundedimageview.RoundedImageView
                        android:id="@+id/walletAvatar"
                        android:layout_width="@dimen/dp_32"
                        android:layout_height="@dimen/dp_32"
                        android:scaleType="fitCenter"
                        app:riv_corner_radius="@dimen/dp_16"
                        tools:src="@drawable/icon_wallet_default" />

                    <LinearLayout
                        android:layout_marginStart="@dimen/dp_10"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content">
                            <LinearLayout
                                android:layout_weight="1"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content">
                                <TextView
                                    android:id="@+id/walletName"
                                    android:maxWidth="@dimen/dp_120"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:lines="1"
                                    android:ellipsize="end"
                                    android:textColor="@color/color_title"
                                    android:textSize="16sp"
                                    tools:text="Wallet 1" />
                                <androidx.appcompat.widget.AppCompatImageView
                                    android:id="@+id/ivEdit"
                                    android:layout_width="@dimen/dp_38"
                                    android:layout_height="@dimen/dp_18"
                                    android:adjustViewBounds="true"
                                    android:paddingStart="@dimen/dp_10"
                                    android:paddingEnd="@dimen/dp_10"
                                    android:tint="@color/color_title"
                                    android:src="@drawable/ic_edit_b4b8cb"/>
                            </LinearLayout>
                            <io.iotex.iopay.widget.DINTextView
                                android:id="@+id/walletAmount"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:gravity="end"
                                android:maxLines="1"
                                android:textColor="@color/color_title"
                                android:textSize="14sp"
                                tools:text="100000.00 IOTX" />
                        </LinearLayout>

                        <LinearLayout
                            android:gravity="center_vertical"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content">

                            <LinearLayout
                                android:gravity="center_vertical"
                                android:layout_weight="1"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content">

                                <TextView
                                    android:id="@+id/walletAddress"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:ellipsize="middle"
                                    android:maxLines="1"
                                    android:textColor="@color/color_title_sub"
                                    android:textSize="@dimen/font_size_small"
                                    tools:text="3c79d4...cc786871" />

                                <androidx.appcompat.widget.AppCompatImageView
                                    android:id="@+id/ivCopy"
                                    android:layout_width="@dimen/dp_26"
                                    android:layout_height="@dimen/dp_26"
                                    android:padding="@dimen/dp_6"
                                    android:tint="@color/color_title_sub"
                                    android:src="@drawable/icon_copy_wallet_gray" />
                            </LinearLayout>

                            <LinearLayout
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal">

                            </LinearLayout>

                        </LinearLayout>

                    </LinearLayout>

                </LinearLayout>
            </LinearLayout>
        </com.daimajia.swipe.SwipeLayout>
    </LinearLayout>

</layout>