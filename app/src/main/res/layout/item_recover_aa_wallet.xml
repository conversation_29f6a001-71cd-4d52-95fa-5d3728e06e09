<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="@dimen/dp_12"
    android:background="@drawable/shape_card_back">

    <LinearLayout
        android:id="@+id/llContent"
        android:padding="@dimen/dp_15"
        android:gravity="center_vertical"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <FrameLayout
            android:layout_width="@dimen/dp_40"
            android:layout_height="@dimen/dp_40">

            <com.makeramen.roundedimageview.RoundedImageView
                android:id="@+id/walletAvatar"
                android:layout_width="@dimen/dp_32"
                android:layout_height="@dimen/dp_32"
                android:layout_gravity="center"
                android:scaleType="fitCenter"
                app:riv_corner_radius="@dimen/dp_16"
                tools:src="@drawable/icon_wallet_default" />

            <ImageView
                android:src="@drawable/icon_wallet_aa"
                android:layout_width="@dimen/dp_14"
                android:layout_height="@dimen/dp_14"
                android:layout_gravity="end"/>

        </FrameLayout>

        <TextView
            android:id="@+id/tvAddress"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_10"
            android:textColor="@color/color_title_sub"
            android:textSize="14sp"
            tools:text="io132k…dvnyad78" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivCopy"
            android:layout_width="@dimen/dp_16"
            android:layout_height="@dimen/dp_16"
            android:layout_marginStart="@dimen/dp_5"
            android:src="@drawable/ic_copy"
            android:tint="@color/color_title_sub" />

        <TextView
            android:id="@+id/tvBalance"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_8"
            android:layout_weight="1"
            android:gravity="end"
            android:textColor="@color/color_title_sub"
            android:textSize="14sp"
            tools:text="100 IOTX" />

        <androidx.appcompat.widget.AppCompatImageView
            android:layout_width="@dimen/dp_16"
            android:layout_height="@dimen/dp_16"
            android:layout_marginStart="@dimen/dp_8"
            android:src="@drawable/icon_setting_arrow_right"
            android:tint="@color/color_title_sub" />
    </LinearLayout>

    <TextView
        android:id="@+id/tvExists"
        android:textSize="10sp"
        android:layout_margin="@dimen/dp_5"
        android:textColor="@color/white"
        android:paddingStart="@dimen/dp_3"
        android:paddingEnd="@dimen/dp_3"
        android:background="@drawable/shape_gradient_r3"
        android:text="@string/already_exists"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"/>
</FrameLayout>