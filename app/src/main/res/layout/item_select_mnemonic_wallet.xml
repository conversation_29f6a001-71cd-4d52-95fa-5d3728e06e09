<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dp_54"
    android:layout_marginBottom="@dimen/dp_12"
    android:background="@drawable/shape_card_back"
    android:gravity="center_vertical"
    android:paddingStart="@dimen/dp_15"
    android:paddingEnd="@dimen/dp_12">

    <ImageView
        android:id="@+id/mIvStatus"
        android:layout_width="@dimen/dp_18"
        android:layout_height="wrap_content"
        android:adjustViewBounds="true"
        android:src="@drawable/ic_unselected_gray"/>

    <TextView
        android:id="@+id/mTvAddress"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_12"
        android:layout_marginEnd="@dimen/dp_12"
        android:layout_weight="1"
        android:textColor="@color/color_title"
        android:textSize="14sp"
        tools:text="io132k…dvnyad78" />

    <TextView
        android:id="@+id/mTvBalance"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:text="200 IOTX"
        android:textColor="@color/color_title"
        android:textSize="14sp" />
</LinearLayout>