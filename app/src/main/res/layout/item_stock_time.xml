<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">
    <TextView
        android:id="@+id/tvItem"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_22"
        android:gravity="center"
        android:paddingStart="@dimen/dp_12"
        android:paddingEnd="@dimen/dp_12"
        android:layout_marginStart="@dimen/dp_15"
        android:layout_marginEnd="@dimen/dp_15"
        android:background="@drawable/shape_item_back"
        tools:text="1day"
        android:textColor="@color/color_title"
        android:textSize="13sp" />
</layout>
