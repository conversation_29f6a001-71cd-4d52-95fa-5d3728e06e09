<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <LinearLayout
        android:id="@+id/llContent"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_16"
        android:layout_marginTop="@dimen/dp_16"
        android:layout_marginEnd="@dimen/dp_16"
        android:background="@drawable/shape_card_back"
        android:gravity="center"
        android:padding="@dimen/dp_12">

        <com.makeramen.roundedimageview.RoundedImageView
            android:id="@+id/ivLogo"
            app:riv_corner_radius="@dimen/dp_14"
            android:layout_width="@dimen/dp_28"
            android:layout_height="@dimen/dp_28"
            android:src="@drawable/icon_bitcoin_logo" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_10"
            android:layout_weight="1"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical">

                <TextView
                    android:id="@+id/tvTitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/color_title"
                    android:textSize="14sp"
                    tools:text="Native Segwit (m/44'/304'/0'/0/0)" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/llAddress"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_3"
                android:gravity="center">

                <TextView
                    android:id="@+id/tvAddress"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:ellipsize="middle"
                    android:lines="1"
                    android:textColor="@color/color_title_sub"
                    android:textSize="12sp"
                    tools:text="io214s...93u3utnd" />

                <ImageView
                    android:id="@+id/ivCopy"
                    android:layout_width="@dimen/dp_22"
                    android:layout_height="@dimen/dp_12"
                    android:paddingStart="@dimen/dp_5"
                    android:paddingEnd="@dimen/dp_5"
                    android:src="@drawable/icon_address_copy" />

            </LinearLayout>
        </LinearLayout>

        <TextView
            android:id="@+id/tvBalance"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="middle"
            android:lines="1"
            android:textColor="@color/color_title"
            android:textSize="14sp"
            tools:text="0.05 BTC" />
    </LinearLayout>

</layout>