<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <LinearLayout
        android:gravity="center_vertical|end"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">
        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivArrow"
            android:tint="@color/color_title"
            android:layout_marginStart="@dimen/dp_6"
            android:src="@drawable/icon_swap_router_arrow"
            android:layout_width="@dimen/dp_16"
            android:layout_height="@dimen/dp_16"/>
        <com.makeramen.roundedimageview.RoundedImageView
            android:id="@+id/ivLogo"
            app:riv_corner_radius="@dimen/dp_8"
            android:layout_marginStart="@dimen/dp_6"
            android:src="@drawable/icon_token_default"
            android:layout_width="@dimen/dp_16"
            android:layout_height="@dimen/dp_16"/>
        <TextView
            android:id="@+id/tvName"
            tools:text="IOTX"
            android:textSize="13sp"
            android:layout_marginStart="@dimen/dp_5"
            android:textColor="@color/color_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>
    </LinearLayout>
</layout>
