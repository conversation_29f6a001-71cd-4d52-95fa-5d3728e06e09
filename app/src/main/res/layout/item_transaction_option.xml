<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center_vertical"
    android:layout_marginTop="@dimen/common_padding_vertical"
    android:gravity="center"
    android:orientation="horizontal">

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/mTvLabel"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_weight="2"
        android:textColor="@color/color_title_sub"
        android:textSize="@dimen/font_size_common"
        tools:text="@string/amount" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/mTvValue"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:gravity="end"
        android:textColor="@color/color_title_sub"
        android:textSize="@dimen/font_size_common"
        tools:text="1.0" />

    <ImageView
        android:id="@+id/mIvCertified"
        android:layout_width="@dimen/dp_24"
        android:layout_height="@dimen/dp_24"
        android:layout_marginStart="@dimen/dp_6"
        android:src="@drawable/icon_certified"
        android:visibility="gone" />

    <ImageView
        android:id="@+id/mIvEdit"
        android:layout_width="@dimen/dp_24"
        android:layout_height="@dimen/dp_24"
        android:layout_marginStart="@dimen/dp_7"
        android:src="@drawable/icon_edit_green"
        android:visibility="gone" />
</LinearLayout> 