<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <LinearLayout
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <TextView
            android:id="@+id/tvTime"
            tools:text="2023/12/11"
            android:textSize="14sp"
            android:layout_marginTop="@dimen/dp_12"
            android:layout_marginStart="@dimen/dp_15"
            android:layout_marginEnd="@dimen/dp_15"
            android:textColor="@color/color_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>
        <LinearLayout
            android:gravity="center"
            android:paddingStart="@dimen/dp_15"
            android:paddingEnd="@dimen/dp_15"
            android:layout_marginTop="@dimen/dp_20"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <FrameLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content">
                <FrameLayout
                    android:layout_marginEnd="@dimen/dp_10"
                    android:background="@drawable/shape_circle_card"
                    android:layout_width="@dimen/dp_34"
                    android:layout_height="@dimen/dp_34">
                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/ivType"
                        android:tint="@color/white"
                        android:layout_gravity="center"
                        android:src="@drawable/icon_action_send"
                        android:layout_width="@dimen/dp_16"
                        android:layout_height="@dimen/dp_16"/>
                </FrameLayout>
                <com.makeramen.roundedimageview.RoundedImageView
                    android:id="@+id/ivChain"
                    android:src="@drawable/ic_network_default"
                    android:layout_gravity="end"
                    android:visibility="gone"
                    tools:visibility="visible"
                    android:layout_marginEnd="@dimen/dp_5"
                    app:riv_corner_radius="@dimen/dp_8"
                    android:layout_width="@dimen/dp_16"
                    android:layout_height="@dimen/dp_16"/>
            </FrameLayout>
            <LinearLayout
                android:orientation="vertical"
                android:gravity="center"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <LinearLayout
                    android:gravity="center_vertical"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">
                    <TextView
                        android:id="@+id/tvSendTitle"
                        tools:text="Send IOTX"
                        android:textSize="14sp"
                        android:textColor="@color/color_title"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"/>
                    <ImageView
                        android:id="@+id/ivShare"
                        android:layout_width="@dimen/dp_30"
                        android:layout_height="@dimen/dp_12"
                        android:paddingStart="@dimen/dp_8"
                        android:paddingEnd="@dimen/dp_10"
                        android:src="@drawable/icon_trans_share" />
                    <TextView
                        android:id="@+id/tvValue"
                        tools:text="-0.01 IOTX"
                        android:textSize="14sp"
                        android:gravity="end"
                        android:textColor="@color/color_title"
                        android:layout_weight="1"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"/>
                </LinearLayout>

                <LinearLayout
                    android:layout_marginTop="@dimen/dp_3"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <TextView
                        android:id="@+id/tvStatus"
                        tools:text="Success"
                        android:textSize="10sp"
                        android:paddingStart="@dimen/dp_4"
                        android:paddingEnd="@dimen/dp_4"
                        android:gravity="center"
                        android:background="@drawable/stroke_shape_action_success"
                        android:textColor="@color/color_00dc9c"
                        android:layout_width="wrap_content"
                        android:layout_height="@dimen/dp_15"/>
                    <TextView
                        android:id="@+id/tvToAddress"
                        tools:text="To: io5dwf...ia5f5ftw"
                        android:textSize="10sp"
                        android:gravity="end"
                        android:textColor="@color/color_title_thr"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"/>
                </LinearLayout>

            </LinearLayout>

        </LinearLayout>
        <LinearLayout
            android:id="@+id/llSpeedUp"
            android:gravity="end"
            android:paddingStart="@dimen/dp_15"
            android:paddingEnd="@dimen/dp_15"
            android:layout_marginTop="@dimen/dp_10"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <TextView
                android:id="@+id/tvCancel"
                android:background="@drawable/stroke_617aff_r4"
                android:text="@string/cancel"
                android:textSize="12sp"
                android:textColor="@color/color_617AFF"
                android:gravity="center"
                android:layout_width="@dimen/dp_77"
                android:layout_height="@dimen/dp_22"/>
            <TextView
                android:id="@+id/tvSpeedUp"
                android:layout_marginStart="@dimen/dp_7"
                android:background="@drawable/shape_617aff_r4"
                android:text="@string/speed_up"
                android:textSize="12sp"
                android:gravity="center"
                android:textColor="@color/white"
                android:layout_width="@dimen/dp_77"
                android:layout_height="@dimen/dp_22"/>
            <TextView
                android:id="@+id/tvSpeedUpCancel"
                android:layout_marginStart="@dimen/dp_7"
                android:background="@drawable/shape_617aff_r4"
                android:text="@string/speed_up_cancellation"
                android:textSize="12sp"
                android:gravity="center"
                android:textColor="@color/white"
                android:paddingStart="@dimen/dp_5"
                android:paddingEnd="@dimen/dp_5"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp_22"/>
        </LinearLayout>
        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_20"/>
        <View
            android:id="@+id/vDivider"
            android:layout_marginStart="@dimen/dp_15"
            android:layout_marginEnd="@dimen/dp_15"
            android:background="@color/transparent_95_white"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_1"/>
    </LinearLayout>
</layout>