<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/theme_window_back"
    xmlns:tools="http://schemas.android.com/tools">

    <RelativeLayout
        android:id="@+id/mRlTabLayout"
        android:gravity="center_vertical"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_50">

        <io.iotex.iopay.home.widget.HomeTabView
            android:id="@+id/tabView"
            android:layout_marginStart="@dimen/dp_3"
            android:layout_centerVertical="true"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp_50"/>
        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivNftSearch"
            android:layout_marginEnd="@dimen/dp_16"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:layout_width="@dimen/dp_20"
            android:layout_height="@dimen/dp_20"
            android:visibility="gone"
            android:src="@drawable/btn_search"
            android:tint="@color/color_title" />
    </RelativeLayout>
    <LinearLayout
        android:id="@+id/llToken"
        android:layout_below="@+id/mRlTabLayout"
        android:gravity="center_vertical"
        android:paddingStart="@dimen/dp_16"
        android:paddingEnd="@dimen/dp_16"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_30">
        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivHideCheck"
            android:tint="@color/color_title"
            android:src="@drawable/icon_hide_assets_uncheck"
            android:layout_width="@dimen/dp_16"
            android:layout_height="@dimen/dp_16"/>
        <TextView
            android:layout_marginStart="@dimen/dp_5"
            android:text="@string/hide_assets_1_usd"
            android:textSize="14sp"
            android:layout_weight="1"
            android:textColor="@color/color_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"/>
        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivTokenSearch"
            android:layout_gravity="center_vertical|end"
            android:layout_width="@dimen/dp_20"
            android:layout_height="@dimen/dp_20"
            android:src="@drawable/btn_search"
            android:tint="@color/color_title" />
    </LinearLayout>
</RelativeLayout>