<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto" >
    <io.supercharge.shimmerlayout.ShimmerLayout
        android:id="@+id/shimmerLayout"
        app:shimmer_auto_start="true"
        android:background="@color/theme_window_back"
        app:shimmer_animation_duration="1000"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <LinearLayout
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="match_parent">
            <include layout="@layout/shimmer_item_home_token"/>
            <include layout="@layout/shimmer_item_home_token"/>
            <include layout="@layout/shimmer_item_home_token"/>
            <include layout="@layout/shimmer_item_home_token"/>
            <include layout="@layout/shimmer_item_home_token"/>
            <include layout="@layout/shimmer_item_home_token"/>
            <include layout="@layout/shimmer_item_home_token"/>
            <include layout="@layout/shimmer_item_home_token"/>
            <include layout="@layout/shimmer_item_home_token"/>
            <include layout="@layout/shimmer_item_home_token"/>
            <include layout="@layout/shimmer_item_home_token"/>
            <include layout="@layout/shimmer_item_home_token"/>
        </LinearLayout>
    </io.supercharge.shimmerlayout.ShimmerLayout>
</layout>