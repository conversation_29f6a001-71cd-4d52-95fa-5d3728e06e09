<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">
    <LinearLayout
        android:gravity="center"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_60">

        <View
            android:background="@drawable/shape_circle_shimmer"
            android:layout_marginStart="@dimen/dp_15"
            android:layout_width="@dimen/dp_34"
            android:layout_height="@dimen/dp_34" />

        <LinearLayout
            android:id="@+id/llTitle"
            android:layout_weight="1"
            android:layout_marginStart="@dimen/dp_12"
            android:orientation="vertical"
            android:gravity="center_vertical"
            android:layout_width="0dp"
            android:layout_height="wrap_content">

            <View
                android:id="@+id/mTvTokenName"
                android:background="@drawable/shape_rect_shimmer"
                android:layout_width="@dimen/dp_50"
                android:layout_height="@dimen/dp_14"/>
            <TextView
                android:layout_width="@dimen/dp_100"
                android:layout_height="@dimen/dp_14"
                android:background="@drawable/shape_rect_shimmer"
                android:layout_marginTop="@dimen/line_margin_top" />
        </LinearLayout>

        <LinearLayout
            android:gravity="end"
            android:layout_marginEnd="@dimen/dp_16"
            android:orientation="vertical"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">
            <View
                android:background="@drawable/shape_rect_shimmer"
                android:layout_width="@dimen/dp_50"
                android:layout_height="@dimen/dp_14"/>
            <View
                android:layout_marginTop="@dimen/dp_2"
                android:background="@drawable/shape_rect_shimmer"
                android:layout_width="@dimen/dp_40"
                android:layout_height="@dimen/dp_12"/>
        </LinearLayout>
    </LinearLayout>
</layout>