<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:orientation="vertical"
    android:layout_height="wrap_content">
    <RelativeLayout
        android:layout_width="match_parent"
        android:background="@drawable/shape_card_back"
        android:layout_height="wrap_content">
        <EditText
            android:id="@+id/edit"
            android:layout_height="@dimen/dp_56"
            android:layout_width="match_parent"
            android:paddingStart="@dimen/dp_16"
            android:inputType="text"
            android:background="@color/transparent"
            android:textColorHint="@color/color_hint"
            android:paddingEnd="@dimen/dp_16"
            android:textColor="@color/color_title" />
    </RelativeLayout>
    <TextView
        android:id="@+id/tv_error"
        android:layout_width="wrap_content"
        android:layout_marginTop="@dimen/dp_6"
        android:textSize="@dimen/font_size_small"
        android:textColor="@color/error_red"
        android:layout_height="wrap_content"/>

</LinearLayout>