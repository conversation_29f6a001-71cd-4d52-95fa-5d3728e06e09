<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

    <LinearLayout
        android:paddingEnd="@dimen/dp_16"
        android:paddingStart="@dimen/dp_16"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <LinearLayout
            android:layout_gravity="center"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">

            <androidx.appcompat.widget.AppCompatTextView
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp_1"
                android:text="@string/approve"
                android:textColor="@color/color_title"
                android:textSize="@dimen/font_size_large" />

            <androidx.appcompat.widget.AppCompatImageView
                android:layout_marginStart="@dimen/dp_10"
                android:tint="@color/color_card_back"
                android:src="@drawable/icon_pop_certified"
                android:layout_width="@dimen/dp_12"
                android:layout_height="@dimen/dp_6" />
        </LinearLayout>

        <TextView
            android:text="@string/by_approving_this_you_are_authorizing_full_access"
            android:background="@drawable/shape_card_back"
            android:padding="@dimen/dp_12"
            android:layout_marginTop="-1dp"
            android:textColor="@color/color_title_sub"
            android:textSize="14sp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />
    </LinearLayout>
</layout>