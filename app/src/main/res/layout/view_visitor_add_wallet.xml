<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

    <LinearLayout
        android:orientation="vertical"
        android:gravity="center_horizontal"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <LinearLayout
            android:gravity="center"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <ImageView
                android:src="@drawable/icon_app_logo_dark"
                android:layout_width="@dimen/dp_40"
                android:layout_height="@dimen/dp_40"/>

            <TextView
                android:layout_marginStart="@dimen/dp_12"
                android:text="@string/app_name"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/color_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
        </LinearLayout>

        <TextView
            android:layout_marginTop="@dimen/dp_8"
            android:text="@string/the_depin_wallet_of_choice"
            android:textSize="16sp"
            android:textStyle="bold"
            android:gravity="center"
            android:layout_marginStart="@dimen/dp_16"
            android:layout_marginEnd="@dimen/dp_16"
            android:textColor="@color/color_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

        <TextView
            android:id="@+id/tvAddWallet"
            android:text="@string/add_wallet"
            android:textSize="14sp"
            android:layout_gravity="center"
            android:background="@drawable/btn_shape_gradient_common"
            android:textColor="@color/white"
            android:gravity="center"
            android:layout_marginTop="@dimen/dp_15"
            android:layout_marginBottom="@dimen/dp_15"
            android:layout_width="@dimen/dp_160"
            android:layout_height="@dimen/dp_40"/>

    </LinearLayout>

</layout>