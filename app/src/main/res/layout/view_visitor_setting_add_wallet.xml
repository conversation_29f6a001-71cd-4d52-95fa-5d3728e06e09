<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android" >

    <LinearLayout
        android:orientation="vertical"
        android:gravity="center_horizontal"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <LinearLayout
            android:id="@+id/mSelectorCreateWallet"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_15"
            android:layout_marginTop="@dimen/dp_12"
            android:layout_marginEnd="@dimen/dp_15"
            android:padding="@dimen/dp_20"
            android:gravity="center"
            android:background="@drawable/shape_card_back">

            <LinearLayout
                android:layout_weight="1"
                android:orientation="vertical"
                android:layout_width="0dp"
                android:layout_marginEnd="@dimen/dp_12"
                android:layout_height="wrap_content">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/create_new_wallet_with_mnemonic_phrase"
                    android:textColor="@color/color_title"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:drawableTint="@color/color_title"
                    android:drawableEnd="@drawable/ic_arrow_right_white"/>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp_8"
                    android:text="@string/create_new_wallet_caption_02"
                    android:textColor="@color/color_title_sub"
                    android:textSize="14sp" />
            </LinearLayout>

            <ImageView
                android:layout_width="@dimen/dp_90"
                android:layout_height="@dimen/dp_80"
                android:src="@drawable/icon_add_wallet_create"/>

        </LinearLayout>

        <LinearLayout
            android:id="@+id/clPriKeyWallet"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_15"
            android:layout_marginTop="@dimen/dp_12"
            android:layout_marginEnd="@dimen/dp_15"
            android:padding="@dimen/dp_20"
            android:gravity="center"
            android:background="@drawable/shape_card_back">

            <LinearLayout
                android:layout_weight="1"
                android:layout_marginEnd="@dimen/dp_12"
                android:orientation="vertical"
                android:layout_width="0dp"
                android:layout_height="wrap_content">
                <LinearLayout
                    android:gravity="center"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content">
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/create_wallet_with_private_key"
                        android:textColor="@color/color_title"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:drawableTint="@color/color_title"
                        android:drawableEnd="@drawable/ic_arrow_right_white" />

                </LinearLayout>
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp_8"
                    android:text="@string/create_a_new_wallet_and_backup_private_key"
                    android:textColor="@color/color_title_sub"
                    android:textSize="14sp" />
            </LinearLayout>

            <ImageView
                android:layout_width="@dimen/dp_90"
                android:layout_height="@dimen/dp_80"
                android:src="@drawable/icon_add_wallet_private"/>

        </LinearLayout>

        <LinearLayout
            android:id="@+id/clAAWallet"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_15"
            android:layout_marginTop="@dimen/dp_12"
            android:layout_marginEnd="@dimen/dp_15"
            android:padding="@dimen/dp_20"
            android:gravity="center"
            android:background="@drawable/shape_card_back">

            <LinearLayout
                android:layout_weight="1"
                android:layout_marginEnd="@dimen/dp_12"
                android:orientation="vertical"
                android:layout_width="0dp"
                android:layout_height="wrap_content">
                <LinearLayout
                    android:gravity="center"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content">
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/aa_wallet"
                        android:textColor="@color/color_title"
                        android:textSize="16sp"
                        android:textStyle="bold" />
                    <ImageView
                        android:layout_marginStart="@dimen/dp_5"
                        android:src="@drawable/icon_aa_new"
                        android:layout_width="@dimen/dp_46"
                        android:layout_height="@dimen/dp_16"/>
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@color/white"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:drawableTint="@color/color_title"
                        android:drawableEnd="@drawable/ic_arrow_right_white" />
                </LinearLayout>
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp_8"
                    android:text="@string/aa_wallet_caption"
                    android:textColor="@color/color_title_sub"
                    android:textSize="14sp" />
            </LinearLayout>

            <ImageView
                android:layout_width="@dimen/dp_100"
                android:layout_height="@dimen/dp_85"
                android:src="@drawable/icon_add_wallet_aa_create"/>

        </LinearLayout>

    </LinearLayout>

</layout>