<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center_horizontal"
    android:orientation="vertical">

    <TextView
        android:id="@+id/mTvProgress"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:fontFamily="@font/space_grotesk_bold"
        android:textColor="@color/color_title"
        android:textSize="18sp"
        tools:text="42%" />

    <ProgressBar
        android:id="@+id/mPbLoading"
        style="@style/LoadingProgress"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="30dp"
        android:layout_marginTop="15dp"
        android:layout_marginEnd="30dp"
        tools:progress="42" />
</LinearLayout>