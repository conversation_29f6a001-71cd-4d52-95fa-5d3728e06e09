<?xml version="1.0" encoding="UTF-8"?>
<resources>
  <string name="app_name">아이오페이</string>
  <string name="continue_text">계속</string>
  <string name="yes">예</string>
  <string name="no">아니요</string>
  <string name="cancel">취소</string>
  <string name="close">닫기</string>
  <string name="no_cancel">아니오, 취소합니다.</string>
  <string name="yes_transfer">예, 트랜잭션을 합니다.</string>
  <string name="ok">승인</string>
  <string name="export">엑스포트</string>
  <string name="delete">삭제</string>
  <string name="private_key">개인 키</string>
  <string name="address">주소</string>
  <string name="key_store">키스토어</string>
  <string name="setting">설정</string>
  <string name="help">도움말</string>
  <string name="menu_switch">전환</string>
  <string name="copy_success">복사 됨!</string>
  <string name="current_balance">현재 잔액: </string>
  <string name="balance">잔액:</string>
  <string name="iotx">%1$s IOTX</string>
  <string name="xrc20_value">≈ $ %1$s</string>
  <string name="iotx_string">IOTX</string>
  <string name="iotex_string">아이오텍스</string>
  <string name="native_string">네이티브</string>
  <string name="web3_string">웹3</string>
  <string name="eth_string">ETH</string>
  <string name="amount">금액</string>
  <string name="choose_coin">코인 선택</string>
  <string name="choose_token">토큰 선택</string>
  <string name="offer_amount">금액:</string>
  <string name="time">시간</string>
  <string name="account">계정</string>
  <string name="from">보낸 사람</string>
  <string name="to">받는 분</string>
  <string name="gas_fee">가스 요금 (IOTX)</string>
  <string name="transfer">전송</string>
  <string name="transaction">트랜잭션</string>
  <string name="hash">해시</string>
  <string name="nonce">Nonce</string>
  <string name="gas_fee_only">가스 요금</string>
  <string name="max_fee_only">최대 가스 요금</string>
  <string name="gas_fee_value">%1$s IOTX</string>
  <string name="network_switch">네트워크 스위치 - %1$s</string>
  <string name="network">네트워크</string>
  <string name="network_desc">네트워크 추가 및 편집</string>
  <string name="community">커뮤니티</string>
  <string name="change_wallet">지갑 관리</string>
  <string name="change_wallet_desc">지갑을 쉽게 관리</string>
  <string name="clear_auth_cache">DApp 인증 관리</string>
  <string name="clear_all">모두 지우기</string>
  <string name="all">모두</string>
  <string name="dapps">디앱스</string>
  <string name="cleared_successfully">성공적으로 삭제됨</string>
  <string name="change_pin">PIN 변경</string>
  <string name="address_book">주소록</string>
  <string name="address_book_desc">편리한 주소 기록</string>
  <string name="web3Address">Web3주소</string>
  <string name="receive">받기</string>
  <string name="send_to">보내기</string>
  <string name="receipt">수신자</string>
  <string name="status">상태</string>
  <string name="action_fee">액션 요금</string>
  <string name="success">성공</string>
  <string name="failed">실패</string>
  <string name="waiting">대기 중</string>
  <string name="menu_wallet">지갑</string>
  <string name="menu_actions">트랜잭션</string>
  <string name="menu_news">뉴스</string>
  <string name="menu_setting">설정</string>
  <string name="menu_discover">발견</string>
  <string name="quick_wallet_switch">빠른 지갑 스위치</string>
  <string name="to_contract">계약으로</string>
  <string name="bucketid">버킷 ID</string>
  <string name="receipt_address">수신자 주소</string>
  <string name="contract_is">계약 : %1$s</string>
  <string name="version">버전</string>
  <string name="scan_qr_code">스캔 QR 코드</string>
  <string name="edit_gas">편집 가스 여금</string>
  <string name="total">총</string>
  <string name="switch_network">네트워크 전환</string>
  <string name="explorer">익스플로러</string>
  <string name="loading">로딩 중입니다</string>
  <string name="token_lower_case">토큰</string>
  <string name="tokens">토큰스</string>
  <string name="qev">Qev</string>
  <string name="gwei">GWEI</string>
  <string name="activities">액티비티스</string>
  <string name="create_wallet_button">지갑 만들기</string>
  <string name="edit_wallet_button">지갑 편집</string>
  <string name="import_wallet_button">지갑에 수입하기</string>
  <string name="wallet_address">지갑 주소:</string>
  <string name="wallet_detail_title">지갑</string>
  <string name="wallet_list">지갑</string>
  <string name="xrc20_tokens">XRC20 토큰</string>
  <string name="xrc20_tokens_desc">%s 토큰 보기</string>
  <string name="nft_tokens">대체 불가능한 토큰(NFT)</string>
  <string name="history">기록</string>
  <string name="transfer_iotx">IOTX 전송</string>
  <string name="transfer_send_iotx">IOTX 보내기</string>
  <string name="transfer_receive_iotx">IOTX 받기</string>
  <string name="claim_vita">VITA 클레임</string>
  <string name="claim_vita_content">스마트 콘트랙트를 통해 VITA를 청구할 것입니다. 주기당 한 번만 청구할 수 있습니다 - 다른 요청은 실패합니다.</string>
  <string name="claim_vita_success">클레임 성공적으로 전송되었습니다.</string>
  <string name="claim_vita_error">클레임 성공적으로 전송되지 않았습니다. 나중에 다시 시도하십시오.</string>
  <string name="refresh_action">기다려주십시오.</string>
  <string name="activity_wallet_name">지갑</string>
  <string name="activity_create_wallet_name">지갑 만들기</string>
  <string name="activity_create_wallet_title">지갑 만들기</string>
  <string name="wallet_create_toolbar_title">지갑 만들기</string>
  <string name="wallet_create_form_thumbnail">지갑 썸네일 이미지 선택</string>
  <string name="wallet_create_form_name">새 지갑 이름 선택</string>
  <string name="wallet_import_toolbar_title">지갑에 수입하기</string>
  <string name="import_scan_qr">QR 코드 스캔</string>
  <string name="keystore_recognized">키스토어 인식</string>
  <string name="input_private_key">개인키를 입력하십시오!</string>
  <string name="input_address">지갑 주소를 입력하십시오!</string>
  <string name="input_wallet_name">지갑 이름 :</string>
  <string name="activity_user_agreement_toolbar">아이오페이에 오신 것을 환영합니다</string>
  <string name="user_agreement_title">이용 약관</string>
  <string name="user_agreement_content"><![CDATA[
 IoTeX Foundation Ltd.(총칭하여 \"IoTeX\", \"당사\", \"당사\")는 당사 웹사이트(iotex.io)는 물론 관련 애플리케이션 및 제품(통칭하여 당사의 \"서비스\", \"사이트\", \"제품\" 및 \"지갑\"). 당사 서비스에 액세스하고 사용함으로써 귀하는 다음 이용약관(통칭하여 본 \"약관\")에 동의하는 것입니다. 이 모든 약관에 동의하지 않으면 당사 서비스를 사용하지 마십시오. IoTeX 서비스에 대한 귀하의 액세스 및 사용은 당사의 개인정보 보호정책을 포함하여 당사의 이용약관에 명시된 각 조건을 수락하고 준수하기로 동의하는 것으로 구성되며, 이는 참조로 당사 약관에 통합됩니다. 귀하가 귀하의 조직을 대신하여 당사 서비스를 사용하는 경우 해당 조직은 본 약관에 동의합니다. 지갑을 생성하거나 당사 웹, 모바일 및 데스크탑 애플리케이션을 다운로드 또는 실행하거나 당사 웹사이트를 방문함으로써 귀하는 당사 약관에 동의하는 것입니다. 관할 법률에 따라 허용되는 경우에만 서비스를 사용할 수 있습니다. 본 약관이 귀하에게 적용되는 모든 법률, 규칙 및 규정을 준수하는지 확인하십시오.

 IoTeX 서비스에 액세스하면 명시된 목적으로 사용하는 데 동의하는 것입니다. 당사 서비스의 불법적, 차별적, 괴롭힘, 명예 훼손 및 유해한 사용 또는 당사 서비스를 통한 IoTeX 또는 기타 사용자의 관련 법률 및 법적 권리 위반은 허용되지 않습니다. 아이오텍스는 귀하가 전체 이용약관을 준수하지 않아 발생하는 손실이나 손해에 대해 책임을 지지 않습니다. 지갑을 생성할 때 지갑에 대한 액세스 및/또는 제어권 상실을 방지하기 위해 예방 조치를 취하는 것이 좋습니다. 제안된 조치에는 다른 웹사이트나 온라인 서비스에 사용하지 않는 강력한 암호 생성이 포함되지만 이에 국한되지 않습니다. (b) 지갑에서 제공하는 백업 기능을 사용하거나 외부 하드 드라이브에서 개인 키와 니모닉(백업)을 보호합니다. © 지갑과 관련된 개인 키 및 니모닉(백업)을 보호하여 지갑의 보안을 유지합니다. (d) 귀하의 지갑과 관련된 보안 위반이 의심되는 경우 즉시 당사에 통지합니다.

 본 약관은 수시로 수정, 변경 또는 보완될 수 있습니다. 변경 사항을 최신 상태로 유지하려면 이 페이지를 정기적으로 방문하는 것이 좋습니다. 귀하가 IoTeX 서비스를 계속 사용하면 당사가 수정, 변경 또는 보완한 본 약관에 대한 귀하의 수락이 확인됩니다.

 개인 정보 정책
 귀하의 개인 정보 및 데이터 보안은 우리에게 매우 중요합니다. 아이오텍스와 그 계열사는 귀하의 개인 데이터를 책임 있는 관리, 보호 및 사용하기 위해 최선을 다하고 있습니다. 귀하가 당사 제품을 사용할 때 당사는 귀하로부터 또는 귀하에 관한 개인 데이터 또는 기타 정보를 수집할 수 있습니다. 일반적으로 우리는 이 정보를 사용하여 서비스를 제공하고 제품의 품질을 향상시킵니다.

 아이오텍스는 개인 식별 정보를 자동으로 수집하지 않습니다. 이메일 주소 및 소셜 미디어 핸들과 같은 연락처 세부 정보는 당사와 통신하거나 버그를 보고하거나 IoTeX Wallet과 관련된 문제를 해결할 때 요청할 수 있습니다. 당사는 귀하에게 서비스를 제공하고, 법률을 준수하고, IoTeX 서비스를 개선하고, 당사의 권리를 보호하는 경우를 제외하고 귀하의 개인 정보를 사용하지 않습니다.

 전체 이용약관 및 개인정보 보호정책(https://iotex.io/policy)을 검토하십시오. 질문이나 우려 사항이 있으면 ****************로 문의하십시오.

]]></string>
  <string name="create_password_notice_title">주의</string>
  <string name="setting_passwords_note">%1$s에 대한 고유 암호를 입력합니다.</string>
  <string name="wallet_password_title">암호</string>
  <string name="wallet_password_skip">건너뛰기</string>
  <string name="back_to_simple">기본 PIN으로 전환</string>
  <string name="success_save_setting">지갑 설정을 저장했습니다.</string>
  <string name="wallet_new_button">새로운 지갑</string>
  <string name="create_password_notice_content">다음 페이지에 입력하는 비밀번호는이 기기에서 HD 월렛 잠금을 해제하는 데만 사용됩니다. 분실 된 경우 복원 할 수 없습니다. 안전한 장소에 기록하십시오! 
</string>
  <string name="create_password_notice_confirm">지갑을 성공적으로 만들었습니다. 개인 키를 기록하고 안전한 장소에 보관하십시오. 스크린 샷이나 다른 사람과 공유하지 마십시오!
	</string>
  <string name="activity_create_password_name">비밀번호 만들기</string>
  <string name="setup_password_title">비밀번호를 입력하십시오.</string>
  <string name="confirm_password_title">비밀번호 확인하십시오</string>
  <string name="password_touch_ID_title">터치 ID</string>
  <string name="password_touch_ID_description">터치 ID를 사용하여 잠금 해제 하시겠습니까?</string>
  <string name="key_store_name">아이오페이키스토어</string>
  <string name="login_failed">로그인 실패 !</string>
  <string name="fingerprint_login_failed">지문 로그인에 실패했습니다!</string>
  <string name="error_get_pass_info">암호 정보를 가져올 수 없습니다.</string>
  <string name="fingerprint_or_pass_unlock">비밀 번호 또는 지문을 사용하여 잠금 해제</string>
  <string name="password_create">비밀번호 만들기</string>
  <string name="password_create_reset">새 PIN 입력</string>
  <string name="advance_password">또는 고급 PIN을 사용해 보시나요?</string>
  <string name="input_password_private_key">비밀번호를 확인하십시오 :</string>
  <string name="view_private_key">개인키 보기</string>
  <string name="prompt_point_at_a_barcode">QR 코드로 카메라를 가리 킵니다.</string>
  <string name="cd_close_button">뒤로</string>
  <string name="cd_flash_button">카메라 플래시 전환</string>
  <string name="select_address">지갑 주소 선택</string>
  <string name="key_invalid">키가 유효하지 않습니다!</string>
  <string name="wallet_invalid">지갑 아이디는 유효하지 않습니다!</string>
  <string name="sending">보내중</string>
  <string name="wallet_list_thumbnail">지갑 썸네일 이미지</string>
  <string name="wallet_header_img">지갑 헤더 이미지</string>
  <string name="icon_key_store">키스토어</string>
  <string name="edit_wallet">지갑 설정</string>
  <string name="edit_wallet_name">지갑 이름 바꾸기</string>
  <string name="switch_net_title">네트워크 전환</string>
  <string name="new_net_title">새 네트워크</string>
  <string name="main">주요</string>
  <string name="test">테스트</string>
  <string name="main_network">주요 네트워크</string>
  <string name="test_network">네트워크 테스트</string>
  <string name="delete_wallet_confirm">%s 을 (를) 삭제하려고합니다.  수행 하시겠습니까?</string>
  <string name="clear_trust_confirm">DApps에서 인증 캐시를 지우려고 합니다. 이 작업을 수행하시겠습니까?</string>
  <string name="open_notification_confirm">앱이 알림을 열지 않음, 알림 열기로 이동</string>
  <string name="validate_not_null">필드는 비워둘 수 없습니다.</string>
  <string name="validate_wallet_name_length">지갑 이름이 너무 깁니다.</string>
  <string name="validate_private_key">개인키를 입력하십시오!</string>
  <string name="please_input_address">주소를 입력해주세요!</string>
  <string name="validate_alias_exist">이 별칭은 이미 사용 중입니다!</string>
  <string name="validate_address_exist">이 지갑 주소가 이미 있습니다. 다른 것을 시도하십시오!</string>
  <string name="erc20_balance">%1$s %2$s</string>
  <string name="gas_fee_symbol">가스 요금 (%1$s)</string>
  <string name="amount_not_empty">0이 아닌 금액이 필요합니다.</string>
  <string name="stake_amount_must">스테이크 금액은 100초과해야합니다.</string>
  <string name="gas_limit">가스 한도</string>
  <string name="must_not_empty">필수 항목입니다.</string>
  <string name="token_transferred">전송된 토큰</string>
  <string name="update_now">지금 업데이트</string>
  <string name="update_available">사용 가능한 업데이트</string>
  <string name="view_wallet">%1$s 보기</string>
  <string name="auto_wallet_name">지갑 %1$s</string>
  <string name="could_not_load_certificate">인증서를 로드할 수 없습니다!</string>
  <string name="no_network">사용할 수 있는 네트워크가 없습니다.</string>
  <string name="network_error">네트워크 오류.</string>
  <string name="pref_previously_started">이전에 출시되었습니다.</string>
  <string name="detail">세부 사항</string>
  <string name="url_invalid">URL이 유효하지 않습니다!</string>
  <string name="claim">클레임</string>
  <string name="claim_vita_item">VITA 클레임</string>
  <string name="claim_as">클레임으로</string>
  <string name="fetching">가져 오는 중</string>
  <string name="wallet_send">보내기</string>
  <string name="wallet_receive">받기</string>
  <string name="wallet_deposit">DEPOSIT</string>
  <string name="wallet_stake">스테이크</string>
  <string name="no_history">기록 없음.</string>
  <string name="transfer_xrc20">XRC20 전송</string>
  <string name="transfer_send_xrc20">XRC20 보내기</string>
  <string name="transfer_receive_xrc20">XRC20 받기</string>
  <string name="transfer_send">보내기</string>
  <string name="transfer_receive">받기</string>
  <string name="transfer_amount_invalid">밸런스가 부족합니다!</string>
  <string name="amount_must_greater_zero">금액은 0보다 커야합니다.</string>
  <string name="no_tokens">아직 추가된 토큰이 없습니다.</string>
  <string name="no_transactions">거래가 없습니다</string>
  <string name="bid">입찰</string>
  <string name="bid_vita">VITA입찰</string>
  <string name="discord">디스코드</string>
  <string name="to_VITA_offer">VITA에 대한 입찰가 설정</string>
  <string name="offer">입찰</string>
  <string name="you_send">당신은 보내려고합니다 …</string>
  <string name="source_address">이 주소부터</string>
  <string name="to_address">이 주소까지</string>
  <string name="method">방법</string>
  <string name="message">메세지</string>
  <string name="gas_price">가스 가격</string>
  <string name="high_gas_price_warning">*가스 요금이 평소보다 높으며 제안된 가스 가격은 1입니다. 이제 최대 가스 요금은 %s 있습니다.</string>
  <string name="confirm_action">이 작업을 수행 하시겠습니까?</string>
  <string name="successful_broadcasting">성공적인 방송</string>
  <string name="successful_broadcasting_tip1">TX가 네트워크로 브로드 캐스트되었습니다. 그렇다고 블록 체인에서 채굴되고 있다는 의미는 아닙니다. 확인에는 보통 몇 초가 걸립니다.</string>
  <string name="successful_broadcasting_tip2">다음 TX 해시를 클릭하여 상태를 지금 확인하거나 TX 해시를 저장하고 나중에 상태를 확인할 수 있습니다.</string>
  <string name="your_hash_value">TX 해시 :</string>
  <string name="smart_contract">스마트 콘트랙트</string>
  <string name="contract_address">계약 주소</string>
  <string name="please_enter">입력하세요</string>
  <string name="please_select">선택하세요</string>
  <string name="enter_correct_number">유효한 숫자를 입력하세요.</string>
  <string name="can_not_exceed_your_balance">잔액을 초과 할 수 없습니다.</string>
  <string name="owner_address">소유자: %s</string>
  <string name="owner">소유자</string>
  <string name="owner_eth">소유자 (Eth)</string>
  <string name="switched_net">%s (으)로 전환됨</string>
  <string name="twitter">트위터</string>
  <string name="telegram_group">텔레그램 그룹</string>
  <string name="facebook">페이스북</string>
  <string name="reddit">레딧</string>
  <string name="youtube">유튜브</string>
  <string name="forum">포럼</string>
  <string name="medium">미디엄</string>
  <string name="date">일</string>
  <string name="switch_language_title">언어 전환</string>
  <string name="general">일반적인</string>
  <string name="language">언어</string>
  <string name="language_desc">지원되는 다른 언어로 애플리케이션을 번역합니다.</string>
  <string name="general_desc_gp">언어, 알림, ETH 주소</string>
  <string name="general_desc">언어, Web3 주소</string>
  <string name="address_invalid">주소가 유효하지 않습니다!</string>
  <string name="need_permission">액세스 권한을 허용해야 합니다.</string>
  <string name="found_address">주소</string>
  <string name="xapps_title">앱스</string>
  <string name="news">뉴스</string>
  <string name="action_error">이런!%s</string>
  <string name="import_by">으로</string>
  <string name="password">비밀번호</string>
  <string name="input_keystore_json">개인 키스토어를 붙여 넣기</string>
  <string name="validate_keystore_json">키 스토어를 입력하십시오!</string>
  <string name="keystore_json_invalid">키 스토어가 유효하지 않습니다!</string>
  <string name="validate_password">비밀번호가 필요합니다</string>
  <string name="or">또는</string>
  <string name="type_password">비밀번호를 입력하십시오</string>
  <string name="exit_app_tip">한 번 더 누르면 응용 프로그램이 종료됩니다</string>
  <string name="webview_title_connect">ioPay와 연결하시겠습니까?</string>
  <string name="timeout">타임 아웃</string>
  <string name="keystore_not_init">애플리케이션 키 저장소가 초기화되지 않았습니다.</string>
  <string name="Search">입력</string>
  <string name="Search_dapp_hint">링크를 사용하여 DApp으로 직접 이동</string>
  <string name="plus_vita"> %s 비타</string>
  <string name="evm_transactions">Contract</string>
  <string name="xrc20_transactions">XRC20</string>
  <string name="stake_transactions">스테이크</string>
  <string name="wallet_register_switch">알림 수신</string>
  <string name="wallet_register_switch_desc">푸시 알림을 사용하도록 설정하여 거래를 추적합니다.</string>
  <string name="eth_address_switch">Web3 주소 표시</string>
  <string name="web3_address_switch">Web3 주소 표시</string>
  <string name="eth_address_switch_desc">애플리케이션에서 이더리움 지갑 주소를 사용하십시오.</string>
  <string name="notification">공고</string>
  <string name="receive_token">잔액 %1$s %2$s 을(를) 받았습니다.</string>
  <string name="claim_receive_token">잔액 %1$s %2$s 을(를) 받았습니다.</string>
  <string name="got_message">아이오페이의 새 메시지</string>
  <string name="unreach_host">푸시 서비스 호스트에 연결할 수 없습니다.</string>
  <string name="invalid_action">잘못된 작업</string>
  <string name="invalid_receiver">잘못된 수신자</string>
  <string name="invalid_amount_number">금액은 숫자여야 합니다.</string>
  <string name="unsupport_type">지원되지 않는 유형! 대문자가 영향을 미칠 수 있음</string>
  <string name="payment">지불</string>
  <string name="wallet_connect">지갑 연결</string>
  <string name="connect_wallet">지갑 연결</string>
  <string name="content">콘텐츠</string>
  <string name="developer_mode_tips">개발자 모드 표시</string>
  <string name="developer_mode">개발자 모드</string>
  <string name="login_with_current_wallet">현재 지갑으로 로그인하시겠습니까?</string>
  <string name="app_login_title">%s가 ioPay 지갑 주소 정보 사용을 요청합니다.</string>
  <string name="ucam">유캄</string>
  <string name="app_login_content">아래 지갑으로 로그인하시겠습니까?</string>
  <string name="switch_wallet">지갑 전환</string>
  <string name="login_expire_time">인증 시간 초과에 대해 다시 시도하십시오.</string>
  <string name="invalid_erc20_type">잘못된 형식</string>
  <string name="receipt_address_required">접수 주소는 필수!</string>
  <string name="add_xrc20">추가하다</string>
  <string name="update_iopay">아이오페이 업데이트?</string>
  <string name="no_thank">괜찮아요</string>
  <string name="update">업데이트</string>
  <string name="update_des">ioPay는 최신 버전으로 업데이트할 것을 권장합니다. 업데이트를 다운로드하는 동안 이 앱을 계속 사용할 수 있습니다.</string>
  <string name="g_play">구글 플레이</string>
  <string name="device_must_api_level">죄송합니다! ioPay를 사용하려면 Android 6.0(API 레벨 23) 이상의 기기가 필요합니다.</string>
  <string name="error_get_account">지갑 계정을 로드할 수 없습니다</string>
  <string name="you_doing">당신은 할 것입니다 ...</string>
  <string name="stake_staking_buckets">스테이킹 버킷 세부 정보</string>
  <string name="stake_vote_for">투표하기</string>
  <string name="stake_stake_duration">스테이크 기간</string>
  <string name="auto_stake">자동 스테이크</string>
  <string name="stake_bucket_status">버킷 상태</string>
  <string name="bucket">버킷</string>
  <string name="unstake">언스테이크</string>
  <string name="stake_details">스테이크 세부 정보</string>
  <string name="nft">x %1$s</string>
  <string name="nft_string">NFT</string>
  <string name="ucam_pioneer">유캄 파이오니아</string>
  <string name="unknown_action">알 수 없는 작업입니다. 최신 ioPay 버전으로 업데이트하십시오.</string>
  <string name="NFT_title">NFT</string>
  <string name="nft_no">번호 %1$s</string>
  <string name="apr_no">APR: %1$s</string>
  <string name="create_stake">스테이크 만들기</string>
  <string name="btn_create_stake">새로운 스테이크</string>
  <string name="staked_amount">스테이크드 금액</string>
  <string name="total_staked_amount">총 스테이크드</string>
  <string name="pending_unstake_amount">언스테이킹</string>
  <string name="ready_to_withdraw_amount">인출 준비 완료</string>
  <string name="total_stakes_number">총 투표수</string>
  <string name="title_my_stakes">내 스테이킹</string>
  <string name="my_stakes_list">내 스테이킹 버킷</string>
  <string name="stake_actions">편집하다</string>
  <string name="votes">%1$s 투표</string>
  <string name="buckets">%1$s 버킷</string>
  <string name="stake_unstake">언스테이크</string>
  <string name="stake_withdraw">스테이크 인출</string>
  <string name="stake_restake">리스테이크</string>
  <string name="stake_add_deposit">스테이크 추가</string>
  <string name="stake_change_candidate">델리게이트 변경</string>
  <string name="stake_register_candidate">델리게이트 등록</string>
  <string name="stake_transfer">버킷 재할당</string>
  <string name="stake_candidate_update">델리게이트 업데이트</string>
  <string name="to_string">받는 사람: %1$s</string>
  <string name="stake_delegate_name">델리게이트 이름</string>
  <string name="stake_amount_sub">처리된 후에만 이 값을 늘릴 수 있습니다.</string>
  <string name="stake_current_balance">지갑 잔액: %1$s IOTX.</string>
  <string name="stake_duration">스테이크 기간</string>
  <string name="stake_duration_days">일</string>
  <string name="stake_duration_sub">언제든지 확장할 수 있습니다.</string>
  <string name="stake_auto_stake">자동-스테이크</string>
  <string name="stake_auto_stake_sub">자동 스테이크를 켜서 스테이크 기간을 잠그고 보너스 투표를 무기한으로 유지하십시오.</string>
  <string name="stake_power">스테이킹 파워</string>
  <string name="stake_power_votes">%1$s 투표</string>
  <string name="stake_power_estimation">스테이킹 파워 추정</string>
  <string name="stake_power_estimation_sub">스테이크 기간 동안의 총 투표수.</string>
  <string name="stake_votes">투표</string>
  <string name="stake_delegates_list">델리게이트스 목록</string>
  <string name="stake_duration_not_none">스테이크 기간을 입력하십시오</string>
  <string name="stake_delegate_valiate">스테이크 기간을 입력하십시오</string>
  <string name="stake_delegate">델리게이트</string>
  <string name="stake_my_reward">내 보상</string>
  <string name="stake_my_stake">내 스테이크</string>
  <string name="stake_title">스테이크</string>
  <string name="from_string">%1$s 부터</string>
  <string name="stake_status_ongoing">진행 중</string>
  <string name="stake_status_unstaking">언스테이킹</string>
  <string name="stake_status_withdrawble">인출 준비 완료</string>
  <string name="stake_status_unstaking_prefix">%1$s 까지</string>
  <string name="stake_status_withdrawable_prefix">%1$s 에서</string>
  <string name="stake_status_no_stake_starttime">스테이크 시작 시간 없음</string>
  <string name="stake_status_no_stake_starttime_prefix">---</string>
  <string name="stake_duration_days_string">%1$s 일</string>
  <string name="not_applicable">적용되지 않음</string>
  <string name="anytime">언제 든 지</string>
  <string name="transfer_address">버킷을 주소로 전송: </string>
  <string name="transfer_stake_warning">버킷 재할당은 버킷 소유권을 다른 주소로 이전합니다. 선택한 주소에 대한 개인 키가 있는지 확인하십시오. 교환 주소를 사용하지 마십시오.</string>
  <string name="transfer_stake">버킷 재할당(번호 %s)</string>
  <string name="add_stake_title">스테이크 예치(번호 %d)</string>
  <string name="delegate_rank_string">순위:# %s</string>
  <string name="withdraw_action_title">인출 버킷(번호 %s)</string>
  <string name="withdraw_action_confirm">토큰을 출금하시겠습니까?</string>
  <string name="button_select">선택</string>
  <string name="after_string">%s 이후</string>
  <string name="add_stake_desc">새로 추가된 토큰은 델리게이트 이름, 남은 지분/투표 기간, 자동 지분 선택 등을 포함한 현재 버킷 설정과 일치합니다.</string>
  <string name="total_amount">총 금액</string>
  <string name="stake_auto_stake_only">자동 스테이크 전용</string>
  <string name="stake_bucket_index">버킷 인덱스</string>
  <string name="unstake_action_title">언스테이킹 버킷(번호 %s )</string>
  <string name="unstake_action_confirm">스테이킹을 취소하시겠습니까? 토큰을 인출하려면 3일이 소요됩니다.</string>
  <string name="amount_must_greater_100">금액은 100IOTX 이상이어야 합니다.</string>
  <string name="off">끄다</string>
  <string name="stake_duration_max">기간은 1051일 미만이어야 합니다.</string>
  <string name="stake_duration_restake_min">기간은 %s 일 이상이어야 합니다.</string>
  <string name="no_bucket">버킷없음!</string>
  <string name="bucket_index">버킷 인덱스</string>
  <string name="burndrop_eligible">번-드롭 적격</string>
  <string name="burndrop_eligible_tips">Burn-Drop 자격을 얻기 위한 ≥ 91일의 자동 스테이킹.</string>
  <string name="format_number">#,###</string>
  <string name="complete">완성</string>
  <string name="xrc20_token_address">XRC20 토큰 주소</string>
  <string name="token_address">토큰 주소</string>
  <string name="custom_tokens">사용자 정의 토큰</string>
  <string name="not_exist">존재하지 않음</string>
  <string name="address_already_exists">주소가 이미 존재합니다.</string>
  <string name="spender">지출자</string>
  <string name="owner_address_string">소유자 주소</string>
  <string name="xrc_send">보내기</string>
  <string name="xrc_receive">받기</string>
  <string name="xrc_more">더</string>
  <string name="xrc_swap">스왑</string>
  <string name="max">최대</string>
  <string name="take_maximum">최대값을 취하기</string>
  <string name="saved_successfully">성공적으로 저장되었다</string>
  <string name="saved_fail">저장 실패</string>
  <string name="action">액션</string>
  <string name="compound">복합</string>
  <string name="compound_title">복리 이자 설정</string>
  <string name="compound_tip">복합 이자를 받으려면 버킷을 선택하십시오.</string>
  <string name="compound_tip_content1">\"복합 버킷\"을 정의하여 스테이킹 보상을 자동으로 다시 스테이킹하도록 선택할 수 있습니다. 버킷은 모든 스테이킹 보상(Hermes 델리게이트만 해당)을 받고 사용자 복리를 극대화합니다.</string>
  <string name="compound_tip_content2">참고: 복리 버킷에는 Stake-Lock이 활성화되어 있어야 합니다.</string>
  <string name="remove">%s 제거</string>
  <string name="index_no">번호 %1$s</string>
  <string name="buckets_actions">버킷</string>
  <string name="other_method">실행</string>
  <string name="menu_transactions">트랜잭션스</string>
  <string name="update_json_url">https://raw.githubusercontent.com/hunglmtb/AppUpdater/master/app/update-changelog.json</string>
  <string name="switched_main_net">메인넷으로 전환</string>
  <string name="switched_test_net">테스트넷으로 전환</string>
  <string name="new_rpc_network">새로운 RPC 네트워크</string>
  <string name="use_custom_rpc_network">대신 사용자 지정 RPC 네트워크 사용</string>
  <string name="other">다른</string>
  <string name="network_name">네트워크 이름</string>
  <string name="rpc_address">RPC 주소</string>
  <string name="symbol_optional">심벌(옵션의)</string>
  <string name="name_optional">이름(선택 가능)</string>
  <string name="decimals_optional">소수（선택 가능)</string>
  <string name="add">추가하기</string>
  <string name="add_network_incomplete_error">필요한 모든 필드를 입력하십시오</string>
  <string name="add_network_success">네트워크 추가 성공</string>
  <string name="method_contract_creation">계약 작성</string>
  <string name="invalid_network_address_tip">잘못된 RPC 주소!</string>
  <string name="deposit">입금하기</string>
  <string name="deposit_add_iotx">IOTX 추가</string>
  <string name="deposit_add_other">XRC20 토큰 추가</string>
  <string name="deposit_hint0_1">이 주소로 iotx 전송</string>
  <string name="deposit_hint1_1">거래소에서 철수하기</string>
  <string name="deposit_hint1_2">인기 있는 거래 쌍의 교환 목록에서 IOTX를 받으세요.</string>
  <string name="deposit_hint2_1">스왑 도구에서 가져오기</string>
  <string name="deposit_hint2_2">DEX를 통해 다른 체인의 자산을 IOTX로 스왑합니다.</string>
  <string name="deposit_hint3_1">이더리움에서 토큰 보내기</string>
  <string name="deposit_hint3_2">ioTube 및 mimo를 사용하여 ERC20 토큰을 입금하십시오. </string>
  <string name="deposit_iotube_hint_1">방문하시기 바랍니다</string>
  <string name="deposit_iotube_hint_2">ioTube는 다른 ETH 지갑을 통해 IOTX 계정에 XRC20 자산을 추가합니다 (</string>
  <string name="deposit_iotube_hint_3">자세한 내용은 여기</string>
  <string name="deposit_iotube_hint_4">) XRC20 자산을 mimo로 IOTX로 교환합니다.</string>
  <string name="deposit_iotube_hint_long">ETH 지갑의 기타 자산. ioTube를 사용하여 ETH 지갑에서 IoTeX로 자산을 전송한 다음 mimo(IoTeX의 DEX)를 통해 IOTX로 교환할 수 있습니다. 다음은 ioTube에서 지원하는 자산 목록입니다: WBTC, ETH, BUSD, UNISWAP, Paxos Gold.</string>
  <string name="exchange_binance">바이낸스</string>
  <string name="exchange_gate">입구</string>
  <string name="exchange_upbit">업비트</string>
  <string name="exchange_kucoin">쿠코인</string>
  <string name="exchange_bittrex">비트렉스</string>
  <string name="exchange_mxc">MXC</string>
  <string name="exchange_coindcx">코인DCX</string>
  <string name="exchange_mimo">미모</string>
  <string name="im_token">im토큰</string>
  <string name="meta_mask">메타마스크</string>
  <string name="trust_wallet">트러스트 지갑</string>
  <string name="deposit_other_wallet_tip">ERC20에서 XRC20으로 변환하려면 지갑을 클릭하여 계속하십시오.</string>
  <string name="deposit_other_tip">다른 지갑 앱의 ioTube를 DApp으로 방문하려면 이 링크를 복사하세요.</string>
  <string name="app_not_found">대상 앱을 찾을 수 없음</string>
  <string name="backup_title">개인 키를 백업하세요.</string>
  <string name="back_up_your_privatekey">개인 키는 자신에게만 보관하십시오. 메모에 적어서 안전하게 보관하십시오. 저장했는지 확인하십시오.</string>
  <string name="err_get_private_key">지갑 계정을 로드할 수 없습니다. 새 계정을 시도하십시오.</string>
  <string name="empty_stake_tip_title">아이오텍스 네트워크 보안을 위한 IOTX 스테이크</string>
  <string name="empty_stake_tip_content">아이오텍스 네트워크는 63명의 분산 대리인이 관리합니다. 델리게이트에 투표하면 네트워크 거버넌스에 참여하고 IOTX 보상을 받을 수 있습니다!</string>
  <string name="how_to_buy_iotx">IOTX &gt;&gt; 구입하는 방법</string>
  <string name="btn_copy_wallet_address">지갑 주소 복사</string>
  <string name="dapp_changelly">Changelly</string>
  <string name="changelly_url">https://changelly.com/ko/exchange/usdt/iotx</string>
  <string name="save">저장</string>
  <string name="describe">묘사</string>
  <string name="name">이름</string>
  <string name="give_it_a_name">이름을 입력해주세요</string>
  <string name="new_address">뉴 주소</string>
  <string name="address_list">주소 목록</string>
  <string name="send_choose_coin">송신-선택 코인</string>
  <string name="type_your_new_address">새로운 ETH 주소를 입력해 주세요.</string>
  <string name="format_balance_small">0.00000001</string>
  <string name="format_balance_3_decimal_places">##0.000</string>
  <string name="format_balance_2_decimal_places">##0.00</string>
  <string name="format_balance_1_decimal_places">##0.0</string>
  <string name="format_balance_decimal_places">.</string>
  <string name="format_balance_qian_decimal">,</string>
  <string name="input_verify_private_key">여기에서 개인 키를 확인하십시오.</string>
  <string name="choose_deposit_iotx">좋아하는 방법을 선택하여 ioPay 지갑에 토큰을 입금하세요!</string>
  <string name="wallet_error_tips">개인 키 데이터가 손상되었습니다.</string>
  <string name="learn_more"><u> 더 알아보기</u></string>
  <string name="wallet_learn_more_content">ioPay는 현재 다음 이유 중 하나로 인해 개인 키를 읽을 수 없습니다.</string>
  <string name="wallet_learn_more_content1">1. 이전 휴대폰 백업에서 ioPay 지갑을 복원했지만 백업에서 휴대폰 키 저장소에 저장된 개인 키에 액세스할 수 없습니다.</string>
  <string name="wallet_learn_more_content2">2. 휴대전화에 개인 키 데이터가 키 저장소에서 지워지는 문제가 발생했습니다.</string>
  <string name="wallet_learn_more_content3">귀하의 개인 키는 유출되지 않았습니다. 개인 키를 백업한 경우 ioPay를 제거/재설치하고 개인 키를 입력하여 지갑에 액세스할 수 있습니다. 개인 키를 백업하지 않으면 지갑을 사용하여 거래를 보내거나 스마트 콘트랙트에 서명할 수 없습니다. 질문이 있는 경우 Telegram에 문의하십시오.</string>
  <string name="what_does_it_mean">무슨 뜻인가요?</string>
  <string name="missing_private_key">개인 키 분실</string>
  <string name="what_sign_message">서명 메시지</string>
  <string name="edit">편집</string>
  <string name="token">토큰</string>
  <string name="add_token">토큰 추가</string>
  <string name="stake">스테이크드</string>
  <string name="error">오류</string>
  <string name="watch_address_string">이것은 \"감시 주소\"입니다. 가져올 때 개인 키를 제공하지 않았기 때문에 이 지갑에 대한 작업에 서명할 수 없습니다.</string>
  <string name="add_watch_address_tips">지갑 계정이 아닌 주소를 가져오고 있다는 점에 유의하십시오. 이 주소에 있는 자금에 액세스할 수 있는 권한이 부여되지 않으며 대신 지갑 잔액과 정보만 표시됩니다. 이것이 귀하의 지갑인 경우 개인 키를 백업해 두십시오.</string>
  <string name="ioAddress">io주소</string>
  <string name="Web3Address">Web3주소</string>
  <string name="native_address">현지 주소</string>
  <string name="web3_address">Web3 주소</string>
  <string name="native_receive_desc">이 주소는 IoTeX 주 네트워크에서 IoTeX만 수락할 수 있습니다.</string>
  <string name="web3_receive_desc">이 주소는 Web3 주 네트워크에서 Web3만 수락할 수 있습니다.</string>
  <string name="web3_native_receive_desc">이 주소는 IoTeX 네트워크에서 기본 IOTX 및 XRC20 토큰만 허용합니다.</string>
  <string name="save_QR_code">QR 코드 저장</string>
  <string name="staking_reward">스테이킹 보상</string>
  <string name="burndrop_reward">번드롭 보상</string>
  <string name="total_reward">총 보상</string>
  <string name="compound_apy">복합 APY</string>
  <string name="last_day">마지막 날</string>
  <string name="last_7_day">지난 7일</string>
  <string name="last_14_day">지난 14일</string>
  <string name="last_30_day">지난 30일</string>
  <string name="security_privacy">보안 및 개인 정보 보호</string>
  <string name="security_privacy_desc">PIN 변경, 디앱 인증 관리</string>
  <string name="about_ioPay">아이오페이 소개</string>
  <string name="ioPay">아이오페이</string>
  <string name="about_ioPay_desc">커뮤니티，~에 대한</string>
  <string name="wallet_switch_desc">홈페이지에서 지갑을 빠르게 전환</string>
  <string name="privacy_policy"><u>개인 정보 정책</u></string>
  <string name="and">그리고</string>
  <string name="terms_of_service"><u> 서비스 약관 </u></string>
  <string name="open_source_license"><u>오픈 소스 라이센스</u></string>
  <string name="add_custom_token_tips">전체 정보를 입력해 주세요.</string>
  <string name="please_input_name">이름을 입력해주세요!</string>
  <string name="please_input_decimals">소수를 입력하세요!</string>
  <string name="please_input_symbol">심벌을 입력하세요!</string>
  <string name="lower_limit">%1$s은(는) 최소한 %2$s 이상이어야 합니다.</string>
  <string name="switch_rpc_networks">RPC 네트워크를 전환합니다.</string>
  <string name="antenna_networks">Antenna 네트웍스</string>
  <string name="antenna_networks_desc">IoTeX 메인 네트워크에서 antenna 네트워크를 전환합니다.</string>
  <string name="switch_title">이 사이트는 네트워크를 전환하려고 합니다</string>
  <string name="switch_tips">이렇게 하면 ioPay 내에서 선택한 네트워크가 이전에 추가된 네트워크로 전환됩니다.</string>
  <string name="see_more">더 보기</string>
  <string name="add_network_title">이 사이트에서 네트워크를 추가하도록 허용하시겠습니까?</string>
  <string name="add_network_tips1">이렇게 하면 이 네트워크를 ioPay 내에서 사용할 수 있습니다.</string>
  <string name="add_network_tips2">ioPay는 사용자 지정 네트워크를 확인하지 않습니다. 사기 및 네트워크 보안 위험에 대해 알아보십시오.</string>
  <string name="add_network_tips3">배우기</string>
  <string name="add_network_tips4">사기 및 네트워크 보안 위험.</string>
  <string name="add_network_tips5">이 체인 ID에 대한 네트워크 세부 정보가 당사 기록과 일치하지 않습니다. 계속하기 전에 네트워크 세부 정보를 확인하는 것이 좋습니다.</string>
  <string name="network_url">네트워크 URL</string>
  <string name="chain_id">체인 ID</string>
  <string name="view_all_detail">모든 세부정보 보기</string>
  <string name="approve">승인</string>
  <string name="input_name">이름을 입력하세요.</string>
  <string name="input_symbol">기호를 입력하세요.</string>
  <string name="input_decimals">소수를 입력하세요.</string>
  <string name="decimals_error">소수점오류가 있습니다.</string>
  <string name="more">더 보기</string>
  <string name="my_dApps">내 디앱스</string>
  <string name="hot_dApps">핫 디앱스</string>
  <string name="transfer_tips_content1">IoTeX 네트워크로 보내기</string>
  <string name="transfer_tips_content2">수신자가 IoTeX 네트워크에 있는지 확인하십시오.거래소 계정으로 보내는 경우 IoTeX 네트워크의 자산을 수락하는지 확인하십시오.확인하려면 여기를 클릭하세요.</string>
  <string name="transfer_tips_title">IOTX 전송 지침</string>
  <string name="transaction_submitted">거래 제출</string>
  <string name="transaction_submitted_tips">확인 대기 중</string>
  <string name="transaction_confirmed">트랜잭션 확정!</string>
  <string name="transaction_confirmed_tips">이 거래를 보려면 탭하세요.</string>
  <string name="input_address_hint">수신 주소 입력</string>
  <string name="input_amount_hint">금액 입력</string>
  <string name="refresh">새로 고침</string>
  <string name="copy_url">URL 복사</string>
  <string name="open_in_browsers">브라우저에서 열기</string>
  <string name="confirm">확인하다</string>
  <string name="confirmation">확인</string>
  <string name="wallets">지갑</string>
  <string name="wallet">지갑</string>
  <string name="staked">스테이크드</string>
  <string name="allow">허용하기</string>
  <string name="oops">Oops</string>
  <string name="failure_alert">문제가 발생했습니다. 나중에 다시 시도 해주십시오.</string>
  <string name="reject">거부하기</string>
  <string name="transaction_error_alert">트랜잭션 오류, 계약에서 예외가 발생했습니다.</string>
  <string name="wallet_connect_account_desc_disconnected">지갑 연결을 위한 WalletConnect 요청 https://walletconnect.com</string>
  <string name="wallet_connect_account_desc_connected">지갑 연결을 위한 WalletConnect 요청 https://walletconnect.com</string>
  <string name="wallet_connect_account_desc1">현재 지갑 주소 받기 허용</string>
  <string name="wallet_connect_account_desc2">현재 지갑에서 서명 요청 허용</string>
  <string name="disconnect">연결 해제</string>
  <string name="days_left_string">%s 일 남음</string>
  <string name="stake_status_locked_duration">%s 일 잠김</string>
  <string name="ready_to_unstake">언스테이크 준비</string>
  <string name="developer_mode_test"><u>개발자 모드가 testnet을 사용하도록 설정</u></string>
  <string name="chain_id_for">체인 ID: %s</string>
  <string name="rpc_url_for">RPC URL: %s</string>
  <string name="open_notification_confirm_title">알림을 켜십시오.</string>
  <string name="add_network_tips5_highlight">네트워크 세부 정보 확인</string>
  <string name="hour_left_string">%s 시간 남음</string>
  <string name="ready_to_stake">스테이크 준비</string>
  <string name="feedback">피드백</string>
  <string name="amount_must_greater_10">금액은 10IOTX 이상이어야 합니다.</string>
  <string name="token_id">토큰 ID</string>
  <string name="buy">구입</string>
  <string name="earn">벌기</string>
  <string name="wallet_create_form_name_bottom">Robohash.org에서 사랑스럽게 전달한 로봇</string>
  <string name="about_desc">커뮤니티, 피드백, 정보</string>
  <string name="transaction_failed">트랜잭션 실패!</string>
  <string name="back_to_source_app">소스 앱으로 돌아가기</string>
  <string name="app_initial_guide_tip_1">올인원 암호화폐 지갑</string>
  <string name="app_initial_guide_sub_tip_1">단순한 지갑이 아닙니다! 고급 기능은 초보자의 단순성을 충족합니다.</string>
  <string name="app_initial_guide_tip_2">여러 체인에 걸쳐 자산 관리</string>
  <string name="app_initial_guide_sub_tip_2">ioPay 을 사용하면 IoTeX, 이더리움, BSC, 폴리곤 및 기타 네트워크 간에 원활하게 전환할 수 있습니다.</string>
  <string name="app_initial_guide_tip_3">구매, 스왑 및 자산 스테이크</string>
  <string name="app_initial_guide_sub_tip_3">DeFi의 세계를 열고 체인을 통해 자산을 전송하십시오. Web3 세계에서 배우고 돈을 벌기 시작하는 것이 그 어느 때보다 쉬워졌습니다.</string>
  <string name="app_initial_guide_tip_4">수백 개의 DApp을 익스플로러하십시오.</string>
  <string name="app_initial_guide_sub_tip_4">DeFi, NFT, GameFi, DePIN, 마켓플레이스 등에서 선택하세요. 모두 ioPay를 떠나지 않고도 가능합니다.</string>
  <string name="robots">로봇</string>
  <string name="blockies">블로키</string>
  <string name="Jazzicons">재즈아이콘</string>
  <string name="choose_avatar">아바타 선택</string>
  <string name="switch_account">계정 전환</string>
  <string name="avatar">화신</string>
  <string name="remove_token">제거</string>
  <string name="remove_token_confirm_message">이 토큰을 제거하시겠습니까?</string>
  <string name="remove_token_confirm_yes_title">토큰이 제거되었습니다!</string>
  <string name="remove_token_confirm_yes_message">마음이 바뀌면 토큰 추가를 눌러 다시 추가할 수 있습니다.</string>
  <string name="notice">통지</string>
  <string name="got_it">네</string>
  <string name="change_avatar_tips">Robots, Blockies 및 Jazzicons는 귀하의 계정에 대해 서로 다른 스타일입니다.</string>
  <string name="skip">건너뛰기</string>
  <string name="guide_title_1">잔액</string>
  <string name="guide_content_1">다양한 네트워크에서 지갑의 기본 토큰 잔액.</string>
  <string name="guide_title_2">보내기</string>
  <string name="guide_content_2">Web3 세계의 누구에게나 토큰을 주고 받고(QR 코드 스캔 지원) 활동 탭에서 거래 내역을 확인하십시오.</string>
  <string name="guide_title_3">입금하기</string>
  <string name="guide_content_3">주소와 QR코드를 이용하여 지갑에 자산을 예치할 수 있으며, 기본 주소와 Web3 주소가 있습니다. 기본 주소는 IoTeX 네트워크의 기본 IOTX 및 XRC20 토큰에만 적용됩니다.</string>
  <string name="guide_title_4">벌기</string>
  <string name="guide_content_4">자산을 안전하게 유지하기 위해 수익성 있는 플랫폼을 확인하고 즐기는 쉬운 방법입니다.</string>
  <string name="guide_title_5">토큰 목록</string>
  <string name="guide_content_5">추가된 모든 토큰 잔액은 토큰 목록에 표시되며 오른쪽 모서리에 있는 + 아이콘을 눌러 토큰을 추가할 수 있습니다.</string>
  <string name="guide_title_6">개인 키</string>
  <string name="guide_content_6">개인 키는 암호와 유사하게 암호화에 사용되는 비밀 번호입니다. 개인 키가 손실되거나 손상되는 것을 방지하는 것이 매우 중요합니다.</string>
  <string name="guide_title_7">네트워크</string>
  <string name="guide_content_7">ioPay는 지금 멀티체인을 지원, 당신은 목록에서 다른 네트워크를 선택할 수 있습니다, 웹 3 세계에서 즐거운 시간을 보내세요. </string>
  <string name="get_started">시작</string>
  <string name="welcome_to_ioPay">ioPay에 오신 것을 환영합니다!</string>
  <string name="no_thanks">아니오, 고마워요</string>
  <string name="take_the_tour">둘러보기</string>
  <string name="welcome_to_ioPay_tips">Web3 세계에서 배우고 돈을 벌기 시작하는 것이 그 어느 때보다 쉬워졌습니다! 이것은 간단한 단계에서 ioPay를 사용하는 방법을 보여주는 빠른 시작 가이드입니다.</string>
  <string name="def">디폴트</string>
  <string name="custom">커스텀</string>
  <string name="add_a_node">커스텀 노드 하나 추가</string>
  <string name="add_node">커스텀 노드 추가</string>
  <string name="nodes_setting">노드 설정</string>
  <string name="delete_node">노드 삭제</string>
  <string name="delete_network_tips">이 네트워크를 삭제하시겠습니까? 이 네트워크는 즉시 삭제됩니다.</string>
  <string name="switch_node">노드 전환</string>
  <string name="edit_network">네트워크 편집</string>
  <string name="rpc_url">RPC URL</string>
  <string name="network_node_error">입력한 RPC URL이 다른 체인 ID를 반환했습니다. 추가하려는 네트워크의 RPC URL과 일치하도록 체인 ID를 업데이트하십시오.</string>
  <string name="new_rpc_url">새 RPC URL</string>
  <string name="currency_symbol">화폐 기호</string>
  <string name="explorer_url_optional">블록 익스플로러 (선택 가능)</string>
  <string name="add_network_tips">신뢰할 수 있는 네트워크를 추가하십시오. ioPay는 사용자 지정 네트워크의 보안을 확인할 수 없습니다. 악의적인 네트워크 제공자가 네트워크 활동을 기록하고 블록체인 상태에 대해 거짓말을 할 수 있습니다.</string>
  <string name="add_custom_network">커스텀 네트워크 추가</string>
  <string name="edit_custom_network">커스텀 네트워크 편집</string>
  <string name="network_name_error">네트워크 이름이 잘못되었습니다.</string>
  <string name="currency_symbol_error">화폐 심벌이 잘못되었습니다!</string>
  <string name="rpc_url_error">Rpc Url이 잘못되었습니다.</string>
  <string name="chain_id_error">체인 ID가 잘못되었습니다.</string>
  <string name="network_exists_error">네트워크가 이미 존재합니다</string>
  <string name="rpc_url_exists_error">Rpc Url이 이미 존재합니다.</string>
  <string name="delete_node_error">마지막 Rpc Url을 삭제할 수 없습니다.</string>
  <string name="general_desc_avatar">언어, Web3 주소, 아바타</string>
  <string name="general_desc_gp_avatar">언어, 알림, Web3 주소, 아바타</string>
  <string name="wallet_connect_request_tips">지갑 연결 요청</string>
  <string name="google_review_title">당신은 ioPay를 좋아합니까?</string>
  <string name="google_review_love_it">마음에 듭니다!</string>
  <string name="google_review_improved">개선이 필요하다</string>
  <string name="authentication">입증</string>
  <string name="auth_before_login">로그인 전 인증</string>
  <string name="auth_before_transaction">거래 전 인증</string>
  <string name="enter_wallet">지갑에 들어가기</string>
  <string name="news_date_format_year">dd MMM, yyyy</string>
  <string name="news_date_format_day">dd MMM</string>
  <string name="watch_wallet_warning">현재 지갑은 감시 주소이므로 이 작업을 실행할 수 없습니다.</string>
  <string name="chart_no_data_desc">사용 가능한 차트 데이터가 없습니다.</string>
  <string name="reward_distributed">보상 분배</string>
  <string name="failed_qr_code">QR 코드를 식별하지 못했습니다.</string>
  <string name="fio_name">FIO 이름</string>
  <string name="fio_name_error">잘못된 fio 이름입니다. 연결된 네트워크 및 토큰을 확인하십시오.</string>
  <string name="fio_name_tips">FIO 프로토콜 이름이 지원됩니다.</string>
  <string name="create_new_wallet">새 지갑 생성</string>
  <string name="create_new_wallet_caption">새 지갑을 만들고 니모닉 또는 개인 키를 백업하십시오.</string>
  <string name="import_exist_wallet">기존 지갑 가져오기</string>
  <string name="import_exist_wallet_caption">니모닉 또는 개인 키 가져오기가 지원됩니다.</string>
  <string name="watch_mode">시청 모드</string>
  <string name="watch_mode_caption">그것을 보려면 지갑 주소를 입력하십시오.</string>
  <string name="your_mnemonic_phrase">당신의 니모닉</string>
  <string name="create_mnemonic_tips">12단어 니모닉을 적어서 안전하게 보관하세요.</string>
  <string name="create_mnemonic_warning">- 문구를 종이에 쓰거나 안전한 암호 관리자에 저장할 수 있습니다.\n- 이메일로 보내거나 스크린샷을 찍지 마세요.\n- 구문에서 단어의 순서가 중요합니다.</string>
  <string name="regenerate_underline"><u> 재생성 </u></string>
  <string name="mnemonic_verify_tips">백업한 니모닉에 따라 순서대로 입력하세요.</string>
  <string name="mnemonic_verify_error">니모닉의 순서가 잘못되었습니다. 백업이 올바른지 확인하십시오.</string>
  <string name="mnemonic_verify">니모닉 확인</string>
  <string name="mnemonic">니모닉</string>
  <string name="mnemonic_phrase">니모닉</string>
  <string name="add_wallet">지갑 추가</string>
  <string name="import_via_private_key_caption">개인 키로 계정 가져오기</string>
  <string name="import_via_mnemonic_caption">복구 문구가 있는 계정을 가져옵니다.</string>
  <string name="import_via_keystore_caption">키 저장소를 가져와 지갑 추가</string>
  <string name="import_iotex_wallet">아이오텍스 지갑 가져오기</string>
  <string name="import_iotex_wallet_caption">ioPay의 니모닉 문구 또는 개인 키만 지원합니다.</string>
  <string name="import_evm_wallet">EVM 지갑 가져오기</string>
  <string name="import_evm_wallet_caption">0x로 시작하는 EVM 지갑의 모든 니모닉 문구 또는 개인 키를 지원합니다.</string>
  <string name="give_a_name">이름을 지어주세요</string>
  <string name="manage_wallet">지갑 관리</string>
  <string name="no_private_key_wallet">개인 키 지갑 없음</string>
  <string name="no_mnemonic_phrase">니모닉 없음</string>
  <string name="mnemonic_invalid">니모닉이 잘못되었습니다</string>
  <string name="mnemonic_exist">니모닉이 이미 존재합니다.</string>
  <string name="input_mnemonic">니모닉을 구분하려면 공백을 사용하십시오.</string>
  <string name="select_wallet">가져올 주소 선택</string>
  <string name="select_wallet_caption">이 니모닉 문구는 다음 지갑 주소에 해당합니다. 가져올 주소를 선택하십시오.</string>
  <string name="back_up">백업</string>
  <string name="manage_mnemonic">니모닉 관리</string>
  <string name="export_mnemonic">니모닉 내보내기</string>
  <string name="choose_network">네트워크 선택</string>
  <string name="load_more_underline"><u> 더 로드 </u></string>
  <string name="edit_mnemonic_name">니모닉 이름 편집</string>
  <string name="add_mnemonic_wallet">니모닉 추가</string>
  <string name="delete_mnemonic_warning">이렇게 하면 기존 지갑이 모두 영구적으로 삭제됩니다! 백업 니모닉 문구가 있는지 확인하십시오!</string>
  <string name="warning">경고!</string>
  <string name="reveal_mnemonic_phrase">니모닉 공개</string>
  <string name="set_the_mnemonic_name">니모닉 이름 설정</string>
  <string name="delete_u"><u> 삭제 </u></string>
  <string name="translation_contributors">번역 기여자</string>
  <string name="fio_name_tips1">FIO 프로토콜 이름 및 ENS가 지원됩니다.</string>
  <string name="fio_name_tips2">FIO 프로토콜 이름과 공간 ID가 지원됩니다.</string>
  <string name="network_anomaly">네트워크 이상!</string>
  <string name="network_anomaly_desc">비정상적인 네트워크가 귀하의 ioPay에 표시되어 귀하의 행동을 방해할 수 있습니다. 설정을 확인하고 노드를 전환하십시오.</string>
  <string name="go_to_setting">설정으로 바로 가기</string>
  <string name="multi_close_all">모두 닫기</string>
  <string name="multi_done">완료</string>
  <string name="multi_browser">브라우저</string>
  <string name="my_wallet">내 지갑</string>
  <string name="search_address">주소 검색</string>
  <string name="add_network">네트워크 추가</string>
  <string name="search_in_chain_list_org">chainlist.org에서 검색</string>
  <string name="function_request">기능 요청</string>
  <string name="manage_token">토큰 관리</string>
  <string name="current_network">현재 네트워크:</string>
  <string name="default_network">기본 네트워크:</string>
  <string name="customized_network">맞춤형 네트워크:</string>
  <string name="current_node_not_reliable">현재 노드는 인증되지 않았으며 신뢰할 수 없습니다.</string>
  <string name="adding_non_default_node">확인되지 않고 신뢰할 수 없는 기본이 아닌 노드를 추가하고 있습니다.</string>
  <string name="i_got_it">알겠어요</string>
  <string name="message_notice">통지</string>
  <string name="setting_w3bstream">W3bstream</string>
  <string name="setting_w3bstream_info">실제 기기 및 데이터를 위한 탈중앙화 인프라</string>
  <string name="geo_location">Geo Location</string>
  <string name="meta_des">신뢰할 수 있는 GPS 위치의 증거를 제공하여 Web3 DApps에서 특정 보상 및 기능을 활성화합니다.</string>
  <string name="connect_mobile_device">이 모바일 장치에 W3bstream 연결</string>
  <string name="connect">연결하다</string>
  <string name="activate_geo_location">활성화 Geo Location</string>
  <string name="connect_w3bstream">W3bstream 연결 중...</string>
  <string name="connected_w3bstream">연결된 W3bstream</string>
  <string name="you_have_successfully_connected_w3bstream">W3bstream을 모바일 장치에 성공적으로 연결했습니다!</string>
  <string name="meta_info">지리적 위치를 켜면 모바일 장치의 IMEI 번호 및 일련 번호를 포함한 분산 ID를 통해 모바일 장치를 지갑 주소에 연결합니다. 지리적 위치를 켜면 신뢰할 수 있는 GPS 위치의 증거를 제공하여 Web3 DApp에서 특정 보상 및 기능을 활성화할 수 있습니다.</string>
  <string name="pebble_upload_failed">데이터 업로드 실패</string>
  <string name="pebble_supported_net">지원 네트워크: IoTeX 테스트넷</string>
  <string name="pebble_uploading_data">데이터 업로드</string>
  <string name="meta_setting">설정</string>
  <string name="meta_location">위치</string>
  <string name="meta_gps_interval">GPS 수집 간격</string>
  <string name="meta_gps_precision">GPS 정밀도</string>
  <string name="supported_network">지원 네트워크: IoTeX 테스트넷</string>
  <string name="location_fail">위치 정보를 가져오지 못했습니다.</string>
  <string name="open_location_permission">설정에서 위치 권한을 켜주세요</string>
  <string name="try_later">나중에 할게요</string>
  <string name="wallet_error">지갑 오류</string>
  <string name="your_mobile_device_is">모바일 장치는 다음 지갑 주소에 바인딩됩니다.</string>
  <string name="the_wallet_you_are_currently_using">현재 이 페이지에 액세스하기 위해 사용 중인 지갑이 위 주소와 일치하지 않습니다. 이전에 이 장치에 바인딩된 지갑으로 변경하십시오.</string>
  <string name="failure">실패</string>
  <string name="select_specific_wallet">지갑 주소가 %s 인 지갑을 선택하세요.</string>
  <string name="security_privacy_desc2">PIN 변경, DApp 인증 관리, 니모닉 관리</string>
  <string name="delete_wallet_warning">이 지갑을 삭제하시겠습니까? 지갑의 백업이 있는지 확인하십시오.</string>
  <string name="select_hd_path">HD 경로 선택</string>
  <string name="select_hd_path_caption">원하는 계정이 표시되지 않으면 HD 경로를 전환해 보세요.</string>
  <string name="select_network">네트워크 선택</string>
  <string name="select_wallet_warning">한 번에 최대 10개의 지갑 주소를 선택할 수 있습니다.</string>
  <string name="supported_main_network">지원되는 네트워크: IoTeX</string>
  <string name="amount_remain">현재 금액</string>
  <string name="risk">위험</string>
  <string name="high_risk">고위험</string>
  <string name="You_have_left_iopay">ioPay가 백그라운드에서 실행 중입니다.</string>
  <string name="backup_tips">백업 팁</string>
  <string name="obtaining_private_key">개인 키를 얻는 것은 모든 자산을 소유하는 것과 같습니다.</string>
  <string name="copy_it_on_the_paper_and_keep">종이에 복사하여 개인키 분실 시 복구가 불가하오니 개인키를 반드시 백업해두시기 바랍니다.</string>
  <string name="for_the_safety">안전을 위해,</string>
  <string name="please_copy_in_sections">섹션을 복사하십시오</string>
  <string name="copy_all">모두 복사</string>
  <string name="search_nft">이름 또는 계약</string>
  <string name="geo_location_tips">지리적 위치 팁</string>
  <string name="to_upload_accurate_gps_data">정확한 GPS 데이터 업로드를 위해 위치 접근을 항상 허용 또는 사용 중 허용으로 설정해주세요.</string>
  <string name="details">세부</string>
  <string name="contract_addr">계약 주소</string>
  <string name="token_standard">토큰 표준</string>
  <string name="blockchain">블록체인</string>
  <string name="share">공유하다</string>
  <string name="share_nft_caption">멀티체인 지원 암호화폐 지갑</string>
  <string name="once_the_private_key_gets_lost">· 개인 키를 분실하면 복구할 수 없습니다.</string>
  <string name="Please_be_sure_to_back_up_the_private_key">· 개인키는 반드시 백업해두시기 바랍니다.</string>
  <string name="theme">주제</string>
  <string name="iopay_default">아이오페이</string>
  <string name="dapp_theme_des">다양한 테마를 통해 ioPay를 사용자 지정하고 좋아하는 DApp을 한 눈에 볼 수 있습니다.</string>
  <string name="iopay_unsafe_state">ioPay 안전하지 않은 상태</string>
  <string name="default_theme">기본 테마:</string>
  <string name="ecosystem_theme">생태계 주제:</string>
  <string name="receive_transaction_notification">거래 알림 받기</string>
  <string name="receive_system_notification">시스템 알림 수신</string>
  <string name="Keep_track_of_important_updates">푸시 알림을 활성화하여 중요한 업데이트, 뉴스레터 또는 기타 관련 정보를 추적하세요.</string>
  <string name="to_change_your_iopay_password">ioPay 비밀번호를 변경하려면 먼저 현재 비밀번호를 확인하세요.</string>
  <string name="after_clearing_the_allowance_record">허용 기록을 삭제한 후에는 향후 각 DApp을 다시 허용해야 합니다.</string>
  <string name="configure_your_authentication">인증 방법 및 절차를 구성합니다.</string>
  <string name="contract_approval_checker">계약 승인 검사기</string>
  <string name="please_make_sure_to_back_up_the_wallet">위치정보 등록에 사용한 지갑을 반드시 백업해두시기 바랍니다. 등록이 완료되면 변경할 수 없습니다.</string>
  <string name="did_not_find_the_expected_network">원하는 네트워크를 찾지 못하셨나요? 설정에서 네트워크를 설정하거나 개발자 모드를 활성화하세요.</string>
  <string name="txid">Txid</string>
  <string name="tips_w3bstream">W3bstream</string>
  <string name="register_geo_location">지리적 위치 등록</string>
  <string name="register_success">등록 성공</string>
  <string name="did_not_find_the_expected_network_tips">설정</string>
  <string name="all_channels">모든 채널</string>
  <string name="sent_nft_success">NFT 성공 전송</string>
  <string name="sent_nft_failed">NFT 전송 실패</string>
  <string name="the_current_gps_signal_is_weak">현재 GPS 신호가 약하고 업로드된 위치가 정확하지 않습니다.</string>
  <string name="upload_data_failed">데이터 업로드 실패</string>
  <string name="weak_signal">약한 신호</string>
  <string name="back_to_the_source_app">소스 앱으로 돌아가기</string>
  <string name="low">낮다</string>
  <string name="market">시장</string>
  <string name="high">높다</string>
  <string name="max_fee">최대 요금: </string>
  <string name="weak_gps">약한 GPS</string>
  <string name="you_are_about_to_enter">타사 DApp에 들어가려고 합니다.</string>
  <string name="please_note_that_the_dapp_page">DApp 페이지는 프로젝트에서 완전히 관리되며 ioPay 앱은 브라우저로만 작동합니다. DApp을 사용할 때는 주의를 기울이고 전송하기 전에 대상 주소 또는 계약 주소를 확인하십시오.</string>
  <string name="i_understood_do_not_show_this_again">이해했습니다. 다시 표시하지 마세요.</string>
  <string name="please_note_that_filda">FilDA는 프로젝트에서 완전히 관리되며 ioPay 앱은 브라우저로만 작동합니다. DApp을 사용할 때는 주의를 기울이고 전송하기 전에 대상 주소 또는 계약 주소를 확인하십시오.</string>
  <string name="please_note_buy_title">면책 조항</string>
  <string name="no_dapp">DApp 없음</string>
  <string name="sbts_are_issued_by">SBT(Soulbound Tokens)는 블록체인 계정 또는 지갑에 발급된 자격 증명 또는 소속이며 양도할 수 없습니다.</string>
  <string name="learn_more_short">더 알아보기</string>
  <string name="i_understood_do_not_show_this_again_today">이해했습니다. 오늘 다시 표시하지 마세요.</string>
  <string name="create_new_wallet_caption_02">새 지갑을 만들고 니모닉 문구를 백업하십시오.</string>
  <string name="import_exist_wallet_caption_02">공개 문구 또는 개인 키 가져오기 지원</string>
  <string name="import_exist_by_mnemonic">니모닉 공개 지원</string>
  <string name="import_via_private_key_caption_02">일반 텍스트 개인 키로 지갑 가져오기</string>
  <string name="import_via_keystore_caption_02">암호화된 키 저장소를 가져와서 지갑 추가하기</string>
  <string name="manage_private_key">개인 키 관리</string>
  <string name="reveal_phrase">니모닉 문구 공개</string>
  <string name="website">웹사이트</string>
  <string name="approve_token">토큰 승인</string>
  <string name="approve_amount">금액 승인</string>
  <string name="unlimited">무제한</string>
  <string name="edit_approve_amount">승인 금액 편집</string>
  <string name="edit_contract_name">콘트랙트 이름 편집</string>
  <string name="current_amount">현재 금액</string>
  <string name="set_approve_amount">승인 금액 설정</string>
  <string name="max_big">MAX</string>
  <string name="enter_a_number">숫자를 입력하세요</string>
  <string name="only_enter_a_number_that_you">현재 또는 미래의 계약 지출에 대해 만족스러운 숫자만 입력하세요. 나중에 언제든지 승인 금액을 늘릴 수 있습니다.</string>
  <string name="delete_network"><u> 네트워크 삭제 </u></string>
  <string name="symbol">심볼</string>
  <string name="menu_explore">익스플로어</string>
  <string name="edit_address">주소 수정</string>
  <string name="place_bid_for_vita">VITA에 입찰하기</string>
  <string name="view">보기</string>
  <string name="you_will_be_redirected_to_a_third_party_dapp">타사 DApp으로 리디렉션됩니다.</string>
  <string name="please_note_that_is_fully_managed_by_the_project">%s 는 프로젝트에서 완전히 관리되며 ioPay 앱은 브라우저로만 작동합니다. DApp을 사용할 때는 주의를 기울이고 송금하기 전에 대상 주소 또는 계약 주소를 확인하십시오.</string>
  <string name="choose_from_defi_nfts_gamefi_depin">DeFi, NFT, GameFi, DePIN, 마켓플레이스 등에서 선택하세요. 모두 ioPay를 떠나지 않고도 가능합니다.</string>
  <string name="contract_name">계약명</string>
  <string name="use_default">기본값 사용</string>
  <string name="view_on_iotexscan"><u> IoTeXScan에서 보기 </u></string>
  <string name="hd_path_caption">ioPay는 기본 BIP44 표준(IOTEX HD PATH: 304 )/(EVM HD PATH:60)을 지원합니다. 자세히 알아보십시오.</string>
  <string name="learn_more_lowercase">더 알아보기</string>
  <string name="verified_contract">검증된 계약</string>
  <string name="view_contract">계약 보기</string>
  <string name="obtaining_private_key_equals_owning_all_assets">· 개인 키를 얻는 것은 모든 자산을 소유하는 것과 같습니다.</string>
  <string name="copy_it_on_the_paper_and_keep_in_a_safe_place">· 종이에 복사하여 안전한 장소에 보관하십시오.</string>
  <string name="obtaining_recovery_phrase_equals_owning_all_assets">· 복구 문구를 얻는 것은 모든 자산을 소유하는 것과 같습니다.</string>
  <string name="once_the_recovery_phrase_gets_lost">· 복구 문구는 한번 분실하면 복구할 수 없습니다.</string>
  <string name="Please_be_sure_to_back_up_the_recovery_phrase">· 복구 문구는 반드시 백업해 두시기 바랍니다.</string>
  <string name="view_on_explorer"><u> 익스플로러에서 보기 </u></string>
  <string name="type_name_or_contract_to_search_token">이름 또는 콘트랙트를 입력하여 토큰을 검색하십시오.</string>
  <string name="set_approve_allowance">승인 수당 설정</string>
  <string name="please_scroll_to_read_all_sections">모든 섹션을 읽으려면 스크롤하십시오</string>
  <string name="terms_of_use">이용 약관</string>
  <string name="i_agree_to_the_terms_of_use">본인은 ioPay 및 모든 기능 사용에 적용되는 이용약관에 동의합니다.</string>
  <string name="please_turn_on_app_notifications_to_stay">ioPay와 관련된 최신 뉴스 및 중요 정보를 계속 업데이트하려면 앱 알림을 켜주세요.</string>
  <string name="you_can_write_the_phrases_down_in_a_piece_of_paper">니모닉을 종이에 적어두거나 안전한 암호 관리자에 저장할 수 있습니다. 이메일을 보내거나 스크린샷을 찍지 마십시오. 단어의 순서가 중요합니다.</string>
  <string name="iopay_is_locked">ioPay가 잠겨 있습니다.</string>
  <string name="try_again_in">%@에서 다시 시도 </string>
  <string name="next_step">다음</string>
  <string name="next_pf">다음</string>
  <string name="receipt_contract">입력하신 주소는 콘트랙트 주소입니다. 유효한 지갑 주소를 확인하고 입력하십시오.</string>
  <string name="section_1">섹션 1</string>
  <string name="section_2">섹션 2</string>
  <string name="copy_mnemonic">자산 안전을 위해 섹션에 있는 니모닉 문구를 친절하게 복사하십시오.</string>
  <string name="join_the_beta">베타 참여</string>
  <string name="nft_marketplace">NFT 마켓플레이스</string>
  <string name="by_approving_this_you_are_authorizing_full_access">이를 승인하면 향후 획득하는 것을 포함하여 선택한 컬렉션의 모든 NFT에 대한 전체 액세스 권한을 부여하는 것입니다. 조심해서 진행해라.</string>
  <string name="sign_request">서명 요청</string>
  <string name="token_id_big">토큰 ID</string>
  <string name="more_details_u"><u>자세한 내용</u></string>
  <string name="more_details">더 세부 정보</string>
  <string name="choose_from_defi_nfts_gamefi_machinefi_marketplaces">ioPay를 떠나지 않고도 DeFi, NFT, GameFi, MachineFi, 마켓플레이스 등을 모두 선택할 수 있습니다.</string>
  <string name="the_wallet_connect_protocol_be_shut_down">WalletConnect v1.0 프로토콜은 2023년 6월 28일 오후 2시(UTC)에 종료됩니다.</string>
  <string name="view_data">데이터 보기</string>
  <string name="big_mimo">미모</string>
  <string name="phrase_hd_path_bip44_standard">구 HD 경로: BIP44 표준(EVM HD PATH:%s)</string>
  <string name="stake_amount_duration">지분 금액 및 기간</string>
  <string name="candidate_name">입후보자 이름</string>
  <string name="stake_lock">잠금 버킷</string>
  <string name="stake_unlock">잠금 해제 버킷</string>
  <string name="stake_withdraw_receiption">수신자</string>
  <string name="withdraw">철회</string>
  <string name="voter_address">유권자 주소</string>
  <string name="sign">기호</string>
  <string name="confirm_password">비밀번호 확인</string>
  <string name="lock_screen_title_pf">PIN 코드 입력 또는 지문 사용</string>
  <string name="no_fingerprints_title_pf">지문을 찾을 수 없습니다.</string>
  <string name="no_fingerprints_message_pf">지문을 찾을 수 없습니다. 이 인증 방법을 사용하려면 설정에서 지문을 추가하세요.\"</string>
  <string name="settings_pf">설정</string>
  <string name="cancel_pf">핀</string>
  <string name="fingerprint_description_pf">계속하려면 지문을 확인하세요.</string>
  <string name="fingerprint_hint_pf">터치 센서</string>
  <string name="fingerprint_not_recognized_pf">지문이 인식되지 않습니다. 다시 시도하십시오</string>
  <string name="fingerprint_success_pf">지문인식</string>
  <string name="sign_in_pf">인증 필요</string>
  <string name="second">초</string>
  <string name="minute">분</string>
  <string name="recovery_phrase">니모닉</string>
  <string name="add_recovery_phrase">니모닉 추가</string>
  <string name="delete_mnemonic_warning_word">이것은 이 니모닉 문구와 관련된 모든 지갑을 영구적으로 지울 것입니다! 니모닉 문구의 백업이 있는지 확인하십시오!</string>
  <string name="fio_name_tips3">FIO 프로토콜 이름과 INS가 지원됩니다. </string>
  <string name="no_fingerprints_message">지문을 찾을 수 없습니다. 이 인증 방법을 사용하려면 설정에서 지문을 추가하세요.</string>
  <string name="estimate_gas_error">거래를 진행할 수 없습니다: 가스 요금 추정 오류. 가스 요금을 수동으로 조정하십시오.</string>
  <string name="you_can_create_or_import_recovery_phrase_wallet">지갑을 추가하여 니모닉 문구 지갑을 생성하거나 가져올 수 있습니다.</string>
  <string name="this_wallet_list_includes_the_wallet_addresses">이 지갑 목록에는 개인 키, 키 저장소 및 감시자 주소를 통해 가져온 지갑 주소가 포함됩니다.</string>
  <string name="this_wallet_list_is_for_recovery_phrase_wallets">이 지갑 목록은 니모닉 지갑에 사용되며 하나의 니모닉을 사용하여 여러 지갑에 액세스할 수 있습니다.</string>
  <string name="you_have_the_option_to_generate_a_new_wallet_using_this_recovery_phrase">이 시드 문구로 새 지갑을 생성하도록 선택할 수 있지만 개인 키는 완전히 다릅니다.</string>
  <string name="you_have_the_option_to_either_generate_a_single_recovery_phrase">선택적으로 단일 니모닉 구를 생성할 수 있고 또는 지갑 관리 기능을 사용하여 여러 니모닉 문구 지갑을 가져옵니다.</string>
  <string name="reveal_and_backup_your_recovery_phrase">니모닉을 보이고 백업하세요. 이 단어는 모든 계정의 핵심입니다. 이 문구를 다른 사람과 공유하지 마세요!</string>
  <string name="by_managing_the_private_key">개인 키는 니모닉 및 개인 키 지갑 개인 키에 대한 액세스를 제공합니다. 보안을 유지하세요. 하나의 니모닉 문구는 고유한 개인 키를 가진 여러 개의 지갑을 생성할 수 있습니다.</string>
  <string name="by_managing_your_recovery_phrase">복구 문구를 관리하면 이와 관련된 지갑을 생성, 공개 또는 삭제할 수 있습니다.</string>
  <string name="please_note_buy_content_1">암호화폐 구매는 제3자 서비스 제공업체를 통해 제공됩니다. 디지털 자산을 받는 수량과 시간은 서비스 제공업체와 블록체인 네트워크 상태에 따라 다릅니다.</string>
  <string name="insufficient_gas_fee">가스비가 부족함</string>
  <string name="activities_big">활동</string>
  <string name="tools_big">도구</string>
  <string name="compound_method_register">등록</string>
  <string name="compound_method_unregister">회원탈퇴</string>
  <string name="staking_details">스테이킹 세부사항</string>
  <string name="vote_for">투표하다</string>
  <string name="lock_duration">잠금 기간</string>
  <string name="stake_lock_detail">스테이크 잠금</string>
  <string name="bucket_status">버킷 상태</string>
  <plurals name="day_num">
    <item quantity="other">%d 날</item>
  </plurals>
  <string name="aa_wallet">AA 월렛</string>
  <string name="aa_wallet_caption">ioPay는 IoTeX 메인넷에서 AA 스마트 계정 생성을 용이하게 합니다.</string>
  <string name="create_new_aa_wallet">새 AA 지갑 만들기</string>
  <string name="create_new_aa_wallet_caption">자산은 외부 소유 계정(EOA)이 아닌 스마트 컨트랙트에 의해 독점적으로 보유됩니다.</string>
  <string name="recover_aa_wallet">AA 지갑 복구</string>
  <string name="recover_aa_wallet_caption">이메일을 통해 AA 지갑 복구</string>
  <string name="recover_aa_wallet_caption_2">자산의 보안을 위해 새 장치에서 AA 지갑을 복구하려면 24시간의 대기 기간이 필요합니다.</string>
  <string name="support_network">지원되는 네트워크:</string>
  <string name="potential_risks">잠재적인 위험에 유의하세요:</string>
  <string name="potential_risks_caption">AA 월렛은 이메일을 복구 수단으로 사용하기 때문에, 개인 키와 이메일을 안전하게 보호하는 것이 중요합니다.</string>
  <string name="recover_aa_wallet_tips">바인딩된 지갑에 연결된 이메일을 사용하여 AA 지갑을 복구합니다.</string>
  <string name="input_your_email">이메일 입력</string>
  <string name="aa_wallet_list_tips">이메일 주소와 연결된 다음 지갑을 찾았습니다. 복구할 지갑을 하나 선택해 주세요.</string>
  <string name="send_recover_email_tips">이 이메일을 보내 AA 지갑을 복구하면 이전에 지갑이 활성화되어 있던 다른 모든 기기에서 지갑이 비활성화된다는 점에 유의하시기 바랍니다.</string>
  <string name="sned_recover_email_arlet">복구 이메일을 사용하여 다음 제목을 보내주시기 바랍니다.</string>
  <string name="to_colon">받는 사람:</string>
  <string name="subject_colon">제목:</string>
  <string name="recover_email_caption">이메일은 로컬 데이터베이스에만 기록됩니다. 이 정보는 로컬에만 저장되며 다른 곳에는 기록되지 않는다는 점에 유의하시기 바랍니다.</string>
  <string name="email">이메일</string>
  <string name="send_code">코드 보내기</string>
  <string name="enter_email">이메일 입력</string>
  <string name="code">코드</string>
  <string name="enter_code">코드 입력</string>
  <string name="learn_more_about">자세히 알아보기</string>
  <string name="aa_wallet_underline"><u>AA 월렛</u></string>
  <string name="apply_free_gas_paragraph_1">스마트 계정 출시 기간 동안에는 다음과 같은 무료 가스 활동이 제공됩니다:</string>
  <string name="apply_free_gas_paragraph_2">ioPay 앱의 AA 지갑을 통해 이체, NFT 이체 또는 계약 상호작용을 시작할 때 현재 가스 요금이 10 IOTX 미만인 경우 가스를 전액 보조받을 수 있습니다.</string>
  <string name="apply_free_gas_paragraph_3">무료 가스 요금은 가스 요금을 결제할 때 차감하는 용도로만 사용되며, 지갑 잔액으로 이체되지 않습니다. 무료 가스 요금은 하루에 10 LOTX를 적용할 수 있습니다.</string>
  <string name="apply_free_gas_paragraph_4">앞으로도 ioPay는 EIP-4337을 기반으로 한 계정 추상화 기술에 지속적으로 기여하고, 소셜 복구 및 기타 고유 기능을 순차적으로 출시할 예정입니다.</string>
  <string name="have_applied_free_gas">오늘 무료 가스 요금이 적용되었습니다.</string>
  <string name="apply_exact_gas">1$s 가스 요금 적용%1$s</string>
  <string name="apply_free_gas_fee">무료 가스 요금 적용</string>
  <string name="apply_free_gas_fee_now_underline"><u>지금 무료 가스 요금 신청하기</u></string>
  <string name="remain_free_gas_fee">남은 무료 가스 요금:</string>
  <string name="setup_email">이메일 설정</string>
  <string name="setup_email_desc">자산의 안전을 위해 복구 이메일을 설정하는 것이 좋습니다. 복구 이메일을 설정하지 않거나 연결된 이메일 계정에 액세스하지 못해 장치에서 복원할 수 없는 경우, 지갑 복구에 도움을 드릴 수 없습니다.</string>
  <string name="setup_email_tips">복구 이메일로 Gmail을 사용하세요.</string>
  <string name="invalid_email">잘못된 이메일</string>
  <string name="invalid_code">오류 코드</string>
  <string name="have_sent_email">이메일을 보냈습니다.</string>
  <string name="wallet_exists">지갑이 이미 존재합니다.</string>
  <string name="has_not_bound_wallet">이메일이 아직 지갑에 바인딩되지 않았습니다.</string>
  <string name="the_aa_wallet_has_not_been_recovered">AA 지갑이 아직 성공적으로 복구되지 않았으며, 현재 거래 및 기타 기능을 사용할 수 없습니다. AA 지갑을 복구하고 모든 기능에 액세스하려면 바인딩된 이메일을 사용하시기 바랍니다.</string>
  <string name="value_money">≈$ %s</string>
  <string name="card_single_value">$ %s</string>
  <string name="private_key_wallet">개인 키 지갑</string>
  <string name="depin_scan">디핀 스캔</string>
  <string name="the_depin_wallet_of_choice">선택의 DePIN 지갑</string>
  <string name="check_your_depin_assets_with_iopay">ioPay로 DePIN 자산을 확인하세요!</string>
  <string name="create_new_wallet_import_wallet">새 지갑 만들기, 지갑 가져오기</string>
  <string name="since_you_are_currently_in_visitor_mode">현재 방문자 모드이므로 디앱 기능을 완전히 경험하지 못할 수 있습니다. 지갑을 만들거나 지갑을 가져와 주시기 바랍니다.</string>
  <string name="aa_wallet_is_recovering">AA 지갑이 복구 중입니다.</string>
  <string name="recovering_alert_1">현재 새 기기에서 AA 지갑을 복구하는 중입니다.</string>
  <string name="recovering_alert_2">복구 애플리케이션이 아닌 경우 지금 중지할 수 있습니다.</string>
  <string name="stop_recovering">복구 중지</string>
  <string name="continue_recovering_underline"><u>나야, 계속 복구해</u></string>
  <string name="please_be_patient">조금만 기다려주세요.</string>
  <string name="congratulation">축하합니다!</string>
  <string name="keep_email_alert">지갑을 성공적으로 만들었습니다. 이메일을 안전하게 보관해 주세요!</string>
  <string name="iopay_gas_subsidy">ioPay 가스 보조금</string>
  <string name="free_gas_alert_1">무료 가스 요금이 자동으로 적용되고 AA 지갑이 성공적으로 활성화되었습니다.</string>
  <string name="free_gas_alert_2">오늘 계정 무료 평가판: %s</string>
  <string name="free_gas_alert_3"><u>ioPay 가스 보조금에 대해 자세히 알아보기</u></string>
  <string name="enter_aa_wallet">AA 월렛 입력</string>
  <string name="aa_wallet_expired">AA 지갑 만료</string>
  <string name="wallet_expired_alert_1">현재 지갑이 만료되었습니다! 현재 거래 기능과 Dapp 운영이 비활성화되어 있습니다.</string>
  <string name="wallet_expired_alert_2">다른 지갑으로 전환해 주세요.</string>
  <string name="send_recovery_email_undeline"><u>복구 이메일 보내기</u></string>
  <string name="send_recovery_email_alert">이미 복구 이메일을 보냈다면, 지갑이 복구될 때까지 기다려 주십시오.</string>
  <string name="query_failed_and_retry">데이터 쿼리에 실패했습니다. 다시 시도하세요.</string>
  <string name="try_again">다시 시도</string>
  <string name="free_gas_fee">가스비 무료: </string>
  <string name="not_supporting_action">이 AA 지갑은 현재 작동을 지원하지 않습니다. 지갑을 전환하세요.</string>
  <string name="since_the_aa_wallet_uses_your_email">AA 월렛은 이메일을 복구 수단으로 사용하기 때문에, 개인 키와 이메일을 안전하게 보호하는 것이 중요합니다.</string>
  <string name="manage_your_private_key_wallet_and_aa_wallet">개인 키 지갑과 AA 지갑을 관리하세요. 지갑을 안전하게 보호하세요. 하나의 복구 문구로 고유한 개인 키를 가진 여러 개의 지갑을 생성할 수 있다는 점에 유의하세요.</string>
  <string name="biometrics_authentication">생체인증</string>
  <string name="active_wallet_failed">AA 지갑 활성화 실패</string>
  <string name="active_wallet_failed_reason">불안정한 네트워크 연결로 인해 AA 지갑 활성화 처리에 실패했습니다. 이메일로 다시 시도해 주세요.</string>
  <string name="send_recovery_email_alert_have_received">이미 복구 메일을 보내셨다면 지갑이 복구될 때까지 기다려 주시기 바랍니다. <u> 복구 이메일을 받았습니다. </u></string>
  <string name="have_received_recovery_email"><u> 복구 이메일을 받았습니다. </u></string>
  <string name="contract_us_underline"><u>도움이 필요하신가요? 문의하세요!</u></string>
  <string name="search">검색</string>
  <string name="address_is_empty">주소가 비어 있습니다.</string>
  <string name="add_address">주소 추가</string>
  <string name="manage_address_book">주소록 관리</string>
  <string name="receiver">수신자</string>
  <string name="apply_today_free_gas_fee_now">오늘의 무료 가스비를 지금 신청하세요 &gt; &gt;</string>
  <string name="around_value_symbol" formatted="false">≈ %s %s</string>
  <string name="not_support_aa_wallet_caption">AA 지갑은 현재 운영(개인 서명 관련 서명 운영)을 지원하지 않는 일종의 컨트랙트 지갑입니다. 다른 DApp을 사용해 보거나 지갑을 바꿔보세요.</string>
  <string name="content_cap">콘텐츠</string>
  <string name="the_process_of_activate_aa">AA 지갑을 활성화하는 과정에는 시간이 걸리므로 잠시 기다려 주십시오.</string>
  <string name="network_status">네트워크 상태:</string>
  <string name="not_available">사용 불가</string>
  <string name="available">사용 가능</string>
  <string name="iopay_aa_wallet">ioPay AA 지갑</string>
  <string name="verify_recovery_email">복구 이메일을 확인하세요</string>
  <string name="apply_gas_automatically">가스 보조금( %s IOTX) 자동 적용</string>
  <string name="verified_failed">확인에 실패했습니다.</string>
  <string name="applied_failed">신청에 실패했습니다.</string>
  <string name="try_again_underline"><u> 다시 시도 </u></string>
  <string name="create_your_aa_wallet">AA 지갑 만들기</string>
  <string name="create_failed">생성 실패</string>
  <string name="bind_email_to_aa_wallet">이메일을 AA 지갑에 바인딩하세요</string>
  <string name="bind_failed">바인딩 실패</string>
  <string name="network_error_title">네트워크 오류</string>
  <string name="network_error_tips">불안정한 네트워크 연결로 인해 AA 지갑 활성화 처리가 실패할 수 있습니다.</string>
  <string name="get_account">계정 만들기</string>
  <string name="wallet_address_only">지갑 주소</string>
  <string name="network_is_busy">네트워크 사용량이 많습니다. Gas가격은 비싸고 추정치는 덜 정확합니다.</string>
  <string name="balance_cap">잔액</string>
  <string name="amount_gas_fee">금액 + 가스비</string>
  <string name="insufficient_balance_for_this_action">이 작업에 대한 잔액이 부족합니다.</string>
  <string name="two_string_add" formatted="false">%s %s</string>
  <string name="no_dot">No.</string>
  <string name="gas_subsidy">가스 보조금</string>
  <string name="advanced_mode">고급 모드</string>
  <string name="data_to_chain">체인으로 연결할 데이터</string>
  <string name="text_cap">텍스트</string>
  <string name="hex_big">HEX</string>
  <string name="the_data_will_be_written">데이터는 블록체인에 기록되며 일정 수수료를 소비합니다.</string>
  <string name="advanced">고급</string>
  <string name="edit_gas_fee">가스 요금 편집</string>
  <string name="gas_option">가스 옵션</string>
  <string name="sec">초</string>
  <string name="base_fee">기본비용</string>
  <string name="priority_fee">우선 수수료</string>
  <string name="advanced_gas_fee">선불 가스비</string>
  <string name="max_base_fee_with_unit">최대 기본 수수료(GWEI)</string>
  <string name="current">현재:</string>
  <string name="priority_fee_with_unit">우선 수수료(GWEI)</string>
  <string name="save_selection">이 값을 Polygon 네트워크의 기본값으로 저장하십시오.</string>
  <string name="aggressive">우선권</string>
  <string name="apply_gas_automatically_1">가스 보조금 자동 신청</string>
  <string name="remaining_gas_subsidy">남은 가스 보조금</string>
  <string name="bind_email_failed">복구 이메일 바인딩 실패</string>
  <string name="bind_email_failed_description">현재 가스비가 너무 높아서 바인딩이 성공하지 못할 수 있으니 나중에 다시 시도해주세요.</string>
  <string name="add_wallet_by_importing_recovery_phrase">니모닉을 가져와서 지갑 추가</string>
  <string name="when_your_transaction_is_included_in_the_block">귀하의 거래가 블록에 포함되면 최대 기본 수수료와 실제 기본 수수료 간의 차액이 환불됩니다. 총액은 최대 기본요금 (GWEI 기준)* 연료 한도 계산됩니다.</string>
  <string name="priority_fee_goes_directly">우선 수수료(일명 \"채굴자 팁\")는 채굴자에게 직접 전달되며 거래의 우선 순위를 정하도록 인센티브를 제공합니다.</string>
  <string name="transaction_sending">거래 전송 중</string>
  <string name="transaction_succeed">거래 성공</string>
  <string name="transaction_fail">거래 실패</string>
  <string name="send_symbol">%s 보내기</string>
  <string name="value_symbol" formatted="false">-%s %s</string>
  <string name="to_address_format">받는 사람: %s</string>
  <string name="speed_up">가속</string>
  <string name="this_gas_fee_will_replace_the_original">이 가스 요금은 원본을 대체합니다.</string>
  <string name="before">가속 전</string>
  <string name="after">가숙 후</string>
  <string name="gas_price_and_limit" formatted="false">= 가스 가격(%s Gwei) * 가스 한도(%s)</string>
  <string name="cancel_transaction">거래 취소</string>
  <string name="canceling_the_transaction">거래를 취소하려면 추가 가스 수수료를 지불해야 합니다.</string>
  <string name="due_to_block_reasons">블록 사유로 인해 트랜잭션이 취소되었다고 해서 성공이 보장되는 것은 아닙니다.</string>
  <string name="canceled">취소됨</string>
  <string name="speed_up_cancellation">가속 취소</string>
  <string name="transaction_canceled">거래 취소됨</string>
  <string name="please_note_buy_content_2">ioPay는 구매 과정에서 주문에 발생한 문제에 대해 책임을 지지 않습니다. 제3자 제공업체에 직접 문의하세요.</string>
  <string name="gas_price_and_limit_qev" formatted="false">= 가스 가격( %s Qev) * 가스 한도( %s )</string>
  <string name="last_7_days">지난 7일</string>
  <string name="save_these_values_as_my_default">이 값을 %s 네트워크의 기본값으로 저장하세요.</string>
  <string name="guide_content_6_v2">개인 키는 비밀번호와 유사하게 암호화에 사용되는 비밀 번호입니다. 개인 키가 분실되거나 손상되지 않도록 방지하는 것이 매우 중요합니다. AA 지갑은 개인 키 내보내기를 지원하지 않습니다.</string>
  <string name="lower_limit_2">%1$s %2$s 보다 커야 합니다.</string>
  <string name="transfer_bucket">버킷 재할당</string>
  <string name="important_tips">중요한 팁</string>
  <string name="this_address_only_accepts_native">· 이 주소는 IoTeX 네트워크에서 기본 IOTX 및 XRC20 토큰만 허용합니다.</string>
  <string name="aa_wallet_operates_on_smart_contracts">· AA wallet은 스마트 계약을 기반으로 운영됩니다. 중앙화 거래소로부터 자산을 수령하기 위해 이 주소를 사용하지 마십시오.</string>
  <string name="tools">도구</string>
  <string name="launch_app">앱 런치</string>
  <string name="slow">느린</string>
  <string name="slow_sec_or_longer">60초 또는 더 길게</string>
  <string name="average">평균</string>
  <string name="average_sec_or_longer">30초 또는 더 길게</string>
  <string name="fast">빠른</string>
  <string name="fast_sec_or_longer">10초 또는 더 길게</string>
  <string name="customize">맞춤형</string>
  <string name="click_to_see_mnemonics">니모닉을 보려면 클릭하세요.</string>
  <string name="you_can_write_the_phrases_down">니모닉 문구를 종이에 적거나 보안 비밀번호 관리자에 저장할 수 있습니다.</string>
  <string name="don_not_email_them_or_screenshot_them">이메일을 보내거나 스크린샷을 찍지 마십시오.</string>
  <string name="the_order_of_words_is_important">단어의 순서가 중요합니다.</string>
  <string name="already_exists">이미 존재 함</string>
  <string name="switch_address">주소 전환</string>
  <string name="brc_20">BRC-20</string>
  <string name="stay_tuned_for_brc20_tokens">BRC20 토큰을 계속 지켜봐 주십시오.</string>
  <string name="transaction_time">%s 초 이상</string>
  <string name="start_exploring">탐색 시작</string>
  <string name="created_wallet_success">지갑을 성공적으로 만들었습니다!</string>
  <string name="import_wallet_success">지갑으로 불러오기 성공!</string>
  <string name="import_watch_address_success">Watch 주소로 불러오기 성공!</string>
  <string name="choosing_a_language">언어 선택:</string>
  <string name="basic_functions">기본 기능</string>
  <string name="iopay_settings">ioPay 설정</string>
  <string name="address_books">주소록</string>
  <string name="networks">네트워크</string>
  <string name="go_now">지금 이동</string>
  <string name="iopay_now_supports_evm_and_btc_private_key">ioPay는 이제 EVM 및 BTC 개인 키를 지원합니다.</string>
  <string name="iopay_now_supports_evm_and_btc_recovery_phrase">ioPay는 이제 EVM 및 BTC 니모닉 문구를 지원합니다.</string>
  <string name="the_watch_mode_not_support_switch_between_evm_and_btc_networks">Watch 모드는 EVM과 BTC 네트워크 간의 전환을 지원하지 않습니다.</string>
  <string name="enter_dapp_name_or_url">DApp 이름 또는 URL을 입력하세요.</string>
  <string name="backup_your_wallet_set_up_your_wallet_network">지갑 백업, 지갑 네트워크 설정, 지갑 관리, 주소 관리, 보안 및 개인 정보 보호 등을 포함한 iopay 기본 설정을 살펴보세요.</string>
  <string name="input_the_path">경로 입력</string>
  <string name="path_invalid">경로가 잘못되었습니다</string>
  <string name="customize_path_desc">길을 찾지 못하셨나요? 경로 맞춤설정을 시도해 보세요.</string>
  <string name="btc_wallet_tips_1">• 이 주소는 비트코인 네트워크의 BTC만 허용합니다.</string>
  <string name="btc_wallet_tips_2">• 거래 전 BTC 주소 유형을 확인하시기 바랍니다.</string>
  <string name="dapp_not_support_bitcoin_network">이 프로젝트는 비트코인 네트워크를 지원하지 않으니 네트워크를 전환해 주세요.</string>
  <string name="note">참고</string>
  <string name="the_current_network_does_not_support_swap">현재 네트워크는 스왑을 지원하지 않습니다. 네트워크를 전환한 후 이용하시기 바랍니다.</string>
  <string name="the_current_network_is_not_bitcoin">현재 네트워크는 비트코인 네트워크가 아닙니다. 네트워크를 전환한 후 이용하시기 바랍니다.</string>
  <string name="wif_private_key">WIF 개인 키</string>
  <string name="hex_private_key">HEX 개인 키</string>
  <string name="note_unsuported_feature">이 기능은 아직 지원되지 않습니다. 계속 지켜봐 주시기 바랍니다.</string>
  <string name="supports_bitcoin_beta">ioPay는 이제 비트코인을 지원합니다!</string>
  <string name="supports_bitcoin_beta_desc">현재 비트코인 기능은 아직 베타 버전이며 BRC-20 관련 기능은 개발 중입니다.</string>
  <string name="explore">익스플로어</string>
  <string name="authorized_network">승인된 네트워크</string>
  <string name="this_site_is_unsafe">이 사이트는 안전하지 않습니다. 계속 진행하면 모든 자산을 잃을 수 있습니다.</string>
  <string name="after_authorization">승인 후 앱은 다음과 같은 권한을 얻게 됩니다 :</string>
  <string name="access_your_information">• 네트워크에서 귀하의 정보에 액세스합니다</string>
  <string name="ask_for_your_permission">• 거래를 수행하기 위해서 귀하의 허가를 요청합니다</string>
  <string name="note_dot">참고:</string>
  <string name="your_private_key_will_not_be_shared">- 이 승인으로 인해 개인 키는 공유되지 않습니다.</string>
  <string name="checking_for_updates">업데이트 체킹</string>
  <string name="iopay_is_already_the_latest_version">ioPay는 이미 최신 버전입니다</string>
  <string name="update_now_cap">지금 업데이트</string>
  <string name="update_your_iopay">ioPay 업데이트</string>
  <string name="wallet_is_connecting">지갑 연결 중</string>
  <string name="network_failed">네트워크 실패</string>
  <string name="there_appears_to_be_an_issue_with_the_network">연결된 네트워크에 문제가 있는 것 같습니다. 네트워크를 확인하십시오.</string>
  <string name="list_is_empty">리스트가 비어 있습니다.</string>
  <string name="you_are_currently_offline">현재 오프라인 상태입니다.</string>
  <string name="connection_to_the_blockchain_host_is_unavailable">블록체인 호스트에 연결할 수 없습니다.</string>
  <string name="retry">다시 시도</string>
  <string name="gas_fee_too_low">거래하기에는 가스 가격이 너무 낮습니다. 가스 가격을 수정하고 다시 시도하세요.</string>
  <string name="all_networks">모든 네트워크</string>
  <string name="switch_network_to_see_network_asset_details"><u> 네트워크 자산 세부정보를 보려면 네트워크를 전환하세요. </u></string>
  <string name="ethereum">이더 리움</string>
  <string name="btc">BTC</string>
  <string name="add_a_wallet">지갑 추가</string>
  <string name="create_wallet">지갑 생성</string>
  <string name="import_wallet">지갑으로 가져 오기</string>
  <string name="create_wallet_with_private_key">개인키로 지갑 만들기</string>
  <string name="create_new_wallet_with_mnemonic_phrase">니모닉 문구로 새 지갑 만들기</string>
  <string name="create_a_new_wallet_and_backup_private_key">새로운 지갑을 생성하고 개인키를 백업하세요.</string>
  <string name="add_wallet_cap">지갑 추가</string>
  <string name="your_private_key">개인 키</string>
  <string name="verify_private_key">개인 키 인증</string>
  <string name="keep_the_private_key_only_to_yourself">개인키는 본인만 보관하세요. 메모에 적어서 안전하게 보관하세요. 꼭 저장해두세요.</string>
  <string name="view_wallet_cap">지갑 보기</string>
  <string name="click_to_see_private_key">개인 키를 보려면 클릭하십시오.</string>
  <string name="swap_from">스왑 대상</string>
  <string name="minimum_receive">최소 수령 금액</string>
  <string name="you_send_only">당신은 보낸다</string>
  <string name="nodes_settings">노드 설정</string>
  <string name="chain_id_dot">체인 ID:</string>
  <string name="default_node">기본 노드</string>
  <string name="custom_node">커스텀 노드</string>
  <string name="polygon">Polygon</string>
  <string name="validate_wallet_exist">이 지갑은 이미 존재합니다. 다른 지갑을 사용해 보세요!</string>
  <string name="iopay_now_supports_evm_solana_and_btc_private_key">ioPay는 이제 EVM, Solana 및 BTC 개인 키(WIF/Hex)를 지원합니다.</string>
  <string name="iopay_now_supports_evm_solana_and_btc_recovery_phrase">ioPay는 이제 EVM, Solana 및 BTC 복구 문구를 지원합니다.</string>
  <string name="the_watch_mode_not_support_switch_between_evm_solana_and_btc_networks">워치 모드는 EVM, Solana 및 BTC 네트워크 간 전환을 지원하지 않습니다.</string>
  <string name="stake_migrate">마이그레이션</string>
  <string name="Okay">좋아요</string>
  <string name="appearance">외관</string>
  <string name="adjust_your_iopay_appearance">ioPay 모양 조정</string>
  <string name="system">체계</string>
  <string name="we_adjust_your_appearance">우리는 귀하의 기기의 시스템 설정에 따라 귀하의 외관을 조정합니다.</string>
  <string name="light">빛</string>
  <string name="dark">어두운</string>
  <string name="iopay_now_supports_solana">ioPay가 이제 Solana를 지원합니다</string>
  <string name="import_your_mnemonic_phrase_or_solana_private_key">니모닉 문구 또는 Solana 개인 키를 가져와서 Solana 네트워크로 원활하게 전환하여 해당 기능에 완전히 액세스하고 활용할 수 있습니다.</string>
  <string name="stake_method_edit">편집</string>
  <string name="dapp_not_support_current_network">이 프로젝트는 현재 네트워크를 지원하지 않습니다. 네트워크를 전환하세요.</string>
  <string name="if_the_wallets_you_are_expecting_are_not_displayed">원하는 지갑이 보이지 않는다면 HD 경로로 전환해보세요.</string>
  <string name="switch_now">지금 전환</string>
  <string name="wallet_not_support_current_network">이 지갑은 현재 네트워크를 지원하지 않습니다. 지갑과 네트워크를 전환하세요. 지갑을 전환하면 자동으로 해당 네트워크에 연결됩니다.</string>
  <string name="ucam_login_currently_only_supports_iotex">현재 Ucam 로그인은 IoTeX 지갑만 지원합니다(Solana 및 AA 지갑은 지원되지 않습니다).</string>
  <string name="tips">팁</string>
  <string name="no_nfts_yet">아직 NFT가 없습니다</string>
  <string name="login_with_iopay">ioPay로 로그인</string>
  <string name="go_to_nft_marketplace">NFT 마켓플레이스로 이동</string>
  <string name="create_or_import_your_solana_wallet">Solana 지갑(니모닉 문구/개인 키)을 만들거나 가져와서 Solana 네트워크로 원활하게 전환하여 모든 기능에 액세스하고 활용하세요.</string>
  <string name="add_solana_wallet">솔라나 지갑 추가</string>
  <string name="you_are_invited_to_iopay">ioPay에 초대되었습니다</string>
  <string name="referral_code">레퍼럴 코드</string>
  <string name="input_your_referral_code">레퍼럴 코드를 입력하세요</string>
  <string name="explore_iopay">ioPay 탐색</string>
  <string name="your_referral_code">레퍼럴 코드</string>
  <string name="gift_center">기프트 센터</string>
  <string name="invite_your_friends_to_iopay">친구를 ioPay에 초대하기</string>
  <string name="referral_link">레퍼럴 링크</string>
  <string name="invite_friends_to_earn_points">친구를 초대하여 포인트 적립</string>
  <string name="invited_users_must_enter_the_referral_code">• 초대된 사용자는 첫 번째 다운로드 후 추천 코드를 입력해야 유효합니다.</string>
  <string name="invitations_are_limited_to_a_single_device">• 초대는 단일 기기에만 허용됩니다. 동일한 기기에서 여러 번 다운로드하거나 지갑을 생성하는 것은 무효로 간주됩니다.</string>
  <string name="iopay_reserves_the_right_to_interpret_the_rules">- ioPay는 규정을 해석할 권리를 보유합니다.</string>
  <string name="join_iopay_and_earn_rewards">ioPay를 열고 보상을 받아 가세요!</string>
  <string name="you_have_completed_task_and_earn">축하합니다! 태스크를 완료되었고 %s 포인트를 획득했습니다!</string>
  <string name="the_current_wallet_is_not_support">현재 지갑은 이 행위를 실행할 수 없습니다.</string>
  <string name="enable_certain_rewards_and_functionality">신뢰할 수 있는 GPS 위치의 증거를 제공하여 Web3 DApps에서 특정 보상 및 기능을 활성화합니다.</string>
  <string name="please_switch_our_network_to_iotex">네트워크를 IoTeX로 전환해주세요.</string>
  <string name="dex_screener">DEX 스크리너</string>
  <string name="please_add_the_network_with_chain">이 DApp에 연결하기 전에 지갑에 체인 ID %s 의 네트워크를 추가하세요.</string>
  <string name="how_to_receive_from_other_wallets">다른 지갑에서 받는 방법은 무엇입니까?</string>
  <string name="how_to_receive_from_exchanges">거래소에서 받는 방법은 무엇입니까?</string>
  <string name="steps_in_another_wallet_app">상세 단계 (다른 지갑의 앱에서)</string>
  <string name="select_send_or_transfer_in_some_wallets">보내기(아니면 일부 지갑에서 전송)를 선택하세요.</string>
  <string name="select_iotx_on_iotex_mainnet_to_send">보낼 IOTX(IoTeX 메인넷)를 선택하세요.</string>
  <string name="enter_your_iopay_wallet_address_as_the_recipient">받는 사람으로 ioPay 지갑 주소를 입력하거나 ioPay 앱에 나타나는 지갑 QR 코드를 스캔하세요.</string>
  <string name="select_the_network_that_matches_your_iopay_wallet_address">IoPay 지갑 주소와 일치하는 네트워크를 선택합니다.</string>
  <string name="iotex_network_mainnet">IoTeX 네트워크 메인넷</string>
  <string name="enter_amount_confirm_send_and_find_the_token">금액을 입력하고 보내기를 확인한 후 ioPay 지갑에서 토큰을 찾으세요.</string>
  <string name="steps_in_the_exchange_app">상세 단계 (거래소 앱 내)</string>
  <string name="select_iotx_to_withdraw">IOTX를 선택하고 출금하세요.</string>
  <string name="enter_amount_confirm_withdrawal">금액을 입력하고 출금을 확인한 후 ioPay 지갑에서 이 토큰을 찾으십시오.</string>
  <string name="receive_iotx_from_exchanges">거래소에서 IOTX 받기:</string>
  <string name="are_you_sure_you_want_to_delete">삭제하시겠습니까?</string>
  <string name="go_back_to_the_dapp_and_proceed">DApp으로 돌아가서 계속 진행하세요.</string>
  <string name="for_your_assets_security">자산 보안을 위해 개인 키와 니모닉 문구를 백업해 두시기 바랍니다.</string>
  <string name="edit_stake">스테이크 편집</string>
  <string name="receive_sol_on_solana">• 이 주소는 Solana 네트워크의 SOL만 허용합니다.</string>
  <string name="background_location">ioPay는 백그라운드 위치를 신청해야 합니다.</string>
  <string name="geo_will_upload_location_data_to_the_blockchain">Geo는 DePIN 관련 기능을 활성화하기 위해 블록체인에 위치 데이터를 업로드합니다. 앱이 백그라운드에서 실행 중일 때에도 사용자는 위치 데이터를 계속 업로드할 수 있습니다. 물론 사용자는 Geo 설정 페이지를 통해 백그라운드 위치 추적 및 업로드를 비활성화할 수도 있습니다.</string>
  <string name="disconnect_all">모든 연결을 끊다</string>
  <string name="authorized_network_dot">공인 네트워크:</string>
  <string name="connected_dapp">연결된 DApp</string>
  <string name="due_to_the_volatility">시장의 변동성이 있으므로 DYOR로 문의해 주세요.</string>
  <string name="add_liquidity">유동성 추가</string>
  <string name="iopay_supports_the_default_bip44_standard">ioPay는 기본 BIP44 표준(IOTEX HD PATH: 304)/(EVM HD PATH:60)/(BTC PATH)/(SOLANA PATH) 및 사용자 지정 경로를 지원합니다. 자세히 알아보세요.</string>
  <string name="sec_or_longer">초 또는 그 이상</string>
  <string name="hd_path">HD 경로</string>
  <string name="bino_ai">Bino AI</string>
  <string name="stake_iotx_and_earn_rewards">IOTX를 스테이킹하고 보상 받기</string>
  <string name="the_staking_interest_rate">IoTeX를 사용하면 스테이킹 이자율이 최대 %s APR입니다.</string>
  <string name="market_details">시장 세부 정보</string>
  <string name="market_value">시장 가치</string>
  <string name="billion">10억</string>
  <string name="million">백만</string>
  <string name="total_transaction_volume">총 거래량(24시간)</string>
  <string name="turnover_market_value">매출/시장 가치</string>
  <string name="circular_supply">순환 공급</string>
  <string name="all_time_high">역대 최고</string>
  <string name="record_low">기록적인 최저치</string>
  <string name="fully_diluted">완전 희석</string>
  <string name="hide_assets_1_usd">자산 숨기기  &lt; 1 USD</string>
  <string name="would_you_like_to_import_these_tokens">이 토큰을 홈페이지의 토큰 목록으로 가져오시겠습니까?</string>
  <string name="please_update_the_iopay">계속하려면 ioPay를 최신 버전으로 업데이트하세요.</string>
  <string name="search_for_wallet_name_or_address">지갑 이름이나 주소를 검색하세요.</string>
  <string name="aa_wallet_operates_on_smart_contracts_only">AA 월렛은 스마트 컨트랙트로 운영되므로 중앙화된 거래소로부터 자산을 받기 위해 이 주소를 사용하지 마시기 바랍니다.</string>
  <string name="the_address_is_copied_to_the_clipboard">주소가 클립보드에 복사되었습니다.</string>
  <string name="this_is_not_an_address_you_interacted">최근에 접속하신 주소가 아닙니다. 주의해서 이용해 주세요.</string>
  <string name="price_impact_is_too_high">가격 충격이 너무 큽니다. 이 거래에서 자금의 상당 부분을 잃게 될 것입니다.</string>
  <string name="enter_a_amount">금액 입력</string>
  <string name="select_token">토큰 선택</string>
  <string name="slippage_tolerance">미끄러짐 허용 범위</string>
  <string name="price">가격</string>
  <string name="minimum_received">최소 수령</string>
  <string name="maximum_pay">최대 급여</string>
  <string name="price_impact">가격 영향</string>
  <string name="fee">수수료</string>
  <string name="route">노선</string>
  <string name="mimo_smart_router">미모 스마트 라우터</string>
  <string name="mimo_smart_router_cap">미모 스마트 라우터</string>
  <string name="best_trade">최고의 무역</string>
  <string name="point_of_slip">슬립의 포인트</string>
  <string name="slippage_is_when_the_transaction_price">슬리피지는 거래 가격이 주문 시점의 예상 가격과 다른 경우로, 일반적으로 주문 시점부터 마감 시점까지의 가격 변화로 인해 발생합니다. 가격이 슬립 포인트 설정을 넘어 변경되면 거래는 취소되지만 연료 요금은 여전히 체인에 부과됩니다.</string>
  <string name="smart_router">스마트 라우터</string>
  <string name="when_available_aggregate_v2_v3">사용 가능한 경우 V2, V3를 집계하여 더 나은 가격을 쿼리하고, 사용 불가능한 경우 미모 V2의 가격만 쿼리합니다.</string>
  <string name="set_slip_point">슬립 포인트 설정</string>
  <string name="optimal_slip_point">최적의 슬립 포인트</string>
  <string name="customization">커스터마이징</string>
  <string name="minimum_acquired_quantity">최소 획득 수량</string>
  <string name="optimal_slip_point_present">최적의 슬립 포인트(%s)</string>
  <string name="according_to_the_current_trading">성공적인 거래를 돕기 위해 현재 거래 코인에 따라 적절한 슬립 포인트 값을 권장합니다.</string>
  <string name="search_name_or_paste_address">이름 검색 또는 주소 붙여넣기</string>
  <string name="selection_network_dot">선택 네트워크:</string>
  <string name="insufficient_balance">잔액 부족</string>
  <string name="please_switch_your_vpn">VPN 노드 또는 네트워크를 전환하고 다시 시도하세요.</string>
  <string name="iopay_binoai">ioPay BinoAI</string>
  <string name="may_be_due_to_price_fluctuation">가격 변동으로 인한 것일 수 있으니 슬리피지 비율을 높여보세요.</string>
  <string name="network_error_please_try_again">네트워크 오류입니다. 다시 시도해 주세요.</string>
  <string name="one_day">1일</string>
  <string name="one_week">1주</string>
  <string name="one_month">1개월</string>
  <string name="one_year">1년</string>
  <string name="swap_to">스왑 대상</string>
  <string name="ignore_this_version">이 버전을 무시하세요</string>
  <string name="crate_new_wallet_for_the_network">네트워크를 위한 새로운 지갑을 만드세요</string>
  <string name="wallet_not_support_current_network_2">이 지갑은 현재 네트워크를 지원하지 않습니다. 지갑과 네트워크를 변경해 주세요. 지갑이나 네트워크를 변경하면 자동으로 해당 네트워크 또는 지갑에 연결됩니다.</string>
  <string name="to_trade_stocks_please_switch_to_the_solana_network">주식 거래를 하려면 Solana 네트워크로 전환하세요.</string>
  <string name="please_switch_wallets_or_create_a_new_wallet" formatted="false">현재 지갑은 %s 네트워크를 지원하지 않습니다. 지갑을 변경하거나 새 %s 지갑을 생성하세요.</string>
  <string name="user_feedback">사용자 피드백</string>
  <string name="your_feedback">피드백</string>
  <string name="contact_email">연락처 이메일</string>
  <string name="enter_email_address">이메일 주소 입력</string>
  <string name="find_us">저희를 찾아주세요:</string>
  <string name="problem_picture_caption">문제 그림 캡션</string>
  <string name="thanks_for_your_feedback">피드백을 보내주셔서 감사합니다!</string>
  <string name="exact_out_feature_currently_not_support_solana">정확히 아웃 기능은 현재 솔라나를 지원하지 않습니다</string>
  <string name="your_stake">귀하의 지분</string>
  <string name="stake_with_iotex">아이오텍스 스테이크</string>
  <string name="per_year">연간</string>
  <string name="learn_more_cap">Learn More</string>
  <string name="start_earning">적립 시작</string>
  <string name="iotex_staked_iotx">iotex 스테이킹 IOTX</string>
  <string name="secure_chain">보안 체인</string>
  <string name="by_staking_token_holders_enhance">토큰 보유자는 스테이킹을 통해 loTeX 네트워크의 보안과 효율성을 강화합니다.</string>
  <string name="earn_rewards">리워드 적립</string>
  <string name="vote">투표</string>
  <string name="any_token_holder_that_stakes">IOTX를 스테이킹하는 토큰 보유자는 누구나 자신이 신뢰하는 한 명 이상의 대표자에게 투표하여 블록을 채굴할 수 있습니다.</string>
  <string name="past_performance_does_not_guarantee">· 과거의 실적은 미래의 결과를 보장하지 않습니다.</string>
  <string name="the_estimated_apy_is_partially_derived">· 예상 APY는 네트워크 인플레이션율에 따라 부분적으로 산출되며, 이는 변경될 수 있으며 ioPay의 통제 범위를 벗어납니다. 실제 수익률은 시간이 지남에 따라 변동될 수 있습니다.</string>
  <string name="annual_yield">연간 수익률</string>
  <string name="stake_with_iotx">IOTX로 스테이크</string>
  <string name="staked_iotx">스테이킹된 IOTX</string>
  <string name="with_iotx_the_staking">IOTX의 스테이킹 이자율은 최대 연 6~11%입니다.</string>
</resources>
