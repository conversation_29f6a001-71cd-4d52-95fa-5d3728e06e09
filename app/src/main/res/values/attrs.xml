<?xml version="1.0" encoding="utf-8"?>
<resources>
    <attr name="inputType" format="enum">
        <enum name="text" value="0x00000001" />
        <enum name="number" value="0x00000002" />
        <enum name="numberDecimal" value="0x00002002" />
    </attr>
    <attr name="customTextStyle" format="enum">
        <enum name="bold" value="2" />
        <enum name="medium" value="1" />
        <enum name="regular" value="0" />
    </attr>

    <declare-styleable name="DINTextView">
        <attr name="textType">
            <enum name="NORMAL" value="0"/>
            <enum name="BOLD" value="1"/>
        </attr>
    </declare-styleable>

    <declare-styleable name="PasswordEditView">
        <attr name="length" format="integer"/>
    </declare-styleable>
    <declare-styleable name="CircleImageView">
        <attr name="civ_border_width" format="dimension" />
        <attr name="civ_border_color" format="color" />
        <attr name="civ_border_overlay" format="boolean" />
        <attr name="civ_circle_background_color" format="color" />
    </declare-styleable>

    <declare-styleable name="RobotoFontTextView">
        <attr name="customTextStyle" />
    </declare-styleable>

    <declare-styleable name="EditTextSelectView">
        <attr name="etHint" format="string" />
        <attr name="inputType" />
    </declare-styleable>

    <declare-styleable name="EditTextStateful">
        <attr name="hint" format="string" />
        <attr name="background" format="reference"/>
        <attr name="errorBackground" format="reference"/>
        <attr name="inputBackground" format="reference"/>
        <attr name="textSize" format="dimension"/>
        <attr name="textColor" format="color"/>
        <attr name="height" format="dimension"/>
        <attr name="inputType" />
        <attr name="hintColor" format="color"/>
        <attr name="paddingStart" format="dimension"/>
        <attr name="paddingEnd" format="dimension"/>
    </declare-styleable>
    <declare-styleable name="IoViewPagerIndicator">
        <attr name="ioIndicatorCount" format="integer" />
        <attr name="ioSelectedIndicatorDrawable" format="reference" />
        <attr name="ioUnselectedIndicatorDrawable" format="reference" />
        <attr name="ioIndicatorWidth" format="dimension" />
        <attr name="ioIndicatorHeight" format="dimension" />
        <attr name="ioIndicatorPadding" format="dimension" />
    </declare-styleable>

    <declare-styleable name="WalletActionSelector">
        <attr name="title" format="string" />
        <attr name="caption" format="string" />
        <attr name="selectedIcon" format="reference" />
        <attr name="unselectedIcon" format="reference" />
    </declare-styleable>

    <declare-styleable name="AsyncViewStub">
        <attr name="layout" format="reference" />
    </declare-styleable>
    <!-- 垂直方向的虚线 -->
    <declare-styleable name="DashLineView">
        <!-- 虚线颜色 -->
        <attr name="lineColor" format="color"/>
        <!-- 虚线朝向 -->
        <attr name="lineOrientation" format="enum">
            <enum name="horizontal" value="0"/>
            <enum name="vertical" value="1"/>
        </attr>
    </declare-styleable>
</resources>