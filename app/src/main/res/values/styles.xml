<resources>

    <style name="selectEditText" parent="Theme.AppCompat.Light">
        <item name="colorControlNormal">@color/selector_background</item>
        <item name="colorControlActivated">@color/colorWarning</item>
    </style>

    <style name="ProgressCustomTheme" parent="android:Theme.Material.Dialog.NoActionBar">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:colorBackgroundCacheHint">@null</item>
    </style>


    <style name="ToolbarCustomStyle" parent="ThemeOverlay.AppCompat.Dark.ActionBar">
        <item name="colorControlNormal">@color/white</item>
    </style>

    <style name="DefaultThumbnail">
        <item name="android:layout_width">@dimen/wallet_create_form_thumbnail_width</item>
        <item name="android:layout_height">@dimen/wallet_create_form_thumbnail_height</item>
        <item name="android:scaleType">centerInside</item>
    </style>
    <style name="Theme_dialog" parent="@android:style/Theme.Dialog">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowAnimationStyle">@style/dialog_anim_style</item>
        <item name="android:windowSoftInputMode">stateHidden|adjustPan</item>
    </style>
    <style name="dialog_anim_style" parent="android:Animation">
        <item name="android:windowEnterAnimation">@anim/pop_enter_anim</item>
        <item name="android:windowExitAnimation">@anim/pop_exit_anim</item>
    </style>

    <style name="ViewTheme" parent="@style/Widget.AppCompat.EditText">
        <item name="android:textColorHint">@color/color_title_thr</item>
        <item name="colorControlNormal">@color/color_title</item>
        <item name="colorControlActivated">@color/color_title</item>
    </style>
    <style name="EditDark" parent="Theme.AppCompat.Light">
        <item name="colorControlNormal">@color/dark_background</item>
        <item name="colorControlActivated">@color/colorPrimary</item>
    </style>

    <style name="EditTrans" parent="Theme.AppCompat.Light">
        <item name="colorControlNormal">@color/color_line_diver</item>
        <item name="colorControlActivated">@color/colorPrimary</item>
    </style>

    <style name="InputTheme">
        <item name="theme">@style/ViewTheme</item>
    </style>

    <style name="TabButton" parent="@style/Widget.MaterialComponents.Button.OutlinedButton">
        <item name="strokeWidth">0dp</item>
        <item name="backgroundTint">@android:color/transparent</item>
    </style>

    <style name="MyTabText" parent="@style/Widget.AppCompat.ActionBar.TabText">
        <item name="android:textSize">@dimen/font_size_middle</item>
    </style>

    <style name="StakeTabStyle" parent="Widget.Design.TabLayout">
        <item name="android:textAllCaps">false</item>
        <item name="textAllCaps">false</item>
        <item name="android:textSize">16sp</item>
        <item name="tabSelectedTextColor">@color/white</item>
        <item name="tabTextColor">@color/gray_B4B8CB</item>
        <item name="tabIndicatorHeight">2dp</item>
    </style>

    <style name="bottom_normal_text">
        <item name="android:textColor">@color/menu_grey_color</item>
        <item name="android:textSize">10sp</item>
    </style>
    <style name="bottom_selected_text">
        <item name="android:textColor">@color/colorPrimary</item>
        <item name="android:textSize">10sp</item>
    </style>

    <style name="CommonDialog" parent="@android:style/Theme.Dialog">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:background">@android:color/transparent</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowSoftInputMode">stateVisible|adjustPan</item>
    </style>

    <style name="DialogAnimation" parent="android:Animation">
        <item name="android:windowEnterAnimation">@anim/dialog_enter</item>
        <item name="android:windowExitAnimation">@anim/dialog_exit</item>
    </style>

    <style name="DialogFadeAnimation" parent="android:Animation">
        <item name="android:windowEnterAnimation">@android:anim/fade_in</item>
        <item name="android:windowExitAnimation">@android:anim/fade_out</item>
    </style>

    <declare-styleable name="MaterialSwitch">
        <attr name="secondaryTextColor" format="color" />
        <attr name="secondaryTextSize" format="dimension" />
        <attr name="secondaryTextFont" format="integer" />
    </declare-styleable>

    <style name="SwitchTheme" parent="@android:style/TextAppearance.Small">
        <item name="android:textColor">@color/white</item>
        <item name="colorSwitchThumbNormal">@color/white_alpha_30</item>
        <item name="colorControlActivated">@color/color_617AFF</item>
        <item name="fontFamily">@font/space_grotesk_bold</item>
        <item name="android:fontFamily">@font/space_grotesk_bold</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textSize">18sp</item>
    </style>

    <style name="LoadingProgress" parent="Widget.AppCompat.ProgressBar.Horizontal">
        <item name="android:indeterminateOnly">false</item>
        <item name="android:progressDrawable">@drawable/loading_progress</item>
        <item name="android:minHeight">6dp</item>
        <item name="android:maxHeight">6dp</item>
    </style>

    <style name="TabStyle" parent="Widget.Design.TabLayout">
        <item name="android:textAllCaps">false</item>
        <item name="textAllCaps">false</item>
        <item name="android:textSize">14sp</item>
        <item name="tabSelectedTextColor">@color/colorPrimary</item>
        <item name="tabTextColor">@color/gray_B4B8CB</item>
        <item name="tabIndicatorHeight">1dp</item>
    </style>

    <style name="LoadingDialog">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowFrame">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowIsTranslucent">false</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowAnimationStyle">@android:style/Animation.Dialog</item>
    </style>

    <style name="BottomSheetAnimation">
        <item name="android:windowEnterAnimation">@anim/slide_up</item>
        <item name="android:windowExitAnimation">@anim/slide_down</item>
    </style>

</resources>
