<?xml version="1.0" encoding="utf-8"?>
<resources>

    <!-- Base application theme. -->
    <style name="AppTheme" parent="Theme.MaterialComponents.Light.NoActionBar">
        <!-- Customize your theme here. -->
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="android:statusBarColor">@color/transparent</item>
        <item name="colorPrimaryDark">@color/colorPrimary</item>
        <item name="colorAccent">@color/colorAccent</item>
        <item name="colorControlHighlight">@color/colorPrimaryDark</item>
        <item name="android:windowBackground">@color/theme_window_back</item>
        <item name="android:textColor">@color/color_title</item>
        <item name="android:fitsSystemWindows">true</item>
        <item name="colorControlNormal">@color/selector_background</item>
        <item name="android:editTextColor">@color/colorPrimary</item>
        <item name="colorOnSurface">@color/color_title_thr</item>
        <item name="android:fontFamily">@font/roboto_font</item>
    </style>

    <style name="SplashTheme" parent="AppTheme">
        <item name="android:windowBackground">@drawable/icon_splash_back</item>
        <item name="android:statusBarColor">@color/transparent</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowActionBar">false</item>
        <item name="android:windowTranslucentNavigation">true</item>
        <item name="android:windowTranslucentStatus">true</item>
    </style>

</resources>