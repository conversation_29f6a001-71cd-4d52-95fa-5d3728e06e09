buildscript {
    ext.kotlin_version = '1.9.22'
    ext.kotlinVersion = '1.9.22'
    ext.kotlinCoroutinesVersion = '1.3.9'
    ext.anko_version='0.10.8'
    ext.apolloVersion='2.5.14'
    ext.minSdkVersion='24'
//    ext.compileSdkVersion='34'
    repositories {
        google()
        mavenCentral()
        jcenter(){url'https://maven.aliyun.com/repository/jcenter'}
        maven { url "https://jitpack.io" }
        maven {
            url 'https://maven.fabric.io/public'
        }
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:7.3.1'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        classpath "org.jetbrains.kotlin:kotlin-serialization:$kotlin_version"
        classpath 'com.github.dcendents:android-maven-gradle-plugin:2.0'
        classpath 'com.google.gms:google-services:4.4.2'  // Google Services plugin
        classpath 'io.fabric.tools:gradle:1.30.0'  // Crashlytics plugin
        classpath "com.apollographql.apollo:apollo-gradle-plugin:$apolloVersion"
        classpath 'com.google.firebase:perf-plugin:1.4.2'  // Performance Monitoring plugin
        classpath("com.facebook.react:react-native-gradle-plugin")
        classpath "org.jmailen.gradle:kotlinter-gradle:3.10.0"
        classpath "com.google.protobuf:protobuf-gradle-plugin:0.9.4"
    }
}

plugins {
    id 'org.jmailen.kotlinter' version "3.10.0"
    id 'com.google.devtools.ksp' version '1.9.22-1.0.17' apply false
}



allprojects {
    repositories {
        google()
        mavenCentral()
        jcenter(){url'https://maven.aliyun.com/repository/jcenter'}
        maven { url "https://jitpack.io" }
        maven { url 'https://maven.fabric.io/public' }
    }
}

subprojects {
    afterEvaluate {project ->
        if (project.hasProperty("android")) {
            android {
                compileSdkVersion 34
//                buildToolsVersion "31.0.0"
            }
        }
    }
}

apply plugin: "com.facebook.react.rootproject"

//task clean(type: Delete) {
//    delete rootProject.buildDir
//}

kotlinter {
    ignoreFailures = false
    reporters = ['checkstyle', 'plain']
}