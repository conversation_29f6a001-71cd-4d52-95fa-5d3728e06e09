## For more details on how to configure your build environment visit
# http://www.gradle.org/docs/current/userguide/build_environment.html
#
# Specifies the JVM arguments used for the daemon process.
# The setting is particularly useful for tweaking memory settings.
# Default value: -Xmx1024m -XX:MaxPermSize=256m
# org.gradle.jvmargs=-Xmx2048m -XX:MaxPermSize=512m -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8
#
# When configured, <PERSON>rad<PERSON> will run in incubating parallel mode.
# This option should only be used with decoupled projects. More details, visit
# http://www.gradle.org/docs/current/userguide/multi_project_builds.html#sec:decoupled_projects
# org.gradle.parallel=true
#Wed Dec 16 14:29:29 CST 2020

android.useDeprecatedNdk=true
android.enableR8.fullMode=false
android.enableJetifier=true
#android.jetifier.blacklist=bcprov
android.useAndroidX=true
kotlin.code.style=official
org.gradle.jvmargs=-XX\:MaxHeapSize\=2g -Dkotlin.daemon.jvm.options\="-Xmx4g" -Xmx4g
android.injected.testOnly=false

# false: module true: applacation.
isModule=false
android.jetifier.ignorelist=com.squareup.moshi

# Use this property to specify which architecture you want to build.
# You can also override it from the CLI using
# ./gradlew <task> -PreactNativeArchitectures=x86_64
reactNativeArchitectures=armeabi-v7a,arm64-v8a
# Use this property to enable support to the new architecture.
# This will allow you to use TurboModules and the Fabric render in
# your application. You should enable this flag either if you want
# to write custom TurboModules/Fabric components OR use libraries that
# are providing them.
newArchEnabled=false
# Use this property to enable or disable the Hermes JS engine.
# If set to false, you will be using JSC instead.
hermesEnabled=false
#react.internal.disableJavaVersionAlignment=true

#android.enableBuildCache=true
android.enbleD8=true

#??kotlin????????
kotlin.incremental=true
kotlin.incremental.java=true
kotlin.incremental.js=true
kotlin.caching.enabled=true
#??kotlin????
kotlin.parallel.tasks.in.project=true


#??kapt
# Decrease gradle builds time
#kapt.use.worker.api=true
#???? kapt1.3.30??????
#kapt.incremental.apt=true
#kapt avoiding ???kapt???????????????????????????:app:kaptGenerateStubsDebugKotlin???
#kapt.include.compile.classpath=false
# Enable android.databinding.annotationprocessor.ProcessDataBinding (DYNAMIC)
android.databinding.incremental=true



#org.gradle.jvmargs=-Xmx4608m
#???????gradle???JVM???????????????
#daemon????????????task?????????????????????
org.gradle.daemon=true
#????
org.gradle.parallel=true
#??Gradle?????Gradle3.5????
org.gradle.caching=true
android.enableD8=true
#???? Project Property ??????????????????????????
#https://firebase.google.cn/docs/perf-mon/disable-sdk?platform=android#project-property-flag
firebasePerformanceInstrumentationEnabled=false