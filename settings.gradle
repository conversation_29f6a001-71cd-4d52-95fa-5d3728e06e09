plugins {
    id 'org.gradle.toolchains.foojay-resolver-convention' version '0.5.0'
}
apply from: file("../node_modules/@react-native-community/cli-platform-android/native_modules.gradle"); applyNativeModulesSettingsGradle(settings)
includeBuild('../node_modules/@react-native/gradle-plugin')
include ':app', ':iopaysdk', ':base'
include ':w3bstream'
include ':walletconnect2'
include ':web3'
include ':web3j-utils'
include ':web3j'
include ':lockscreen'
include ':userop'
include ':bitcoin-lib'
include ':solana'
