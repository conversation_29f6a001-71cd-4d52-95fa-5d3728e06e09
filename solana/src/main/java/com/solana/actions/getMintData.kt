package com.solana.actions

import com.solana.api.getAccountInfo
import com.solana.api.getMultipleAccountsInfo
import com.solana.core.PublicKey
import com.solana.models.buffer.Mint
import com.solana.programs.TokenProgram
import com.solana.vendor.ResultError
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import kotlinx.serialization.Serializable

@Serializable
data class MintResp(
    val value:MintData
)
@Serializable
data class MintData(
    val data: List<String>,
)

@Serializable
data class MintToken(
    val decimals: Int,
)

fun Action.getMintData(
    mintAddress: PublicKey,
    onComplete: ((Result<MintResp>) -> Unit)
){
    CoroutineScope(dispatcher).launch {
        onComplete(getMintData(mintAddress))
    }
}

suspend fun Action.getMintData(
    mintAddress: PublicKey,
): Result<MintResp> {
    val mintResp = this.api.getAccountInfo(MintResp.serializer(), mintAddress).getOrElse {
        return Result.failure(it)
    }
    return Result.success(mintResp)
}

fun Action.getMultipleMintDatas(
    mintAddresses: List<PublicKey>,
    programId: PublicKey = TokenProgram.PROGRAM_ID,
    onComplete: ((Result<Map<PublicKey, Mint>>) -> Unit)
){
    CoroutineScope(dispatcher).launch {
        onComplete(getMultipleMintDatas(mintAddresses, programId))
    }
}

suspend fun Action.getMultipleMintDatas(
    mintAddresses: List<PublicKey>,
    programId: PublicKey = TokenProgram.PROGRAM_ID
): Result<Map<PublicKey, Mint>> {
    val account = this.api.getMultipleAccountsInfo(Mint.serializer(), mintAddresses).getOrElse {
        return Result.failure(it)
    }
    if(account.find { it?.owner == programId.toBase58()} == null) {
        return Result.failure(ResultError("Invalid mint owner"))
    }
    val values = account.mapNotNull { it?.data }
    if(values.size != mintAddresses.size) {
        return Result.failure(ResultError("Some of mint data are missing"))
    }

    val mintDict = mutableMapOf<PublicKey,Mint>()
    mintAddresses.forEachIndexed { index, address ->
        mintDict[address] = values[index]
    }
    return Result.success(mintDict.toMap())
}