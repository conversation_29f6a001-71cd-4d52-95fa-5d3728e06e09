[{"inputs": [{"internalType": "string", "name": "_url", "type": "string"}, {"internalType": "address[]", "name": "_signers", "type": "address[]"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "string[]", "name": "urls", "type": "string[]"}, {"internalType": "bytes", "name": "callData", "type": "bytes"}, {"internalType": "bytes4", "name": "callbackFunction", "type": "bytes4"}, {"internalType": "bytes", "name": "extraData", "type": "bytes"}], "name": "OffchainLookup", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address[]", "name": "signers", "type": "address[]"}], "name": "NewSigners", "type": "event"}, {"inputs": [{"internalType": "address", "name": "target", "type": "address"}, {"internalType": "uint64", "name": "expires", "type": "uint64"}, {"internalType": "bytes", "name": "request", "type": "bytes"}, {"internalType": "bytes", "name": "result", "type": "bytes"}], "name": "makeSignatureHash", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "bytes", "name": "name", "type": "bytes"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "resolve", "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes", "name": "response", "type": "bytes"}, {"internalType": "bytes", "name": "extraData", "type": "bytes"}], "name": "resolveWithProof", "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "signers", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "interfaceID", "type": "bytes4"}], "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "pure", "type": "function"}, {"inputs": [], "name": "url", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes", "name": "request", "type": "bytes"}, {"internalType": "bytes", "name": "response", "type": "bytes"}], "name": "verify", "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}], "stateMutability": "view", "type": "function"}]